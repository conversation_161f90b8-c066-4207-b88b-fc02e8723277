<template>
  <div :class="containerClasses">
    <div :class="spinnerClasses"></div>
    <p v-if="text" :class="textClasses">{{ text }}</p>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'primary' | 'secondary' | 'white'
  text?: string
  center?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  variant: 'primary',
  center: false
})

const containerClasses = computed(() => {
  const baseClasses = ['flex flex-col items-center gap-3']
  
  if (props.center) {
    baseClasses.push('justify-center min-h-32')
  }
  
  return baseClasses
})

const spinnerClasses = computed(() => {
  const baseClasses = ['animate-spin rounded-full border-2 border-solid']
  
  // 尺寸样式
  const sizeClasses = {
    sm: 'w-4 h-4 border-2',
    md: 'w-6 h-6 border-2',
    lg: 'w-8 h-8 border-2',
    xl: 'w-12 h-12 border-3'
  }
  
  // 颜色样式
  const variantClasses = {
    primary: 'border-primary-200 border-t-primary-500',
    secondary: 'border-gray-200 border-t-gray-500',
    white: 'border-white/30 border-t-white'
  }
  
  return [
    ...baseClasses,
    sizeClasses[props.size],
    variantClasses[props.variant]
  ]
})

const textClasses = computed(() => {
  const baseClasses = ['text-sm font-medium animate-pulse']
  
  const variantClasses = {
    primary: 'text-primary-600 dark:text-primary-400',
    secondary: 'text-gray-600 dark:text-gray-400',
    white: 'text-white'
  }
  
  return [
    ...baseClasses,
    variantClasses[props.variant]
  ]
})
</script>
