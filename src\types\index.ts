// 数据库表类型定义

export interface Resource {
  id?: number
  url: string
  title: string
  description: string // 支持markdown
  cover_image_url?: string
  category_id: number
  view_count: number
  created_at: Date
  updated_at: Date
}

export interface Category {
  id?: number
  name: string
  parent_id: number // 根分类为0
  resource_count: number
  sort_order: number
  created_at: Date
  isTemporary?: boolean // 标记临时分类（编辑模式下创建但未保存）
}

export interface Tag {
  id?: number
  name: string
  color: string
  resource_count: number
  created_at: Date
  isTemporary?: boolean // 标记临时标签（编辑模式下创建但未保存）
}

export interface ResourceTag {
  resource_id: number
  tag_id: number
}

// 扩展类型，用于前端展示
export interface ResourceWithDetails extends Resource {
  category?: Category
  tags?: Tag[]
}

export interface CategoryWithChildren extends Category {
  children?: CategoryWithChildren[]
}

// 搜索和排序相关类型
export type SortOption = 'created_at' | 'updated_at' | 'view_count' | 'title'
export type SortOrder = 'asc' | 'desc'

export interface SearchOptions {
  keyword?: string
  category_id?: number
  tag_ids?: number[]
  sort_by?: SortOption
  sort_order?: SortOrder
  limit?: number
  offset?: number
}

// 主题相关类型
export type ThemeMode = 'light' | 'dark' | 'system'

// 表单相关类型
export interface ResourceForm {
  url: string
  title: string
  description: string
  cover_image_url: string
  category_id: number | null
  tag_ids: number[]
}

export interface CategoryForm {
  name: string
  parent_id: number
  sort_order: number
}

export interface TagForm {
  name: string
  color: string
}

// AI相关类型定义

// AI服务提供商类型
export type AiProvider =
  | 'openai' // OpenAI GPT系列
  | 'claude' // Anthropic Claude系列
  | 'gemini' // Google Gemini系列
  | 'baidu' // 百度文心一言
  | 'alibaba' // 阿里通义千问
  | 'tencent' // 腾讯混元
  | 'zhipu' // 智谱GLM
  | 'moonshot' // 月之暗面Kimi
  | 'bytedance' // 字节豆包
  | 'custom' // 添加AI服务商

// 数据库相关类型定义

// AI服务商接口
export interface AiProviderEntity {
  id: string
  name: string
  display_name: string
  type: 'builtin' | 'custom'
  base_url?: string
  color?: string
  icon?: string
  description?: string
  is_active: boolean
  sort_order: number
  created_at: Date
  updated_at: Date
}

// AI模型接口
export interface AiModelEntity {
  id: string
  provider_id: string
  name: string
  display_name: string
  type: 'builtin' | 'custom'
  max_tokens?: number
  description?: string
  is_active: boolean
  sort_order: number
  created_at: Date
  updated_at: Date
}

// AI配置实体接口
export interface AiConfigEntity {
  id: string
  name: string
  provider_id: string
  model_id?: string
  api_key?: string
  base_url?: string
  temperature: number
  max_tokens: number
  timeout: number
  system_prompt?: string
  custom_headers?: Record<string, string>
  custom_params?: Record<string, any>
  is_default: boolean
  is_enabled: boolean
  created_at: Date
  updated_at: Date
}

// 自定义模型接口（保持向后兼容）
export interface CustomModel {
  id: string
  provider: AiProvider
  name: string
  label: string
  createdAt: Date
}

// AI配置接口
export interface AiConfig {
  id: string
  name: string // 配置名称，用于用户识别
  provider: AiProvider
  apiKey: string
  baseUrl: string
  modelName?: string
  temperature?: number
  maxTokens?: number
  timeout?: number
  systemPrompt?: string // 系统提示词
  customHeaders?: Record<string, string> // 自定义请求头
  customParams?: Record<string, any> // 自定义参数
  enabled: boolean
  isDefault?: boolean // 是否为默认配置
  createdAt: Date
  updatedAt: Date
}

// AI配置表单接口
export interface AiConfigForm {
  id?: string
  name: string
  provider: AiProvider
  apiKey: string
  baseUrl: string
  modelName: string
  temperature: number
  maxTokens: number
  timeout: number
  systemPrompt?: string // 系统提示词
  customHeaders?: Record<string, string> // 自定义请求头
  customParams?: Record<string, any> // 自定义参数
  enabled: boolean
  isDefault?: boolean
}

// AI消息类型
export type AiMessageRole = 'user' | 'assistant' | 'system'

// AI消息接口
export interface AiMessage {
  id: string
  role: AiMessageRole
  content: string
  timestamp: Date
  tokens?: number
  error?: string
}

// AI对话会话接口
export interface AiChatSession {
  id: string
  title: string
  messages: AiMessage[]
  createdAt: Date
  updatedAt: Date
  totalTokens?: number
  isTemporary?: boolean // 是否为临时会话（未保存到历史记录）
  isTemporaryChat?: boolean // 是否为24小时临时对话
  expiresAt?: Date // 过期时间（仅临时对话）
}

// AI对话状态
export type AiChatStatus = 'idle' | 'connecting' | 'sending' | 'receiving' | 'error'

// AI服务响应接口
export interface AiResponse {
  content: string
  tokens?: number
  model?: string
  finishReason?: string
}

// AI错误接口
export interface AiError {
  code: string
  message: string
  details?: any
}
