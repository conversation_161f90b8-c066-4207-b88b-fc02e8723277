<template>
  <a-modal :open="visible" :title="modalTitle" width="90%" :style="{ maxWidth: '1200px' }" :footer="null"
    :maskClosable="false" @cancel="handleCancel">
    <div class="image-editor">
      <!-- 编辑器主体 -->
      <div class="editor-main">
        <!-- 裁剪器容器 -->
        <div class="cropper-container">
          <Cropper ref="cropperRef" :src="imageSrc" :stencil-props="stencilProps" :canvas-props="canvasProps"
            :image-props="imageProps" @change="onChange" @ready="onReady" />
        </div>

        <!-- 工具栏 -->
        <div class="editor-toolbar">
          <!-- 基础操作 -->
          <div class="toolbar-section">
            <h4 class="section-title">基础操作</h4>
            <div class="button-group">
              <a-button @click="rotate(-90)" :disabled="!isReady">
                <template #icon>
                  <div class="i-heroicons-arrow-path w-4 h-4 transform rotate-90"></div>
                </template>
                逆时针
              </a-button>
              <a-button @click="rotate(90)" :disabled="!isReady">
                <template #icon>
                  <div class="i-heroicons-arrow-path w-4 h-4 transform -rotate-90"></div>
                </template>
                顺时针
              </a-button>
              <a-button @click="flip(true, false)" :disabled="!isReady">
                <template #icon>
                  <div class="i-heroicons-arrows-right-left w-4 h-4"></div>
                </template>
                水平翻转
              </a-button>
              <a-button @click="flip(false, true)" :disabled="!isReady">
                <template #icon>
                  <div class="i-heroicons-arrows-up-down w-4 h-4"></div>
                </template>
                垂直翻转
              </a-button>
            </div>
          </div>

          <!-- 裁剪比例 -->
          <div class="toolbar-section">
            <h4 class="section-title">裁剪比例</h4>
            <div class="ratio-buttons">
              <a-button v-for="ratio in aspectRatios" :key="ratio.label"
                :type="currentAspectRatio === ratio.value ? 'primary' : 'default'" size="small"
                @click="setAspectRatio(ratio.value)">
                {{ ratio.label }}
              </a-button>
            </div>
          </div>

          <!-- 预设尺寸 -->
          <div class="toolbar-section">
            <h4 class="section-title">预设尺寸</h4>
            <div class="size-buttons">
              <a-button v-for="size in presetSizes" :key="size.label" size="small" @click="setPresetSize(size)">
                {{ size.label }}
              </a-button>
            </div>
          </div>

          <!-- 质量设置 -->
          <div class="toolbar-section">
            <h4 class="section-title">输出质量</h4>
            <a-slider v-model:value="outputQuality" :min="0.1" :max="1" :step="0.1"
              :tooltip-formatter="(value) => `${Math.round(value * 100)}%`" />
          </div>
        </div>
      </div>

      <!-- 底部操作栏 -->
      <div class="editor-footer">
        <div class="footer-info">
          <span v-if="originalSize" class="size-info">
            原始尺寸: {{ originalSize.width }}×{{ originalSize.height }}
          </span>
          <span v-if="cropSize" class="size-info">
            裁剪尺寸: {{ cropSize.width }}×{{ cropSize.height }}
          </span>
        </div>
        <div class="footer-actions">
          <a-button @click="handleCancel">取消</a-button>
          <a-button @click="handleReset" :disabled="!isReady">重置</a-button>
          <a-button type="primary" @click="handleSave" :loading="saving" :disabled="!isReady">
            保存
          </a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { Cropper } from 'vue-advanced-cropper'
import 'vue-advanced-cropper/dist/style.css'

// 定义接口
interface ImageSize {
  width: number
  height: number
}

interface PresetSize {
  label: string
  width: number
  height: number
}

interface AspectRatio {
  label: string
  value: number | null
}

// Props 定义
interface Props {
  visible: boolean
  imageSrc: string
  imageName?: string
}

const props = withDefaults(defineProps<Props>(), {
  imageName: '图片'
})

// Emits 定义
const emit = defineEmits<{
  'update:visible': [value: boolean]
  save: [result: { canvas: HTMLCanvasElement; blob: Blob; dataUrl: string }]
  cancel: []
}>()

// 响应式数据
const cropperRef = ref()
const isReady = ref(false)
const saving = ref(false)
const outputQuality = ref(0.9)
const currentAspectRatio = ref<number | null>(null)
const originalSize = ref<ImageSize | null>(null)
const cropSize = ref<ImageSize | null>(null)

// 计算属性
const modalTitle = computed(() => `编辑图片 - ${props.imageName}`)

const stencilProps = computed(() => ({
  aspectRatio: currentAspectRatio.value,
  movable: true,
  resizable: true,
  handlers: {},
  lines: {}
}))

const canvasProps = computed(() => ({
  maxWidth: 800,
  maxHeight: 600
}))

const imageProps = computed(() => ({
  crossOrigin: 'anonymous'
}))

// 预设比例
const aspectRatios: AspectRatio[] = [
  { label: '自由', value: null },
  { label: '1:1', value: 1 },
  { label: '4:3', value: 4 / 3 },
  { label: '3:4', value: 3 / 4 },
  { label: '16:9', value: 16 / 9 },
  { label: '9:16', value: 9 / 16 },
  { label: '3:2', value: 3 / 2 },
  { label: '2:3', value: 2 / 3 }
]

// 预设尺寸
const presetSizes: PresetSize[] = [
  { label: '头像', width: 200, height: 200 },
  { label: '缩略图', width: 300, height: 200 },
  { label: '横幅', width: 800, height: 200 },
  { label: '封面', width: 600, height: 400 },
  { label: 'HD', width: 1280, height: 720 },
  { label: 'FHD', width: 1920, height: 1080 }
]

// 方法
const onReady = () => {
  isReady.value = true
  updateSizes()
}

const onChange = () => {
  updateSizes()
}

const updateSizes = () => {
  if (!cropperRef.value) return

  const imageSize = cropperRef.value.getImageSize()
  const coordinates = cropperRef.value.getCoordinates()

  if (imageSize) {
    originalSize.value = {
      width: Math.round(imageSize.width),
      height: Math.round(imageSize.height)
    }
  }

  if (coordinates) {
    cropSize.value = {
      width: Math.round(coordinates.width),
      height: Math.round(coordinates.height)
    }
  }
}

const rotate = (angle: number) => {
  if (cropperRef.value) {
    cropperRef.value.rotate(angle)
  }
}

const flip = (horizontal: boolean, vertical: boolean) => {
  if (cropperRef.value) {
    cropperRef.value.flip(horizontal, vertical)
  }
}

const setAspectRatio = (ratio: number | null) => {
  currentAspectRatio.value = ratio
}

const setPresetSize = (size: PresetSize) => {
  if (cropperRef.value) {
    cropperRef.value.setCoordinates({
      width: size.width,
      height: size.height
    })
  }
}

const handleReset = () => {
  if (cropperRef.value) {
    cropperRef.value.reset()
    currentAspectRatio.value = null
  }
}

const handleCancel = () => {
  emit('update:visible', false)
  emit('cancel')
}

const handleSave = async () => {
  if (!cropperRef.value || saving.value) return

  saving.value = true

  try {
    const result = cropperRef.value.getResult()
    if (result && result.canvas) {
      // 生成 Blob
      const blob = await new Promise<Blob>((resolve) => {
        result.canvas.toBlob(resolve, 'image/jpeg', outputQuality.value)
      })

      // 生成 DataURL
      const dataUrl = result.canvas.toDataURL('image/jpeg', outputQuality.value)

      emit('save', {
        canvas: result.canvas,
        blob,
        dataUrl
      })

      emit('update:visible', false)
    }
  } catch (error) {
    console.error('保存图片失败:', error)
  } finally {
    saving.value = false
  }
}

// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal) {
    isReady.value = false
    currentAspectRatio.value = null
    originalSize.value = null
    cropSize.value = null
  }
})
</script>

<style scoped>
.image-editor {
  @apply flex flex-col h-full;
}

.editor-main {
  @apply flex flex-1 gap-6 mb-6;
}

.cropper-container {
  @apply flex-1 min-h-0;
  min-height: 400px;
}

.editor-toolbar {
  @apply w-80 space-y-6 bg-gray-50 dark:bg-gray-800 p-4 rounded-lg;
}

.toolbar-section {
  @apply space-y-3;
}

.section-title {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300 mb-2;
}

.button-group {
  @apply grid grid-cols-2 gap-2;
}

.ratio-buttons {
  @apply grid grid-cols-4 gap-1;
}

.size-buttons {
  @apply grid grid-cols-3 gap-1;
}

.editor-footer {
  @apply flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700;
}

.footer-info {
  @apply space-x-4 text-sm text-gray-600 dark:text-gray-400;
}

.footer-actions {
  @apply space-x-2;
}

.size-info {
  @apply inline-block;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .editor-main {
    @apply flex-col;
  }

  .editor-toolbar {
    @apply w-full;
  }
}
</style>
