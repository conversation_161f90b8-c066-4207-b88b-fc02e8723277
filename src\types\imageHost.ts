// 图床相关类型定义

export interface ImageHostConfig {
  id: string
  name: string // 用户自定义名称
  provider: string // 图床标识
  enabled: boolean // 是否启用
  priority: number // 优先级 (1-10, 数字越小优先级越高)

  // 通用配置
  apiUrl: string // 上传接口地址
  method: 'POST' | 'GET' // 请求方法

  // 认证配置
  authType: 'none' | 'token' | 'header' | 'query'
  authKey?: string // 认证密钥
  authHeader?: string // 认证头名称 (如 'Authorization', 'X-API-Key')
  authPrefix?: string // 认证前缀 (如 'Bearer ')
  authParam?: string // 查询参数名称 (如 'token', 'key')

  // 请求配置
  fileField: string // 文件字段名
  contentType?: string // Content-Type
  headers?: Record<string, string> // 额外请求头
  params?: Record<string, any> // 额外参数

  // 响应配置
  responseType: 'json' | 'text' // 响应类型
  successField?: string // 成功标识字段路径
  successValue?: any // 成功标识值
  urlField: string // 图片URL字段路径
  errorField?: string // 错误信息字段路径

  // 限制配置
  maxFileSize?: number // 最大文件大小(MB)
  allowedFormats?: string[] // 支持的格式

  createdAt: Date
  updatedAt: Date
}

export interface ImageHostConfigForm {
  name: string
  provider: string
  enabled: boolean
  priority: number
  apiUrl: string
  method: 'POST' | 'GET'
  authType: 'none' | 'token' | 'header' | 'query'
  authKey?: string
  authHeader?: string
  authPrefix?: string
  authParam?: string
  fileField: string
  contentType?: string
  headers?: Record<string, string>
  params?: Record<string, any>
  responseType: 'json' | 'text'
  successField?: string
  successValue?: any
  urlField: string
  errorField?: string
  maxFileSize?: number
  allowedFormats?: string[]
}

export interface ImageHostPreset {
  name: string
  description: string
  provider: string
  config: Partial<ImageHostConfigForm>
}

export interface UploadResult {
  url: string
  originalName: string
  size: number
  uploadTime: Date
  hostName: string
  hostProvider: string
  rawResponse?: any
}

export interface ImageRecord {
  id: string
  originalName: string
  filename: string
  size: number
  width?: number
  height?: number
  format: string
  uploadTime: Date
  tags: string[]

  // 多图床备份信息
  backups: ImageBackup[]

  // 生命周期管理
  expirationSettings: {
    enabled: boolean
    expiresAt?: Date
    notifyBefore: number // 提前多少天通知
  }

  // 状态信息
  status: 'active' | 'expired' | 'failed' | 'checking'
  lastChecked?: Date

  createdAt: Date
  updatedAt: Date
}

export interface ImageBackup {
  id: string
  hostConfigId: string
  hostName: string
  hostProvider: string
  url: string
  deleteUrl?: string
  uploadTime: Date
  status: 'active' | 'failed' | 'expired'
  lastChecked?: Date
  errorMessage?: string
}

export interface ImageUploadSettings {
  backupCount: number // 备份到几个图床 (1-5)
  defaultExpiration: number // 默认过期时间(天) 0表示永久
  notifyBefore: number // 提前多少天通知过期
  autoRetry: boolean // 上传失败是否自动重试
  maxRetries: number // 最大重试次数
  retryDelay: number // 重试延迟(秒)
}

export interface ImageHostTestResult {
  success: boolean
  message: string
  responseTime?: number
  error?: string
  timestamp?: number
}
