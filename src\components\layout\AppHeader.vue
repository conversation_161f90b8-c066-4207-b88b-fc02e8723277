<template>
  <a-layout-header class="app-header-antd">
    <div class="header-content">
      <!-- Logo区域 -->
      <div class="header-logo">
        <router-link to="/" class="logo-link">
          <div class="logo-icon">
            <BookOutlined />
          </div>
          <span class="logo-text">KnowlEdge</span>
        </router-link>
      </div>

      <!-- 主导航菜单 -->
      <div class="header-nav">
        <a-menu v-model:selectedKeys="selectedKeys" mode="horizontal" :items="menuItems" class="main-menu"
          @click="handleMenuClick" />
      </div>

      <!-- 导航菜单 -->
      <nav class="flex items-center space-x-6">
        <router-link to="/" class="nav-link" active-class="nav-link-active">
          <div :class="[
            'i-heroicons-home mr-2',
            $route.path === '/' ? 'text-primary-600 dark:text-primary-400' : ''
          ]"></div>
          首页
        </router-link>
        <router-link to="/knowledge" class="nav-link" active-class="nav-link-active">
          <div :class="[
            'i-heroicons-book-open mr-2',
            $route.path === '/knowledge' ? 'text-primary-600 dark:text-primary-400' : ''
          ]"></div>
          知识库
        </router-link>
        <router-link to="/image-gallery" class="nav-link" active-class="nav-link-active">
          <div :class="[
            'i-heroicons-photo mr-2',
            $route.path === '/image-gallery' ? 'text-primary-600 dark:text-primary-400' : ''
          ]"></div>
          图床管理
        </router-link>
        <router-link to="/component-showcase" class="nav-link" active-class="nav-link-active">
          <div :class="[
            'i-heroicons-swatch mr-2',
            $route.path === '/component-showcase' ? 'text-primary-600 dark:text-primary-400' : ''
          ]"></div>
          样式展示
        </router-link>

      </nav>
    </div>

    <!-- 智能搜索框 -->
    <div class="flex-1 max-w-2xl mx-8">
      <SmartSearchBox />
    </div>

    <!-- 右侧操作区 -->
    <div class="flex items-center space-x-4">
      <!-- 云存储 -->
      <CloudStorageButton />

      <!-- AI对话 -->
      <div class="relative">
        <router-link to="/ai-chat" :class="[
          'p-2 transition-colors rounded-lg block',
          aiStore.isConfigured && aiStore.isEnabled
            ? 'text-primary-500 hover:text-primary-600 dark:text-primary-400 dark:hover:text-primary-300 hover:bg-primary-50 dark:hover:bg-primary-900/20'
            : 'text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800'
        ]" :title="aiStore.isConfigured ? (aiStore.isEnabled ? 'AI对话' : 'AI服务未启用') : '请先配置AI服务'">
          <div class="relative">
            <div class="i-heroicons-chat-bubble-left-right w-5 h-5"></div>
            <!-- 状态指示器 -->
            <div v-if="aiStore.isConfigured" :class="[
              'absolute -top-1 -right-1 w-2 h-2 rounded-full',
              aiStore.isEnabled ? 'bg-green-500' : 'bg-yellow-500'
            ]"></div>
            <div v-else class="absolute -top-1 -right-1 w-2 h-2 rounded-full bg-gray-400"></div>
          </div>
        </router-link>
      </div>

      <!-- 主题切换 -->
      <BaseDropdown placement="bottom-end">
        <template #trigger>
          <button
            class="p-2 text-primary-500 hover:text-primary-600 dark:text-primary-400 dark:hover:text-primary-300 transition-colors rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/20">
            <div :class="[themeIcon, 'w-5 h-5']"></div>
          </button>
        </template>

        <div class="w-40">
          <div class="px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
            主题设置
          </div>
          <DropdownItem text="浅色模式" icon="i-heroicons-sun" @click="setThemeMode('light')" />
          <DropdownItem text="深色模式" icon="i-heroicons-moon" @click="setThemeMode('dark')" />
          <DropdownItem text="跟随系统" icon="i-heroicons-computer-desktop" @click="setThemeMode('system')" />
        </div>
      </BaseDropdown>

      <!-- 设置按钮 -->
      <div class="relative" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
        <!-- 设置按钮 -->
        <button @click="handleSettingsClick"
          class="p-2 text-primary-500 hover:text-primary-600 dark:text-primary-400 dark:hover:text-primary-300 transition-colors rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/20">
          <div class="i-heroicons-cog-6-tooth w-5 h-5"></div>
        </button>

        <!-- 悬浮菜单 -->
        <div v-if="showSettingsHover" @mouseenter="handleMenuMouseEnter" @mouseleave="handleMenuMouseLeave"
          class="absolute right-0 top-full mt-2 w-56 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
          <div class="px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
            快速操作
          </div>
          <DropdownItem text="分类管理" icon="i-heroicons-folder" @click="handleQuickAction('category')" />
          <DropdownItem text="标签管理" icon="i-heroicons-tag" @click="handleQuickAction('tag')" />
          <DropdownItem text="搜索引擎设置" icon="i-heroicons-magnifying-glass" @click="handleQuickAction('search-engine')" />
          <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
          <DropdownItem text="数据管理" icon="i-heroicons-circle-stack" @click="handleQuickAction('data')" />
          <DropdownItem text="导入数据" icon="i-heroicons-arrow-down-tray" @click="handleQuickAction('import')" />
          <DropdownItem text="导出数据" icon="i-heroicons-arrow-up-tray" @click="handleQuickAction('export')" />
          <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
          <DropdownItem text="系统设置" icon="i-heroicons-cog-6-tooth" @click="handleQuickAction('settings')" />
          <DropdownItem text="关于" icon="i-heroicons-information-circle" @click="handleQuickAction('about')" />
        </div>
      </div>
    </div>
    </div>
    </div>

    <!-- 分类管理模态框 -->
    <CategoryManagementModal v-model="showCategoryManagement" />

    <!-- 标签管理模态框 -->
    <TagManagementModal v-model="showTagManagement" />

    <!-- 搜索引擎设置模态框 -->
    <SearchEngineSettings v-model="showSearchEngineSettings" />

    <!-- AI设置模态框 -->
    <AiSettingsModal v-model="aiStore.isConfigModalOpen" />

    <!-- AI对话模态框 -->
    <AiChatModal v-model="aiStore.isChatModalOpen" />
    </header>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useTheme } from '@/composables/useTheme'
import { useAiStore } from '@/stores/aiStore'
import BaseDropdown from '@/components/ui/BaseDropdown.vue'
import DropdownItem from '@/components/ui/DropdownItem.vue'
import SmartSearchBox from '@/components/search/SmartSearchBox.vue'
import SearchEngineSettings from '@/components/settings/SearchEngineSettings.vue'
import CategoryManagementModal from '@/components/management/CategoryManagementModal.vue'
import TagManagementModal from '@/components/management/TagManagementModal.vue'
import CloudStorageButton from './CloudStorageButton.vue'
import AiSettingsModal from '@/components/ai/AiSettingsModalAntd.vue'
import AiChatModal from '@/components/ai/AiChatModal.vue'

const router = useRouter()
const { themeIcon, setThemeMode } = useTheme()
const aiStore = useAiStore()

// 设置相关
const showSettingsHover = ref(false)
const showCategoryManagement = ref(false)
const showTagManagement = ref(false)
const showSearchEngineSettings = ref(false)
const showDataManagement = ref(false)
const showImportDialog = ref(false)
const showAbout = ref(false)

// 悬浮菜单定时器
let hoverTimer: ReturnType<typeof setTimeout> | null = null

const handleExportData = () => {
  // TODO: 实现数据导出功能
  console.log('导出数据')
}



// 处理设置按钮点击
const handleSettingsClick = async () => {
  showSettingsHover.value = false
  try {
    await router.push('/settings')
  } catch (error) {
    console.error('导航到设置页面失败:', error)
  }
}

// 处理鼠标进入设置按钮区域
const handleMouseEnter = () => {
  if (hoverTimer) {
    clearTimeout(hoverTimer)
    hoverTimer = null
  }
  showSettingsHover.value = true
}

// 处理鼠标离开设置按钮区域
const handleMouseLeave = () => {
  hoverTimer = setTimeout(() => {
    showSettingsHover.value = false
  }, 100) // 100ms延迟
}

// 处理鼠标进入菜单区域
const handleMenuMouseEnter = () => {
  if (hoverTimer) {
    clearTimeout(hoverTimer)
    hoverTimer = null
  }
}

// 处理鼠标离开菜单区域
const handleMenuMouseLeave = () => {
  hoverTimer = setTimeout(() => {
    showSettingsHover.value = false
  }, 100) // 100ms延迟
}

// 处理快速操作
const handleQuickAction = (action: string) => {
  showSettingsHover.value = false

  switch (action) {
    case 'category':
      showCategoryManagement.value = true
      break
    case 'tag':
      showTagManagement.value = true
      break
    case 'search-engine':
      showSearchEngineSettings.value = true
      break
    case 'data':
      showDataManagement.value = true
      break
    case 'import':
      showImportDialog.value = true
      break
    case 'export':
      handleExportData()
      break
    case 'settings':
      router.push('/settings')
      break
    case 'about':
      showAbout.value = true
      break
  }
}

// 处理搜索功能触发
const handleSearchFunctionTrigger = (event: CustomEvent) => {
  const { type } = event.detail

  switch (type) {
    case 'category-management':
      showCategoryManagement.value = true
      break
    case 'tag-management':
      showTagManagement.value = true
      break
    case 'search-engine-settings':
      showSearchEngineSettings.value = true
      break
    case 'add-resource':
      // TODO: 触发添加资源功能
      console.log('添加资源')
      break
    case 'import-data':
      showImportDialog.value = true
      break
    case 'export-data':
      handleExportData()
      break
    case 'theme-settings':
      // TODO: 触发主题设置
      console.log('主题设置')
      break
  }
}

// 生命周期
onMounted(async () => {
  window.addEventListener('search-function-trigger', handleSearchFunctionTrigger as EventListener)

  // 初始化AI Store
  try {
    await aiStore.initialize()
  } catch (error) {
    console.error('初始化AI Store失败:', error)
  }
})

onUnmounted(() => {
  window.removeEventListener('search-function-trigger', handleSearchFunctionTrigger as EventListener)
})
</script>

<style scoped>
/* 导航栏容器样式 - 占满整个页面宽度 */
.app-header {
  width: 100%;
  /* 移除max-width限制，让背景色占满整个页面宽度 */
}

/* 导航栏内容容器 - 占满整个页面宽度 */
.header-container {
  width: 100%;
  /* 移除max-width限制，让内容也占满整个页面宽度 */
  padding: 0 2rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .header-container {
    padding: 0 1rem;
  }
}

/* 导航链接样式 - 优化对比度和可访问性 */
.nav-link {
  @apply flex items-center px-3 py-2.5 text-sm font-semibold text-gray-700 dark:text-gray-200 hover:text-primary-700 dark:hover:text-primary-300 transition-all duration-200 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/30 focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600;
}

.nav-link-active {
  @apply text-primary-700 dark:text-primary-300 bg-primary-100 dark:bg-primary-800/40 border border-primary-200 dark:border-primary-700;
}
</style>
