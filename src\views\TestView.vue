<template>
  <div class="min-h-screen bg-secondary p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-primary mb-8">组件测试页面</h1>
      
      <!-- 按钮测试 -->
      <section class="mb-8">
        <h2 class="text-xl font-semibold text-primary mb-4">按钮组件</h2>
        <div class="flex flex-wrap gap-4">
          <BaseButton>默认按钮</BaseButton>
          <BaseButton variant="secondary">次要按钮</BaseButton>
          <BaseButton variant="outline">轮廓按钮</BaseButton>
          <BaseButton variant="ghost">幽灵按钮</BaseButton>
          <BaseButton variant="danger">危险按钮</BaseButton>
          <BaseButton :loading="true">加载中</BaseButton>
          <BaseButton icon="i-heroicons-plus">带图标</BaseButton>
        </div>
      </section>
      
      <!-- 输入框测试 -->
      <section class="mb-8">
        <h2 class="text-xl font-semibold text-primary mb-4">输入框组件</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl">
          <BaseInput
            v-model="testInput1"
            label="基础输入框"
            placeholder="请输入内容"
          />
          <BaseInput
            v-model="testInput2"
            label="带图标输入框"
            placeholder="搜索..."
            prefix-icon="i-heroicons-magnifying-glass"
            clearable
          />
          <BaseInput
            v-model="testInput3"
            label="错误状态"
            placeholder="输入内容"
            error="这是一个错误提示"
          />
          <BaseInput
            v-model="testInput4"
            label="禁用状态"
            placeholder="禁用输入框"
            disabled
          />
        </div>
      </section>
      
      <!-- 卡片测试 -->
      <section class="mb-8">
        <h2 class="text-xl font-semibold text-primary mb-4">卡片组件</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <BaseCard variant="shadow">
            <div class="p-6">
              <h3 class="text-lg font-semibold text-primary mb-2">默认卡片</h3>
              <p class="text-secondary">这是一个默认样式的卡片组件</p>
            </div>
          </BaseCard>
          
          <BaseCard variant="bordered" hoverable>
            <div class="p-6">
              <h3 class="text-lg font-semibold text-primary mb-2">悬浮卡片</h3>
              <p class="text-secondary">鼠标悬浮时会有动画效果</p>
            </div>
          </BaseCard>
          
          <BaseCard variant="elevated" clickable @click="handleCardClick">
            <div class="p-6">
              <h3 class="text-lg font-semibold text-primary mb-2">可点击卡片</h3>
              <p class="text-secondary">点击我试试看</p>
            </div>
          </BaseCard>
        </div>
      </section>
      
      <!-- 下拉菜单测试 -->
      <section class="mb-8">
        <h2 class="text-xl font-semibold text-primary mb-4">下拉菜单组件</h2>
        <div class="flex gap-4">
          <BaseDropdown trigger-text="下拉菜单">
            <DropdownItem text="选项 1" icon="i-heroicons-home" @click="handleDropdownClick('选项 1')" />
            <DropdownItem text="选项 2" icon="i-heroicons-cog-6-tooth" @click="handleDropdownClick('选项 2')" />
            <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
            <DropdownItem text="危险选项" icon="i-heroicons-trash" danger @click="handleDropdownClick('危险选项')" />
          </BaseDropdown>
          
          <BaseDropdown>
            <template #trigger>
              <BaseButton variant="outline">
                自定义触发器
                <div class="i-heroicons-chevron-down ml-2"></div>
              </BaseButton>
            </template>
            <DropdownItem text="自定义选项 1" @click="handleDropdownClick('自定义选项 1')" />
            <DropdownItem text="自定义选项 2" @click="handleDropdownClick('自定义选项 2')" />
          </BaseDropdown>
        </div>
      </section>
      
      <!-- 加载动画测试 -->
      <section class="mb-8">
        <h2 class="text-xl font-semibold text-primary mb-4">加载动画组件</h2>
        <div class="flex items-center gap-8">
          <LoadingSpinner size="sm" text="小尺寸" />
          <LoadingSpinner size="md" text="中等尺寸" />
          <LoadingSpinner size="lg" text="大尺寸" />
          <LoadingSpinner size="xl" variant="secondary" text="不同颜色" />
        </div>
      </section>
      
      <!-- 模态框测试 -->
      <section class="mb-8">
        <h2 class="text-xl font-semibold text-primary mb-4">模态框组件</h2>
        <div class="flex gap-4">
          <BaseButton @click="showModal = true">打开模态框</BaseButton>
          <BaseButton @click="showLargeModal = true">大尺寸模态框</BaseButton>
        </div>
      </section>
      
      <!-- 主题切换测试 -->
      <section class="mb-8">
        <h2 class="text-xl font-semibold text-primary mb-4">主题系统</h2>
        <div class="flex items-center gap-4">
          <span class="text-secondary">当前主题：{{ themeName }}</span>
          <BaseButton @click="toggleDarkMode">
            <div :class="[themeIcon, 'mr-2']"></div>
            切换主题
          </BaseButton>
        </div>
      </section>
    </div>
    
    <!-- 测试模态框 -->
    <BaseModal v-model="showModal" title="测试模态框">
      <p class="text-secondary mb-4">这是一个测试模态框的内容。</p>
      <p class="text-secondary">您可以在这里放置任何内容。</p>
      
      <template #footer>
        <BaseButton variant="secondary" @click="showModal = false">取消</BaseButton>
        <BaseButton @click="showModal = false">确定</BaseButton>
      </template>
    </BaseModal>
    
    <BaseModal v-model="showLargeModal" title="大尺寸模态框" size="lg">
      <div class="space-y-4">
        <p class="text-secondary">这是一个大尺寸的模态框。</p>
        <BaseInput v-model="modalInput" label="测试输入" placeholder="在模态框中输入" />
        <div class="grid grid-cols-2 gap-4">
          <BaseCard>
            <div class="p-4">
              <h4 class="font-semibold text-primary mb-2">卡片 1</h4>
              <p class="text-secondary text-sm">模态框中的卡片内容</p>
            </div>
          </BaseCard>
          <BaseCard>
            <div class="p-4">
              <h4 class="font-semibold text-primary mb-2">卡片 2</h4>
              <p class="text-secondary text-sm">更多内容展示</p>
            </div>
          </BaseCard>
        </div>
      </div>
      
      <template #footer>
        <BaseButton variant="secondary" @click="showLargeModal = false">关闭</BaseButton>
      </template>
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useTheme } from '@/composables/useTheme'
import BaseButton from '@/components/ui/BaseButton.vue'
import BaseInput from '@/components/ui/BaseInput.vue'
import BaseCard from '@/components/ui/BaseCard.vue'
import BaseDropdown from '@/components/ui/BaseDropdown.vue'
import DropdownItem from '@/components/ui/DropdownItem.vue'
import LoadingSpinner from '@/components/ui/LoadingSpinner.vue'
import BaseModal from '@/components/ui/BaseModal.vue'

// 主题系统
const { themeIcon, themeName, toggleDarkMode } = useTheme()

// 测试数据
const testInput1 = ref('')
const testInput2 = ref('')
const testInput3 = ref('')
const testInput4 = ref('禁用的值')
const modalInput = ref('')

// 模态框状态
const showModal = ref(false)
const showLargeModal = ref(false)

// 事件处理
const handleCardClick = () => {
  alert('卡片被点击了！')
}

const handleDropdownClick = (option: string) => {
  alert(`选择了：${option}`)
}
</script>
