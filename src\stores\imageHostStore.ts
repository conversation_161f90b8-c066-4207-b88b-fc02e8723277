import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import pLimit from 'p-limit'
import { imageHostService } from '@/services/imageHostService'
import type { ImageHostConfig, ImageHostConfigForm, ImageHostTestResult } from '@/types/imageHost'
import type { DetailedTestResult } from '@/services/imageHostTestService'

export const useImageHostStore = defineStore('imageHost', () => {
  // 状态
  const configs = ref<ImageHostConfig[]>([])
  const testResults = ref<Record<string, ImageHostTestResult>>({})
  const testing = ref<Record<string, boolean>>({})
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 批量测试状态
  const batchTesting = ref(false)
  const batchProgress = ref({
    total: 0,
    completed: 0,
    current: '',
    results: {
      success: 0,
      failed: 0,
      enabled: 0,
    },
  })
  const batchResults = ref<Map<string, DetailedTestResult>>(new Map())
  const batchCancelled = ref(false)

  // 计算属性
  const enabledConfigs = computed(() => configs.value.filter((config) => config.enabled))

  const sortedConfigs = computed(() => [...configs.value].sort((a, b) => a.priority - b.priority))

  const configsCount = computed(() => ({
    total: configs.value.length,
    enabled: enabledConfigs.value.length,
    disabled: configs.value.length - enabledConfigs.value.length,
  }))

  const hasEnabledConfigs = computed(() => enabledConfigs.value.length > 0)

  // 获取指定配置的测试结果
  const getTestResult = computed(() => (configId: string) => testResults.value[configId])

  // 检查指定配置是否正在测试
  const isTesting = computed(() => (configId: string) => testing.value[configId] || false)

  // 批量测试相关计算属性
  const batchProgressPercent = computed(() => {
    if (batchProgress.value.total === 0) return 0
    return Math.round((batchProgress.value.completed / batchProgress.value.total) * 100)
  })

  const batchTestCompleted = computed(
    () =>
      batchProgress.value.total > 0 && batchProgress.value.completed >= batchProgress.value.total,
  )

  const batchResultsList = computed(() =>
    Array.from(batchResults.value.entries()).map(([configId, result]) => ({
      configId,
      config: configs.value.find((c) => c.id === configId),
      result,
    })),
  )

  // Actions
  const loadConfigs = async () => {
    loading.value = true
    error.value = null

    try {
      console.log('加载图床配置...')
      const loadedConfigs = await imageHostService.getAllConfigs()
      configs.value = loadedConfigs
      console.log(`成功加载 ${loadedConfigs.length} 个图床配置`)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '加载配置失败'
      error.value = errorMessage
      console.error('加载图床配置失败:', err)
      message.error(`加载图床配置失败: ${errorMessage}`)
    } finally {
      loading.value = false
    }
  }

  const saveConfig = async (configForm: ImageHostConfigForm, configId?: string) => {
    try {
      console.log('保存图床配置:', configForm.name)
      const savedConfig = await imageHostService.saveConfig(configForm, configId)

      if (configId) {
        // 更新现有配置
        const index = configs.value.findIndex((c) => c.id === configId)
        if (index !== -1) {
          configs.value[index] = savedConfig
        }
      } else {
        // 添加新配置
        configs.value.push(savedConfig)
      }

      console.log(`图床配置 "${savedConfig.name}" 保存成功`)
      message.success(`图床配置 "${savedConfig.name}" 保存成功`)

      return savedConfig
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '保存配置失败'
      console.error('保存图床配置失败:', err)
      message.error(`保存配置失败: ${errorMessage}`)
      throw err
    }
  }

  const deleteConfig = async (configId: string) => {
    try {
      const config = configs.value.find((c) => c.id === configId)
      if (!config) {
        throw new Error('配置不存在')
      }

      await imageHostService.deleteConfig(configId)

      // 从本地状态中移除
      configs.value = configs.value.filter((c) => c.id !== configId)

      // 清理相关的测试结果
      delete testResults.value[configId]
      delete testing.value[configId]

      console.log(`图床配置 "${config.name}" 删除成功`)
      message.success(`图床配置 "${config.name}" 删除成功`)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '删除配置失败'
      console.error('删除图床配置失败:', err)
      message.error(`删除配置失败: ${errorMessage}`)
      throw err
    }
  }

  const testConfig = async (config: ImageHostConfig, showNotification = false) => {
    if (testing.value[config.id]) {
      return // 防止重复测试
    }

    testing.value[config.id] = true

    try {
      console.log(`开始测试图床 "${config.name}" 的连接...`)
      const result = await imageHostService.testConfig(config)
      testResults.value[config.id] = result

      if (result.success) {
        console.log(`图床 "${config.name}" 连接测试成功`)
        if (showNotification) {
          message.success(`图床 "${config.name}" 连接正常，可以正常使用`)
        }
      } else {
        console.warn(`图床 "${config.name}" 测试失败:`, result.message)
        if (showNotification) {
          message.error(`图床 "${config.name}" 连接失败: ${result.message}`)
        }
      }

      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '测试失败'
      console.error('测试配置失败:', err)

      testResults.value[config.id] = {
        success: false,
        message: '测试失败',
        error: errorMessage,
      }

      if (showNotification) {
        message.error(`图床 "${config.name}" 连接测试异常: ${errorMessage}`)
      }

      throw err
    } finally {
      testing.value[config.id] = false
    }
  }

  // 辅助函数：直接禁用图床（避免循环调用）
  const disableImageHost = async (config: ImageHostConfig) => {
    try {
      const updatedConfig = { ...config, enabled: false }
      await imageHostService.saveConfig(updatedConfig, config.id)

      // 更新本地状态
      const configIndex = configs.value.findIndex((c) => c.id === config.id)
      if (configIndex !== -1) {
        configs.value[configIndex].enabled = false
      }

      console.log(`图床 "${config.name}" 已自动禁用`)
    } catch (error) {
      console.error('自动禁用图床失败:', error)
    }
  }

  const testConfigWithEnhancedNotification = async (config: ImageHostConfig) => {
    console.log(`Store: testConfigWithEnhancedNotification 被调用，图床: "${config.name}"`)

    if (testing.value[config.id]) {
      console.log(`Store: 图床 "${config.name}" 正在测试中，跳过`)
      return // 防止重复测试
    }

    testing.value[config.id] = true

    // 使用唯一的 key 来更新消息内容
    const messageKey = `imagehost-test-${config.id}`
    console.log(`Store: 使用消息 key: ${messageKey}`)

    try {
      console.log(`Store: 开始测试图床 "${config.name}" 的连接...`)

      // 显示加载中的消息
      console.log('Store: 显示加载中消息')
      message.loading({
        content: `正在检测图床 "${config.name}" 的连接状态...`,
        key: messageKey,
        duration: 0, // 不自动关闭
      })
      console.log('Store: 加载中消息已显示')

      const result = await imageHostService.testConfig(config)
      testResults.value[config.id] = result

      if (result.success) {
        console.log(`Store: 图床 "${config.name}" 连接测试成功`)

        // 更新为成功消息
        console.log('Store: 显示成功消息')
        message.success({
          content: `图床 "${config.name}" 连接正常，可以正常使用`,
          key: messageKey,
          duration: 4,
        })
        console.log('Store: 成功消息已显示')
      } else {
        console.warn(`Store: 图床 "${config.name}" 测试失败:`, result.message)

        // 更新为错误消息
        console.log('Store: 显示错误消息')
        message.error({
          content: `图床 "${config.name}" 连接失败，已自动禁用: ${result.message}`,
          key: messageKey,
          duration: 6,
        })
        console.log('Store: 错误消息已显示')

        // 自动禁用图床（直接更新，避免循环调用）
        await disableImageHost(config)
      }

      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '测试失败'
      console.error('测试配置失败:', err)

      testResults.value[config.id] = {
        success: false,
        message: '测试失败',
        error: errorMessage,
      }

      // 更新为异常消息
      message.error({
        content: `图床 "${config.name}" 连接异常，已自动禁用: ${errorMessage}`,
        key: messageKey,
        duration: 6,
      })

      // 自动禁用图床（直接更新，避免循环调用）
      await disableImageHost(config)

      throw err
    } finally {
      testing.value[config.id] = false
    }
  }

  const toggleConfigEnabled = async (configId: string, enabled: boolean) => {
    const config = configs.value.find((c) => c.id === configId)
    if (!config) {
      throw new Error('配置不存在')
    }

    // 防止重复操作
    if (testing.value[configId]) {
      return
    }

    try {
      // 更新配置
      const updatedConfig = { ...config, enabled }
      await imageHostService.saveConfig(updatedConfig, configId)

      // 立即更新本地状态
      const configIndex = configs.value.findIndex((c) => c.id === configId)
      if (configIndex !== -1) {
        configs.value[configIndex].enabled = enabled
      }

      if (enabled) {
        // 启用图床时：自动测试连接（使用增强版通知）
        console.log(`Store: 图床 "${config.name}" 已启用，开始自动检测连接...`)
        console.log('Store: 调用 testConfigWithEnhancedNotification')
        await testConfigWithEnhancedNotification(updatedConfig)
        console.log('Store: testConfigWithEnhancedNotification 完成')
      } else {
        // 禁用图床时：静默处理，不显示通知
        console.log(`Store: 图床 "${config.name}" 已禁用`)
        // 清除测试结果
        delete testResults.value[configId]
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误'
      console.error('切换图床状态失败:', err)

      // 发生错误时恢复原状态
      const configIndex = configs.value.findIndex((c) => c.id === configId)
      if (configIndex !== -1) {
        configs.value[configIndex].enabled = !enabled
      }

      // 显示错误通知
      message.error(`无法${enabled ? '启用' : '禁用'}图床 "${config.name}": ${errorMessage}`)
      throw err
    }
  }

  const clearError = () => {
    error.value = null
  }

  const clearTestResults = () => {
    testResults.value = {}
  }

  // 批量测试所有图床配置
  const batchTestAllConfigs = async () => {
    if (batchTesting.value) {
      console.warn('批量测试已在进行中')
      return
    }

    const allConfigs = configs.value.filter((config) => config.apiUrl) // 只测试有API地址的配置
    if (allConfigs.length === 0) {
      message.warning('没有可测试的图床配置')
      return
    }

    // 初始化批量测试状态
    batchTesting.value = true
    batchCancelled.value = false
    batchProgress.value = {
      total: allConfigs.length,
      completed: 0,
      current: '',
      results: {
        success: 0,
        failed: 0,
        enabled: 0,
      },
    }
    batchResults.value.clear()

    console.log(`开始批量测试 ${allConfigs.length} 个图床配置`)
    message.info(`开始批量测试 ${allConfigs.length} 个图床配置`)

    // 使用并发控制，同时最多测试2个图床
    const limit = pLimit(2)

    try {
      const testPromises = allConfigs.map((config) =>
        limit(async () => {
          if (batchCancelled.value) {
            console.log(`批量测试已取消，跳过 ${config.name}`)
            return { config, result: null, skipped: true }
          }

          // 更新当前测试状态
          batchProgress.value.current = config.name
          console.log(`正在测试: ${config.name}`)

          try {
            const result = await testConfigWithEnhancedNotification(config)

            if (!result) {
              throw new Error('测试结果为空')
            }

            // 将 ImageHostTestResult 转换为 DetailedTestResult
            const detailedResult: DetailedTestResult = {
              ...result,
              stage: 'upload' as any,
              duration: result.responseTime || 0,
              timestamp: result.timestamp || Date.now(),
              details: (result as any).details || {},
            }

            batchResults.value.set(config.id, detailedResult)

            // 更新统计
            if (result.success) {
              batchProgress.value.results.success++

              // 自动启用测试通过的图床
              if (!config.enabled) {
                await toggleConfigEnabled(config.id, true)
                batchProgress.value.results.enabled++
                console.log(`自动启用图床: ${config.name}`)
              }
            } else {
              batchProgress.value.results.failed++
            }

            batchProgress.value.completed++
            console.log(`测试完成: ${config.name} - ${result.success ? '成功' : '失败'}`)

            return { config, result: detailedResult, skipped: false }
          } catch (error) {
            console.error(`测试图床 ${config.name} 时发生错误:`, error)

            const errorResult: DetailedTestResult = {
              success: false,
              message: `测试异常: ${error instanceof Error ? error.message : '未知错误'}`,
              stage: 'connectivity' as any,
              duration: 0,
              timestamp: Date.now(),
              details: {},
            }

            batchResults.value.set(config.id, errorResult)
            batchProgress.value.results.failed++
            batchProgress.value.completed++

            return { config, result: errorResult, skipped: false }
          }
        }),
      )

      const results = await Promise.allSettled(testPromises)

      if (!batchCancelled.value) {
        const { success, failed, enabled } = batchProgress.value.results
        const summaryMessage = `批量测试完成！成功: ${success}个，失败: ${failed}个，已启用: ${enabled}个`

        console.log(summaryMessage)
        message.success(summaryMessage)
      } else {
        console.log('批量测试已取消')
        message.warning('批量测试已取消')
      }
    } catch (error) {
      console.error('批量测试过程中发生错误:', error)
      message.error(`批量测试失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      batchTesting.value = false
      batchProgress.value.current = ''
    }
  }

  // 取消批量测试
  const cancelBatchTest = () => {
    if (batchTesting.value) {
      batchCancelled.value = true
      console.log('正在取消批量测试...')
      message.warning('正在取消批量测试，请稍候...')
    }
  }

  // 重试失败的图床
  const retryFailedConfigs = async () => {
    const failedConfigs = Array.from(batchResults.value.entries())
      .filter(([_, result]) => !result.success)
      .map(([configId, _]) => configs.value.find((c) => c.id === configId))
      .filter(Boolean) as ImageHostConfig[]

    if (failedConfigs.length === 0) {
      message.info('没有失败的图床需要重试')
      return
    }

    console.log(`重试 ${failedConfigs.length} 个失败的图床`)

    // 重置这些配置的结果
    failedConfigs.forEach((config) => {
      batchResults.value.delete(config.id)
    })

    // 重新测试失败的配置
    for (const config of failedConfigs) {
      try {
        console.log(`重试测试: ${config.name}`)
        const result = await testConfigWithEnhancedNotification(config)

        if (!result) {
          throw new Error('重试测试结果为空')
        }

        // 将 ImageHostTestResult 转换为 DetailedTestResult
        const detailedResult: DetailedTestResult = {
          ...result,
          stage: 'upload' as any,
          duration: result.responseTime || 0,
          timestamp: result.timestamp || Date.now(),
          details: (result as any).details || {},
        }

        batchResults.value.set(config.id, detailedResult)

        if (result.success && !config.enabled) {
          await toggleConfigEnabled(config.id, true)
          console.log(`重试成功，自动启用图床: ${config.name}`)
        }
      } catch (error) {
        console.error(`重试测试 ${config.name} 失败:`, error)
      }
    }

    message.success(`重试完成！`)
  }

  // 重置所有状态
  const reset = () => {
    configs.value = []
    testResults.value = {}
    testing.value = {}
    loading.value = false
    error.value = null

    // 重置批量测试状态
    batchTesting.value = false
    batchCancelled.value = false
    batchProgress.value = {
      total: 0,
      completed: 0,
      current: '',
      results: {
        success: 0,
        failed: 0,
        enabled: 0,
      },
    }
    batchResults.value.clear()
  }

  return {
    // 状态
    configs,
    testResults,
    testing,
    loading,
    error,

    // 批量测试状态
    batchTesting,
    batchProgress,
    batchResults,
    batchCancelled,

    // 计算属性
    enabledConfigs,
    sortedConfigs,
    configsCount,
    hasEnabledConfigs,
    getTestResult,
    isTesting,
    batchProgressPercent,
    batchTestCompleted,
    batchResultsList,

    // Actions
    loadConfigs,
    saveConfig,
    deleteConfig,
    testConfig,
    testConfigWithEnhancedNotification,
    toggleConfigEnabled,
    clearError,
    clearTestResults,
    reset,

    // 批量测试 Actions
    batchTestAllConfigs,
    cancelBatchTest,
    retryFailedConfigs,
  }
})
