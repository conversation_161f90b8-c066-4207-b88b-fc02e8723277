<template>
  <div class="relative">
    <label v-if="label" :for="inputId" class="block text-sm font-medium text-secondary mb-2 text-chinese">
      {{ label }}
      <span v-if="required" class="text-red-500 ml-1">*</span>
    </label>

    <div :class="['custom-input-wrapper', { error: error, compact: size === 'sm' }]">
      <div v-if="prefixIcon" class="custom-input-icon">
        <div :class="[prefixIcon, 'w-5 h-5 text-gray-500']"></div>
      </div>

      <input :id="inputId" :type="type" :value="modelValue" :placeholder="placeholder" :disabled="disabled"
        :readonly="readonly" :class="inputClasses" @input="handleInput" @blur="handleBlur" @focus="handleFocus" />

      <button v-if="clearable && modelValue" @click="handleClear" class="custom-clear-button" type="button" title="清空">
        <div class="i-heroicons-x-mark w-4 h-4"></div>
      </button>

      <div v-else-if="suffixIcon" class="custom-input-icon">
        <div :class="[suffixIcon, 'w-5 h-5 text-gray-500']"></div>
      </div>
    </div>

    <p v-if="error" class="mt-1 text-sm text-red-600 animate-slide-down text-chinese">
      {{ error }}
    </p>

    <p v-else-if="hint" class="mt-1 text-sm text-secondary text-chinese">
      {{ hint }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'

interface Props {
  modelValue?: string | number
  type?: 'text' | 'email' | 'password' | 'url' | 'search' | 'tel' | 'number'
  label?: string
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  error?: string
  hint?: string
  prefixIcon?: string
  suffixIcon?: string
  clearable?: boolean
  size?: 'sm' | 'md' | 'lg'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  disabled: false,
  readonly: false,
  required: false,
  clearable: false,
  size: 'md'
})

const emit = defineEmits<{
  'update:modelValue': [value: string | number]
  blur: [event: FocusEvent]
  focus: [event: FocusEvent]
}>()

const inputId = ref(`input-${Math.random().toString(36).substring(2, 11)}`)
const isFocused = ref(false)

const inputClasses = computed(() => {
  const classes = ['custom-input-field']
  if (props.size === 'sm') {
    classes.push('compact')
  }
  return classes
})

const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  const value = props.type === 'number' ? Number(target.value) : target.value
  emit('update:modelValue', value)
}

const handleBlur = (event: FocusEvent) => {
  isFocused.value = false
  emit('blur', event)
}

const handleFocus = (event: FocusEvent) => {
  isFocused.value = true
  emit('focus', event)
}

const handleClear = () => {
  emit('update:modelValue', '')
}
</script>

<style scoped>
/* 输入框容器样式 */
.custom-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #f9fafb;
  border-radius: 12px;
  transition: all 0.2s ease;
  min-height: 44px;
}

.custom-input-wrapper:hover {
  background-color: #f3f4f6;
}

.custom-input-wrapper:focus-within {
  background-color: #ffffff;
  border: 1px solid var(--primary-500, #3b82f6);
  box-shadow: none;
}

/* 紧凑模式容器样式 */
.custom-input-wrapper.compact {
  min-height: 36px;
  border-radius: 8px;
}

/* 暗色模式 */
:root.dark .custom-input-wrapper {
  background-color: #374151;
}

:root.dark .custom-input-wrapper:hover {
  background-color: #4b5563;
}

:root.dark .custom-input-wrapper:focus-within {
  background-color: #1f2937;
  border: 1px solid var(--primary-400, #60a5fa);
  box-shadow: none;
}

/* 图标样式 */
.custom-input-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 12px;
  padding-right: 8px;
  color: #6b7280;
}

:root.dark .custom-input-icon {
  color: #9ca3af;
}

/* 输入框样式 */
.custom-input-field {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  padding: 12px 12px 12px 4px;
  font-size: 16px;
  color: #111827;
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei', '微软雅黑', sans-serif;
  line-height: 1.75;
  letter-spacing: 0.02em;
}

/* 紧凑模式输入框样式 */
.custom-input-field.compact {
  padding: 8px 10px 8px 10px;
  font-size: 14px;
  line-height: 1.5;
}

/* 紧凑模式下有前缀图标时的样式 */
.custom-input-wrapper.compact .custom-input-icon+.custom-input-field {
  padding-left: 4px;
}

.custom-input-field::placeholder {
  color: #6b7280;
}

.custom-input-field:disabled {
  color: #9ca3af;
  cursor: not-allowed;
}

:root.dark .custom-input-field {
  color: #f9fafb;
}

:root.dark .custom-input-field::placeholder {
  color: #9ca3af;
}

:root.dark .custom-input-field:disabled {
  color: #6b7280;
}

/* 圆形清空按钮样式 */
.custom-clear-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  color: #9ca3af;
  background: transparent;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.custom-clear-button:hover {
  color: #6b7280;
  background-color: rgba(0, 0, 0, 0.08);
  opacity: 1;
  transform: scale(1.1);
}

:root.dark .custom-clear-button {
  color: #6b7280;
}

:root.dark .custom-clear-button:hover {
  color: #9ca3af;
  background-color: rgba(255, 255, 255, 0.15);
  transform: scale(1.1);
}

/* 紧凑模式清空按钮 */
.custom-input-wrapper.compact .custom-clear-button {
  width: 18px;
  height: 18px;
  margin-right: 6px;
}

/* 错误状态样式 */
.custom-input-wrapper.error {
  background-color: #fef2f2;
  border: 1px solid #fca5a5;
}

.custom-input-wrapper.error:focus-within {
  background-color: #ffffff;
  border: 1px solid #ef4444;
}

:root.dark .custom-input-wrapper.error {
  background-color: #450a0a;
  border: 1px solid #dc2626;
}

:root.dark .custom-input-wrapper.error:focus-within {
  background-color: #1f2937;
  border: 1px solid #ef4444;
}

/* 设置界面优化 - 增强对比度 */
.settings-page .custom-input-wrapper {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
}

.settings-page .custom-input-wrapper:hover {
  background-color: #ffffff;
  border: 1px solid #d1d5db;
}

.settings-page .custom-input-wrapper:focus-within {
  background-color: #ffffff;
  border: 1px solid var(--primary-500, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

:root.dark .settings-page .custom-input-wrapper {
  background-color: #1f2937;
  border: 1px solid #374151;
}

:root.dark .settings-page .custom-input-wrapper:hover {
  background-color: #1f2937;
  border: 1px solid #4b5563;
}

:root.dark .settings-page .custom-input-wrapper:focus-within {
  background-color: #1f2937;
  border: 1px solid var(--primary-400, #60a5fa);
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}
</style>
