import { aiDatabaseService } from './aiDatabaseService'
import type { AiProvider, AiModel, AiConfig } from '@/database'
import type { AiProvider as AiProviderType, AiConfigForm } from '@/types'

/**
 * AI配置管理服务（基于数据库）
 * 替代原有的localStorage实现，使用数据库存储
 */
export class AiConfigDatabaseService {
  // ==================== 服务商管理 ====================

  /**
   * 获取所有可用的AI服务商
   */
  async getAvailableProviders(): Promise<Array<{ label: string; value: string; color?: string }>> {
    try {
      console.log('开始获取可用服务商...')
      const providers = await aiDatabaseService.getActiveProviders()
      console.log('从数据库获取到的原始服务商数据:', providers)

      const result = providers.map((provider) => ({
        label: provider.display_name,
        value: provider.name,
        color: provider.color,
      }))

      console.log('转换后的服务商数据:', result)
      return result
    } catch (error) {
      console.error('获取可用服务商失败:', error)
      return []
    }
  }

  /**
   * 获取服务商标签
   */
  async getProviderLabel(providerName: string): Promise<string> {
    try {
      const provider = await aiDatabaseService.getProviderByName(providerName)
      return provider?.display_name || providerName
    } catch (error) {
      console.error('获取服务商标签失败:', error)
      return providerName
    }
  }

  /**
   * 获取服务商颜色
   */
  async getProviderColor(providerName: string): Promise<string> {
    try {
      const provider = await aiDatabaseService.getProviderByName(providerName)
      return provider?.color || '#8c8c8c'
    } catch (error) {
      console.error('获取服务商颜色失败:', error)
      return '#8c8c8c'
    }
  }

  /**
   * 获取服务商默认配置
   */
  async getProviderDefaults(providerName: string): Promise<Partial<AiConfigForm>> {
    try {
      const provider = await aiDatabaseService.getProviderByName(providerName)
      if (!provider) {
        return {
          provider: providerName as AiProviderType,
          baseUrl: '',
          modelName: '',
          temperature: 0.7,
          maxTokens: 4000,
          timeout: 30000,
        }
      }

      // 获取该服务商的第一个模型作为默认模型
      const models = await aiDatabaseService.getModelsByProvider(provider.id!)
      const defaultModel = models[0]

      return {
        provider: provider.name as AiProviderType,
        baseUrl: provider.base_url || '',
        modelName: defaultModel?.name || '',
        temperature: 0.7,
        maxTokens: defaultModel?.max_tokens || 4000,
        timeout: 30000,
      }
    } catch (error) {
      console.error('获取服务商默认配置失败:', error)
      return {
        provider: providerName as AiProviderType,
        baseUrl: '',
        modelName: '',
        temperature: 0.7,
        maxTokens: 4000,
        timeout: 30000,
      }
    }
  }

  // ==================== 模型管理 ====================

  /**
   * 修复模型数据 - 确保所有模型都有正确的display_name
   */
  async fixModelData(): Promise<void> {
    try {
      console.log('开始修复模型数据...')

      // 获取所有模型
      const allModels = await aiDatabaseService.getAllModels()
      console.log('数据库中所有模型:', allModels)

      // 检查并修复缺失的display_name
      for (const model of allModels) {
        if (!model.display_name || model.display_name.trim() === '') {
          console.log(`修复模型 ${model.id} 的display_name，当前值:`, model.display_name)

          // 根据name生成display_name
          let displayName = model.name
          if (model.name) {
            // 将模型名称转换为更友好的显示名称
            displayName = model.name
              .replace(/^gpt-/, 'GPT-')
              .replace(/^claude-/, 'Claude ')
              .replace(/-/g, ' ')
              .replace(/\b\w/g, (l) => l.toUpperCase())
          } else {
            displayName = `模型 ${model.id}`
          }

          // 更新模型
          await aiDatabaseService.updateModel(model.id!, { display_name: displayName })
          console.log(`已修复模型 ${model.id} 的display_name为:`, displayName)
        }
      }

      console.log('模型数据修复完成')
    } catch (error) {
      console.error('修复模型数据失败:', error)
    }
  }

  /**
   * 获取服务商的系统预设模型（不包含自定义模型）
   */
  async getAvailableModels(providerName: string): Promise<Array<{ label: string; value: string }>> {
    try {
      console.log('获取系统模型，服务商名称:', providerName)

      const provider = await aiDatabaseService.getProviderByName(providerName)
      console.log('找到的服务商:', provider)

      if (!provider) {
        console.log('服务商不存在')
        return []
      }

      const allModels = await aiDatabaseService.getModelsByProvider(provider.id!)
      console.log('从数据库获取的所有模型数据:', allModels)

      // 只返回系统预设模型（type为'builtin'）
      const systemModels = allModels.filter((model) => model.type === 'builtin')
      console.log('过滤后的系统模型:', systemModels)

      // 详细检查每个系统模型的数据结构
      systemModels.forEach((model, index) => {
        console.log(`系统模型 ${index}:`, {
          id: model.id,
          name: model.name,
          display_name: model.display_name,
          provider_id: model.provider_id,
          type: model.type,
          is_active: model.is_active,
        })
      })

      const result = systemModels.map((model) => ({
        label: model.display_name || model.name || `模型${model.id}`, // 添加备用显示名称
        value: model.name || `model_${model.id}`, // 添加备用值
      }))

      console.log('转换后的系统模型数据:', result)
      return result
    } catch (error) {
      console.error('获取系统模型失败:', error)
      return []
    }
  }

  /**
   * 获取服务商的自定义模型（只包含自定义模型）
   */
  async getAvailableModelsWithCustom(
    providerName: string,
  ): Promise<Array<{ label: string; value: string; isCustom?: boolean }>> {
    try {
      console.log('获取自定义模型，服务商名称:', providerName)

      const provider = await aiDatabaseService.getProviderByName(providerName)
      if (!provider) {
        console.log('服务商不存在')
        return []
      }

      // 获取该服务商的所有模型
      const allModels = await aiDatabaseService.getModelsByProvider(provider.id!)
      console.log('从数据库获取的所有模型数据:', allModels)

      // 只返回自定义模型（type为'custom'）
      const customModels = allModels.filter((model) => model.type === 'custom')
      console.log('过滤后的自定义模型:', customModels)

      const result = customModels.map((model) => ({
        label: model.display_name || model.name || `模型${model.id}`,
        value: model.name || `model_${model.id}`,
        isCustom: true, // 自定义模型标识
      }))

      console.log('转换后的自定义模型数据:', result)
      return result
    } catch (error) {
      console.error('获取自定义模型失败:', error)
      return []
    }
  }

  // ==================== 配置管理 ====================

  /**
   * 测试AI配置连接
   */
  async testConfig(config: {
    provider: string
    apiKey: string
    baseUrl: string
    modelName: string
    temperature?: number
    maxTokens?: number
    timeout?: number
  }): Promise<{ success: boolean; message: string; responseTime?: number }> {
    try {
      console.log('开始测试AI配置连接:', {
        provider: config.provider,
        baseUrl: config.baseUrl,
        modelName: config.modelName,
        hasApiKey: !!config.apiKey,
      })

      const startTime = Date.now()

      // 验证必要参数
      if (!config.apiKey || !config.apiKey.trim()) {
        throw new Error('API Key不能为空')
      }

      if (!config.baseUrl || !config.baseUrl.trim()) {
        throw new Error('API端点不能为空')
      }

      if (!config.modelName || !config.modelName.trim()) {
        throw new Error('模型名称不能为空')
      }

      // 构建测试请求
      const testMessage = '你好，这是一个连接测试。请简单回复"连接成功"。'
      const requestBody = this.buildTestRequest(config, testMessage)

      console.log('构建的测试请求:', requestBody)

      // 构建完整的API端点URL
      const apiUrl = this.buildApiUrl(config.baseUrl, config.provider)
      console.log('完整的API端点:', apiUrl)

      // 构建请求头
      const headers = this.buildRequestHeaders(config.provider, config.apiKey)
      console.log('请求头:', headers)

      // 发送测试请求
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(config.timeout || 30000),
      })

      const responseTime = Date.now() - startTime

      if (!response.ok) {
        const errorText = await response.text()
        console.error('API请求失败:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText,
        })

        throw new Error(
          `API请求失败 (${response.status}): ${this.parseErrorMessage(errorText, config.provider)}`,
        )
      }

      const responseData = await response.json()
      console.log('API响应成功:', responseData)

      // 验证响应格式
      const isValidResponse = this.validateResponse(responseData, config.provider)
      if (!isValidResponse) {
        throw new Error('API响应格式不正确，请检查配置参数')
      }

      return {
        success: true,
        message: `连接测试成功！响应时间: ${responseTime}ms`,
        responseTime,
      }
    } catch (error: any) {
      console.error('AI配置测试失败:', error)

      if (error.name === 'AbortError') {
        return {
          success: false,
          message: '连接超时，请检查网络连接和API端点',
        }
      }

      return {
        success: false,
        message: error.message || '连接测试失败，请检查配置参数',
      }
    }
  }

  /**
   * 获取所有AI配置
   */
  async getAllConfigs(): Promise<AiConfigForm[]> {
    try {
      const configs = await aiDatabaseService.getAllConfigs()
      const result: AiConfigForm[] = []

      for (const config of configs) {
        const provider = await aiDatabaseService.getProviderById(config.provider_id)
        const model = config.model_id ? await aiDatabaseService.getModelById(config.model_id) : null

        if (provider) {
          result.push({
            id: config.id!.toString(),
            name: config.name,
            provider: provider.name as AiProviderType,
            apiKey: config.api_key || '',
            baseUrl: config.base_url || provider.base_url || '',
            modelName: model?.name || '',
            temperature: config.temperature,
            maxTokens: config.max_tokens,
            timeout: config.timeout,
            systemPrompt: config.system_prompt || '',
            customHeaders: config.custom_headers ? JSON.parse(config.custom_headers) : {},
            customParams: config.custom_params ? JSON.parse(config.custom_params) : {},
            enabled: config.is_enabled,
            isDefault: config.is_default,
          })
        }
      }

      return result
    } catch (error) {
      console.error('获取所有配置失败:', error)
      return []
    }
  }

  /**
   * 保存AI配置
   */
  async saveConfig(configData: AiConfigForm, providerId?: number): Promise<AiConfigForm> {
    try {
      // 获取服务商ID
      let provider
      if (providerId) {
        // 如果直接提供了providerId，使用它
        provider = await aiDatabaseService.getProviderById(providerId)
      } else {
        // 否则通过名称查找
        provider = await aiDatabaseService.getProviderByName(configData.provider)
      }

      if (!provider) {
        throw new Error('服务商不存在')
      }

      // 获取模型ID（如果指定了模型）
      let modelId: number | undefined
      if (configData.modelName) {
        const models = await aiDatabaseService.getModelsByProvider(provider.id!)
        const model = models.find((m) => m.name === configData.modelName)
        modelId = model?.id
      }

      const dbConfig: Omit<AiConfig, 'id' | 'created_at' | 'updated_at'> = {
        name: configData.name,
        provider_id: provider.id!,
        model_id: modelId,
        api_key: configData.apiKey,
        base_url: configData.baseUrl,
        temperature: configData.temperature,
        max_tokens: configData.maxTokens,
        timeout: configData.timeout,
        system_prompt: configData.systemPrompt,
        custom_headers: JSON.stringify(configData.customHeaders || {}),
        custom_params: JSON.stringify(configData.customParams || {}),
        is_default: configData.isDefault || false,
        is_enabled: configData.enabled !== false,
      }

      let configId: number
      if (configData.id) {
        // 更新现有配置
        const success = await aiDatabaseService.updateConfig(parseInt(configData.id), dbConfig)
        if (!success) {
          throw new Error('更新配置失败')
        }
        configId = parseInt(configData.id)
      } else {
        // 创建新配置
        configId = await aiDatabaseService.createConfig(dbConfig)
      }

      // 返回保存后的配置
      const savedConfig = await aiDatabaseService.getConfigById(configId)
      if (!savedConfig) {
        throw new Error('获取保存后的配置失败')
      }

      return {
        id: savedConfig.id!.toString(),
        name: savedConfig.name,
        provider: provider.name as AiProviderType,
        apiKey: savedConfig.api_key || '',
        baseUrl: savedConfig.base_url || provider.base_url || '',
        modelName: modelId ? (await aiDatabaseService.getModelById(modelId))?.name || '' : '',
        temperature: savedConfig.temperature,
        maxTokens: savedConfig.max_tokens,
        timeout: savedConfig.timeout,
        systemPrompt: savedConfig.system_prompt || '',
        customHeaders: savedConfig.custom_headers ? JSON.parse(savedConfig.custom_headers) : {},
        customParams: savedConfig.custom_params ? JSON.parse(savedConfig.custom_params) : {},
        enabled: savedConfig.is_enabled,
        isDefault: savedConfig.is_default,
      }
    } catch (error) {
      console.error('保存配置失败:', error)
      throw error
    }
  }

  /**
   * 更新配置启用状态
   */
  async updateConfigEnabled(configId: string, enabled: boolean): Promise<boolean> {
    try {
      console.log('更新配置启用状态:', { configId, enabled })

      await aiDatabaseService.updateConfig(parseInt(configId), { is_enabled: enabled })

      console.log('配置启用状态更新成功')
      return true
    } catch (error) {
      console.error('更新配置启用状态失败:', error)
      return false
    }
  }

  /**
   * 删除AI配置
   */
  async deleteConfig(configId: string): Promise<boolean> {
    try {
      return await aiDatabaseService.deleteConfig(parseInt(configId))
    } catch (error) {
      console.error('删除配置失败:', error)
      return false
    }
  }

  /**
   * 设置默认配置
   */
  async setDefaultConfig(configId: string): Promise<boolean> {
    try {
      return await aiDatabaseService.setDefaultConfig(parseInt(configId))
    } catch (error) {
      console.error('设置默认配置失败:', error)
      return false
    }
  }

  /**
   * 获取默认配置
   */
  async getDefaultConfig(): Promise<AiConfigForm | null> {
    try {
      const config = await aiDatabaseService.getDefaultConfig()
      if (!config) {
        return null
      }

      const provider = await aiDatabaseService.getProviderById(config.provider_id)
      const model = config.model_id ? await aiDatabaseService.getModelById(config.model_id) : null

      if (!provider) {
        return null
      }

      return {
        id: config.id!.toString(),
        name: config.name,
        provider: provider.name as AiProviderType,
        apiKey: config.api_key || '',
        baseUrl: config.base_url || provider.base_url || '',
        modelName: model?.name || '',
        temperature: config.temperature,
        maxTokens: config.max_tokens,
        timeout: config.timeout,
        systemPrompt: config.system_prompt || '',
        customHeaders: config.custom_headers ? JSON.parse(config.custom_headers) : {},
        customParams: config.custom_params ? JSON.parse(config.custom_params) : {},
        enabled: config.is_enabled,
        isDefault: config.is_default,
      }
    } catch (error) {
      console.error('获取默认配置失败:', error)
      return null
    }
  }

  /**
   * 根据ID获取配置
   */
  async getConfigById(configId: string): Promise<AiConfigForm | null> {
    try {
      const config = await aiDatabaseService.getConfigById(parseInt(configId))
      if (!config) {
        return null
      }

      const provider = await aiDatabaseService.getProviderById(config.provider_id)
      const model = config.model_id ? await aiDatabaseService.getModelById(config.model_id) : null

      if (!provider) {
        return null
      }

      return {
        id: config.id!.toString(),
        name: config.name,
        provider: provider.name as AiProviderType,
        apiKey: config.api_key || '',
        baseUrl: config.base_url || provider.base_url || '',
        modelName: model?.name || '',
        temperature: config.temperature,
        maxTokens: config.max_tokens,
        timeout: config.timeout,
        systemPrompt: config.system_prompt || '',
        customHeaders: config.custom_headers ? JSON.parse(config.custom_headers) : {},
        customParams: config.custom_params ? JSON.parse(config.custom_params) : {},
        enabled: config.is_enabled,
        isDefault: config.is_default,
      }
    } catch (error) {
      console.error('获取配置失败:', error)
      return null
    }
  }

  // ==================== 自定义服务商和模型管理 ====================

  /**
   * 添加自定义服务商
   */
  async addCustomProvider(
    name: string,
    displayName: string,
    baseUrl: string,
  ): Promise<{ success: boolean; providerId?: number }> {
    try {
      const providerId = await aiDatabaseService.createCustomProvider({
        name,
        display_name: displayName,
        type: 'custom',
        base_url: baseUrl,
        color: '#8c8c8c',
        is_active: true,
        sort_order: 999,
      })
      return { success: true, providerId }
    } catch (error) {
      console.error('添加自定义服务商失败:', error)
      return { success: false }
    }
  }

  /**
   * 添加自定义模型
   */
  async addCustomModel(
    providerName: string,
    modelName: string,
    displayName: string,
    maxTokens?: number,
  ): Promise<boolean> {
    try {
      const provider = await aiDatabaseService.getProviderByName(providerName)
      if (!provider) {
        throw new Error('服务商不存在')
      }

      await aiDatabaseService.createCustomModel({
        provider_id: provider.id!,
        name: modelName,
        display_name: displayName,
        type: 'custom',
        max_tokens: maxTokens,
        is_active: true,
        sort_order: 999,
      })
      return true
    } catch (error) {
      console.error('添加自定义模型失败:', error)
      return false
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 构建完整的API端点URL
   */
  private buildApiUrl(baseUrl: string, provider: string): string {
    // 移除baseUrl末尾的斜杠
    const cleanBaseUrl = baseUrl.replace(/\/+$/, '')

    switch (provider) {
      case 'openai':
        // OpenAI兼容的API端点
        return `${cleanBaseUrl}/chat/completions`

      case 'claude':
        // Claude API端点
        return `${cleanBaseUrl}/messages`

      case 'gemini':
        // Gemini API端点
        return `${cleanBaseUrl}/generateContent`

      default:
        // 默认使用OpenAI格式
        return `${cleanBaseUrl}/chat/completions`
    }
  }

  /**
   * 构建测试请求体
   */
  private buildTestRequest(config: any, message: string): any {
    const baseRequest = {
      model: config.modelName,
      temperature: config.temperature || 0.7,
      max_tokens: Math.min(config.maxTokens || 100, 100), // 测试时限制token数量
    }

    switch (config.provider) {
      case 'openai':
        return {
          ...baseRequest,
          messages: [
            {
              role: 'user',
              content: message,
            },
          ],
        }

      case 'claude':
        return {
          ...baseRequest,
          messages: [
            {
              role: 'user',
              content: message,
            },
          ],
        }

      case 'gemini':
        return {
          ...baseRequest,
          contents: [
            {
              parts: [
                {
                  text: message,
                },
              ],
            },
          ],
        }

      default:
        // 默认使用OpenAI格式
        return {
          ...baseRequest,
          messages: [
            {
              role: 'user',
              content: message,
            },
          ],
        }
    }
  }

  /**
   * 构建请求头
   */
  private buildRequestHeaders(provider: string, apiKey: string): Record<string, string> {
    const baseHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
    }

    switch (provider) {
      case 'openai':
        return {
          ...baseHeaders,
          Authorization: `Bearer ${apiKey}`,
        }

      case 'claude':
        return {
          ...baseHeaders,
          'x-api-key': apiKey,
          'anthropic-version': '2023-06-01',
        }

      case 'gemini':
        return {
          ...baseHeaders,
          'x-goog-api-key': apiKey,
        }

      default:
        // 默认使用OpenAI格式
        return {
          ...baseHeaders,
          Authorization: `Bearer ${apiKey}`,
        }
    }
  }

  /**
   * 解析错误消息
   */
  private parseErrorMessage(errorText: string, provider: string): string {
    try {
      const errorData = JSON.parse(errorText)

      // OpenAI错误格式
      if (errorData.error?.message) {
        return errorData.error.message
      }

      // Claude错误格式
      if (errorData.error?.type && errorData.error?.message) {
        return `${errorData.error.type}: ${errorData.error.message}`
      }

      // Gemini错误格式
      if (errorData.error?.message) {
        return errorData.error.message
      }

      return errorText
    } catch {
      return errorText || '未知错误'
    }
  }

  /**
   * 验证API响应格式
   */
  private validateResponse(responseData: any, provider: string): boolean {
    try {
      switch (provider) {
        case 'openai':
          return !!(responseData.choices && responseData.choices.length > 0)

        case 'claude':
          return !!(responseData.content && responseData.content.length > 0)

        case 'gemini':
          return !!(responseData.candidates && responseData.candidates.length > 0)

        default:
          // 默认检查是否有基本的响应结构
          return !!(responseData.choices || responseData.content || responseData.candidates)
      }
    } catch {
      return false
    }
  }
}

// 导出单例实例
export const aiConfigDatabaseService = new AiConfigDatabaseService()
export default aiConfigDatabaseService
