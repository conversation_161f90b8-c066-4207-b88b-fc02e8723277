import type { AiConfig, AiMessage, AiResponse, AiError } from '@/types'
import { aiConfigService } from './aiConfigService'

/**
 * AI对话服务
 * 负责与AI API的通信、消息发送和接收
 */
class AiChatService {
  private abortController: AbortController | null = null

  /**
   * 发送消息到AI服务
   */
  async sendMessage(
    messages: AiMessage[],
    onProgress?: (content: string) => void,
    modelName?: string,
  ): Promise<AiResponse> {
    const config = await aiConfigService.getDefaultConfig()
    if (!config) {
      throw new Error('未找到AI配置，请先配置AI服务')
    }

    if (!config.enabled) {
      throw new Error('AI服务未启用，请在设置中启用')
    }

    try {
      // 创建新的AbortController
      this.abortController = new AbortController()

      // 构建请求体
      const requestBody = this.buildRequestBody(config, messages, !!onProgress, modelName)

      // 发送请求
      const response = await fetch(`${config.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: this.buildHeaders(config),
        body: JSON.stringify(requestBody),
        signal: this.abortController.signal,
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw this.createAiError(response.status, errorText)
      }

      // 处理流式响应
      if (onProgress && response.body) {
        return await this.handleStreamResponse(response, onProgress)
      }

      // 处理普通响应
      return await this.handleNormalResponse(response)
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求已取消')
      }
      throw error
    } finally {
      this.abortController = null
    }
  }

  /**
   * 取消当前请求
   */
  cancelRequest(): void {
    if (this.abortController) {
      this.abortController.abort()
      this.abortController = null
    }
  }

  /**
   * 构建请求头
   */
  private buildHeaders(config: AiConfig): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }

    // 根据不同的提供商设置认证头
    switch (config.provider) {
      case 'openai':
      case 'custom':
        headers['Authorization'] = `Bearer ${config.apiKey}`
        break
      case 'claude':
        headers['x-api-key'] = config.apiKey
        headers['anthropic-version'] = '2023-06-01'
        break
    }

    return headers
  }

  /**
   * 构建请求体
   */
  private buildRequestBody(
    config: AiConfig,
    messages: AiMessage[],
    stream: boolean = false,
    modelName?: string,
  ): any {
    // 转换消息格式
    const formattedMessages = messages.map((msg) => ({
      role: msg.role,
      content: msg.content,
    }))

    const requestBody: any = {
      model: modelName || config.modelName,
      messages: formattedMessages,
      temperature: config.temperature || 0.7,
      max_tokens: config.maxTokens || 2000,
      stream,
    }

    // Claude API的特殊处理
    if (config.provider === 'claude') {
      // Claude API使用不同的参数名
      requestBody.max_tokens = requestBody.max_tokens
      delete requestBody.max_tokens
    }

    return requestBody
  }

  /**
   * 处理普通响应
   */
  private async handleNormalResponse(response: Response): Promise<AiResponse> {
    const data = await response.json()

    if (!data.choices || !Array.isArray(data.choices) || data.choices.length === 0) {
      throw new Error('API响应格式不正确')
    }

    const choice = data.choices[0]
    const content = choice.message?.content || choice.text || ''

    return {
      content: content.trim(),
      tokens: data.usage?.total_tokens,
      model: data.model,
      finishReason: choice.finish_reason,
    }
  }

  /**
   * 处理流式响应
   */
  private async handleStreamResponse(
    response: Response,
    onProgress: (content: string) => void,
  ): Promise<AiResponse> {
    const reader = response.body!.getReader()
    const decoder = new TextDecoder()
    let fullContent = ''
    let totalTokens = 0

    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim()

            if (data === '[DONE]') {
              continue
            }

            try {
              const parsed = JSON.parse(data)
              const choice = parsed.choices?.[0]

              if (choice?.delta?.content) {
                const content = choice.delta.content
                fullContent += content
                onProgress(fullContent)
              }

              if (parsed.usage?.total_tokens) {
                totalTokens = parsed.usage.total_tokens
              }
            } catch (error) {
              // 忽略解析错误，继续处理下一行
              console.warn('解析流式响应失败:', error)
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }

    return {
      content: fullContent.trim(),
      tokens: totalTokens,
      finishReason: 'stop',
    }
  }

  /**
   * 创建AI错误对象
   */
  private createAiError(status: number, errorText: string): AiError {
    let message = '未知错误'
    let code = 'UNKNOWN_ERROR'

    try {
      const errorData = JSON.parse(errorText)
      message = errorData.error?.message || errorData.message || errorText
      code = errorData.error?.code || errorData.code || `HTTP_${status}`
    } catch {
      // 如果不是JSON格式，直接使用错误文本
      message = errorText || `HTTP ${status} 错误`
      code = `HTTP_${status}`
    }

    // 根据状态码提供更友好的错误信息
    switch (status) {
      case 401:
        message = 'API密钥无效或已过期'
        code = 'INVALID_API_KEY'
        break
      case 403:
        message = '访问被拒绝，请检查API权限'
        code = 'ACCESS_DENIED'
        break
      case 429:
        message = '请求过于频繁，请稍后再试'
        code = 'RATE_LIMIT_EXCEEDED'
        break
      case 500:
        message = 'AI服务内部错误'
        code = 'SERVER_ERROR'
        break
      case 503:
        message = 'AI服务暂时不可用'
        code = 'SERVICE_UNAVAILABLE'
        break
    }

    return {
      code,
      message,
      details: { status, originalError: errorText },
    }
  }

  /**
   * 生成消息ID
   */
  generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 创建用户消息
   */
  createUserMessage(content: string): AiMessage {
    return {
      id: this.generateMessageId(),
      role: 'user',
      content: content.trim(),
      timestamp: new Date(),
    }
  }

  /**
   * 创建助手消息
   */
  createAssistantMessage(content: string, tokens?: number): AiMessage {
    return {
      id: this.generateMessageId(),
      role: 'assistant',
      content: content.trim(),
      timestamp: new Date(),
      tokens,
    }
  }

  /**
   * 创建错误消息
   */
  createErrorMessage(error: string): AiMessage {
    return {
      id: this.generateMessageId(),
      role: 'assistant',
      content: '',
      timestamp: new Date(),
      error,
    }
  }

  /**
   * 估算token数量（简单估算）
   */
  estimateTokens(text: string): number {
    // 简单的token估算：英文约4个字符=1个token，中文约1.5个字符=1个token
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length
    const otherChars = text.length - chineseChars

    return Math.ceil(chineseChars / 1.5 + otherChars / 4)
  }
}

// 导出单例实例
export const aiChatService = new AiChatService()
export default aiChatService
