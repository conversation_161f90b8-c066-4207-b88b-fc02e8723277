import { db } from '@/database'
import type { Tag, TagForm } from '@/types'

export class TagService {
  // 创建标签
  async createTag(tagData: TagForm): Promise<number> {
    // 检查标签名是否已存在
    const existingTag = await db.tags.where('name').equals(tagData.name).first()
    if (existingTag) {
      throw new Error('标签名已存在')
    }

    const tag: Omit<Tag, 'id'> = {
      name: tagData.name,
      color: tagData.color,
      resource_count: 0,
      created_at: new Date(),
    }

    return (await db.tags.add(tag)) as number
  }

  // 获取标签详情
  async getTagById(id: number): Promise<Tag | undefined> {
    return await db.tags.get(id)
  }

  // 更新标签
  async updateTag(id: number, tagData: Partial<TagForm>): Promise<void> {
    const updateData: Partial<Tag> = {}

    if (tagData.name !== undefined) {
      // 检查新名称是否已存在（排除当前标签）
      const existingTag = await db.tags.where('name').equals(tagData.name).first()
      if (existingTag && existingTag.id !== id) {
        throw new Error('标签名已存在')
      }
      updateData.name = tagData.name
    }

    if (tagData.color !== undefined) updateData.color = tagData.color

    await db.tags.update(id, updateData)
  }

  // 删除标签
  async deleteTag(id: number): Promise<void> {
    // 删除标签关联
    await db.resource_tags.where('tag_id').equals(id).delete()

    // 删除标签
    await db.tags.delete(id)
  }

  // 获取所有标签
  async getAllTags(): Promise<Tag[]> {
    return await db.tags.orderBy('name').toArray()
  }

  // 获取热门标签（按资源数量排序）
  async getPopularTags(limit?: number): Promise<Tag[]> {
    const tags = await db.tags.orderBy('resource_count').reverse().toArray()

    return limit ? tags.slice(0, limit) : tags
  }

  // 获取标签（支持自定义排序）
  async getTagsWithSorting(sortBy: string = 'name_asc'): Promise<Tag[]> {
    const tags = await db.tags.toArray()

    // 解析排序参数
    const [field, order] = sortBy.split('_')

    // 排序逻辑
    tags.sort((a, b) => {
      let aValue: any, bValue: any

      switch (field) {
        case 'name':
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
          break
        case 'count':
          aValue = a.resource_count || 0
          bValue = b.resource_count || 0
          break
        case 'created':
          aValue = new Date(a.created_at).getTime()
          bValue = new Date(b.created_at).getTime()
          break
        default:
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
      }

      if (order === 'asc') {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
      } else {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
      }
    })

    return tags
  }

  // 搜索标签
  async searchTags(keyword: string): Promise<Tag[]> {
    const lowerKeyword = keyword.toLowerCase()
    return await db.tags.filter((tag) => tag.name.toLowerCase().includes(lowerKeyword)).toArray()
  }

  // 获取资源的标签
  async getResourceTags(resourceId: number): Promise<Tag[]> {
    const tagRelations = await db.resource_tags.where('resource_id').equals(resourceId).toArray()

    const tags = await Promise.all(tagRelations.map((relation) => db.tags.get(relation.tag_id)))

    return tags.filter(Boolean) as Tag[]
  }

  // 获取标签的资源ID列表
  async getTagResourceIds(tagId: number): Promise<number[]> {
    const relations = await db.resource_tags.where('tag_id').equals(tagId).toArray()

    return relations.map((relation) => relation.resource_id)
  }

  // 批量创建标签（如果不存在）
  async createTagsIfNotExist(tagNames: string[]): Promise<Tag[]> {
    const results: Tag[] = []

    for (const name of tagNames) {
      let tag = await db.tags.where('name').equals(name).first()

      if (!tag) {
        // 生成随机颜色
        const colors = [
          '#EF4444',
          '#F97316',
          '#F59E0B',
          '#EAB308',
          '#84CC16',
          '#22C55E',
          '#10B981',
          '#14B8A6',
          '#06B6D4',
          '#0EA5E9',
          '#3B82F6',
          '#6366F1',
          '#8B5CF6',
          '#A855F7',
          '#D946EF',
          '#EC4899',
          '#F43F5E',
        ]
        const randomColor = colors[Math.floor(Math.random() * colors.length)]

        const tagId = await this.createTag({
          name,
          color: randomColor,
        })

        tag = await db.tags.get(tagId)
      }

      if (tag) {
        results.push(tag)
      }
    }

    return results
  }

  // 合并标签
  async mergeTags(sourceTagId: number, targetTagId: number): Promise<void> {
    // 获取源标签的所有资源关联
    const sourceRelations = await db.resource_tags.where('tag_id').equals(sourceTagId).toArray()

    // 将源标签的关联转移到目标标签
    for (const relation of sourceRelations) {
      // 检查目标标签是否已经关联了这个资源
      const existingRelation = await db.resource_tags
        .where(['resource_id', 'tag_id'])
        .equals([relation.resource_id, targetTagId])
        .first()

      if (!existingRelation) {
        await db.resource_tags.add({
          resource_id: relation.resource_id,
          tag_id: targetTagId,
        })
      }
    }

    // 删除源标签
    await this.deleteTag(sourceTagId)

    // 更新目标标签的资源计数
    await this.updateTagResourceCount(targetTagId)
  }

  // 更新标签的资源计数
  async updateTagResourceCount(tagId: number): Promise<void> {
    const count = await db.resource_tags.where('tag_id').equals(tagId).count()
    await db.tags.update(tagId, { resource_count: count })
  }

  // 清理未使用的标签
  async cleanupUnusedTags(): Promise<number> {
    const unusedTags = await db.tags.where('resource_count').equals(0).toArray()

    for (const tag of unusedTags) {
      await db.tags.delete(tag.id!)
    }

    return unusedTags.length
  }

  // 获取标签统计信息
  async getTagStats(): Promise<{
    total: number
    used: number
    unused: number
    mostPopular?: Tag
  }> {
    const allTags = await db.tags.toArray()
    const total = allTags.length
    const used = allTags.filter((tag) => tag.resource_count > 0).length
    const unused = total - used
    const mostPopular = allTags.reduce((prev, current) =>
      prev.resource_count > current.resource_count ? prev : current,
    )

    return {
      total,
      used,
      unused,
      mostPopular: mostPopular.resource_count > 0 ? mostPopular : undefined,
    }
  }
}

export const tagService = new TagService()
