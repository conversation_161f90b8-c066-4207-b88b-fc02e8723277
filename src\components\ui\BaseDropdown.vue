<template>
  <div class="relative" ref="dropdownRef">
    <!-- 触发器 -->
    <div @click="toggle" class="cursor-pointer">
      <slot name="trigger" :isOpen="isOpen">
        <button class="btn-secondary">
          <div v-if="triggerIcon" :class="[triggerIcon, 'mr-2']"></div>
          {{ triggerText }}
          <div :class="['i-heroicons-chevron-down ml-2 transition-transform duration-200', { 'rotate-180': isOpen }]">
          </div>
        </button>
      </slot>
    </div>

    <!-- 下拉菜单 -->
    <Transition enter-active-class="transition-all duration-200" enter-from-class="opacity-0 scale-95 translate-y-1"
      enter-to-class="opacity-100 scale-100 translate-y-0" leave-active-class="transition-all duration-200"
      leave-from-class="opacity-100 scale-100 translate-y-0" leave-to-class="opacity-0 scale-95 translate-y-1">
      <div v-if="isOpen" :class="menuClasses" @click.stop>
        <slot :close="close"></slot>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface Props {
  triggerText?: string
  triggerIcon?: string
  placement?: 'bottom-start' | 'bottom-end' | 'top-start' | 'top-end'
  width?: 'auto' | 'trigger' | 'full'
}

const props = withDefaults(defineProps<Props>(), {
  placement: 'bottom-start',
  width: 'auto'
})

const emit = defineEmits<{
  open: []
  close: []
}>()

const dropdownRef = ref<HTMLElement>()
const isOpen = ref(false)

const menuClasses = computed(() => {
  const baseClasses = [
    'absolute z-[99999] mt-2 bg-white dark:bg-gray-800 rounded-xl shadow-lg shadow-primary-200/50 dark:shadow-primary-900/30 border border-primary-200 dark:border-primary-700',
    'py-2 animate-slide-down'
  ]

  // 位置样式
  const placementClasses = {
    'bottom-start': 'top-full left-0',
    'bottom-end': 'top-full right-0',
    'top-start': 'bottom-full left-0 mb-2',
    'top-end': 'bottom-full right-0 mb-2'
  }

  // 宽度样式
  const widthClasses = {
    auto: 'min-w-48',
    trigger: 'w-full',
    full: 'w-screen max-w-sm'
  }

  return [
    ...baseClasses,
    placementClasses[props.placement],
    widthClasses[props.width]
  ]
})

const toggle = () => {
  if (isOpen.value) {
    close()
  } else {
    open()
  }
}

const open = () => {
  isOpen.value = true
  emit('open')
}

const close = () => {
  isOpen.value = false
  emit('close')
}

// 点击外部关闭
const handleClickOutside = (event: MouseEvent) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    close()
  }
}

// ESC键关闭
const handleEscape = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isOpen.value) {
    close()
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  document.addEventListener('keydown', handleEscape)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('keydown', handleEscape)
})

defineExpose({
  open,
  close,
  toggle,
  isOpen
})
</script>
