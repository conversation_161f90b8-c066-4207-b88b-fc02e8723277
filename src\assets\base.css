/* 优化的配色方案 - 提升可读性和视觉层次 */
:root {
  /* 基础白色系 - 更柔和的背景色 */
  --vt-c-white: #ffffff;
  --vt-c-white-soft: #fafafa;
  --vt-c-white-mute: #f5f5f5;
  --vt-c-white-subtle: #f9f9f9;

  /* 基础黑色系 - 更温和的深色 */
  --vt-c-black: #0f0f0f;
  --vt-c-black-soft: #1a1a1a;
  --vt-c-black-mute: #242424;
  --vt-c-black-subtle: #171717;

  /* 主要文字颜色 */
  --vt-c-text-primary: #1f2937;
  --vt-c-text-secondary: #4b5563;
  --vt-c-text-tertiary: #6b7280;

  /* 分割线颜色 - 更清晰的层次 */
  --vt-c-divider-light-1: rgba(0, 0, 0, 0.08);
  --vt-c-divider-light-2: rgba(0, 0, 0, 0.04);
  --vt-c-divider-dark-1: rgba(255, 255, 255, 0.12);
  --vt-c-divider-dark-2: rgba(255, 255, 255, 0.06);

  /* 文字颜色变量 */
  --vt-c-text-light-1: var(--vt-c-text-primary);
  --vt-c-text-light-2: var(--vt-c-text-secondary);
  --vt-c-text-light-3: var(--vt-c-text-tertiary);
  --vt-c-text-dark-1: #f9fafb;
  --vt-c-text-dark-2: #d1d5db;
  --vt-c-text-dark-3: #9ca3af;
}

/* 语义化颜色变量 - 浅色主题 */
:root {
  /* 背景色层次 */
  --color-background: var(--vt-c-white);
  --color-background-soft: var(--vt-c-white-soft);
  --color-background-mute: var(--vt-c-white-mute);
  --color-background-subtle: var(--vt-c-white-subtle);

  /* 页面主背景 - 更柔和的灰色 */
  --color-page-background: #fafafa;

  /* 边框颜色 */
  --color-border: var(--vt-c-divider-light-2);
  --color-border-hover: var(--vt-c-divider-light-1);
  --color-border-strong: rgba(0, 0, 0, 0.12);

  /* 文字颜色层次 */
  --color-heading: var(--vt-c-text-light-1);
  --color-text: var(--vt-c-text-light-1);
  --color-text-secondary: var(--vt-c-text-light-2);
  --color-text-tertiary: var(--vt-c-text-light-3);

  /* 导航栏专用颜色 */
  --color-header-bg: rgba(255, 255, 255, 0.95);
  --color-header-border: rgba(0, 0, 0, 0.08);

  /* 自定义主题色变量 - 可被JavaScript动态覆盖 */
  --color-primary-25: var(--color-primary-25, #f0fdf4);
  --color-primary-50: var(--color-primary-50, #ecfdf5);
  --color-primary-100: var(--color-primary-100, #d1fae5);
  --color-primary-200: var(--color-primary-200, #a7f3d0);
  --color-primary-300: var(--color-primary-300, #6ee7b7);
  --color-primary-400: var(--color-primary-400, #34d399);
  --color-primary-500: var(--color-primary-500, #10b981);
  --color-primary-600: var(--color-primary-600, #059669);
  --color-primary-700: var(--color-primary-700, #047857);
  --color-primary-800: var(--color-primary-800, #065f46);
  --color-primary-900: var(--color-primary-900, #064e3b);

  --section-gap: 160px;
}

/* 布局工具类 */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  max-width: 1440px;
  margin: 0 auto;
  background: var(--color-page-background);
  width: 100%;
}

.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
  width: 100%;
}

/* 页面内容包装器 - 背景占满屏幕，解决双重滚动条问题 */
.page-content-wrapper {
  width: 100%;
  min-height: calc(100vh - 64px);
  /* 移除固定高度和overflow设置，让浏览器处理滚动 */
}

/* 保留原有的滚动容器类，用于特殊需要的页面 */
.page-scroll-container {
  height: calc(100vh - 64px);
  overflow-y: auto;
  overflow-x: hidden;
}

/* 内容宽度限制类 */
.content-container {
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* 响应式内边距 */
@media (max-width: 768px) {
  .content-container {
    padding: 0 1rem;
  }

  .page-content {
    padding: 1rem;
  }
}

/* 自定义滚动条样式 */
.scroll-container::-webkit-scrollbar,
.page-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.scroll-container::-webkit-scrollbar-track,
.page-scroll-container::-webkit-scrollbar-track {
  background: var(--color-background-mute);
  border-radius: 3px;
}

.scroll-container::-webkit-scrollbar-thumb,
.page-scroll-container::-webkit-scrollbar-thumb {
  background: var(--color-border-hover);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.scroll-container::-webkit-scrollbar-thumb:hover,
.page-scroll-container::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-strong);
}

/* Firefox 滚动条样式 */
.scroll-container,
.page-scroll-container {
  scrollbar-width: thin;
  scrollbar-color: var(--color-border-hover) var(--color-background-mute);
}

/* 通用卡片样式 */
.card-base {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 0.75rem; /* rounded-xl */
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
}

.card-base:hover {
  border-color: var(--color-border-hover);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 通用按钮样式 */
.btn-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.75rem; /* rounded-xl */
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  outline: none;
}

.btn-primary {
  background: var(--color-primary-500);
  color: white;
}

.btn-primary:hover {
  background: var(--color-primary-600);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--color-primary-500), 0.4);
}

.btn-secondary {
  background: var(--color-primary-50);
  color: var(--color-primary-700);
  border: 1px solid var(--color-primary-200);
}

.btn-secondary:hover {
  background: var(--color-primary-100);
  border-color: var(--color-primary-300);
}

/* 通用输入框样式 */
.input-base {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--color-border);
  border-radius: 0.75rem; /* rounded-xl */
  background: var(--color-background);
  color: var(--color-text);
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.2s ease;
}

.input-base:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgba(var(--color-primary-500), 0.1);
}

.input-base:disabled {
  background: var(--color-background-mute);
  color: var(--color-text-tertiary);
  cursor: not-allowed;
}

/* 网格布局工具类 */
.grid-responsive {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-responsive {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .grid-responsive {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 文本样式工具类 */
.text-heading {
  color: var(--color-heading);
  font-weight: 600;
}

.text-body {
  color: var(--color-text);
  line-height: 1.6;
}

.text-muted {
  color: var(--color-text-secondary);
}

.text-subtle {
  color: var(--color-text-tertiary);
}

/* 间距工具类 */
.section-spacing {
  padding: 2rem 0;
}

.content-spacing {
  padding: 1.5rem;
}

@media (max-width: 768px) {
  .section-spacing {
    padding: 1rem 0;
  }

  .content-spacing {
    padding: 1rem;
  }
}

/* 深色主题配色 */
@media (prefers-color-scheme: dark) {
  :root {
    /* 背景色层次 */
    --color-background: var(--vt-c-black);
    --color-background-soft: var(--vt-c-black-soft);
    --color-background-mute: var(--vt-c-black-mute);
    --color-background-subtle: var(--vt-c-black-subtle);

    /* 页面主背景 - 更深的灰色 */
    --color-page-background: #111827;

    /* 边框颜色 */
    --color-border: var(--vt-c-divider-dark-2);
    --color-border-hover: var(--vt-c-divider-dark-1);
    --color-border-strong: rgba(255, 255, 255, 0.15);

    /* 文字颜色层次 */
    --color-heading: var(--vt-c-text-dark-1);
    --color-text: var(--vt-c-text-dark-1);
    --color-text-secondary: var(--vt-c-text-dark-2);
    --color-text-tertiary: var(--vt-c-text-dark-3);

    /* 导航栏专用颜色 */
    --color-header-bg: rgba(17, 24, 39, 0.95);
    --color-header-border: rgba(255, 255, 255, 0.1);

    /* 深色主题下的自定义主题色变量 - 可被JavaScript动态覆盖 */
    --color-primary-25: var(--color-primary-25, #f0fdf4);
    --color-primary-50: var(--color-primary-50, #ecfdf5);
    --color-primary-100: var(--color-primary-100, #d1fae5);
    --color-primary-200: var(--color-primary-200, #a7f3d0);
    --color-primary-300: var(--color-primary-300, #6ee7b7);
    --color-primary-400: var(--color-primary-400, #34d399);
    --color-primary-500: var(--color-primary-500, #10b981);
    --color-primary-600: var(--color-primary-600, #059669);
    --color-primary-700: var(--color-primary-700, #047857);
    --color-primary-800: var(--color-primary-800, #065f46);
    --color-primary-900: var(--color-primary-900, #064e3b);
  }
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

html,
body {
  width: 100%;
  margin: 0;
  padding: 0;
}

html {
  background: var(--color-page-background);
  transition: background-color 0.3s ease;
}

body {
  min-height: 100vh;
  color: var(--color-text);
  background: var(--color-page-background);
  transition:
    color 0.3s ease,
    background-color 0.3s ease;
  line-height: 1.6;
  /* 确保只有一个滚动条，防止双重滚动条问题 */
  overflow-x: hidden;
  /* 中文优先的字体栈 */
  font-family:
    'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei',
    '微软雅黑', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 16px; /* 提升基础字体大小，改善中文可读性 */
  /* 字体渲染优化 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: 'kern' 1;
}

/* 表单元素样式重置 */
button {
  background: none;
  cursor: pointer;
  border: none;
  outline: none;
}

button:focus {
  outline: none;
}

/* 只重置默认的浏览器样式，保留组件自定义样式 */
input:not([class*='border']),
textarea:not([class*='border']),
select:not([class*='border']) {
  border: none;
  outline: none;
  box-shadow: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
}

/* 隐藏滚动条但保持滚动功能 */
.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* ===== 中文阅读体验优化 ===== */

/* 最佳阅读宽度容器 - 针对中文内容优化 */
.reading-container {
  width: 100%;
  max-width: 720px; /* 约45-50个中文字符的最佳阅读宽度 */
  margin: 0 auto;
  padding: 0 2rem;
}

/* 宽松阅读宽度 - 适用于较短的文本 */
.reading-container-wide {
  width: 100%;
  max-width: 900px; /* 约55-60个中文字符 */
  margin: 0 auto;
  padding: 0 2rem;
}

/* 中文文本排版优化 */
.text-chinese {
  font-size: 16px;
  line-height: 1.75; /* 中文最佳行高 */
  letter-spacing: 0.02em; /* 轻微字间距改善可读性 */
  word-break: break-word;
  text-align: justify; /* 两端对齐，改善中文排版 */
  text-justify: inter-ideograph; /* 中文字符间对齐 */
}

/* 中文标题排版 */
.heading-chinese {
  font-weight: 600;
  line-height: 1.4;
  letter-spacing: 0.01em;
  margin-bottom: 0.75em;
}

/* 中文段落排版 */
.paragraph-chinese {
  margin-bottom: 1.25em; /* 增加段落间距 */
  text-indent: 2em; /* 中文段落首行缩进 */
}

/* 无缩进段落（用于列表项等） */
.paragraph-chinese-no-indent {
  margin-bottom: 1.25em;
  text-indent: 0;
}

/* ===== 响应式断点优化 - 针对中文阅读体验 ===== */

/* 移动端：< 480px（单列布局） */
@media (max-width: 479px) {
  .reading-container,
  .reading-container-wide {
    padding: 0 1rem;
    max-width: 100%;
  }

  .text-chinese {
    font-size: 15px;
    line-height: 1.7;
    text-align: left; /* 移动端左对齐更易读 */
  }

  .paragraph-chinese {
    text-indent: 1.5em; /* 移动端减少缩进 */
  }
}

/* 小屏平板：480px - 768px（适中行长） */
@media (min-width: 480px) and (max-width: 768px) {
  .reading-container {
    max-width: 600px;
  }

  .reading-container-wide {
    max-width: 750px;
  }

  .text-chinese {
    font-size: 16px;
    line-height: 1.75;
  }
}

/* 大屏平板：768px - 1024px（接近最佳阅读宽度） */
@media (min-width: 769px) and (max-width: 1024px) {
  .reading-container {
    max-width: 680px;
  }

  .reading-container-wide {
    max-width: 850px;
  }

  .text-chinese {
    font-size: 17px;
    line-height: 1.8;
  }
}

/* 桌面端：> 1024px（使用最佳阅读宽度，两侧留白） */
@media (min-width: 1025px) {
  .reading-container {
    max-width: 720px;
  }

  .reading-container-wide {
    max-width: 900px;
  }

  .text-chinese {
    font-size: 18px;
    line-height: 1.8;
  }
}

/* ===== 颜色系统优化 - WCAG 2.1 AA标准 ===== */

/* 高对比度文本颜色 - 确保4.5:1对比度 */
:root {
  /* 浅色模式 - 高对比度文本 */
  --text-primary-high-contrast: #1a1a1a;
  --text-secondary-high-contrast: #4a4a4a;
  --text-tertiary-high-contrast: #6a6a6a;

  /* 浅色模式 - 背景色 */
  --bg-primary-high-contrast: #ffffff;
  --bg-secondary-high-contrast: #f8f9fa;
  --bg-tertiary-high-contrast: #f1f3f4;

  /* 浅色模式 - 边框色 */
  --border-primary-high-contrast: #e1e5e9;
  --border-secondary-high-contrast: #dadce0;
  --border-focus-high-contrast: #1976d2;

  /* 状态颜色 - 高对比度版本 */
  --success-high-contrast: #137333;
  --warning-high-contrast: #b3400a;
  --error-high-contrast: #c5221f;
  --info-high-contrast: #1565c0;

  /* 交互状态颜色 */
  --hover-overlay-light: rgba(0, 0, 0, 0.04);
  --active-overlay-light: rgba(0, 0, 0, 0.08);
  --focus-ring-light: rgba(25, 118, 210, 0.2);
}

/* 暗色模式 - 完全对标 Ant Design 官网标准 */
:root.dark {
  /* ===== Ant Design 标准主题色系统 ===== */
  --color-primary: #1677ff; /* Ant Design 官网标准主色 */
  --color-success: #52c41a; /* Ant Design 标准成功色 */
  --color-warning: #faad14; /* Ant Design 标准警告色 */
  --color-error: #ff4d4f; /* Ant Design 标准错误色 */
  --color-info: #1677ff; /* 信息色跟随主色 */

  /* ===== Ant Design 暗色模式背景色层次 ===== */
  /* 参考官网暗色模式的背景色设计 */
  --color-page-background: #000000; /* 页面背景 - 纯黑 */
  --color-background: #141414; /* 容器背景 - 深灰 */
  --color-background-soft: #1f1f1f; /* 卡片背景 - 中灰 */
  --color-background-mute: #262626; /* 悬浮背景 - 浅灰 */
  --color-background-elevated: #2f2f2f; /* 高层级背景 */

  /* ===== Ant Design 暗色模式文本颜色 ===== */
  /* 基于官网暗色模式的文本对比度设计 */
  --color-text: rgba(255, 255, 255, 0.85); /* 主要文本 */
  --color-text-secondary: rgba(255, 255, 255, 0.65); /* 次要文本 */
  --color-text-tertiary: rgba(255, 255, 255, 0.45); /* 三级文本 */
  --color-text-quaternary: rgba(255, 255, 255, 0.25); /* 禁用文本 */
  --color-heading: rgba(255, 255, 255, 0.85); /* 标题文本 */

  /* ===== Ant Design 暗色模式边框颜色 ===== */
  --color-border: #424242; /* 基础边框 */
  --color-border-hover: #595959; /* 悬停边框 */
  --color-border-strong: #8c8c8c; /* 强调边框 */
  --color-border-light: #303030; /* 轻量边框 */

  /* ===== Ant Design 暗色模式填充色 ===== */
  --color-fill: rgba(255, 255, 255, 0.18); /* 基础填充 */
  --color-fill-secondary: rgba(255, 255, 255, 0.12); /* 次要填充 */
  --color-fill-tertiary: rgba(255, 255, 255, 0.08); /* 三级填充 */
  --color-fill-quaternary: rgba(255, 255, 255, 0.04); /* 四级填充 */

  /* ===== Ant Design 交互状态 ===== */
  --hover-overlay: rgba(255, 255, 255, 0.08); /* 悬停遮罩 */
  --active-overlay: rgba(255, 255, 255, 0.12); /* 激活遮罩 */
  --focus-ring: rgba(22, 119, 255, 0.2); /* 焦点环 - 基于主色 */
  --disabled-overlay: rgba(255, 255, 255, 0.25); /* 禁用遮罩 */

  /* ===== 兼容性变量（保持向后兼容） ===== */
  --text-primary-high-contrast: var(--color-text);
  --text-secondary-high-contrast: var(--color-text-secondary);
  --text-tertiary-high-contrast: var(--color-text-tertiary);
  --bg-primary-high-contrast: var(--color-page-background);
  --bg-secondary-high-contrast: var(--color-background);
  --bg-tertiary-high-contrast: var(--color-background-soft);
  --border-primary-high-contrast: var(--color-border);
  --border-secondary-high-contrast: var(--color-border-hover);
  --border-focus-high-contrast: var(--focus-ring);
  --success-high-contrast: var(--color-success);
  --warning-high-contrast: var(--color-warning);
  --error-high-contrast: var(--color-error);
  --info-high-contrast: var(--color-info);
  --hover-overlay-light: var(--hover-overlay);
  --active-overlay-light: var(--active-overlay);
  --focus-ring-light: var(--focus-ring);
}

/* 确保暗色模式下html和body都应用正确的背景色 */
:root.dark html,
:root.dark body {
  background: var(--color-page-background, #000000) !important;
}

/* 高对比度文本类 */
.text-primary-hc {
  color: var(--text-primary-high-contrast);
}

.text-secondary-hc {
  color: var(--text-secondary-high-contrast);
}

.text-tertiary-hc {
  color: var(--text-tertiary-high-contrast);
}

/* 高对比度背景类 */
.bg-primary-hc {
  background-color: var(--bg-primary-high-contrast);
}

.bg-secondary-hc {
  background-color: var(--bg-secondary-high-contrast);
}

.bg-tertiary-hc {
  background-color: var(--bg-tertiary-high-contrast);
}

/* 高对比度边框类 */
.border-primary-hc {
  border-color: var(--border-primary-high-contrast);
}

.border-secondary-hc {
  border-color: var(--border-secondary-high-contrast);
}

/* 状态颜色类 - 高对比度 */
.text-success-hc {
  color: var(--success-high-contrast);
}

.text-warning-hc {
  color: var(--warning-high-contrast);
}

.text-error-hc {
  color: var(--error-high-contrast);
}

.text-info-hc {
  color: var(--info-high-contrast);
}

/* ===== 交互元素高对比度优化 ===== */

/* 按钮高对比度样式 */
.btn-primary-hc {
  background-color: var(--info-high-contrast);
  color: var(--bg-primary-high-contrast);
  border: 2px solid var(--info-high-contrast);
  font-weight: 600;
  transition: all 0.2s ease;
}

.btn-primary-hc:hover {
  background-color: transparent;
  color: var(--info-high-contrast);
  box-shadow: 0 0 0 2px var(--focus-ring-light);
}

.btn-primary-hc:focus {
  outline: none;
  box-shadow: 0 0 0 3px var(--focus-ring-light);
}

.btn-secondary-hc {
  background-color: transparent;
  color: var(--text-primary-high-contrast);
  border: 2px solid var(--border-secondary-high-contrast);
  font-weight: 600;
  transition: all 0.2s ease;
}

.btn-secondary-hc:hover {
  background-color: var(--hover-overlay-light);
  border-color: var(--text-primary-high-contrast);
}

.btn-secondary-hc:focus {
  outline: none;
  box-shadow: 0 0 0 3px var(--focus-ring-light);
}

/* 链接高对比度样式 */
.link-hc {
  color: var(--info-high-contrast);
  text-decoration: underline;
  text-underline-offset: 2px;
  transition: all 0.2s ease;
}

.link-hc:hover {
  color: var(--text-primary-high-contrast);
  text-decoration-thickness: 2px;
}

.link-hc:focus {
  outline: 2px solid var(--focus-ring-light);
  outline-offset: 2px;
  border-radius: 2px;
}

/* 表单元素高对比度样式 */
.input-hc {
  background-color: var(--bg-primary-high-contrast);
  color: var(--text-primary-high-contrast);
  border: 2px solid var(--border-secondary-high-contrast);
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 16px;
  transition: all 0.2s ease;
}

.input-hc:hover {
  border-color: var(--border-focus-high-contrast);
}

.input-hc:focus {
  outline: none;
  border-color: var(--border-focus-high-contrast);
  box-shadow: 0 0 0 3px var(--focus-ring-light);
}

.input-hc::placeholder {
  color: var(--text-tertiary-high-contrast);
}

/* 卡片高对比度样式 */
.card-hc {
  background-color: var(--bg-primary-high-contrast);
  border: 1px solid var(--border-primary-high-contrast);
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.card-hc:hover {
  border-color: var(--border-secondary-high-contrast);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 导航元素高对比度样式 */
.nav-item-hc {
  color: var(--text-secondary-high-contrast);
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.2s ease;
  text-decoration: none;
}

.nav-item-hc:hover {
  background-color: var(--hover-overlay-light);
  color: var(--text-primary-high-contrast);
}

.nav-item-hc.active {
  background-color: var(--info-high-contrast);
  color: var(--bg-primary-high-contrast);
  font-weight: 600;
}

.nav-item-hc:focus {
  outline: 2px solid var(--focus-ring-light);
  outline-offset: 2px;
}
