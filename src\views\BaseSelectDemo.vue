<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-4xl mx-auto px-4">
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-8">BaseSelect 组件演示</h1>
        
        <div class="space-y-8">
          <!-- 基础用法 -->
          <section>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">基础用法</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <BaseSelect
                  v-model="basicValue"
                  label="选择城市"
                  placeholder="请选择城市"
                  :options="cityOptions"
                  @change="handleBasicChange"
                />
                <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  选中值: {{ basicValue || '未选择' }}
                </p>
              </div>
              
              <div>
                <BaseSelect
                  v-model="basicValue2"
                  label="带前缀图标"
                  placeholder="请选择"
                  prefix-icon="i-heroicons-map-pin"
                  :options="cityOptions"
                  clearable
                />
                <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  选中值: {{ basicValue2 || '未选择' }}
                </p>
              </div>
            </div>
          </section>

          <!-- 不同尺寸 -->
          <section>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">不同尺寸</h2>
            <div class="space-y-4">
              <div>
                <BaseSelect
                  v-model="sizeValue1"
                  label="小尺寸 (sm)"
                  placeholder="请选择"
                  size="sm"
                  :options="colorOptions"
                  clearable
                />
              </div>
              
              <div>
                <BaseSelect
                  v-model="sizeValue2"
                  label="中等尺寸 (md)"
                  placeholder="请选择"
                  size="md"
                  :options="colorOptions"
                  clearable
                />
              </div>
              
              <div>
                <BaseSelect
                  v-model="sizeValue3"
                  label="大尺寸 (lg)"
                  placeholder="请选择"
                  size="lg"
                  :options="colorOptions"
                  clearable
                />
              </div>
            </div>
          </section>

          <!-- 搜索功能 -->
          <section>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">搜索功能</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <BaseSelect
                  v-model="searchValue"
                  label="可搜索选择框"
                  placeholder="请选择编程语言"
                  :options="languageOptions"
                  searchable
                  clearable
                />
                <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  选中值: {{ searchValue || '未选择' }}
                </p>
              </div>
              
              <div>
                <BaseSelect
                  v-model="searchValue2"
                  label="带说明的搜索框"
                  placeholder="请选择框架"
                  :options="frameworkOptions"
                  searchable
                  clearable
                  menu-description="选择您最喜欢的前端框架"
                />
                <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  选中值: {{ searchValue2 || '未选择' }}
                </p>
              </div>
            </div>
          </section>

          <!-- 状态演示 -->
          <section>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">状态演示</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <BaseSelect
                  v-model="disabledValue"
                  label="禁用状态"
                  placeholder="已禁用"
                  :options="cityOptions"
                  disabled
                />
              </div>
              
              <div>
                <BaseSelect
                  v-model="errorValue"
                  label="错误状态"
                  placeholder="请选择"
                  :options="cityOptions"
                  error="请选择一个城市"
                  required
                />
              </div>
            </div>
          </section>

          <!-- 操作按钮 -->
          <section>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">操作</h2>
            <div class="flex flex-wrap gap-4">
              <button
                @click="clearAll"
                class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                清空所有选择
              </button>
              
              <button
                @click="setRandomValues"
                class="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
              >
                随机设置值
              </button>
            </div>
          </section>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BaseSelect from '@/components/ui/BaseSelect.vue'

// 基础值
const basicValue = ref('')
const basicValue2 = ref('')

// 尺寸值
const sizeValue1 = ref('')
const sizeValue2 = ref('')
const sizeValue3 = ref('')

// 搜索值
const searchValue = ref('')
const searchValue2 = ref('')

// 状态值
const disabledValue = ref('beijing')
const errorValue = ref('')

// 选项数据
const cityOptions = [
  { value: 'beijing', label: '北京' },
  { value: 'shanghai', label: '上海' },
  { value: 'guangzhou', label: '广州' },
  { value: 'shenzhen', label: '深圳' },
  { value: 'hangzhou', label: '杭州' },
  { value: 'nanjing', label: '南京' },
  { value: 'wuhan', label: '武汉' },
  { value: 'chengdu', label: '成都' }
]

const colorOptions = [
  { value: 'red', label: '红色' },
  { value: 'blue', label: '蓝色' },
  { value: 'green', label: '绿色' },
  { value: 'yellow', label: '黄色' },
  { value: 'purple', label: '紫色' }
]

const languageOptions = [
  { value: 'javascript', label: 'JavaScript' },
  { value: 'typescript', label: 'TypeScript' },
  { value: 'python', label: 'Python' },
  { value: 'java', label: 'Java' },
  { value: 'csharp', label: 'C#' },
  { value: 'cpp', label: 'C++' },
  { value: 'go', label: 'Go' },
  { value: 'rust', label: 'Rust' },
  { value: 'php', label: 'PHP' },
  { value: 'ruby', label: 'Ruby' }
]

const frameworkOptions = [
  { value: 'vue', label: 'Vue.js' },
  { value: 'react', label: 'React' },
  { value: 'angular', label: 'Angular' },
  { value: 'svelte', label: 'Svelte' },
  { value: 'solid', label: 'SolidJS' }
]

// 事件处理
const handleBasicChange = (value: string | number | undefined) => {
  console.log('基础选择框值变化:', value)
}

const clearAll = () => {
  basicValue.value = ''
  basicValue2.value = ''
  sizeValue1.value = ''
  sizeValue2.value = ''
  sizeValue3.value = ''
  searchValue.value = ''
  searchValue2.value = ''
  errorValue.value = ''
}

const setRandomValues = () => {
  const getRandomOption = (options: any[]) => {
    return options[Math.floor(Math.random() * options.length)].value
  }
  
  basicValue.value = getRandomOption(cityOptions)
  basicValue2.value = getRandomOption(cityOptions)
  sizeValue1.value = getRandomOption(colorOptions)
  sizeValue2.value = getRandomOption(colorOptions)
  sizeValue3.value = getRandomOption(colorOptions)
  searchValue.value = getRandomOption(languageOptions)
  searchValue2.value = getRandomOption(frameworkOptions)
}
</script>
