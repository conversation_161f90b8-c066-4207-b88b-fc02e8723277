<template>
  <div class="relative">
    <label v-if="label" :for="selectId" class="block text-sm font-medium text-secondary mb-2 text-chinese">
      {{ label }}
      <span v-if="required" class="text-red-500 ml-1">*</span>
    </label>

    <div ref="selectRef"
      :class="['custom-select-wrapper', { error: error, compact: size === 'sm', open: isOpen, disabled: disabled }]"
      @click="toggleDropdown" :tabindex="disabled ? -1 : 0" @keydown="handleTriggerKeydown">
      <div v-if="prefixIcon" class="custom-select-icon">
        <div :class="[prefixIcon, 'w-5 h-5 text-gray-500']"></div>
      </div>

      <div :class="selectClasses" :id="selectId">
        <span v-if="selectedOption" class="select-value">{{ selectedOption.label }}</span>
        <span v-else class="select-placeholder">{{ placeholder }}</span>
      </div>

      <div class="custom-select-actions">
        <button v-if="clearable && modelValue" @click.stop="handleClear" class="custom-clear-button" type="button"
          title="清空">
          <div class="i-heroicons-x-mark w-4 h-4"></div>
        </button>

        <div class="custom-select-chevron">
          <div
            :class="['i-heroicons-chevron-down w-4 h-4 text-gray-500 transition-transform', { 'rotate-180': isOpen }]">
          </div>
        </div>
      </div>
    </div>

    <!-- 下拉菜单 -->
    <Teleport to="body">
      <Transition enter-active-class="transition-all duration-200" enter-from-class="opacity-0 scale-95 translate-y-1"
        enter-to-class="opacity-100 scale-100 translate-y-0" leave-active-class="transition-all duration-200"
        leave-from-class="opacity-100 scale-100 translate-y-0" leave-to-class="opacity-0 scale-95 translate-y-1">
        <div v-if="isOpen && !disabled" ref="dropdownRef"
          :class="['custom-select-dropdown', { compact: size === 'sm' }]" :style="dropdownStyle" @click.stop>
          <!-- 搜索框 -->
          <div v-if="searchable" class="custom-select-search">
            <BaseInput v-model="searchQuery" placeholder="搜索选项..." prefix-icon="i-heroicons-magnifying-glass" size="sm"
              clearable @input="handleSearch" />
          </div>

          <!-- 菜单说明 -->
          <div v-if="menuDescription" class="custom-select-description">
            {{ menuDescription }}
          </div>

          <!-- 选项列表 -->
          <div class="custom-select-options">
            <div v-for="(option, index) in filteredOptions" :key="option.value" :class="[
              'custom-select-option',
              {
                selected: option.value === modelValue,
                highlighted: highlightedIndex === index,
                disabled: option.disabled
              }
            ]" @click="selectOption(option)" @mouseenter="highlightedIndex = index">
              <span class="option-label">{{ option.label }}</span>
              <div v-if="option.value === modelValue" class="i-heroicons-check w-4 h-4 text-primary-500"></div>
            </div>

            <div v-if="filteredOptions.length === 0" class="custom-select-empty">
              {{ searchQuery ? '未找到匹配选项' : '暂无选项' }}
            </div>
          </div>
        </div>
      </Transition>
    </Teleport>

    <p v-if="error" class="mt-1 text-sm text-red-600 animate-slide-down text-chinese">
      {{ error }}
    </p>

    <p v-else-if="hint" class="mt-1 text-sm text-secondary text-chinese">
      {{ hint }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, nextTick, onMounted, onUnmounted } from 'vue'
import BaseInput from './BaseInput.vue'

interface SelectOption {
  value: string | number
  label: string
  disabled?: boolean
}

interface Props {
  modelValue?: string | number
  options?: SelectOption[]
  label?: string
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  error?: string
  hint?: string
  prefixIcon?: string
  clearable?: boolean
  searchable?: boolean
  menuDescription?: string
  size?: 'sm' | 'md' | 'lg'
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  readonly: false,
  required: false,
  clearable: false,
  searchable: false,
  size: 'md',
  options: () => [],
  placeholder: '请选择...'
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string | number | undefined]
  change: [value: string | number | undefined]
  blur: [event: FocusEvent]
  focus: [event: FocusEvent]
}>()

const selectId = ref(`select-${Math.random().toString(36).substring(2, 11)}`)
const isOpen = ref(false)
const searchQuery = ref('')
const highlightedIndex = ref(-1)
const dropdownRef = ref<HTMLElement>()
const selectRef = ref<HTMLElement>()
const dropdownStyle = ref({})

const selectClasses = computed(() => {
  const classes = ['custom-select-field']
  if (props.size === 'sm') {
    classes.push('compact')
  }
  return classes
})

const selectedOption = computed(() => {
  return props.options.find(option => option.value === props.modelValue)
})

const filteredOptions = computed(() => {
  if (!props.searchable || !searchQuery.value) {
    return props.options
  }

  const query = searchQuery.value.toLowerCase()
  return props.options.filter(option =>
    option.label.toLowerCase().includes(query)
  )
})

const toggleDropdown = () => {
  if (props.disabled || props.readonly) return

  isOpen.value = !isOpen.value
  if (isOpen.value) {
    nextTick(() => {
      updateDropdownPosition()
      highlightedIndex.value = filteredOptions.value.findIndex(option => option.value === props.modelValue)
    })
  }
}

const selectOption = (option: SelectOption) => {
  if (option.disabled) return

  emit('update:modelValue', option.value)
  emit('change', option.value)
  isOpen.value = false
  searchQuery.value = ''
}

const handleClear = () => {
  emit('update:modelValue', undefined)
  emit('change', undefined)
}

const handleSearch = () => {
  highlightedIndex.value = -1
}

const updateDropdownPosition = () => {
  if (!selectRef.value || !dropdownRef.value) return

  const selectRect = selectRef.value.getBoundingClientRect()
  const dropdownHeight = dropdownRef.value.offsetHeight
  const viewportHeight = window.innerHeight

  const spaceBelow = viewportHeight - selectRect.bottom
  const spaceAbove = selectRect.top

  const showAbove = spaceBelow < dropdownHeight && spaceAbove > dropdownHeight

  dropdownStyle.value = {
    position: 'fixed',
    left: `${selectRect.left}px`,
    width: `${selectRect.width}px`,
    top: showAbove ? `${selectRect.top - dropdownHeight - 4}px` : `${selectRect.bottom + 4}px`,
    zIndex: 1000
  }
}

const handleTriggerKeydown = (event: KeyboardEvent) => {
  switch (event.key) {
    case 'Enter':
    case ' ':
      event.preventDefault()
      toggleDropdown()
      break
    case 'ArrowDown':
      event.preventDefault()
      if (!isOpen.value) {
        toggleDropdown()
      }
      break
    case 'Escape':
      if (isOpen.value) {
        isOpen.value = false
      }
      break
  }
}

const handleDropdownKeydown = (event: KeyboardEvent) => {
  if (!isOpen.value) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      highlightedIndex.value = Math.min(highlightedIndex.value + 1, filteredOptions.value.length - 1)
      break
    case 'ArrowUp':
      event.preventDefault()
      highlightedIndex.value = Math.max(highlightedIndex.value - 1, 0)
      break
    case 'Enter':
      event.preventDefault()
      if (highlightedIndex.value >= 0) {
        selectOption(filteredOptions.value[highlightedIndex.value])
      }
      break
    case 'Escape':
      isOpen.value = false
      break
  }
}

const handleClickOutside = (event: MouseEvent) => {
  if (!selectRef.value || !dropdownRef.value) return

  if (!selectRef.value.contains(event.target as Node) &&
    !dropdownRef.value.contains(event.target as Node)) {
    isOpen.value = false
  }
}

watch(isOpen, (newValue) => {
  if (newValue) {
    document.addEventListener('click', handleClickOutside)
    document.addEventListener('keydown', handleDropdownKeydown)
    nextTick(updateDropdownPosition)
  } else {
    document.removeEventListener('click', handleClickOutside)
    document.removeEventListener('keydown', handleDropdownKeydown)
    searchQuery.value = ''
  }
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('keydown', handleDropdownKeydown)
})
</script>

<style scoped>
/* 选择框容器样式 - 与BaseInput保持一致 */
.custom-select-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #f9fafb;
  border: 1px solid transparent;
  border-radius: 12px;
  min-height: 44px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.custom-select-wrapper:hover {
  background-color: #f3f4f6;
}

.custom-select-wrapper:focus-within,
.custom-select-wrapper.open {
  background-color: #ffffff;
  border: 1px solid var(--primary-500, #3b82f6);
  box-shadow: none;
}

.custom-select-wrapper.disabled {
  background-color: #f3f4f6;
  cursor: not-allowed;
  opacity: 0.6;
}

/* 前缀图标样式 */
.custom-select-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 12px;
  flex-shrink: 0;
}

/* 选择框字段样式 */
.custom-select-field {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  padding: 12px 12px 12px 4px;
  font-size: 16px;
  color: #111827;
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei', '微软雅黑', sans-serif;
  line-height: 1.75;
  letter-spacing: 0.02em;
  cursor: pointer;
}

.custom-select-field.compact {
  padding: 8px 10px 8px 10px;
  font-size: 14px;
  line-height: 1.5;
}

/* 有前缀图标时的样式 */
.custom-select-wrapper .custom-select-icon+.custom-select-field {
  padding-left: 4px;
}

.custom-select-wrapper.compact .custom-select-icon+.custom-select-field {
  padding-left: 4px;
}

.select-value {
  color: #111827;
}

.select-placeholder {
  color: #6b7280;
}

/* 操作按钮区域 */
.custom-select-actions {
  display: flex;
  align-items: center;
  padding-right: 12px;
  gap: 4px;
}

/* 清空按钮样式 */
.custom-clear-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #e5e7eb;
  color: #6b7280;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: 4px;
}

.custom-clear-button:hover {
  background-color: #d1d5db;
  color: #374151;
  transform: scale(1.1);
}

/* 下拉箭头样式 */
.custom-select-chevron {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* 下拉菜单样式 */
.custom-select-dropdown {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  max-height: 300px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.custom-select-dropdown.compact {
  border-radius: 8px;
}

/* 搜索框容器 */
.custom-select-search {
  padding: 12px;
  border-bottom: 1px solid #f3f4f6;
}

/* 菜单说明 */
.custom-select-description {
  padding: 8px 12px;
  font-size: 12px;
  color: #6b7280;
  background-color: #f9fafb;
  border-bottom: 1px solid #f3f4f6;
}

/* 选项列表容器 */
.custom-select-options {
  max-height: 200px;
  overflow-y: auto;
  padding: 4px 0;
}

/* 选项样式 */
.custom-select-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #374151;
}

.custom-select-option:hover,
.custom-select-option.highlighted {
  background-color: #f3f4f6;
}

.custom-select-option.selected {
  background-color: #eff6ff;
  color: var(--primary-600, #2563eb);
}

.custom-select-option.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.custom-select-option.disabled:hover {
  background-color: transparent;
}

.option-label {
  flex: 1;
}

/* 空状态 */
.custom-select-empty {
  padding: 16px 12px;
  text-align: center;
  color: #9ca3af;
  font-size: 14px;
}

/* 暗色模式 */
:root.dark .custom-select-wrapper {
  background-color: #1f2937;
  color: #f9fafb;
}

:root.dark .custom-select-wrapper:hover {
  background-color: #374151;
}

:root.dark .custom-select-wrapper:focus-within,
:root.dark .custom-select-wrapper.open {
  background-color: #1f2937;
  border: 1px solid var(--primary-400, #60a5fa);
}

:root.dark .custom-select-field {
  color: #f9fafb;
}

:root.dark .select-placeholder {
  color: #9ca3af;
}

:root.dark .custom-clear-button {
  background-color: #374151;
  color: #9ca3af;
}

:root.dark .custom-clear-button:hover {
  background-color: #4b5563;
  color: #d1d5db;
}

:root.dark .custom-select-dropdown {
  background-color: #1f2937;
  border: 1px solid #374151;
}

:root.dark .custom-select-search {
  border-bottom: 1px solid #374151;
}

:root.dark .custom-select-description {
  background-color: #111827;
  border-bottom: 1px solid #374151;
  color: #9ca3af;
}

:root.dark .custom-select-option {
  color: #d1d5db;
}

:root.dark .custom-select-option:hover,
:root.dark .custom-select-option.highlighted {
  background-color: #374151;
}

:root.dark .custom-select-option.selected {
  background-color: #1e3a8a;
  color: var(--primary-300, #93c5fd);
}

:root.dark .custom-select-empty {
  color: #6b7280;
}

/* 错误状态样式 */
.custom-select-wrapper.error {
  background-color: #fef2f2;
  border: 1px solid #fca5a5;
}

.custom-select-wrapper.error:focus-within {
  background-color: #ffffff;
  border: 1px solid #ef4444;
}

:root.dark .custom-select-wrapper.error {
  background-color: #450a0a;
  border: 1px solid #dc2626;
}

:root.dark .custom-select-wrapper.error:focus-within {
  background-color: #1f2937;
  border: 1px solid #ef4444;
}

/* 紧凑模式容器样式 */
.custom-select-wrapper.compact {
  min-height: 36px;
  border-radius: 8px;
}

/* 紧凑模式清空按钮 */
.custom-select-wrapper.compact .custom-clear-button {
  width: 18px;
  height: 18px;
  margin-right: 6px;
}

/* 设置界面优化 - 增强对比度 */
.settings-page .custom-select-wrapper {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
}

.settings-page .custom-select-wrapper:hover {
  background-color: #ffffff;
  border: 1px solid #d1d5db;
}

.settings-page .custom-select-wrapper:focus-within,
.settings-page .custom-select-wrapper.open {
  background-color: #ffffff;
  border: 1px solid var(--primary-500, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

:root.dark .settings-page .custom-select-wrapper {
  background-color: #1f2937;
  border: 1px solid #374151;
}

:root.dark .settings-page .custom-select-wrapper:hover {
  background-color: #1f2937;
  border: 1px solid #4b5563;
}

:root.dark .settings-page .custom-select-wrapper:focus-within,
:root.dark .settings-page .custom-select-wrapper.open {
  background-color: #1f2937;
  border: 1px solid var(--primary-400, #60a5fa);
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}
</style>
