import { computed } from 'vue'
import type { ThemeConfig } from 'ant-design-vue/es/config-provider/context'
import { useTheme } from '@/composables/useTheme'

/**
 * Ant Design Vue 主题配置 Composable
 * 完全对标 Ant Design 官网标准配色方案
 */
export function useAntdTheme() {
  const { isDark } = useTheme()

  // 浅色模式主题配置 - Ant Design 官网标准
  const lightTheme = computed((): ThemeConfig => {
    return {
      algorithm: undefined, // 使用默认算法（浅色）
      token: {
        // Ant Design 官网标准色彩
        colorPrimary: '#1677ff', // 官网标准主色
        colorSuccess: '#52c41a', // 官网标准成功色
        colorWarning: '#faad14', // 官网标准警告色
        colorError: '#ff4d4f', // 官网标准错误色
        colorInfo: '#1677ff', // 信息色跟随主色

        // 中性色系
        colorTextBase: '#000000',
        colorBgBase: '#ffffff',

        // 边框和分割线
        colorBorder: '#d9d9d9',
        colorBorderSecondary: '#f0f0f0',

        // 字体 - 参考官网设计
        fontFamily:
          "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'",
        fontSize: 14,

        // 圆角 - 参考官网设计
        borderRadius: 6,
        borderRadiusLG: 8,
        borderRadiusSM: 4,

        // 间距 - 参考官网设计
        padding: 16,
        paddingLG: 24,
        paddingSM: 12,
        paddingXS: 8,

        // 阴影 - 参考官网设计
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
        boxShadowSecondary: '0 4px 12px rgba(0, 0, 0, 0.15)',
      },
      components: {
        // 按钮组件 - 参考官网设计
        Button: {
          borderRadius: 6,
          controlHeight: 32,
          controlHeightLG: 40,
          controlHeightSM: 24,
        },

        // 输入框组件 - 参考官网设计
        Input: {
          borderRadius: 6,
          controlHeight: 32,
          controlHeightLG: 40,
          controlHeightSM: 24,
        },

        // 选择框组件 - 参考官网设计
        Select: {
          borderRadius: 6,
          controlHeight: 32,
          controlHeightLG: 40,
          controlHeightSM: 24,
        },

        // 卡片组件 - 参考官网设计
        Card: {
          borderRadius: 8,
          paddingLG: 24,
        },

        // 模态框组件 - 参考官网设计
        Modal: {
          borderRadius: 8,
        },
      },
    }
  })

  // 暗色模式主题配置 - 完全对标 Ant Design 官网暗色模式
  const darkTheme = computed((): ThemeConfig => {
    return {
      algorithm: undefined, // 手动配置暗色主题
      token: {
        // Ant Design 官网标准色彩
        colorPrimary: '#1677ff', // 官网标准主色
        colorSuccess: '#52c41a', // 官网标准成功色
        colorWarning: '#faad14', // 官网标准警告色
        colorError: '#ff4d4f', // 官网标准错误色
        colorInfo: '#1677ff', // 信息色跟随主色

        // 中性色系 - 参考官网暗色模式
        colorTextBase: '#ffffff',
        colorBgBase: '#000000',
        colorBgContainer: '#141414',
        colorBgElevated: '#1f1f1f',
        colorBgLayout: '#000000',

        // 文本颜色 - 参考官网暗色模式
        colorText: 'rgba(255, 255, 255, 0.85)',
        colorTextSecondary: 'rgba(255, 255, 255, 0.65)',
        colorTextTertiary: 'rgba(255, 255, 255, 0.45)',
        colorTextQuaternary: 'rgba(255, 255, 255, 0.25)',

        // 边框和分割线 - 参考官网暗色模式
        colorBorder: '#424242',
        colorBorderSecondary: '#303030',
        colorSplit: '#424242',

        // 填充色 - 参考官网暗色模式
        colorFill: 'rgba(255, 255, 255, 0.18)',
        colorFillSecondary: 'rgba(255, 255, 255, 0.12)',
        colorFillTertiary: 'rgba(255, 255, 255, 0.08)',
        colorFillQuaternary: 'rgba(255, 255, 255, 0.04)',

        // 字体 - 参考官网设计
        fontFamily:
          "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'",
        fontSize: 14,

        // 圆角 - 参考官网设计
        borderRadius: 6,
        borderRadiusLG: 8,
        borderRadiusSM: 4,

        // 间距 - 参考官网设计
        padding: 16,
        paddingLG: 24,
        paddingSM: 12,
        paddingXS: 8,

        // 阴影 - 参考官网暗色模式
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.45)',
        boxShadowSecondary: '0 4px 12px rgba(0, 0, 0, 0.45)',
      },
      components: {
        // 按钮组件 - 参考官网设计
        Button: {
          borderRadius: 6,
          controlHeight: 32,
          controlHeightLG: 40,
          controlHeightSM: 24,
        },

        // 输入框组件 - 参考官网设计
        Input: {
          borderRadius: 6,
          controlHeight: 32,
          controlHeightLG: 40,
          controlHeightSM: 24,
        },

        // 选择框组件 - 参考官网设计
        Select: {
          borderRadius: 6,
          controlHeight: 32,
          controlHeightLG: 40,
          controlHeightSM: 24,
        },

        // 卡片组件 - 参考官网暗色模式
        Card: {
          borderRadius: 8,
          paddingLG: 24,
          colorBgContainer: '#141414',
          colorBorderSecondary: '#303030',
        },

        // 模态框组件 - 参考官网暗色模式
        Modal: {
          borderRadius: 8,
          colorBgElevated: '#1f1f1f',
        },
      },
    }
  })

  // 当前主题配置
  const currentTheme = computed(() => {
    return isDark.value ? darkTheme.value : lightTheme.value
  })

  // 简化的主题系统 - 不需要额外的CSS变量更新
  // Ant Design 的主题系统已经足够完善

  return {
    currentTheme,
    lightTheme,
    darkTheme,
    isDark,
  }
}
