# MarkdownPreview 组件使用指南

## 概述

`MarkdownPreview` 是一个基于 md-editor-v3 的独立 Markdown 预览组件，可以完美渲染 Markdown 内容，支持所有标准语法和扩展功能。

## 特性

### 1. 完整的 Markdown 支持
- ✅ **基础语法**：标题、段落、粗体、斜体、删除线
- ✅ **列表**：有序列表、无序列表、任务列表
- ✅ **代码**：行内代码和代码块，支持语法高亮
- ✅ **链接和图片**：自动链接处理和图片显示
- ✅ **表格**：完整的表格渲染
- ✅ **引用**：块引用支持
- ✅ **分割线**：水平分割线

### 2. 高级功能
- ✅ **数学公式**：支持 KaTeX 数学公式渲染
- ✅ **流程图**：支持 Mermaid 图表
- ✅ **语法高亮**：代码块语法高亮
- ✅ **主题切换**：自动跟随系统主题

### 3. 样式特性
- ✅ **GitHub 风格**：使用 GitHub 风格的预览主题
- ✅ **Ant Design 集成**：完美集成 Ant Design 主题系统
- ✅ **响应式设计**：在不同屏幕尺寸下都有良好显示
- ✅ **暗黑模式**：完整的暗色主题支持

## 安装依赖

```bash
npm install md-editor-v3
```

## 基础使用

### 1. 导入组件

```vue
<script setup>
import MarkdownPreview from '@/components/common/MarkdownPreview.vue'

const markdownContent = ref(`
# 标题示例

这是一个 **粗体** 和 *斜体* 的示例。

## 代码示例

\`\`\`javascript
function hello() {
  console.log('Hello, World!')
}
\`\`\`

## 列表示例

- 项目 1
- 项目 2
  - 子项目 2.1
  - 子项目 2.2

## 表格示例

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |
`)
</script>
```

### 2. 使用组件

```vue
<template>
  <div class="preview-container">
    <MarkdownPreview :content="markdownContent" />
  </div>
</template>
```

## 高级配置

### 1. 自定义主题

```vue
<template>
  <MarkdownPreview
    :content="markdownContent"
    theme="dark"
    preview-theme="github"
    code-theme="github"
  />
</template>
```

### 2. 主题选项

#### 预览主题 (preview-theme)
- `default` - 默认主题
- `github` - GitHub 风格（推荐）
- `vuepress` - VuePress 风格
- `mk-cute` - 可爱风格
- `smart-blue` - 智能蓝色
- `cyanosis` - 青色主题

#### 代码主题 (code-theme)
- `atom` - Atom 编辑器风格
- `a11y` - 无障碍友好
- `github` - GitHub 风格（推荐）
- `gradient` - 渐变风格
- `kimbie` - Kimbie 风格
- `paraiso` - Paraiso 风格
- `qtcreator` - Qt Creator 风格
- `stackoverflow` - Stack Overflow 风格

## 实际应用示例

### 1. 在资源创建页面中使用

```vue
<template>
  <div class="resource-form">
    <!-- 左侧编辑器 -->
    <div class="editor-section">
      <MarkdownEditor
        v-model="form.description"
        :preview="false"
        placeholder="请输入资源描述..."
      />
    </div>
    
    <!-- 右侧预览 -->
    <div class="preview-section">
      <MarkdownPreview :content="form.description" />
    </div>
  </div>
</template>

<style scoped>
.resource-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.editor-section,
.preview-section {
  min-height: 400px;
}
</style>
```

### 2. 在文章详情页中使用

```vue
<template>
  <div class="article-detail">
    <h1>{{ article.title }}</h1>
    <div class="article-meta">
      <span>作者：{{ article.author }}</span>
      <span>发布时间：{{ article.createdAt }}</span>
    </div>
    
    <!-- 文章内容预览 -->
    <div class="article-content">
      <MarkdownPreview :content="article.content" />
    </div>
  </div>
</template>
```

### 3. 在评论系统中使用

```vue
<template>
  <div class="comment-system">
    <div class="comment-editor">
      <MarkdownEditor
        v-model="commentContent"
        :preview="false"
        placeholder="支持 Markdown 语法..."
        height="200px"
      />
    </div>
    
    <div class="comment-preview">
      <h4>预览</h4>
      <MarkdownPreview :content="commentContent" />
    </div>
    
    <div class="comment-actions">
      <a-button type="primary" @click="submitComment">
        发布评论
      </a-button>
    </div>
  </div>
</template>
```

## 样式自定义

### 1. 自定义容器样式

```vue
<template>
  <div class="custom-preview">
    <MarkdownPreview :content="content" />
  </div>
</template>

<style scoped>
.custom-preview {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: var(--ant-color-bg-container);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
```

### 2. 覆盖内部样式

```vue
<style>
/* 自定义标题样式 */
.markdown-preview-container :deep(.md-editor-preview h1) {
  color: #1890ff;
  border-bottom: 2px solid #1890ff;
}

/* 自定义代码块样式 */
.markdown-preview-container :deep(.md-editor-preview pre) {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
}
</style>
```

## 性能优化

### 1. 大文档处理

对于大型 Markdown 文档，建议：

```vue
<script setup>
import { computed } from 'vue'

const props = defineProps(['content'])

// 截断过长的内容
const truncatedContent = computed(() => {
  if (props.content.length > 10000) {
    return props.content.substring(0, 10000) + '\n\n...(内容已截断)'
  }
  return props.content
})
</script>

<template>
  <MarkdownPreview :content="truncatedContent" />
</template>
```

### 2. 懒加载

```vue
<script setup>
import { ref, onMounted } from 'vue'

const showPreview = ref(false)
const content = ref('')

onMounted(() => {
  // 延迟加载预览
  setTimeout(() => {
    showPreview.value = true
  }, 100)
})
</script>

<template>
  <div v-if="showPreview">
    <MarkdownPreview :content="content" />
  </div>
  <div v-else class="loading">
    加载中...
  </div>
</template>
```

## 注意事项

1. **依赖管理**：确保已安装 `md-editor-v3` 依赖
2. **主题同步**：组件会自动跟随系统主题变化
3. **内容安全**：组件内置 HTML 清理功能，防止 XSS 攻击
4. **性能考虑**：大文档建议使用截断或懒加载
5. **样式隔离**：使用 `:deep()` 选择器自定义内部样式

## 故障排除

### 1. 样式问题
- 检查是否正确导入了 CSS 文件
- 确认主题变量是否正确配置
- 查看是否有样式冲突

### 2. 渲染问题
- 确认 Markdown 语法是否正确
- 检查特殊字符是否需要转义
- 验证数学公式和图表语法

### 3. 性能问题
- 检查文档大小是否过大
- 考虑使用内容截断
- 启用懒加载机制
