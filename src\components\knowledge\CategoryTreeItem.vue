<template>
  <div>
    <!-- 当前分类项 -->
    <div :class="[
      'flex items-center px-4 py-2 text-sm hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors',
      level > 0 ? `ml-${level * 4}` : '',
      selectedCategory?.id === category.id ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400' : ''
    ]">
      <!-- 展开/收起图标 -->
      <button v-if="hasChildren" class="mr-2 p-0.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        @click.stop="toggleExpanded">
        <div :class="[
          'w-3 h-3 transition-transform duration-200',
          isExpanded ? 'i-heroicons-chevron-down' : 'i-heroicons-chevron-right'
        ]"></div>
      </button>

      <!-- 缩进占位符 -->
      <div v-else-if="level > 0" class="w-4 mr-2"></div>

      <!-- 分类图标 -->
      <div :class="[
        'w-4 h-4 mr-2',
        hasChildren ? 'i-heroicons-folder' : 'i-heroicons-document'
      ]"></div>

      <!-- 分类名称和资源数量 - 可点击区域 -->
      <div class="flex-1 flex items-center justify-between cursor-pointer" @click="handleClick">
        <span>{{ category.name }}</span>
        <span class="text-xs text-gray-500 ml-2">({{ category.resource_count }})</span>
      </div>

      <!-- 选中图标 -->
      <div v-if="selectedCategory?.id === category.id" class="i-heroicons-check w-4 h-4 ml-2 text-primary-500"></div>
    </div>

    <!-- 子分类 -->
    <template v-if="hasChildren && isExpanded">
      <CategoryTreeItem v-for="child in category.children" :key="child.id" :category="child"
        :selected-category="selectedCategory" :level="level + 1" @select="$emit('select', $event)" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { Category, CategoryWithChildren } from '@/types'

interface Props {
  category: CategoryWithChildren
  selectedCategory?: Category | null
  level: number
}

interface Emits {
  (e: 'select', category: Category): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isExpanded = ref(false)

const hasChildren = computed(() => {
  return props.category.children && props.category.children.length > 0
})

// 处理点击
const handleClick = () => {
  // 选择分类
  emit('select', props.category)

  // 如果有子分类，自动展开
  if (hasChildren.value) {
    isExpanded.value = true
  }
}

// 切换展开状态
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

// 选择分类
const selectCategory = () => {
  emit('select', props.category)
}
</script>

<style scoped>
/* 动态缩进样式 */
.ml-4 {
  margin-left: 1rem;
}

.ml-8 {
  margin-left: 2rem;
}

.ml-12 {
  margin-left: 3rem;
}

.ml-16 {
  margin-left: 4rem;
}

.ml-20 {
  margin-left: 5rem;
}
</style>
