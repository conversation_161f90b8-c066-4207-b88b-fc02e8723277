/**
 * JSON数据压缩工具
 * 使用gzip压缩减少存储空间和传输大小
 */

import pako from 'pako'

// 压缩JSON数据
export function compressJson(data: any): string {
  try {
    const jsonString = JSON.stringify(data)
    const compressed = pako.gzip(jsonString)
    return btoa(String.fromCharCode(...compressed))
  } catch (error) {
    console.error('JSON压缩失败:', error)
    throw new Error('数据压缩失败')
  }
}

// 解压JSON数据
export function decompressJson<T = any>(compressedData: string): T {
  try {
    const compressed = new Uint8Array(
      atob(compressedData).split('').map(char => char.charCodeAt(0))
    )
    const decompressed = pako.ungzip(compressed, { to: 'string' })
    return JSON.parse(decompressed)
  } catch (error) {
    console.error('JSON解压失败:', error)
    throw new Error('数据解压失败')
  }
}

// 检查数据是否已压缩
export function isCompressed(data: string): boolean {
  try {
    // 尝试解压，如果成功说明是压缩数据
    decompressJson(data)
    return true
  } catch {
    return false
  }
}

// 智能解析数据（自动检测是否压缩）
export function smartParseJson<T = any>(data: string): T {
  if (isCompressed(data)) {
    return decompressJson<T>(data)
  } else {
    return JSON.parse(data)
  }
}

// 计算压缩率
export function getCompressionRatio(originalData: any): { 
  originalSize: number
  compressedSize: number
  ratio: number
  savings: string
} {
  const originalString = JSON.stringify(originalData)
  const compressedString = compressJson(originalData)
  
  const originalSize = new Blob([originalString]).size
  const compressedSize = new Blob([compressedString]).size
  const ratio = compressedSize / originalSize
  const savings = ((1 - ratio) * 100).toFixed(1) + '%'
  
  return {
    originalSize,
    compressedSize,
    ratio,
    savings
  }
}

// 格式化文件大小
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
