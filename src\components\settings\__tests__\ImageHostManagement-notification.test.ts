import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import ImageHostManagement from '../ImageHostManagement.vue'

// Mock Ant Design Vue message
const mockMessage = {
  loading: vi.fn().mockReturnValue(Promise.resolve()),
  success: vi.fn().mockReturnValue(Promise.resolve()),
  error: vi.fn().mockReturnValue(Promise.resolve()),
  info: vi.fn().mockReturnValue(Promise.resolve())
}

vi.mock('ant-design-vue', () => ({
  message: mockMessage
}))

// Mock services
vi.mock('@/services/imageHostService', () => ({
  imageHostService: {
    getConfigs: vi.fn().mockResolvedValue([
      {
        id: '1',
        name: '测试图床',
        provider: 'test',
        enabled: false,
        priority: 1,
        apiUrl: 'https://test.com/upload',
        method: 'POST',
        authType: 'none',
        fileField: 'file',
        responseType: 'json',
        urlField: 'url'
      }
    ]),
    saveConfig: vi.fn().mockResolvedValue({
      id: '1',
      name: '测试图床',
      provider: 'test',
      enabled: true,
      priority: 1,
      apiUrl: 'https://test.com/upload',
      method: 'POST',
      authType: 'none',
      fileField: 'file',
      responseType: 'json',
      urlField: 'url'
    }),
    testConfig: vi.fn().mockResolvedValue({
      success: true,
      message: '连接成功'
    }),
    deleteConfig: vi.fn().mockResolvedValue(undefined)
  }
}))

// Mock Ant Design Vue components
const mockAntdComponents = {
  'a-space': { template: '<div class="ant-space"><slot /></div>' },
  'a-card': { template: '<div class="ant-card"><slot name="title" /><slot /></div>' },
  'a-button': { 
    template: '<button class="ant-btn" @click="$emit(\'click\')"><slot /></button>',
    emits: ['click']
  },
  'a-switch': {
    template: '<input type="checkbox" class="ant-switch" :checked="checked" @change="$emit(\'update:checked\', $event.target.checked)" />',
    props: ['checked'],
    emits: ['update:checked']
  },
  'a-tooltip': { template: '<div class="ant-tooltip"><slot /></div>' },
  'a-dropdown': { template: '<div class="ant-dropdown"><slot /></div>' },
  'a-menu': { template: '<div class="ant-menu"><slot /></div>' },
  'a-menu-item': { 
    template: '<div class="ant-menu-item" @click="$emit(\'click\')"><slot /></div>',
    emits: ['click']
  },
  'a-table': { 
    template: '<div class="ant-table"><slot /></div>',
    props: ['dataSource', 'columns', 'pagination']
  },
  'a-tag': { template: '<span class="ant-tag"><slot /></span>' },
  'a-modal': { template: '<div class="ant-modal"><slot /></div>' },
  'a-collapse-transition': { template: '<div><slot /></div>' }
}

describe('ImageHostManagement - 通知功能测试', () => {
  let wrapper: any

  beforeEach(() => {
    vi.clearAllMocks()
    
    wrapper = mount(ImageHostManagement, {
      global: {
        components: mockAntdComponents,
        stubs: {
          'ImageHostConfigModal': true,
          'ImageHostTutorial': true,
          'ImageHostTutorialModal': true,
          'ImageTagManagementModal': true,
          'ImageHostAnalytics': true,
          'Teleport': true
        }
      }
    })
  })

  it('应该使用 Promise 接口和 key 来更新消息内容', async () => {
    // 等待组件挂载
    await nextTick()
    await wrapper.vm.$nextTick()

    const testConfig = {
      id: '1',
      name: '测试图床',
      enabled: false,
      apiUrl: 'https://test.com/upload'
    }

    // 模拟启用图床
    await wrapper.vm.handleToggleEnabled(testConfig, true)

    // 验证使用了正确的消息 key
    expect(mockMessage.loading).toHaveBeenCalledWith({
      content: '正在检测图床 "测试图床" 的连接状态...',
      key: 'imagehost-test-1',
      duration: 0
    })

    // 验证成功时更新了消息内容
    expect(mockMessage.success).toHaveBeenCalledWith({
      content: '图床 "测试图床" 连接正常，可以正常使用',
      key: 'imagehost-test-1',
      duration: 4
    })
  })

  it('应该在连接失败时显示错误消息并自动禁用图床', async () => {
    // Mock 失败的测试结果
    const { imageHostService } = await import('@/services/imageHostService')
    vi.mocked(imageHostService.testConfig).mockResolvedValueOnce({
      success: false,
      message: '连接超时'
    })

    await nextTick()
    await wrapper.vm.$nextTick()

    const testConfig = {
      id: '1',
      name: '测试图床',
      enabled: false,
      apiUrl: 'https://test.com/upload'
    }

    // 模拟启用图床
    await wrapper.vm.handleToggleEnabled(testConfig, true)

    // 验证显示了加载消息
    expect(mockMessage.loading).toHaveBeenCalledWith({
      content: '正在检测图床 "测试图床" 的连接状态...',
      key: 'imagehost-test-1',
      duration: 0
    })

    // 验证显示了错误消息
    expect(mockMessage.error).toHaveBeenCalledWith({
      content: '图床 "测试图床" 连接失败，已自动禁用: 连接超时',
      key: 'imagehost-test-1',
      duration: 6
    })

    // 验证图床被自动禁用
    expect(imageHostService.saveConfig).toHaveBeenCalledWith(
      expect.objectContaining({ enabled: false }),
      '1'
    )
  })

  it('应该在连接异常时显示异常消息并自动禁用图床', async () => {
    // Mock 异常情况
    const { imageHostService } = await import('@/services/imageHostService')
    vi.mocked(imageHostService.testConfig).mockRejectedValueOnce(new Error('网络错误'))

    await nextTick()
    await wrapper.vm.$nextTick()

    const testConfig = {
      id: '1',
      name: '测试图床',
      enabled: false,
      apiUrl: 'https://test.com/upload'
    }

    // 模拟启用图床
    await wrapper.vm.handleToggleEnabled(testConfig, true)

    // 验证显示了异常消息
    expect(mockMessage.error).toHaveBeenCalledWith({
      content: '图床 "测试图床" 连接异常，已自动禁用: 网络错误',
      key: 'imagehost-test-1',
      duration: 6
    })
  })

  it('应该在禁用图床时不显示任何通知', async () => {
    await nextTick()
    await wrapper.vm.$nextTick()

    const testConfig = {
      id: '1',
      name: '测试图床',
      enabled: true,
      apiUrl: 'https://test.com/upload'
    }

    // 清除之前的调用记录
    vi.clearAllMocks()

    // 模拟禁用图床
    await wrapper.vm.handleToggleEnabled(testConfig, false)

    // 验证没有显示任何通知
    expect(mockMessage.loading).not.toHaveBeenCalled()
    expect(mockMessage.success).not.toHaveBeenCalled()
    expect(mockMessage.error).not.toHaveBeenCalled()
    expect(mockMessage.info).not.toHaveBeenCalled()
  })

  it('应该在手动测试连接时使用增强版通知', async () => {
    await nextTick()
    await wrapper.vm.$nextTick()

    const testConfig = {
      id: '1',
      name: '测试图床',
      apiUrl: 'https://test.com/upload'
    }

    // 模拟手动测试连接
    await wrapper.vm.testConfig(testConfig, true)

    // 验证使用了增强版通知
    expect(mockMessage.loading).toHaveBeenCalledWith({
      content: '正在检测图床 "测试图床" 的连接状态...',
      key: 'imagehost-test-1',
      duration: 0
    })

    expect(mockMessage.success).toHaveBeenCalledWith({
      content: '图床 "测试图床" 连接正常，可以正常使用',
      key: 'imagehost-test-1',
      duration: 4
    })
  })

  it('应该为不同的图床使用不同的消息 key', async () => {
    await nextTick()
    await wrapper.vm.$nextTick()

    const testConfig1 = { id: '1', name: '图床1', enabled: false }
    const testConfig2 = { id: '2', name: '图床2', enabled: false }

    // 测试第一个图床
    await wrapper.vm.handleToggleEnabled(testConfig1, true)
    expect(mockMessage.loading).toHaveBeenCalledWith(
      expect.objectContaining({ key: 'imagehost-test-1' })
    )

    // 测试第二个图床
    await wrapper.vm.handleToggleEnabled(testConfig2, true)
    expect(mockMessage.loading).toHaveBeenCalledWith(
      expect.objectContaining({ key: 'imagehost-test-2' })
    )
  })
})
