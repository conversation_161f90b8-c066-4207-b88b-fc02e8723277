import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  AiConfig,
  AiConfigForm,
  AiChatSession,
  AiMessage,
  AiChatStatus,
  AiResponse,
} from '@/types'
import { aiConfigService } from '@/services/aiConfigService'
import { aiChatService } from '@/services/aiChatService'
import { aiHistoryService } from '@/services/aiHistoryService'

/**
 * AI功能状态管理Store
 */
export const useAiStore = defineStore('ai', () => {
  // 状态定义
  const configs = ref<AiConfig[]>([])
  const currentConfig = ref<AiConfig | null>(null)
  const selectedConfigId = ref<string>('')
  const currentSession = ref<AiChatSession | null>(null)
  const sessions = ref<AiChatSession[]>([])
  const chatStatus = ref<AiChatStatus>('idle')
  const isConfigModalOpen = ref(false)
  const isChatModalOpen = ref(false)
  const currentMessage = ref('')
  const isStreaming = ref(false)
  const selectedModel = ref('')

  // 计算属性
  const isConfigured = computed(() => {
    return currentConfig.value && currentConfig.value.apiKey && currentConfig.value.baseUrl
  })

  const isEnabled = computed(() => {
    return currentConfig.value?.enabled || false
  })

  const canSendMessage = computed(() => {
    return (
      isConfigured.value &&
      isEnabled.value &&
      chatStatus.value === 'idle' &&
      currentMessage.value.trim().length > 0
    )
  })

  const canUseInput = computed(() => {
    return isConfigured.value && isEnabled.value && chatStatus.value === 'idle'
  })

  const currentMessages = computed(() => {
    return currentSession.value?.messages || []
  })

  const availableConfigs = computed(() => {
    return configs.value.filter((config) => config.enabled)
  })

  const availableModels = computed(() => {
    // 获取所有启用的配置的模型
    const allModels: Array<{ label: string; value: string; provider: string; configName: string }> =
      []

    availableConfigs.value.forEach((config) => {
      const models = aiConfigService.getAvailableModels(config.provider)
      models.forEach((model) => {
        allModels.push({
          ...model,
          provider: config.provider,
          configName: config.name,
          label: `${model.label} (${config.name})`,
        })
      })
    })

    return allModels
  })

  const currentModelName = computed(() => {
    return selectedModel.value || currentConfig.value?.modelName || ''
  })

  // 检查当前会话是否已经开始对话（有消息）
  const hasStartedConversation = computed(() => {
    return currentSession.value && currentSession.value.messages.length > 0
  })

  // 检查是否可以更改模型（只有在新会话或空会话时才能更改）
  const canChangeModel = computed(() => {
    return !hasStartedConversation.value
  })

  // 配置相关操作
  const loadConfigs = async () => {
    try {
      console.log('开始加载AI配置...')
      configs.value = await aiConfigService.getAllConfigs()
      console.log(
        `加载了 ${configs.value.length} 个AI配置:`,
        configs.value.map((c) => ({
          name: c.name,
          enabled: c.enabled,
          isDefault: c.isDefault,
        })),
      )

      currentConfig.value = await aiConfigService.getDefaultConfig()
      console.log('当前默认配置:', currentConfig.value?.name)

      if (currentConfig.value) {
        selectedConfigId.value = currentConfig.value.id
        console.log('设置选中的配置ID:', selectedConfigId.value)
      } else {
        console.log('没有找到默认配置')
      }
    } catch (error) {
      console.error('加载AI配置失败:', error)
    }
  }

  const saveConfig = async (configForm: AiConfigForm, configId?: string) => {
    try {
      const savedConfig = await aiConfigService.saveConfig(configForm, configId)
      await loadConfigs() // 重新加载配置列表
      return savedConfig
    } catch (error) {
      console.error('保存AI配置失败:', error)
      throw error
    }
  }

  const deleteConfig = async (configId: string) => {
    try {
      await aiConfigService.deleteConfig(configId)
      await loadConfigs() // 重新加载配置列表
    } catch (error) {
      console.error('删除AI配置失败:', error)
      throw error
    }
  }

  const setDefaultConfig = async (configId: string) => {
    try {
      await aiConfigService.setDefaultConfig(configId)
      await loadConfigs() // 重新加载配置列表
    } catch (error) {
      console.error('设置默认配置失败:', error)
      throw error
    }
  }

  const selectConfig = (configId: string) => {
    const config = configs.value.find((c) => c.id === configId)
    if (config) {
      currentConfig.value = config
      selectedConfigId.value = configId
    }
  }

  const testConfig = async (configForm: AiConfigForm) => {
    try {
      return await aiConfigService.testConfig(configForm)
    } catch (error) {
      console.error('测试AI配置失败:', error)
      throw error
    }
  }

  const getDefaultConfigForm = () => {
    return aiConfigService.getDefaultConfigForm()
  }

  const getProviderDefaults = (provider: AiProvider) => {
    return aiConfigService.getProviderDefaults(provider)
  }

  const getAvailableModels = (provider: AiProvider) => {
    return aiConfigService.getAvailableModels(provider)
  }

  const selectModel = (modelName: string) => {
    // 检查是否可以更改模型
    if (!canChangeModel.value) {
      console.warn('无法在对话进行中更改模型')
      return false
    }

    selectedModel.value = modelName
    // 保存用户的模型选择偏好到localStorage
    localStorage.setItem('ai_selected_model', modelName)
    return true
  }

  const loadModelPreference = () => {
    const savedModel = localStorage.getItem('ai_selected_model')
    if (savedModel) {
      selectedModel.value = savedModel
    }
  }

  // 会话相关操作
  const loadSessions = async () => {
    try {
      sessions.value = await aiHistoryService.getAllSessions()
    } catch (error) {
      console.error('加载会话列表失败:', error)
    }
  }

  const createNewSession = async (title?: string, isTemporaryChat?: boolean) => {
    try {
      // 创建新会话时，重置模型选择（允许用户重新选择）
      // 但保持用户之前的偏好作为默认值
      const savedModel = localStorage.getItem('ai_selected_model')
      if (savedModel) {
        selectedModel.value = savedModel
      }

      if (isTemporaryChat) {
        // 创建24小时临时对话，立即保存到历史记录
        const session = await aiHistoryService.createSession(title, true)
        currentSession.value = session
        sessions.value.unshift(session)
        return session
      } else {
        // 创建普通临时会话，不立即保存到历史记录
        const session = {
          id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          title: title || '新对话',
          messages: [],
          createdAt: new Date(),
          updatedAt: new Date(),
          totalTokens: 0,
          isTemporary: true, // 标记为临时会话
        }

        currentSession.value = session
        return session
      }
    } catch (error) {
      console.error('创建新会话失败:', error)
      throw error
    }
  }

  const selectSession = async (sessionId: string) => {
    try {
      const session = await aiHistoryService.getSession(sessionId)
      if (session) {
        currentSession.value = session
      }
    } catch (error) {
      console.error('选择会话失败:', error)
      throw error
    }
  }

  const deleteSession = async (sessionId: string) => {
    try {
      await aiHistoryService.deleteSession(sessionId)
      sessions.value = sessions.value.filter((s) => s.id !== sessionId)

      // 如果删除的是当前会话，清空当前会话
      if (currentSession.value?.id === sessionId) {
        currentSession.value = null
      }
    } catch (error) {
      console.error('删除会话失败:', error)
      throw error
    }
  }

  const clearAllHistory = async () => {
    try {
      await aiHistoryService.clearAllHistory()
      sessions.value = []
      currentSession.value = null
    } catch (error) {
      console.error('清空历史记录失败:', error)
      throw error
    }
  }

  const updateSessionTitle = async (sessionId: string, title: string) => {
    try {
      await aiHistoryService.updateSessionTitle(sessionId, title)

      // 更新本地状态
      const sessionIndex = sessions.value.findIndex((s) => s.id === sessionId)
      if (sessionIndex >= 0) {
        sessions.value[sessionIndex].title = title
      }

      if (currentSession.value?.id === sessionId) {
        currentSession.value.title = title
      }
    } catch (error) {
      console.error('更新会话标题失败:', error)
      throw error
    }
  }

  // 消息相关操作
  const sendMessage = async (content: string) => {
    if (!canSendMessage.value) {
      throw new Error('当前无法发送消息')
    }

    try {
      chatStatus.value = 'sending'

      // 如果没有当前会话，创建新会话
      if (!currentSession.value) {
        await createNewSession()
      }

      // 创建用户消息
      const userMessage = aiChatService.createUserMessage(content)
      await addMessageToCurrentSession(userMessage)

      // 如果这是第一条用户消息，更新会话标题
      if (
        currentSession.value &&
        currentSession.value.messages.filter((m) => m.role === 'user').length === 1
      ) {
        const newTitle = generateTitleFromMessage(content)
        if (newTitle !== currentSession.value.title) {
          await updateSessionTitle(currentSession.value.id, newTitle)
        }
      }

      // 准备发送给AI的消息历史
      const messagesToSend = currentMessages.value.filter((msg) => !msg.error)

      chatStatus.value = 'receiving'
      isStreaming.value = true

      // 创建助手消息占位符
      const assistantMessage = aiChatService.createAssistantMessage('')
      await addMessageToCurrentSession(assistantMessage)

      // 发送消息到AI服务，使用选中的模型
      const response = await aiChatService.sendMessage(
        messagesToSend,
        (partialContent: string) => {
          // 更新流式响应内容
          if (currentSession.value) {
            const lastMessage =
              currentSession.value.messages[currentSession.value.messages.length - 1]
            if (lastMessage.role === 'assistant') {
              lastMessage.content = partialContent
            }
          }
        },
        currentModelName.value, // 传递选中的模型名称
      )

      // 更新最终响应
      if (currentSession.value) {
        const lastMessage = currentSession.value.messages[currentSession.value.messages.length - 1]
        if (lastMessage.role === 'assistant') {
          lastMessage.content = response.content
          lastMessage.tokens = response.tokens
          // 保存使用的模型信息
          ;(lastMessage as any).model = currentModelName.value || response.model
          await aiHistoryService.saveSession(currentSession.value)
        }
      }

      chatStatus.value = 'idle'
    } catch (error) {
      chatStatus.value = 'error'

      // 添加错误消息
      const errorMessage = aiChatService.createErrorMessage(
        error instanceof Error ? error.message : '发送消息失败',
      )
      await addMessageToCurrentSession(errorMessage)

      throw error
    } finally {
      isStreaming.value = false
    }
  }

  const cancelMessage = () => {
    aiChatService.cancelRequest()
    chatStatus.value = 'idle'
    isStreaming.value = false
  }

  const addMessageToCurrentSession = async (message: AiMessage) => {
    if (!currentSession.value) {
      throw new Error('没有当前会话')
    }

    // 如果是临时会话且是第一条消息，先保存到历史记录
    if (currentSession.value.isTemporary && currentSession.value.messages.length === 0) {
      // 创建正式会话
      const savedSession = await aiHistoryService.createSession(currentSession.value.title)

      // 更新当前会话ID和状态
      currentSession.value.id = savedSession.id
      currentSession.value.isTemporary = false
      currentSession.value.createdAt = savedSession.createdAt

      // 添加到会话列表
      sessions.value.unshift(currentSession.value)
    }

    // 添加消息
    if (!currentSession.value.isTemporary) {
      await aiHistoryService.addMessage(currentSession.value.id, message)
    }

    // 更新本地状态
    currentSession.value.messages.push(message)
    currentSession.value.updatedAt = new Date()

    // 更新会话列表中的对应项（如果不是临时会话）
    if (!currentSession.value.isTemporary) {
      const sessionIndex = sessions.value.findIndex((s) => s.id === currentSession.value!.id)
      if (sessionIndex >= 0) {
        sessions.value[sessionIndex] = { ...currentSession.value }

        // 将更新的会话移到列表顶部
        if (sessionIndex > 0) {
          const [updatedSession] = sessions.value.splice(sessionIndex, 1)
          sessions.value.unshift(updatedSession)
        }
      }
    }
  }

  // 模态框控制
  const openConfigModal = () => {
    isConfigModalOpen.value = true
  }

  const closeConfigModal = () => {
    isConfigModalOpen.value = false
  }

  const openChatModal = async () => {
    if (!isConfigured.value) {
      openConfigModal()
      return
    }

    isChatModalOpen.value = true

    // 加载会话列表
    await loadSessions()

    // 如果没有当前会话，选择最近的会话或创建新会话
    if (!currentSession.value && sessions.value.length > 0) {
      currentSession.value = sessions.value[0]
    }
  }

  const closeChatModal = () => {
    isChatModalOpen.value = false
    currentMessage.value = ''
    chatStatus.value = 'idle'
    isStreaming.value = false
  }

  // 搜索功能
  const searchHistory = async (keyword: string) => {
    try {
      return await aiHistoryService.searchHistory(keyword)
    } catch (error) {
      console.error('搜索历史记录失败:', error)
      return []
    }
  }

  // 统计信息
  const getStatistics = async () => {
    try {
      return await aiHistoryService.getStatistics()
    } catch (error) {
      console.error('获取统计信息失败:', error)
      return {
        totalSessions: 0,
        totalMessages: 0,
        totalTokens: 0,
      }
    }
  }

  // 清理过期临时对话
  const cleanupExpiredTemporaryChats = async () => {
    try {
      const removedCount = await aiHistoryService.cleanupExpiredTemporaryChats()
      if (removedCount > 0) {
        await loadSessions() // 重新加载会话列表
      }
      return removedCount
    } catch (error) {
      console.error('清理过期临时对话失败:', error)
      return 0
    }
  }

  // 对话类型转换
  const convertToPermanent = async (sessionId: string) => {
    try {
      await aiHistoryService.convertToPermanent(sessionId)
      await loadSessions() // 重新加载会话列表

      // 如果转换的是当前会话，更新当前会话状态
      if (currentSession.value && currentSession.value.id === sessionId) {
        currentSession.value.isTemporaryChat = false
        currentSession.value.expiresAt = undefined
      }
    } catch (error) {
      console.error('转为永久对话失败:', error)
      throw error
    }
  }

  const convertToTemporary = async (sessionId: string) => {
    try {
      await aiHistoryService.convertToTemporary(sessionId)
      await loadSessions() // 重新加载会话列表

      // 如果转换的是当前会话，更新当前会话状态
      if (currentSession.value && currentSession.value.id === sessionId) {
        const now = new Date()
        currentSession.value.isTemporaryChat = true
        currentSession.value.expiresAt = new Date(now.getTime() + 24 * 60 * 60 * 1000)
      }
    } catch (error) {
      console.error('转为临时对话失败:', error)
      throw error
    }
  }

  // 强制重新加载配置（暴露给全局使用）
  const forceReloadConfigs = async () => {
    console.log('强制重新加载AI配置...')
    await loadConfigs()
    console.log('强制重新加载完成')
  }

  // 初始化
  const initialize = async () => {
    await loadConfigs()
    await loadSessions()
    // 清理过期的临时对话
    await cleanupExpiredTemporaryChats()
    // 加载模型偏好
    loadModelPreference()

    // 将重新加载方法暴露到全局
    if (typeof window !== 'undefined') {
      ;(window as any).forceReloadAiConfigs = forceReloadConfigs

      window.addEventListener('ai-configs-imported', async (event: any) => {
        console.log('检测到AI配置导入，重新加载配置...', event.detail)
        await loadConfigs()
        console.log('AI配置重新加载完成，当前配置数量:', configs.value.length)
        console.log('当前默认配置:', currentConfig.value?.name)
      })

      // 备用的强制重新加载事件
      window.addEventListener('force-reload-ai-configs', async () => {
        console.log('强制重新加载AI配置...')
        await loadConfigs()
        console.log('强制重新加载完成，当前配置数量:', configs.value.length)
      })
    }
  }

  // 根据消息内容生成标题
  const generateTitleFromMessage = (content: string): string => {
    const trimmedContent = content.trim()

    // 如果内容很短，直接使用
    if (trimmedContent.length <= 40) {
      return trimmedContent
    }

    // 尝试找到第一个句子（以句号、问号、感叹号结尾）
    const sentenceMatch = trimmedContent.match(/^[^。？！.?!]*[。？！.?!]/)
    if (sentenceMatch && sentenceMatch[0].length <= 50) {
      return sentenceMatch[0]
    }

    // 尝试找到第一个逗号前的内容
    const commaMatch = trimmedContent.match(/^[^，,]*/)
    if (commaMatch && commaMatch[0].length > 10 && commaMatch[0].length <= 40) {
      return commaMatch[0]
    }

    // 默认截取前40个字符
    const title = trimmedContent.substring(0, 40)
    return title.length < trimmedContent.length ? `${title}...` : title
  }

  return {
    // 状态
    configs,
    currentConfig,
    selectedConfigId,
    currentSession,
    sessions,
    chatStatus,
    isConfigModalOpen,
    isChatModalOpen,
    currentMessage,
    isStreaming,
    selectedModel,

    // 计算属性
    isConfigured,
    isEnabled,
    canSendMessage,
    canUseInput,
    currentMessages,
    availableConfigs,
    availableModels,
    currentModelName,
    hasStartedConversation,
    canChangeModel,

    // 配置操作
    loadConfigs,
    saveConfig,
    deleteConfig,
    setDefaultConfig,
    selectConfig,
    testConfig,

    // 会话操作
    loadSessions,
    createNewSession,
    selectSession,
    deleteSession,
    clearAllHistory,
    updateSessionTitle,

    // 消息操作
    sendMessage,
    cancelMessage,

    // 模态框控制
    openConfigModal,
    closeConfigModal,
    openChatModal,
    closeChatModal,

    // 其他功能
    searchHistory,
    getStatistics,
    initialize,
    getDefaultConfigForm,
    getProviderDefaults,
    getAvailableModels,
    selectModel,
    cleanupExpiredTemporaryChats,
    convertToPermanent,
    convertToTemporary,
  }
})
