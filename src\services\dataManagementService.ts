import { themeSettingsService } from './themeSettingsService'
import { db } from '@/database'
import { resourceService } from './resourceService'
import { categoryService } from './categoryService'
import { tagService } from './tagService'
import { aiConfigService } from './aiConfigService'
import { aiHistoryService } from './aiHistoryService'
import type { AiChatSession } from '@/types'
import {
  encryptData,
  decryptData,
  generateSalt,
  generateDeviceFingerprint,
} from '@/utils/encryption'
import { compressJson, decompressJson } from '@/utils/compression'

// 数据导出导入接口 - 新版本支持AI功能
export interface ExportData {
  version: string
  exportTime: string
  data: {
    knowledgeEntries: KnowledgeEntry[]
    categories: Category[]
    tags: Tag[]
    settings: {
      theme: any
      searchEngines: any[]
      knowledgeSettings: any
    }
    // 新增AI相关数据
    aiConfigs?: EncryptedAiConfig[]
    aiSessions?: AiChatSession[]
  }
}

// 加密的AI配置接口
export interface EncryptedAiConfig {
  id: string
  name: string
  provider: string
  baseUrl: string
  modelName: string
  temperature: number
  maxTokens: number
  timeout: number
  enabled: boolean
  isDefault: boolean
  createdAt: string
  updatedAt: string
  // 加密的敏感数据
  encryptedApiKey: string
  encryptionSalt: string
}

export interface KnowledgeEntry {
  id: number
  title: string
  content: string
  category_id: number
  tags: number[]
  created_at: string
  updated_at: string
  url?: string
  description?: string
  view_count?: number
  cover_image?: string
}

export interface Category {
  id: number
  name: string
  parent_id: number
  description?: string
  created_at: string
  resource_count: number
  sort_order: number
}

export interface Tag {
  id: number
  name: string
  color?: string
  created_at: string
  resource_count: number
}

export interface ImportOptions {
  mode: 'overwrite' | 'merge'
  importKnowledge: boolean
  importCategories: boolean
  importTags: boolean
  importSettings: boolean
  importAiConfigs?: boolean
  importAiSessions?: boolean
}

export interface ImportResult {
  success: boolean
  message: string
  details: {
    knowledgeEntries: number
    categories: number
    tags: number
    settingsApplied: boolean
  }
  errors: string[]
}

class DataManagementService {
  private readonly STORAGE_KEYS = {
    KNOWLEDGE: 'knowledge-entries',
    CATEGORIES: 'knowledge-categories',
    TAGS: 'knowledge-tags',
    SEARCH_ENGINES: 'knowledge-search-engines',
    KNOWLEDGE_SETTINGS: 'knowledge-settings',
  }

  // 导出所有数据
  async exportAllData(): Promise<ExportData> {
    try {
      const exportData: ExportData = {
        version: '2.0.0', // 升级版本号支持AI功能
        exportTime: new Date().toISOString(),
        data: {
          knowledgeEntries: await this.getKnowledgeEntries(),
          categories: await this.getCategories(),
          tags: await this.getTags(),
          settings: {
            theme: themeSettingsService.getSettings(),
            searchEngines: this.getSearchEngines(),
            knowledgeSettings: this.getKnowledgeSettings(),
          },
          // 新增AI数据
          aiConfigs: await this.getEncryptedAiConfigs(),
          aiSessions: await this.getAiSessions(),
        },
      }

      return exportData
    } catch (error) {
      console.error('导出数据失败:', error)
      throw new Error('导出数据失败: ' + (error as Error).message)
    }
  }

  // 获取加密的AI配置
  private async getEncryptedAiConfigs(): Promise<EncryptedAiConfig[]> {
    try {
      const configs = await aiConfigService.getAllConfigs()
      const deviceFingerprint = generateDeviceFingerprint()
      const encryptedConfigs: EncryptedAiConfig[] = []

      for (const config of configs) {
        const salt = generateSalt()
        const encryptedApiKey = await encryptData(config.apiKey, deviceFingerprint, salt)

        encryptedConfigs.push({
          id: config.id,
          name: config.name,
          provider: config.provider,
          baseUrl: config.baseUrl,
          modelName: config.modelName || '',
          temperature: config.temperature || 0.7,
          maxTokens: config.maxTokens || 2000,
          timeout: config.timeout || 30000,
          enabled: config.enabled,
          isDefault: config.isDefault || false,
          createdAt: config.createdAt.toISOString(),
          updatedAt: config.updatedAt.toISOString(),
          encryptedApiKey,
          encryptionSalt: salt,
        })
      }

      return encryptedConfigs
    } catch (error) {
      console.error('获取加密AI配置失败:', error)
      return []
    }
  }

  // 获取AI会话记录（排除临时对话）
  private async getAiSessions(): Promise<AiChatSession[]> {
    try {
      const allSessions = await aiHistoryService.getAllSessions()
      // 过滤掉临时对话，只导出永久保存的对话
      const permanentSessions = allSessions.filter(
        (session) => !session.isTemporary && !session.isTemporaryChat,
      )
      console.log(
        `获取AI会话记录: 总计 ${allSessions.length} 个，永久保存 ${permanentSessions.length} 个`,
      )
      return permanentSessions
    } catch (error) {
      console.error('获取AI会话记录失败:', error)
      return []
    }
  }

  // 下载导出文件（支持压缩）
  downloadExportFile(data: ExportData, compressed: boolean = true): void {
    try {
      let content: string
      let filename: string
      let mimeType: string

      const timestamp = new Date().toISOString().split('T')[0]

      if (compressed) {
        // 压缩数据
        content = compressJson(data)
        filename = `knowledge-backup-${timestamp}.kbz` // 自定义扩展名
        mimeType = 'application/octet-stream'
      } else {
        // 未压缩的JSON
        content = JSON.stringify(data, null, 2)
        filename = `knowledge-backup-${timestamp}.json`
        mimeType = 'application/json'
      }

      const blob = new Blob([content], { type: mimeType })
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('下载文件失败:', error)
      throw new Error('下载文件失败: ' + (error as Error).message)
    }
  }

  // 验证导入数据
  validateImportData(data: any): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    // 检查基本结构
    if (!data || typeof data !== 'object') {
      errors.push('无效的JSON格式')
      return { valid: false, errors }
    }

    if (!data.version) {
      errors.push('缺少版本信息')
    }

    if (!data.data || typeof data.data !== 'object') {
      errors.push('缺少数据部分')
      return { valid: false, errors }
    }

    // 检查数据结构
    const { data: importData } = data

    if (importData.knowledgeEntries && !Array.isArray(importData.knowledgeEntries)) {
      errors.push('知识库条目数据格式错误')
    }

    if (importData.categories && !Array.isArray(importData.categories)) {
      errors.push('分类数据格式错误')
    }

    if (importData.tags && !Array.isArray(importData.tags)) {
      errors.push('标签数据格式错误')
    }

    if (importData.settings && typeof importData.settings !== 'object') {
      errors.push('设置数据格式错误')
    }

    return { valid: errors.length === 0, errors }
  }

  // 导入数据
  async importData(data: ExportData, options: ImportOptions): Promise<ImportResult> {
    try {
      const result: ImportResult = {
        success: false,
        message: '',
        details: {
          knowledgeEntries: 0,
          categories: 0,
          tags: 0,
          settingsApplied: false,
        },
        errors: [],
      }

      // 验证数据
      const validation = this.validateImportData(data)
      if (!validation.valid) {
        result.errors = validation.errors
        result.message = '数据验证失败'
        return result
      }

      const { data: importData } = data

      // 创建ID映射表
      let categoryIdMapping = new Map<number, number>()
      let tagIdMapping = new Map<number, number>()

      // 导入分类（需要先导入，因为知识库条目依赖分类）
      if (options.importCategories && importData.categories) {
        const categoryResult = await this.importCategories(importData.categories, options.mode)
        result.details.categories = categoryResult.count
        categoryIdMapping = categoryResult.idMapping
      }

      // 导入标签
      if (options.importTags && importData.tags) {
        const tagResult = await this.importTags(importData.tags, options.mode)
        result.details.tags = tagResult.count
        tagIdMapping = tagResult.idMapping
      }

      // 导入知识库条目（使用ID映射）
      if (options.importKnowledge && importData.knowledgeEntries) {
        const knowledgeCount = await this.importKnowledgeEntries(
          importData.knowledgeEntries,
          options.mode,
          categoryIdMapping,
          tagIdMapping,
        )
        result.details.knowledgeEntries = knowledgeCount
      }

      // 导入设置
      if (options.importSettings && importData.settings) {
        await this.importSettings(importData.settings)
        result.details.settingsApplied = true
      }

      // 导入AI配置（如果存在且用户选择导入）
      if (options.importAiConfigs && importData.aiConfigs && importData.aiConfigs.length > 0) {
        try {
          console.log(`开始导入 ${importData.aiConfigs.length} 个AI配置...`)
          await this.importAiConfigs(importData.aiConfigs)
          console.log('AI配置导入完成，通知AI Store重新加载...')

          // 通知AI Store重新加载配置
          if (typeof window !== 'undefined') {
            window.dispatchEvent(
              new CustomEvent('ai-configs-imported', {
                detail: { count: importData.aiConfigs.length },
              }),
            )

            // 额外的直接调用作为备用方案
            setTimeout(() => {
              console.log('执行备用的AI Store重新加载...')
              if ((window as any).forceReloadAiConfigs) {
                ;(window as any).forceReloadAiConfigs()
              } else {
                window.dispatchEvent(new CustomEvent('force-reload-ai-configs'))
              }
            }, 200)
          }
        } catch (error) {
          console.error('导入AI配置失败:', error)
          result.errors.push('AI配置导入失败: ' + (error as Error).message)
        }
      }

      // 导入AI会话记录（如果存在且用户选择导入）
      if (options.importAiSessions && importData.aiSessions && importData.aiSessions.length > 0) {
        try {
          await this.importAiSessions(importData.aiSessions)
        } catch (error) {
          console.error('导入AI会话记录失败:', error)
          result.errors.push('AI会话记录导入失败: ' + (error as Error).message)
        }
      }

      result.success = true
      result.message = '数据导入成功'

      return result
    } catch (error) {
      console.error('导入数据失败:', error)
      return {
        success: false,
        message: '导入数据失败: ' + (error as Error).message,
        details: {
          knowledgeEntries: 0,
          categories: 0,
          tags: 0,
          settingsApplied: false,
        },
        errors: [(error as Error).message],
      }
    }
  }

  // 获取知识库条目
  private async getKnowledgeEntries(): Promise<KnowledgeEntry[]> {
    try {
      const resources = await db.resources.toArray()
      const knowledgeEntries: KnowledgeEntry[] = []

      for (const resource of resources) {
        // 获取资源的标签
        const resourceTags = await db.resource_tags
          .where('resource_id')
          .equals(resource.id!)
          .toArray()
        const tagIds = resourceTags.map((rt) => rt.tag_id)

        knowledgeEntries.push({
          id: resource.id!,
          title: resource.title,
          content: resource.description || '',
          category_id: resource.category_id,
          tags: tagIds,
          created_at: resource.created_at.toISOString(),
          updated_at: resource.updated_at.toISOString(),
          url: resource.url,
          description: resource.description,
          view_count: resource.view_count,
          cover_image: resource.cover_image_url || '',
        })
      }

      return knowledgeEntries
    } catch (error) {
      console.error('获取知识库条目失败:', error)
      return []
    }
  }

  // 获取分类
  private async getCategories(): Promise<Category[]> {
    try {
      const categories = await db.categories.toArray()
      return categories.map((cat) => ({
        id: cat.id!,
        name: cat.name,
        parent_id: cat.parent_id,
        description: '',
        created_at: cat.created_at.toISOString(),
        resource_count: cat.resource_count,
        sort_order: cat.sort_order,
      }))
    } catch (error) {
      console.error('获取分类失败:', error)
      return []
    }
  }

  // 获取标签
  private async getTags(): Promise<Tag[]> {
    try {
      const tags = await db.tags.toArray()
      return tags.map((tag) => ({
        id: tag.id!,
        name: tag.name,
        color: tag.color,
        created_at: tag.created_at.toISOString(),
        resource_count: tag.resource_count,
      }))
    } catch (error) {
      console.error('获取标签失败:', error)
      return []
    }
  }

  // 获取搜索引擎配置
  private getSearchEngines(): any[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEYS.SEARCH_ENGINES)
      return data ? JSON.parse(data) : []
    } catch {
      return []
    }
  }

  // 获取知识库设置
  private getKnowledgeSettings(): any {
    try {
      const data = localStorage.getItem(this.STORAGE_KEYS.KNOWLEDGE_SETTINGS)
      return data ? JSON.parse(data) : {}
    } catch {
      return {}
    }
  }

  // 导入分类
  private async importCategories(
    categories: Category[],
    mode: 'overwrite' | 'merge',
  ): Promise<{ count: number; idMapping: Map<number, number> }> {
    try {
      const idMapping = new Map<number, number>() // 旧ID -> 新ID的映射

      if (mode === 'overwrite') {
        // 清空现有分类
        await db.categories.clear()
      }

      // 按层级排序，确保父分类先导入
      const sortedCategories = [...categories].sort((a, b) => {
        // 根分类（parent_id为0）优先
        if (a.parent_id === 0 && b.parent_id !== 0) return -1
        if (a.parent_id !== 0 && b.parent_id === 0) return 1
        return 0
      })

      let importedCount = 0

      for (const cat of sortedCategories) {
        try {
          // 检查是否已存在（合并模式下）
          if (mode === 'merge') {
            const existing = await db.categories.where('name').equals(cat.name).first()
            if (existing) {
              // 记录ID映射
              idMapping.set(cat.id, existing.id!)
              continue
            }
          }

          // 处理父分类ID映射
          let newParentId = cat.parent_id
          if (cat.parent_id !== 0 && idMapping.has(cat.parent_id)) {
            newParentId = idMapping.get(cat.parent_id)!
          }

          // 导入分类
          const categoryData = {
            name: cat.name,
            parent_id: newParentId,
            resource_count: cat.resource_count || 0,
            sort_order: cat.sort_order || 0,
            created_at: new Date(cat.created_at),
          }

          const newId = await db.categories.add(categoryData)

          // 记录ID映射
          idMapping.set(cat.id, newId as number)
          importedCount++
        } catch (error) {
          console.error('导入单个分类失败:', error)
          // 继续导入其他分类
        }
      }

      return { count: importedCount, idMapping }
    } catch (error) {
      console.error('导入分类失败:', error)
      throw error
    }
  }

  // 导入标签
  private async importTags(
    tags: Tag[],
    mode: 'overwrite' | 'merge',
  ): Promise<{ count: number; idMapping: Map<number, number> }> {
    try {
      const idMapping = new Map<number, number>() // 旧ID -> 新ID的映射

      if (mode === 'overwrite') {
        // 清空现有标签
        await db.tags.clear()
      }

      let importedCount = 0

      for (const tag of tags) {
        try {
          // 检查是否已存在（合并模式下）
          if (mode === 'merge') {
            const existing = await db.tags.where('name').equals(tag.name).first()
            if (existing) {
              // 记录ID映射
              idMapping.set(tag.id, existing.id!)
              continue
            }
          }

          // 导入标签
          const tagData = {
            name: tag.name,
            color: tag.color || '#6B7280',
            resource_count: tag.resource_count || 0,
            created_at: new Date(tag.created_at),
          }

          const newId = await db.tags.add(tagData)

          // 记录ID映射
          idMapping.set(tag.id, newId as number)
          importedCount++
        } catch (error) {
          console.error('导入单个标签失败:', error)
          // 继续导入其他标签
        }
      }

      return { count: importedCount, idMapping }
    } catch (error) {
      console.error('导入标签失败:', error)
      throw error
    }
  }

  // 导入知识库条目
  private async importKnowledgeEntries(
    entries: KnowledgeEntry[],
    mode: 'overwrite' | 'merge',
    categoryIdMapping: Map<number, number>,
    tagIdMapping: Map<number, number>,
  ): Promise<number> {
    try {
      if (mode === 'overwrite') {
        // 清空现有数据
        await db.resources.clear()
        await db.resource_tags.clear()
      }

      let importedCount = 0

      for (const entry of entries) {
        try {
          // 检查是否已存在（合并模式下）
          if (mode === 'merge') {
            const existing = await db.resources
              .where('url')
              .equals(entry.url || '')
              .first()
            if (existing) continue
          }

          // 处理分类ID映射
          let newCategoryId = entry.category_id
          if (categoryIdMapping.has(entry.category_id)) {
            newCategoryId = categoryIdMapping.get(entry.category_id)!
          }

          // 导入资源
          const resourceData = {
            title: entry.title,
            url: entry.url || '',
            description: entry.description || entry.content,
            cover_image_url: entry.cover_image || '',
            category_id: newCategoryId,
            view_count: entry.view_count || 0,
            created_at: new Date(entry.created_at),
            updated_at: new Date(entry.updated_at),
          }

          const resourceId = await db.resources.add(resourceData)

          // 导入标签关联（使用ID映射）
          if (entry.tags && entry.tags.length > 0) {
            const resourceTags = entry.tags
              .map((oldTagId) => {
                const newTagId = tagIdMapping.get(oldTagId)
                if (newTagId) {
                  return {
                    resource_id: resourceId as number,
                    tag_id: newTagId,
                  }
                }
                return null
              })
              .filter((tag) => tag !== null) as { resource_id: number; tag_id: number }[]

            if (resourceTags.length > 0) {
              await db.resource_tags.bulkAdd(resourceTags)
            }
          }

          importedCount++
        } catch (error) {
          console.error('导入单个条目失败:', error)
          // 继续导入其他条目
        }
      }

      return importedCount
    } catch (error) {
      console.error('导入知识库条目失败:', error)
      throw error
    }
  }

  // 导入设置
  private async importSettings(settings: any): Promise<void> {
    try {
      // 导入主题设置
      if (settings.theme) {
        themeSettingsService.saveSettings(settings.theme)
      }

      // 导入搜索引擎配置
      if (settings.searchEngines) {
        localStorage.setItem(
          this.STORAGE_KEYS.SEARCH_ENGINES,
          JSON.stringify(settings.searchEngines),
        )
      }

      // 导入知识库设置
      if (settings.knowledgeSettings) {
        localStorage.setItem(
          this.STORAGE_KEYS.KNOWLEDGE_SETTINGS,
          JSON.stringify(settings.knowledgeSettings),
        )
      }
    } catch (error) {
      console.error('导入设置失败:', error)
      throw error
    }
  }

  // 导入AI配置
  private async importAiConfigs(encryptedConfigs: EncryptedAiConfig[]): Promise<void> {
    try {
      const deviceFingerprint = generateDeviceFingerprint()
      console.log('设备指纹:', deviceFingerprint.substring(0, 8) + '...')

      for (const encryptedConfig of encryptedConfigs) {
        try {
          console.log(`正在导入配置: ${encryptedConfig.name}`)

          // 解密API密钥
          let apiKey: string
          try {
            apiKey = await decryptData(
              encryptedConfig.encryptedApiKey,
              deviceFingerprint,
              encryptedConfig.encryptionSalt,
            )
            console.log(`配置 ${encryptedConfig.name} 使用设备指纹解密成功`)
          } catch (error) {
            console.warn(`配置 ${encryptedConfig.name} 设备指纹解密失败，尝试通用密钥...`)
            try {
              // 尝试使用通用密钥解密（用于跨设备兼容）
              const fallbackKey = 'knowledge-base-fallback-key'
              apiKey = await decryptData(
                encryptedConfig.encryptedApiKey,
                fallbackKey,
                encryptedConfig.encryptionSalt,
              )
              console.log(`配置 ${encryptedConfig.name} 使用通用密钥解密成功`)
            } catch (fallbackError) {
              console.error(`配置 ${encryptedConfig.name} 所有解密方法都失败`)
              throw new Error(`无法解密配置 ${encryptedConfig.name}`)
            }
          }

          // 重构为AiConfigForm格式
          const configForm = {
            name: encryptedConfig.name,
            provider: encryptedConfig.provider as any,
            apiKey,
            baseUrl: encryptedConfig.baseUrl,
            modelName: encryptedConfig.modelName,
            temperature: encryptedConfig.temperature,
            maxTokens: encryptedConfig.maxTokens,
            timeout: encryptedConfig.timeout,
            enabled: encryptedConfig.enabled,
            isDefault: encryptedConfig.isDefault,
          }

          console.log(`配置 ${encryptedConfig.name} 表单数据:`, {
            name: configForm.name,
            provider: configForm.provider,
            enabled: configForm.enabled,
            isDefault: configForm.isDefault,
            hasApiKey: !!configForm.apiKey,
          })

          // 检查是否存在同名配置
          const existingConfigs = await aiConfigService.getAllConfigs()
          const existingConfig = existingConfigs.find((c) => c.name === configForm.name)

          let savedConfig
          if (existingConfig) {
            // 更新现有配置
            console.log(`配置 ${encryptedConfig.name} 已存在，更新现有配置`)
            savedConfig = await aiConfigService.saveConfig(configForm, existingConfig.id)
          } else {
            // 创建新配置
            console.log(`配置 ${encryptedConfig.name} 不存在，创建新配置`)
            savedConfig = await aiConfigService.saveConfig(configForm)
          }
          console.log(`配置 ${encryptedConfig.name} 保存成功:`, savedConfig?.id)
        } catch (error) {
          console.error(`导入AI配置 ${encryptedConfig.name} 失败:`, error)
          // 继续处理其他配置
        }
      }

      console.log('所有AI配置导入处理完成')
    } catch (error) {
      console.error('导入AI配置失败:', error)
      throw error
    }
  }

  // 导入AI会话记录
  private async importAiSessions(sessions: AiChatSession[]): Promise<void> {
    try {
      // 将会话数组转换为JSON字符串，然后导入
      const sessionsJson = JSON.stringify(sessions)
      await aiHistoryService.importHistory(sessionsJson)
    } catch (error) {
      console.error('导入AI会话记录失败:', error)
      throw error
    }
  }
}

// 创建单例实例
export const dataManagementService = new DataManagementService()
