<template>
  <a-space direction="vertical" size="middle" style="width: 100%">
    <!-- AI配置管理 -->
    <a-card size="small" class="ai-config-management-card">
      <template #title>
        <div class="card-title-wrapper">
          <span class="card-title">AI配置管理</span>
          <a-tooltip title="查看操作说明" placement="bottom">
            <a-button type="text" size="small" class="help-icon-btn" @click="showHelpModal = true">
              <QuestionCircleOutlined />
            </a-button>
          </a-tooltip>
        </div>
      </template>

      <!-- AI配置统计 -->
      <a-row :gutter="12" class="mb-3">
        <a-col :span="6">
          <div class="stat-card">
            <div class="stat-icon stat-icon-primary">
              <ApiOutlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ configStats.total }}</div>
              <div class="stat-label">总配置数</div>
            </div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-card">
            <div class="stat-icon stat-icon-success">
              <CheckCircleOutlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ configStats.enabled }}</div>
              <div class="stat-label">已启用</div>
            </div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-card">
            <div class="stat-icon stat-icon-warning">
              <StarFilled />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ configStats.default ? 1 : 0 }}</div>
              <div class="stat-label">默认配置</div>
            </div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-card">
            <div class="stat-icon stat-icon-info">
              <CloudOutlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ configStats.providers }}</div>
              <div class="stat-label">服务商数</div>
            </div>
          </div>
        </a-col>
      </a-row>

      <!-- 操作工具栏 -->
      <div class="toolbar-section">
        <div class="toolbar-left">
          <a-button type="primary" @click="showAddForm = !showAddForm">
            <PlusOutlined />
            {{ showAddForm ? '收起表单' : '添加新配置' }}
          </a-button>
        </div>
        <div class="toolbar-right">
          <a-space>
            <a-input-search v-model:value="searchText" placeholder="搜索配置" @search="handleSearch" />
            <a-select v-model:value="filterProvider" placeholder="筛选服务商" allowClear>
              <a-select-option v-for="provider in availableProviders.filter(p => p.value !== 'custom')"
                :key="provider.value" :value="provider.value">
                {{ provider.label }}
              </a-select-option>
            </a-select>
            <a-button @click="handleRefresh" :loading="loading">
              <ReloadOutlined v-if="!loading" />
              刷新
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 添加配置表单 -->
      <div class="add-form-container">
        <a-collapse-transition>
          <div v-show="showAddForm" class="add-config-form">
            <a-form :model="newConfig" :rules="configRules" layout="vertical" @finish="handleAddConfig"
              class="form-content">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="配置名称" name="name">
                    <a-input v-model:value="newConfig.name" placeholder="请输入配置名称" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="服务提供商" name="provider">
                    <a-select v-model:value="newConfig.provider" placeholder="选择服务提供商" @change="handleProviderChange">
                      <a-select-option v-for="provider in availableProviders" :key="provider.value"
                        :value="provider.value">
                        {{ provider.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="API Key" name="apiKey">
                    <a-input-password v-model:value="newConfig.apiKey" placeholder="请输入API Key" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="API端点" name="baseUrl">
                    <a-input v-model:value="newConfig.baseUrl" placeholder="请输入API端点" />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item label="模型名称" name="modelName">
                    <a-select v-model:value="newConfig.modelName" placeholder="选择模型" @change="handleModelChange"
                      :key="`models-${newConfig.provider}-${systemModels.length}`" style="width: 100%;">
                      <!-- 系统预设模型 -->
                      <a-select-option v-for="(model, index) in systemModels" :key="`system-${index}`"
                        :value="model.value">
                        {{ model.label }}
                      </a-select-option>
                      <!-- 自定义模型 -->
                      <a-select-option v-for="(model, index) in customModels" :key="`custom-${index}`"
                        :value="model.value">
                        {{ model.label }} (自定义)
                      </a-select-option>
                      <!-- 添加自定义模型选项 -->
                      <a-select-option value="__ADD_CUSTOM__" style="border-top: 1px solid #f0f0f0;">
                        <PlusOutlined /> 添加自定义模型
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="温度" name="temperature">
                    <a-slider v-model:value="newConfig.temperature" :min="0" :max="2" :step="0.1"
                      :tooltip="{ formatter: (value) => `${value}` }" />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="最大令牌数" name="maxTokens">
                    <a-input-number v-model:value="newConfig.maxTokens" :min="1" :max="32000" placeholder="2000"
                      style="width: 100%" />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="超时时间(秒)" name="timeout">
                    <a-input-number v-model:value="newConfig.timeout" :min="5" :max="300" placeholder="30"
                      style="width: 100%" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item>
                    <div class="form-switches">
                      <a-space>
                        <a-checkbox v-model:checked="newConfig.enabled">启用配置</a-checkbox>
                        <a-checkbox v-model:checked="newConfig.isDefault">设为默认</a-checkbox>
                      </a-space>
                    </div>
                  </a-form-item>
                </a-col>
              </a-row>

              <!-- 系统提示词 -->
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="系统提示词" name="systemPrompt">
                    <a-textarea v-model:value="newConfig.systemPrompt" placeholder="可选：设置系统提示词，用于指导AI的行为和回复风格"
                      :rows="3" />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-form-item>
                <a-space>
                  <a-button type="primary" html-type="submit" :loading="adding">
                    {{ adding ? '添加中...' : '添加配置' }}
                  </a-button>
                  <a-button @click="resetForm">
                    重置
                  </a-button>
                  <a-button @click="handleTestNewConfig" :loading="testing">
                    {{ testing ? '测试中...' : '测试连接' }}
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </div>
        </a-collapse-transition>
      </div>

      <!-- 配置列表 -->
      <div class="config-list-section">
        <div v-if="loading" class="loading-container">
          <a-spin size="large" />
        </div>

        <div v-else-if="filteredConfigs.length === 0" class="empty-container">
          <a-empty description="暂无AI配置">
            <a-button type="primary" @click="showAddForm = true">
              <PlusOutlined />
              添加配置
            </a-button>
          </a-empty>
        </div>

        <div v-else class="config-grid">
          <div v-for="config in filteredConfigs" :key="config.id" class="config-card-compact">
            <!-- 紧凑式头部：标题、状态、操作在一行 -->
            <div class="config-header-compact">
              <div class="config-title-compact">
                <span class="config-name-compact">{{ config.name }}</span>
                <a-tag :color="getProviderColor(config.provider)" size="small">
                  {{ getProviderLabel(config.provider) }}
                </a-tag>
                <a-badge v-if="config.isDefault" status="warning" text="默认" />
              </div>
              <div class="config-actions-compact">
                <a-switch :checked="config.enabled" @change="(checked) => handleToggleEnabled(config, checked)"
                  size="small" :loading="toggleLoadingId === config.id" />
                <a-dropdown>
                  <a-button type="text" size="small" class="action-btn">
                    <MoreOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="handleEditConfig(config)">
                        <EditOutlined />
                        编辑
                      </a-menu-item>
                      <a-menu-item @click="handleTestConfig(config)" :disabled="testingConfigId === config.id">
                        <ApiOutlined />
                        {{ testingConfigId === config.id ? '测试中...' : '测试连接' }}
                      </a-menu-item>
                      <a-menu-item @click="handleSetDefault(config)" :disabled="config.isDefault">
                        <StarOutlined />
                        设为默认
                      </a-menu-item>
                      <a-menu-item @click="handleDuplicateConfig(config)">
                        <CopyOutlined />
                        复制配置
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item danger @click="handleDeleteConfig(config)">
                        <DeleteOutlined />
                        删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </div>

            <!-- 紧凑式内容：两列布局 -->
            <div class="config-content-compact">
              <div class="config-info-left">
                <div class="info-item-compact">
                  <ApiOutlined class="info-icon" />
                  <span class="info-text">{{ config.modelName || '未设置' }}</span>
                </div>
                <div class="info-item-compact">
                  <LinkOutlined class="info-icon" />
                  <span class="info-text config-url" :title="config.baseUrl">{{ formatUrl(config.baseUrl) }}</span>
                </div>
                <div class="info-item-compact" v-if="config.systemPrompt">
                  <MessageOutlined class="info-icon" />
                  <span class="info-text system-prompt" :title="config.systemPrompt">{{
                    formatSystemPrompt(config.systemPrompt) }}</span>
                </div>
              </div>

              <div class="config-info-right">
                <div class="info-item-compact">
                  <FireOutlined class="info-icon" />
                  <span class="info-text">{{ config.temperature || 0.7 }}</span>
                </div>
                <div class="info-item-compact">
                  <ThunderboltOutlined class="info-icon" />
                  <span class="info-text">{{ formatTokens(config.maxTokens || 2000) }}</span>
                </div>
                <div class="info-item-compact">
                  <ClockCircleOutlined class="info-icon" />
                  <span class="info-text">{{ formatTimeout(config.timeout || 30000) }}</span>
                </div>
              </div>
            </div>

            <!-- 紧凑式底部：API Key和状态 -->
            <div class="config-footer-compact">
              <div class="api-key-compact">
                <KeyOutlined class="info-icon" />
                <span class="api-key-masked-compact">{{ formatApiKey(config.apiKey) }}</span>
              </div>
              <div class="config-status-compact">
                <a-badge :status="config.enabled ? 'success' : 'default'" :text="config.enabled ? '启用' : '禁用'" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 编辑配置模态框 -->
    <a-modal v-model:open="showEditModal" title="编辑AI配置" @cancel="resetEditForm" :footer="null">
      <a-form :model="editingConfig" :rules="configRules" layout="vertical" @finish="handleUpdateConfig">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="配置名称" name="name">
              <a-input v-model:value="editingConfig.name" placeholder="请输入配置名称" class="form-input" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="服务提供商" name="provider">
              <a-select v-model:value="editingConfig.provider" placeholder="选择服务提供商" class="form-input"
                @change="handleEditProviderChange">
                <a-select-option v-for="provider in availableProviders" :key="provider.value" :value="provider.value">
                  {{ provider.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="API Key" name="apiKey">
              <a-input-password v-model:value="editingConfig.apiKey" placeholder="请输入API Key" class="form-input" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="API端点" name="baseUrl">
              <a-input v-model:value="editingConfig.baseUrl" placeholder="请输入API端点" class="form-input" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="模型名称" name="modelName">
              <a-select v-model:value="editingConfig.modelName" placeholder="选择模型" class="form-input"
                @change="handleEditModelChange"
                :key="`edit-models-${editingConfig.provider}-${editSystemModels.length}`" style="width: 100%;">
                <!-- 系统预设模型 -->
                <a-select-option v-for="(model, index) in editSystemModels" :key="`edit-system-${index}`"
                  :value="model.value">
                  {{ model.label }}
                </a-select-option>
                <!-- 自定义模型 -->
                <a-select-option v-for="(model, index) in editCustomModels" :key="`edit-custom-${index}`"
                  :value="model.value">
                  {{ model.label }} (自定义)
                </a-select-option>
                <!-- 添加自定义模型选项 -->
                <a-select-option value="__ADD_CUSTOM__" style="border-top: 1px solid #f0f0f0;">
                  <PlusOutlined /> 添加自定义模型
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="温度" name="temperature">
              <a-slider v-model:value="editingConfig.temperature" :min="0" :max="2" :step="0.1"
                :tooltip="{ formatter: (value) => `${value}` }" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="最大令牌数" name="maxTokens">
              <a-input-number v-model:value="editingConfig.maxTokens" :min="1" :max="32000" placeholder="2000"
                class="form-input" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="超时时间(秒)" name="timeout">
              <a-input-number v-model:value="editingConfig.timeout" :min="5" :max="300" placeholder="30"
                class="form-input" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item>
              <div class="form-switches">
                <a-space>
                  <a-checkbox v-model:checked="editingConfig.enabled">启用配置</a-checkbox>
                  <a-checkbox v-model:checked="editingConfig.isDefault">设为默认</a-checkbox>
                </a-space>
              </div>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 系统提示词 -->
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="系统提示词" name="systemPrompt">
              <a-textarea v-model:value="editingConfig.systemPrompt" placeholder="可选：设置系统提示词，用于指导AI的行为和回复风格"
                :rows="3" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item>
          <a-space>
            <a-button @click="resetEditForm" class="form-button form-button-default">
              取消
            </a-button>
            <a-button type="primary" html-type="submit" :loading="updating" class="form-button form-button-primary">
              {{ updating ? '更新中...' : '更新配置' }}
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 帮助模态框 -->
    <a-modal v-model:open="showHelpModal" title="AI配置管理操作说明" :footer="null" width="700px" class="help-modal">
      <div class="help-content">
        <!-- 功能概览 -->
        <div class="help-section help-section-overview">
          <div class="help-section-header">
            <div class="help-icon-wrapper overview">
              <ApiOutlined />
            </div>
            <h3 class="help-section-title">功能概览</h3>
          </div>
          <div class="help-section-content">
            <p class="help-description">
              AI配置管理帮助您管理多个AI服务提供商的配置，支持OpenAI、Claude、自定义服务等，提供完整的配置生命周期管理。
            </p>
          </div>
        </div>

        <!-- 添加配置 -->
        <div class="help-section help-section-add">
          <div class="help-section-header">
            <div class="help-icon-wrapper add">
              <PlusOutlined />
            </div>
            <h3 class="help-section-title">添加配置</h3>
          </div>
          <div class="help-section-content">
            <div class="help-steps">
              <div class="help-step">
                <span class="step-number">1</span>
                <span class="step-text">点击"添加新配置"按钮展开配置表单</span>
              </div>
              <div class="help-step">
                <span class="step-number">2</span>
                <span class="step-text">输入配置名称（必填，2-50个字符）</span>
              </div>
              <div class="help-step">
                <span class="step-number">3</span>
                <span class="step-text">选择服务提供商（OpenAI、Claude或自定义）</span>
              </div>
              <div class="help-step">
                <span class="step-number">4</span>
                <span class="step-text">填写API Key和API端点地址</span>
              </div>
              <div class="help-step">
                <span class="step-number">5</span>
                <span class="step-text">选择模型并调整参数（温度、令牌数、超时时间）</span>
              </div>
              <div class="help-step">
                <span class="step-number">6</span>
                <span class="step-text">可选择启用配置或设为默认配置</span>
              </div>
              <div class="help-step">
                <span class="step-number">7</span>
                <span class="step-text">点击"测试连接"验证配置正确性</span>
              </div>
              <div class="help-step">
                <span class="step-number">8</span>
                <span class="step-text">点击"添加配置"完成创建</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 编辑配置 -->
        <div class="help-section help-section-edit">
          <div class="help-section-header">
            <div class="help-icon-wrapper edit">
              <EditOutlined />
            </div>
            <h3 class="help-section-title">编辑配置</h3>
          </div>
          <div class="help-section-content">
            <div class="help-steps">
              <div class="help-step">
                <span class="step-number">1</span>
                <span class="step-text">点击配置卡片右上角的更多按钮（⋯）</span>
              </div>
              <div class="help-step">
                <span class="step-number">2</span>
                <span class="step-text">选择"编辑"选项打开编辑模态框</span>
              </div>
              <div class="help-step">
                <span class="step-number">3</span>
                <span class="step-text">修改需要更改的配置项</span>
              </div>
              <div class="help-step">
                <span class="step-number">4</span>
                <span class="step-text">点击"更新配置"保存修改</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 配置管理 -->
        <div class="help-section help-section-manage">
          <div class="help-section-header">
            <div class="help-icon-wrapper manage">
              <StarOutlined />
            </div>
            <h3 class="help-section-title">配置管理</h3>
          </div>
          <div class="help-section-content">
            <div class="help-steps">
              <div class="help-step">
                <span class="step-number">1</span>
                <span class="step-text">使用配置卡片上的开关快速启用/禁用配置</span>
              </div>
              <div class="help-step">
                <span class="step-number">2</span>
                <span class="step-text">点击"设为默认"将配置设为系统默认使用</span>
              </div>
              <div class="help-step">
                <span class="step-number">3</span>
                <span class="step-text">使用"复制配置"快速创建相似配置</span>
              </div>
              <div class="help-step">
                <span class="step-number">4</span>
                <span class="step-text">点击"测试连接"验证配置可用性</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="help-section help-section-search">
          <div class="help-section-header">
            <div class="help-icon-wrapper search">
              <ReloadOutlined />
            </div>
            <h3 class="help-section-title">搜索和筛选</h3>
          </div>
          <div class="help-section-content">
            <div class="help-steps">
              <div class="help-step">
                <span class="step-number">1</span>
                <span class="step-text">在搜索框中输入关键词搜索配置</span>
              </div>
              <div class="help-step">
                <span class="step-number">2</span>
                <span class="step-text">支持按配置名称、服务商、端点、模型搜索</span>
              </div>
              <div class="help-step">
                <span class="step-number">3</span>
                <span class="step-text">使用服务商筛选器快速筛选特定类型配置</span>
              </div>
              <div class="help-step">
                <span class="step-number">4</span>
                <span class="step-text">点击刷新按钮重新加载最新配置数据</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 参数说明 -->
        <div class="help-section help-section-params">
          <div class="help-section-header">
            <div class="help-icon-wrapper params">
              <CloudOutlined />
            </div>
            <h3 class="help-section-title">参数说明</h3>
          </div>
          <div class="help-section-content">
            <div class="help-steps">
              <div class="help-step">
                <span class="step-number">📝</span>
                <span class="step-text"><strong>温度(Temperature)</strong>：控制AI回复的随机性，0-2之间，建议0.7</span>
              </div>
              <div class="help-step">
                <span class="step-number">🔢</span>
                <span class="step-text"><strong>最大令牌数</strong>：限制AI回复的最大长度，建议2000-4000</span>
              </div>
              <div class="help-step">
                <span class="step-number">⏱️</span>
                <span class="step-text"><strong>超时时间</strong>：API请求超时时间，建议30-60秒</span>
              </div>
              <div class="help-step">
                <span class="step-number">🔗</span>
                <span class="step-text"><strong>API端点</strong>：服务商的API接口地址，需要完整的URL</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 注意事项 -->
        <div class="help-section help-section-warning">
          <div class="help-section-header">
            <div class="help-icon-wrapper warning">
              <QuestionCircleOutlined />
            </div>
            <h3 class="help-section-title">注意事项</h3>
          </div>
          <div class="help-section-content">
            <div class="help-warnings">
              <div class="help-warning">
                <span class="warning-icon">🔐</span>
                <span class="warning-text">API Key是敏感信息，请妥善保管，不要泄露给他人</span>
              </div>
              <div class="help-warning">
                <span class="warning-icon">🌐</span>
                <span class="warning-text">不同服务商的API端点和模型名称可能不同，请参考官方文档</span>
              </div>
              <div class="help-warning">
                <span class="warning-icon">🎛️</span>
                <span class="warning-text">温度值过高会导致回复不稳定，过低会导致回复过于固定</span>
              </div>
              <div class="help-warning">
                <span class="warning-icon">⚠️</span>
                <span class="warning-text">删除配置前请确认不再需要，此操作不可撤销</span>
              </div>
              <div class="help-warning">
                <span class="warning-icon">💡</span>
                <span class="warning-text">建议为不同用途创建不同配置，如聊天、写作、代码等</span>
              </div>
              <div class="help-warning">
                <span class="warning-icon">🔄</span>
                <span class="warning-text">定期测试配置连接，确保服务可用性</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 添加自定义AI服务商模态框 -->
    <a-modal v-model:open="showAddProviderModal" title="添加自定义AI服务商" :footer="null" width="600px">
      <a-form layout="vertical" @submit.prevent="handleAddCustomProvider">
        <a-form-item label="AI服务商名称" required>
          <a-input v-model:value="customProviderForm.name" placeholder="请输入AI服务商名称（如：我的私有GPT）" />
          <div style="color: #8c8c8c; font-size: 12px; margin-top: 4px;">
            这是在界面中显示的AI服务商名称
          </div>
        </a-form-item>

        <a-form-item label="API端点" required>
          <a-input v-model:value="customProviderForm.baseUrl" placeholder="请输入API端点（如：https://api.example.com/v1）" />
          <div style="color: #8c8c8c; font-size: 12px; margin-top: 4px;">
            完整的API服务地址，包含协议和路径
          </div>
        </a-form-item>

        <a-form-item label="API Key" required>
          <a-input-password v-model:value="customProviderForm.apiKey" placeholder="请输入API Key" />
          <div style="color: #8c8c8c; font-size: 12px; margin-top: 4px;">
            用于身份验证的API密钥
          </div>
        </a-form-item>

        <a-form-item label="默认模型">
          <a-input v-model:value="customProviderForm.modelName" placeholder="请输入默认模型名称（可选）" />
          <div style="color: #8c8c8c; font-size: 12px; margin-top: 4px;">
            可以稍后在模型选择中添加更多模型
          </div>
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="false">
              添加AI服务商
            </a-button>
            <a-button @click="handleCancelAddProvider">
              取消
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 添加自定义模型模态框 -->
    <a-modal v-model:open="showAddModelModal" title="添加自定义模型" :footer="null" width="500px">
      <a-form layout="vertical" @submit.prevent="handleAddCustomModel">
        <a-form-item label="模型名称" required>
          <a-input v-model:value="customModelForm.name" placeholder="请输入模型名称（如：gpt-4-custom）" />
          <div style="color: #8c8c8c; font-size: 12px; margin-top: 4px;">
            这是调用API时使用的模型名称，请确保与服务商文档一致
          </div>
        </a-form-item>

        <a-form-item label="显示名称" required>
          <a-input v-model:value="customModelForm.label" placeholder="请输入显示名称（如：GPT-4 自定义版）" />
          <div style="color: #8c8c8c; font-size: 12px; margin-top: 4px;">
            这是在界面中显示的友好名称
          </div>
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="false">
              添加模型
            </a-button>
            <a-button @click="handleCancelAddModel">
              取消
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>


  </a-space>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, h } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  StarFilled,
  StarOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  ApiOutlined,
  CopyOutlined,
  CheckCircleOutlined,
  CloudOutlined,
  QuestionCircleOutlined,
  ExclamationCircleOutlined,
  LinkOutlined,
  FireOutlined,
  ThunderboltOutlined,
  ClockCircleOutlined,
  MessageOutlined,
  KeyOutlined
} from '@ant-design/icons-vue'
import { aiConfigDatabaseService } from '@/services/aiConfigDatabaseService'
import type { AiConfigForm, AiProvider } from '@/types'

// 状态
const loading = ref(false)
const adding = ref(false)
const updating = ref(false)
const testing = ref(false)
const testingConfigId = ref<string | undefined>('')
const toggleLoadingId = ref<string | undefined>('')
const searchText = ref('')
const filterProvider = ref<AiProvider | undefined>(undefined)
const showAddForm = ref(false)
const showEditModal = ref(false)
const showHelpModal = ref(false)

// 数据
const configs = ref<AiConfigForm[]>([])
const editingConfig = reactive<AiConfigForm & { id?: string }>({
  name: '',
  provider: 'openai',
  apiKey: '',
  baseUrl: '',
  modelName: '',
  temperature: 0.7,
  maxTokens: 2000,
  timeout: 30000,
  systemPrompt: '',
  customHeaders: {},
  customParams: {},
  enabled: true,
  isDefault: false
})

const newConfig = reactive<AiConfigForm>({
  name: '',
  provider: 'openai',
  apiKey: '',
  baseUrl: '',
  modelName: '',
  temperature: 0.7,
  maxTokens: 2000,
  timeout: 30000,
  systemPrompt: '',
  customHeaders: {},
  customParams: {},
  enabled: true,
  isDefault: false
})

// 计算属性
const configStats = computed(() => {
  const total = configs.value.length
  const enabled = configs.value.filter(config => config.enabled).length
  const defaultConfig = configs.value.find(config => config.isDefault)
  const providers = new Set(configs.value.map(config => config.provider)).size

  return {
    total,
    enabled,
    default: defaultConfig,
    providers
  }
})

const filteredConfigs = computed(() => {
  let filtered = configs.value

  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    filtered = filtered.filter(config =>
      config.name.toLowerCase().includes(search) ||
      config.provider.toLowerCase().includes(search) ||
      config.baseUrl.toLowerCase().includes(search) ||
      (config.modelName && config.modelName.toLowerCase().includes(search))
    )
  }

  if (filterProvider.value) {
    filtered = filtered.filter(config => config.provider === filterProvider.value)
  }

  return filtered
})

// 系统预设模型
const systemModels = ref<Array<{ label: string; value: string }>>([])

// 自定义模型
const customModels = ref<Array<{ label: string; value: string; isCustom?: boolean }>>([])

// 注意：直接使用 systemModels 和 customModels 响应式数据，不再需要计算属性

// ==================== 格式化工具方法 ====================

/**
 * 格式化URL显示
 */
const formatUrl = (url: string): string => {
  if (!url) return '未设置'

  try {
    const urlObj = new URL(url)
    const domain = urlObj.hostname

    // 如果域名太长，截取显示
    if (domain.length > 25) {
      return domain.substring(0, 22) + '...'
    }

    return domain
  } catch {
    // 如果不是有效URL，直接截取显示
    return url.length > 25 ? url.substring(0, 22) + '...' : url
  }
}

/**
 * 格式化令牌数量
 */
const formatTokens = (tokens: number): string => {
  if (tokens >= 1000000) {
    return `${(tokens / 1000000).toFixed(1)}M`
  } else if (tokens >= 1000) {
    return `${(tokens / 1000).toFixed(1)}K`
  }
  return tokens.toString()
}

/**
 * 格式化超时时间
 */
const formatTimeout = (timeout: number): string => {
  const seconds = timeout / 1000
  if (seconds >= 60) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分钟`
  }
  return `${seconds}秒`
}

/**
 * 格式化系统提示
 */
const formatSystemPrompt = (prompt: string): string => {
  if (!prompt) return ''
  return prompt.length > 50 ? prompt.substring(0, 47) + '...' : prompt
}

/**
 * 格式化API Key（脱敏显示）
 */
const formatApiKey = (apiKey: string): string => {
  if (!apiKey) return '未设置'

  if (apiKey.length <= 8) {
    return '*'.repeat(apiKey.length)
  }

  // 显示前4位和后4位，中间用*代替
  const start = apiKey.substring(0, 4)
  const end = apiKey.substring(apiKey.length - 4)
  const middle = '*'.repeat(Math.min(apiKey.length - 8, 12))

  return `${start}${middle}${end}`
}

// 可用的服务商列表
const availableProviders = ref<Array<{ label: string; value: string; color?: string }>>([])

// 服务商信息映射（用于快速查找标签和颜色）
const providerInfoMap = ref<Map<string, { label: string; color: string }>>(new Map())

// 加载服务商列表
const loadProviders = async () => {
  console.log('开始加载服务商列表')
  try {
    availableProviders.value = await aiConfigDatabaseService.getAvailableProviders()
    console.log('加载到的服务商列表:', availableProviders.value)

    // 构建服务商信息映射
    providerInfoMap.value.clear()
    for (const provider of availableProviders.value) {
      providerInfoMap.value.set(provider.value, {
        label: provider.label,
        color: provider.color || '#8c8c8c'
      })
    }

    // 添加"添加AI服务商"选项
    availableProviders.value.push({
      label: '添加AI服务商',
      value: 'custom',
      color: '#8c8c8c'
    })

    console.log('最终服务商列表:', availableProviders.value)
    console.log('服务商信息映射:', providerInfoMap.value)
  } catch (error) {
    console.error('加载服务商列表失败:', error)
    availableProviders.value = []
    providerInfoMap.value.clear()
  }
}

// 加载模型列表
const loadModels = async (provider: AiProvider) => {
  console.log('开始加载模型列表，服务商:', provider)
  try {
    const models = await aiConfigDatabaseService.getAvailableModels(provider)
    const customModelsList = await aiConfigDatabaseService.getAvailableModelsWithCustom(provider)

    console.log('从数据库获取的系统模型:', models)
    console.log('从数据库获取的自定义模型:', customModelsList)

    // 清空现有数据
    systemModels.value = []
    customModels.value = []

    // 等待下一个tick
    await nextTick()

    // 重新赋值
    systemModels.value = models
    customModels.value = customModelsList

    console.log('赋值后的系统模型:', systemModels.value)
    console.log('赋值后的自定义模型:', customModels.value)
    console.log('systemModels.value.length:', systemModels.value.length)
  } catch (error) {
    console.error('加载模型失败:', error)
    systemModels.value = []
    customModels.value = []
  }
}



// 编辑模态框的系统预设模型
const editSystemModels = ref<Array<{ label: string; value: string }>>([])

// 编辑模态框的自定义模型
const editCustomModels = ref<Array<{ label: string; value: string; isCustom?: boolean }>>([])

// 注意：直接使用 editSystemModels 和 editCustomModels 响应式数据，不再需要计算属性

// 加载编辑模态框的模型列表
const loadEditModels = async (provider: AiProvider) => {
  try {
    editSystemModels.value = await aiConfigDatabaseService.getAvailableModels(provider)
    editCustomModels.value = await aiConfigDatabaseService.getAvailableModelsWithCustom(provider)
  } catch (error) {
    console.error('加载编辑模型失败:', error)
    editSystemModels.value = []
    editCustomModels.value = []
  }
}



// 表单验证规则
const configRules = {
  name: [
    { required: true, message: '请输入配置名称', trigger: 'blur' },
    { min: 2, max: 50, message: '配置名称长度应为2-50个字符', trigger: 'blur' }
  ],
  provider: [
    { required: true, message: '请选择服务提供商', trigger: 'change' }
  ],
  apiKey: [
    { required: true, message: '请输入API Key', trigger: 'blur' }
  ],
  baseUrl: [
    { required: true, message: '请输入API端点', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ],
  modelName: [
    { required: true, message: '请选择模型', trigger: 'change' }
  ]
}

// 工具方法
// TODO: 添加日期格式化方法

// 同步获取服务商标签（从缓存的映射中获取）
const getProviderLabel = (provider: AiProvider): string => {
  if (!provider) return '未知服务商'

  const info = providerInfoMap.value.get(provider)
  if (info?.label) {
    return info.label
  }

  // 如果映射中没有找到，返回默认值
  console.warn(`未找到服务商 ${provider} 的标签信息，使用默认值`)
  return provider
}

// 同步获取服务商颜色（从缓存的映射中获取）
const getProviderColor = (provider: AiProvider): string => {
  if (!provider) return '#8c8c8c'

  const info = providerInfoMap.value.get(provider)
  if (info?.color) {
    return info.color
  }

  // 如果映射中没有找到，返回默认颜色
  console.warn(`未找到服务商 ${provider} 的颜色信息，使用默认颜色`)
  return '#8c8c8c'
}

// 数据加载
const loadConfigs = async () => {
  try {
    loading.value = true
    configs.value = await aiConfigDatabaseService.getAllConfigs()
  } catch (error: any) {
    message.error('加载配置失败!')
    console.error('Load configs error:', error)
  } finally {
    loading.value = false
  }
}

// 表单处理
const handleProviderChange = async (provider: AiProvider) => {
  console.log('切换服务商:', provider)

  if (provider === 'custom') {
    // 如果选择"添加AI服务商"，弹出自定义服务商模态框
    showAddProviderModal.value = true
    // 重置选择
    newConfig.provider = 'openai'
    return
  }

  try {
    const defaults = await aiConfigDatabaseService.getProviderDefaults(provider)
    console.log('获取到的默认配置:', defaults)
    Object.assign(newConfig, defaults)

    // 加载该服务商的模型列表
    await loadModels(provider)
    console.log('加载的模型列表:', systemModels.value)

    // 强制触发响应式更新
    await nextTick()
    console.log('响应式更新后的模型列表:', systemModels.value)
  } catch (error: any) {
    console.error('切换服务商失败:', error)
    message.error('切换服务商失败: ' + (error.message || error))
  }
}

const resetForm = () => {
  Object.assign(newConfig, {
    name: '',
    provider: 'openai',
    apiKey: '',
    baseUrl: '',
    modelName: '',
    temperature: 0.7,
    maxTokens: 4000,
    timeout: 30000,
    systemPrompt: '',
    customHeaders: {},
    customParams: {},
    enabled: true,
    isDefault: false
  })
}

const resetEditForm = () => {
  showEditModal.value = false
  Object.assign(editingConfig, {
    id: '',
    name: '',
    provider: 'openai',
    apiKey: '',
    baseUrl: '',
    modelName: '',
    temperature: 0.7,
    maxTokens: 2000,
    timeout: 30000,
    systemPrompt: '',
    customHeaders: {},
    customParams: {},
    enabled: true,
    isDefault: false
  })
}

const handleEditProviderChange = async (provider: AiProvider) => {
  if (provider === 'custom') {
    // 如果在编辑模态框中选择"添加AI服务商"，弹出自定义服务商模态框
    showAddProviderModal.value = true
    // 保持原来的选择
    return
  }

  const defaults = await aiConfigDatabaseService.getProviderDefaults(provider)
  Object.assign(editingConfig, { ...editingConfig, ...defaults })

  // 加载该服务商的模型列表
  await loadEditModels(provider)
}

const handleAddConfig = async () => {
  adding.value = true
  try {
    await aiConfigDatabaseService.saveConfig(newConfig)
    message.success('配置添加成功!')
    resetForm()
    showAddForm.value = false
    await loadConfigs()
  } catch (error: any) {
    message.error(error.message || '配置添加失败!')
  } finally {
    adding.value = false
  }
}

const handleTestNewConfig = async () => {
  testing.value = true
  try {
    console.log('开始测试新配置:', newConfig)

    // 验证必要字段
    if (!newConfig.apiKey?.trim()) {
      throw new Error('请输入API Key')
    }

    if (!newConfig.baseUrl?.trim()) {
      throw new Error('请输入API端点')
    }

    if (!newConfig.modelName?.trim()) {
      throw new Error('请选择模型')
    }

    // 调用真正的测试方法
    const result = await aiConfigDatabaseService.testConfig({
      provider: newConfig.provider,
      apiKey: newConfig.apiKey,
      baseUrl: newConfig.baseUrl,
      modelName: newConfig.modelName,
      temperature: newConfig.temperature,
      maxTokens: newConfig.maxTokens,
      timeout: newConfig.timeout
    })

    if (result.success) {
      message.success(result.message)
    } else {
      throw new Error(result.message)
    }
  } catch (error: any) {
    console.error('测试新配置失败:', error)
    message.error(error.message || '连接测试失败!')
  } finally {
    testing.value = false
  }
}

// 配置操作
const handleEditConfig = (config: AiConfigForm) => {
  Object.assign(editingConfig, {
    id: config.id,
    name: config.name,
    provider: config.provider,
    apiKey: config.apiKey,
    baseUrl: config.baseUrl,
    modelName: config.modelName || '',
    temperature: config.temperature || 0.7,
    maxTokens: config.maxTokens || 2000,
    timeout: config.timeout || 30000,
    systemPrompt: config.systemPrompt || '',
    customHeaders: config.customHeaders || {},
    customParams: config.customParams || {},
    enabled: config.enabled,
    isDefault: config.isDefault || false
  })
  showEditModal.value = true
}

const handleUpdateConfig = async () => {
  updating.value = true
  try {
    await aiConfigDatabaseService.saveConfig(editingConfig)
    message.success('配置更新成功!')
    showEditModal.value = false
    await loadConfigs()
  } catch (error: any) {
    message.error(error.message || '配置更新失败!')
  } finally {
    updating.value = false
  }
}

const handleToggleEnabled = async (config: AiConfigForm, newEnabled: boolean) => {
  // 防止重复操作
  if (toggleLoadingId.value === config.id) {
    return
  }

  toggleLoadingId.value = config.id

  try {
    console.log('切换配置启用状态:', {
      configId: config.id,
      configName: config.name,
      currentEnabled: config.enabled,
      newEnabled: newEnabled
    })

    // 直接更新配置的启用状态
    const success = await aiConfigDatabaseService.updateConfigEnabled(config.id!, newEnabled)

    if (!success) {
      throw new Error('更新配置状态失败')
    }

    // 直接更新本地状态，避免重新加载整个列表
    const configIndex = configs.value.findIndex(c => c.id === config.id)
    if (configIndex !== -1) {
      configs.value[configIndex].enabled = newEnabled
    }

    message.success(`配置"${config.name}"已${newEnabled ? '启用' : '禁用'}`)
    console.log('配置状态更新成功')

  } catch (error: any) {
    console.error('切换配置启用状态失败:', error)
    message.error(error.message || '操作失败!')

    // 如果操作失败，重新加载配置以恢复正确状态
    await loadConfigs()
  } finally {
    toggleLoadingId.value = ''
  }
}

const handleTestConfig = async (config: AiConfigForm) => {
  testingConfigId.value = config.id
  try {
    console.log('开始测试现有配置:', config)

    // 验证必要字段
    if (!config.apiKey?.trim()) {
      throw new Error('配置中缺少API Key')
    }

    if (!config.baseUrl?.trim()) {
      throw new Error('配置中缺少API端点')
    }

    if (!config.modelName?.trim()) {
      throw new Error('配置中缺少模型名称')
    }

    // 调用真正的测试方法
    const result = await aiConfigDatabaseService.testConfig({
      provider: config.provider,
      apiKey: config.apiKey,
      baseUrl: config.baseUrl,
      modelName: config.modelName,
      temperature: config.temperature || 0.7,
      maxTokens: config.maxTokens || 2000,
      timeout: config.timeout || 30000
    })

    if (result.success) {
      message.success(result.message)
    } else {
      throw new Error(result.message)
    }
  } catch (error: any) {
    console.error('测试现有配置失败:', error)
    message.error(error.message || '连接测试失败!')
  } finally {
    testingConfigId.value = ''
  }
}

const handleSetDefault = async (config: AiConfigForm) => {
  try {
    await aiConfigDatabaseService.setDefaultConfig(config.id!)
    message.success('默认配置设置成功!')
    await loadConfigs()
  } catch (error: any) {
    message.error(error.message || '设置默认配置失败!')
  }
}

const handleDuplicateConfig = async (config: AiConfigForm) => {
  try {
    const duplicatedConfig: AiConfigForm = {
      name: `${config.name} (副本)`,
      provider: config.provider,
      apiKey: config.apiKey,
      baseUrl: config.baseUrl,
      modelName: config.modelName || '',
      temperature: config.temperature || 0.7,
      maxTokens: config.maxTokens || 2000,
      timeout: config.timeout || 30000,
      enabled: false,
      isDefault: false
    }

    await aiConfigDatabaseService.saveConfig(duplicatedConfig)
    message.success('配置复制成功!')
    await loadConfigs()
  } catch (error: any) {
    message.error(error.message || '配置复制失败!')
  }
}

const handleDeleteConfig = (config: AiConfigForm) => {
  Modal.confirm({
    title: '确认删除配置',
    content: `确定要删除配置"${config.name}"吗？此操作不可撤销。`,
    icon: h(ExclamationCircleOutlined, { style: { color: '#faad14' } }),
    okText: '确认删除',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      try {
        await aiConfigDatabaseService.deleteConfig(config.id!)
        message.success('配置删除成功!')
        await loadConfigs()
      } catch (error: any) {
        message.error(error.message || '配置删除失败!')
      }
    }
  })
}

// 其他操作
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleRefresh = async () => {
  await loadConfigs()
  message.success('数据刷新成功!')
}

// ==================== 自定义模型管理 ====================

// 响应式数据
const showAddModelModal = ref(false)
const showAddProviderModal = ref(false)
const customModelForm = reactive({
  name: '',
  label: ''
})
const customProviderForm = reactive({
  name: '',
  baseUrl: '',
  apiKey: '',
  modelName: ''
})

// 处理模型选择变化
const handleModelChange = (value: string) => {
  if (value === '__ADD_CUSTOM__') {
    showAddModelModal.value = true
    // 重置选择
    newConfig.modelName = ''
  }
}

// 处理编辑模态框的模型选择变化
const handleEditModelChange = (value: string) => {
  if (value === '__ADD_CUSTOM__') {
    showAddModelModal.value = true
    // 重置选择
    editingConfig.modelName = ''
  }
}



// 添加自定义模型
const handleAddCustomModel = async () => {
  try {
    if (!customModelForm.name.trim()) {
      message.error('请输入模型名称')
      return
    }

    if (!customModelForm.label.trim()) {
      message.error('请输入模型显示名称')
      return
    }

    const provider = showEditModal.value ? editingConfig.provider : newConfig.provider
    const modelName = customModelForm.name.trim()
    const modelLabel = customModelForm.label.trim()

    console.log('开始添加自定义模型:', { provider, modelName, modelLabel })

    // 调用数据库服务添加自定义模型
    const success = await aiConfigDatabaseService.addCustomModel(provider, modelName, modelLabel)

    if (!success) {
      throw new Error('添加自定义模型失败，请检查模型名称是否已存在')
    }

    console.log('自定义模型添加成功')
    message.success('自定义模型添加成功!')

    // 重新加载模型列表以显示新添加的模型
    if (showEditModal.value) {
      await loadEditModels(provider)
      editingConfig.modelName = modelName
    } else {
      await loadModels(provider)
      newConfig.modelName = modelName
    }

    // 重置表单
    customModelForm.name = ''
    customModelForm.label = ''
    showAddModelModal.value = false

  } catch (error: any) {
    console.error('添加自定义模型失败:', error)
    message.error(error.message || '添加自定义模型失败!')
  }
}

// 删除自定义模型
const handleDeleteCustomModel = (modelId: string) => {
  try {
    // TODO: 实现自定义模型删除功能
    const success = false // await aiConfigDatabaseService.deleteCustomModel(modelId)
    if (success) {
      message.success('自定义模型删除成功!')
    } else {
      message.error('未找到要删除的模型')
    }
  } catch (error: any) {
    message.error(error.message || '删除自定义模型失败!')
  }
}

// 取消添加自定义模型
const handleCancelAddModel = () => {
  customModelForm.name = ''
  customModelForm.label = ''
  showAddModelModal.value = false
}

// ==================== 自定义服务商管理 ====================

// 添加自定义服务商
const handleAddCustomProvider = async () => {
  try {
    if (!customProviderForm.name.trim()) {
      message.error('请输入AI服务商名称')
      return
    }

    if (!customProviderForm.baseUrl.trim()) {
      message.error('请输入API端点')
      return
    }

    if (!customProviderForm.apiKey.trim()) {
      message.error('请输入API Key')
      return
    }

    // 创建配置对象
    const configData: AiConfigForm = {
      name: customProviderForm.name.trim(),
      provider: 'custom',
      apiKey: customProviderForm.apiKey.trim(),
      baseUrl: customProviderForm.baseUrl.trim(),
      modelName: customProviderForm.modelName.trim() || '',
      temperature: 0.7,
      maxTokens: 4000,
      timeout: 30000,
      systemPrompt: '',
      customHeaders: {},
      customParams: {},
      enabled: true,
      isDefault: false
    }

    // 判断是在添加配置还是编辑配置
    if (showEditModal.value) {
      // 编辑模态框中使用 - 只填入表单，不直接保存
      editingConfig.provider = 'custom'
      editingConfig.baseUrl = customProviderForm.baseUrl.trim()
      editingConfig.apiKey = customProviderForm.apiKey.trim()
      editingConfig.modelName = customProviderForm.modelName.trim() || ''
      // 如果配置名称为空，使用服务商名称
      if (!editingConfig.name.trim()) {
        editingConfig.name = customProviderForm.name.trim()
      }
      message.success('自定义AI服务商信息已填入编辑表单!')
    } else {
      // 添加配置场景 - 先创建自定义服务商，再保存配置

      // 1. 生成服务商标识
      const providerName = customProviderForm.name.trim().toLowerCase().replace(/\s+/g, '_')

      // 2. 先创建自定义服务商
      const result = await aiConfigDatabaseService.addCustomProvider(
        providerName, // 服务商标识
        customProviderForm.name.trim(), // 显示名称
        customProviderForm.baseUrl.trim() // API端点
      )

      if (!result.success || !result.providerId) {
        throw new Error('创建自定义AI服务商失败')
      }

      // 3. 更新配置数据中的provider为新创建的服务商标识
      configData.provider = providerName as any

      // 4. 保存配置，直接使用providerId
      await aiConfigDatabaseService.saveConfig(configData, result.providerId)
      message.success('自定义AI服务商配置添加成功!')

      // 刷新服务商列表和配置列表
      await loadProviders()
      await loadConfigs()
    }

    // 重置表单
    customProviderForm.name = ''
    customProviderForm.baseUrl = ''
    customProviderForm.apiKey = ''
    customProviderForm.modelName = ''
    showAddProviderModal.value = false

  } catch (error: any) {
    message.error(error.message || '添加自定义AI服务商失败!')
  }
}

// 取消添加自定义服务商
const handleCancelAddProvider = () => {
  customProviderForm.name = ''
  customProviderForm.baseUrl = ''
  customProviderForm.apiKey = ''
  customProviderForm.modelName = ''
  showAddProviderModal.value = false
}



// 初始化
onMounted(async () => {
  console.log('组件初始化开始')

  // 检查数据库状态
  try {
    const providers = await aiConfigDatabaseService.getAvailableProviders()
    console.log('数据库状态检查 - 可用服务商数量:', providers.length)
    console.log('可用服务商列表:', providers)

    // 如果没有服务商数据，提示用户
    if (providers.length === 0) {
      console.warn('没有找到服务商数据，可能需要重新初始化数据库')
      message.warning('未找到AI服务商数据，请刷新页面重试')
    }
  } catch (error) {
    console.error('数据库状态检查失败:', error)
  }

  await loadProviders()
  await loadConfigs()
  // 初始化默认服务商的模型列表
  await loadModels('openai')
  await loadEditModels('openai')

  console.log('组件初始化完成')
})
</script>

<style scoped>
/* AI配置管理卡片 */
.ai-config-management-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.dark .ai-config-management-card {
  background: #1f1f1f;
  border-color: #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 卡片标题 */
.card-title-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.dark .card-title {
  color: #f0f0f0;
}

.help-icon-btn {
  color: #8c8c8c;
  font-size: 14px;
  width: 22px;
  height: 22px;
  padding: 0;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  /* 防止图标被压缩 */
}

.help-icon-btn:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.dark .help-icon-btn {
  color: #a6a6a6;
}

.dark .help-icon-btn:hover {
  color: #40a9ff;
  background: rgba(64, 169, 255, 0.1);
}

/* 统计卡片紧凑样式 - 统一标准 */
.stat-card {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
  position: relative;
}

.stat-card:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.dark .stat-card {
  background: #1f1f1f;
  border-color: #303030;
}

.dark .stat-card:hover {
  border-color: #434343;
  box-shadow: 0 2px 6px rgba(255, 255, 255, 0.02);
}

.stat-icon {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-size: 16px;
  color: white;
}

.stat-icon-primary {
  background: #e6f7ff;
  color: #1890ff;
}

.stat-icon-success {
  background: #f6ffed;
  color: #52c41a;
}

.stat-icon-warning {
  background: #fffbe6;
  color: #faad14;
}

.stat-icon-info {
  background: #f0f5ff;
  color: #2f54eb;
}

.dark .stat-icon-primary {
  background: #111b26;
  color: #177ddc;
}

.dark .stat-icon-success {
  background: #162312;
  color: #49aa19;
}

.dark .stat-icon-warning {
  background: #2b2611;
  color: #d89614;
}

.dark .stat-icon-info {
  background: #10239e;
  color: #2f54eb;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  line-height: 1;
  margin-bottom: 2px;
}

.dark .stat-value {
  color: #fff;
}

.stat-label {
  font-size: 13px;
  color: #8c8c8c;
  font-weight: 400;
}

.dark .stat-label {
  color: #a6a6a6;
}

/* 工具栏 - 紧凑型统一标准 */
.toolbar-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 8px 0;
  gap: 12px;
}

.toolbar-left {
  flex: 0 0 auto;
}

.toolbar-right {
  flex: 0 0 auto;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
}



/* 添加表单 - 紧凑型统一标准 */
.add-form-container {
  margin-bottom: 12px;
}

.add-config-form {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-top: 8px;
  transition: all 0.3s ease;
}

.dark .add-config-form {
  background: #1f1f1f;
  border-color: #303030;
}

.form-content {
  margin: 0;
}

.form-switches {
  display: flex;
  align-items: center;
  height: 32px;
}

/* 配置列表 */
.config-list-section {
  margin-top: 16px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 0;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 0;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 12px;
}

@media (max-width: 768px) {
  .config-grid {
    grid-template-columns: 1fr;
  }
}

.config-card {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.config-card:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.dark .config-card {
  background: #262626;
  border-color: #303030;
}

.dark .config-card:hover {
  border-color: #434343;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 紧凑式卡片样式 */
.config-card-compact {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 10px;
  transition: all 0.2s ease;
  cursor: pointer;
  min-height: 120px;
}

.config-card-compact:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.dark .config-card-compact {
  background: #262626;
  border-color: #303030;
}

.dark .config-card-compact:hover {
  border-color: #434343;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 紧凑式头部 */
.config-header-compact {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  min-height: 24px;
}

.config-title-compact {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
  min-width: 0;
}

.config-name-compact {
  font-weight: 600;
  font-size: 14px;
  color: #262626;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.dark .config-name-compact {
  color: #fff;
}

.config-actions-compact {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-shrink: 0;
}

.action-btn {
  padding: 2px 4px;
  height: 24px;
  width: 24px;
}

/* 紧凑式内容 */
.config-content-compact {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 8px;
}

.config-info-left,
.config-info-right {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item-compact {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  line-height: 1.4;
}

.info-icon {
  font-size: 11px;
  color: #999;
  flex-shrink: 0;
  width: 12px;
}

.info-text {
  color: #595959;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
}

.dark .info-text {
  color: #d9d9d9;
}

/* 紧凑式底部 */
.config-footer-compact {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 6px;
  border-top: 1px solid #f5f5f5;
  font-size: 12px;
}

.dark .config-footer-compact {
  border-top-color: #303030;
}

.api-key-compact {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
  min-width: 0;
}

.api-key-masked-compact {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  color: #fa8c16;
  background: #fff7e6;
  padding: 1px 4px;
  border-radius: 3px;
  border: 1px solid #ffd591;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
}

.config-status-compact {
  flex-shrink: 0;
}

/* 紧凑式特殊样式 */
.config-url {
  color: #1890ff;
  cursor: pointer;
}

.config-url:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.system-prompt {
  color: #722ed1;
  font-style: italic;
  cursor: pointer;
}

.system-prompt:hover {
  color: #9254de;
}

/* 响应式优化 */
@media (max-width: 480px) {
  .config-content-compact {
    grid-template-columns: 1fr;
  }

  .config-name-compact {
    max-width: 100px;
  }

  .api-key-masked-compact {
    max-width: 80px;
  }
}

.config-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
}

.config-title-section {
  flex: 1;
  min-width: 0;
}

.config-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
  word-break: break-word;
}

.dark .config-name {
  color: #f0f0f0;
}

.config-provider {
  margin-bottom: 8px;
}

.config-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 12px;
}

.config-content {
  padding: 12px 0;
}

.config-info-grid {
  display: grid;
  gap: 8px;
  margin-bottom: 12px;
}

.config-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  padding: 4px 0;
}

.info-label {
  color: #666;
  font-weight: 500;
  min-width: 80px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.info-label .anticon {
  font-size: 12px;
  color: #999;
}

.dark .info-label {
  color: #a6a6a6;
}

.info-value {
  color: #333;
  text-align: right;
  flex: 1;
  margin-left: 12px;
  font-weight: 500;
  word-break: break-all;
}

.dark .info-value {
  color: #d9d9d9;
}

.model-name {
  color: #52c41a;
  font-weight: 600;
}

.system-prompt {
  color: #722ed1;
  font-style: italic;
  cursor: pointer;
}

.system-prompt:hover {
  color: #9254de;
}

.config-api-key {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
}

.api-key-masked {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #fa8c16;
  background: #fff7e6;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #ffd591;
}

.config-url {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #1890ff;
  cursor: pointer;
}

.config-url:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.config-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.dark .config-footer {
  border-top-color: #303030;
}

.config-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-date {
  font-size: 12px;
  color: #8c8c8c;
}

.dark .config-date {
  color: #a6a6a6;
}

/* 帮助模态框样式 */
.help-modal :deep(.ant-modal-header) {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.help-modal :deep(.ant-modal-body) {
  padding: 0;
  max-height: 70vh;
  overflow: hidden;
}

.help-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 24px;
  line-height: 1.6;
}

/* 自定义滚动条样式 */
.help-content::-webkit-scrollbar {
  width: 6px;
}

.help-content::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.help-content::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.help-content::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 帮助章节样式 - 紧凑型统一标准 */
.help-section {
  margin-bottom: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.help-section:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.help-section:last-child {
  margin-bottom: 0;
}

.help-section-header {
  background: #fafafa;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.help-icon-wrapper {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #fff;
}

.help-icon-wrapper.overview {
  background: #1890ff;
}

.help-icon-wrapper.add {
  background: #52c41a;
}

.help-icon-wrapper.edit {
  background: #faad14;
}

.help-icon-wrapper.manage {
  background: #722ed1;
}

.help-icon-wrapper.search {
  background: #13c2c2;
}

.help-icon-wrapper.params {
  background: #2f54eb;
}

.help-icon-wrapper.warning {
  background: #fa541c;
}

.help-section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.help-section-content {
  padding: 16px;
}

.help-description {
  color: #595959;
  margin: 0;
  line-height: 1.6;
}

/* 操作步骤样式 - 紧凑型统一标准 */
.help-steps {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.help-step {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 10px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
}

.step-number {
  width: 20px;
  height: 20px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
  margin-top: 1px;
}

.step-text {
  color: #262626;
  line-height: 1.5;
  font-size: 14px;
}

/* 警告信息样式 - 紧凑型统一标准 */
.help-warnings {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.help-warning {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 10px 12px;
  background: #fff7e6;
  border-radius: 6px;
  border-left: 3px solid #faad14;
}

.warning-icon {
  font-size: 16px;
  flex-shrink: 0;
  margin-top: 1px;
}

.warning-text {
  color: #262626;
  line-height: 1.5;
  font-size: 14px;
}

/* 不同章节的步骤编号颜色 */
.help-section-add .step-number {
  background: #52c41a;
}

.help-section-add .help-step {
  border-left-color: #52c41a;
}

.help-section-edit .step-number {
  background: #faad14;
}

.help-section-edit .help-step {
  border-left-color: #faad14;
}

.help-section-manage .step-number {
  background: #722ed1;
}

.help-section-manage .help-step {
  border-left-color: #722ed1;
}

.help-section-search .step-number {
  background: #13c2c2;
}

.help-section-search .help-step {
  border-left-color: #13c2c2;
}

.help-section-params .step-number {
  background: #2f54eb;
}

.help-section-params .help-step {
  border-left-color: #2f54eb;
}

/* 暗黑模式下的帮助模态框 */
.dark .help-modal :deep(.ant-modal-header) {
  border-bottom-color: #303030;
  background: #1f1f1f;
}

.dark .help-modal :deep(.ant-modal-content) {
  background: #1f1f1f;
}

.dark .help-content::-webkit-scrollbar-track {
  background: #262626;
}

.dark .help-content::-webkit-scrollbar-thumb {
  background: #434343;
}

.dark .help-content::-webkit-scrollbar-thumb:hover {
  background: #595959;
}

.dark .help-section {
  border-color: #303030;
  background: #1f1f1f;
}

.dark .help-section-header {
  background: #262626;
  border-bottom-color: #303030;
}

.dark .help-section-title {
  color: #f0f0f0;
}

.dark .help-description {
  color: #d9d9d9;
}

.dark .help-step {
  background: #262626;
  color: #d9d9d9;
}

.dark .step-text {
  color: #d9d9d9;
}

.dark .help-warning {
  background: #2b2611;
  color: #d9d9d9;
}

.dark .warning-text {
  color: #d9d9d9;
}
</style>
