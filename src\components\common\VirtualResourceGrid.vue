<template>
  <div class="virtual-resource-grid" ref="containerRef">
    <!-- 虚拟滚动容器 -->
    <div
      ref="scrollElementRef"
      class="virtual-scroll-container"
      :style="{
        height: `${containerHeight}px`,
        overflowY: 'auto'
      }"
      @scroll="handleScroll"
    >
      <!-- 虚拟列表 -->
      <div
        class="virtual-list"
        :style="{
          height: `${totalHeight}px`,
          position: 'relative'
        }"
      >
        <!-- 渲染的项目 -->
        <div
          v-for="virtualItem in virtualItems"
          :key="virtualItem.key"
          class="virtual-item"
          :style="{
            position: 'absolute',
            top: `${virtualItem.start}px`,
            left: `${virtualItem.left}px`,
            width: `${itemWidth}px`,
            height: `${itemHeight}px`
          }"
        >
          <slot
            :item="virtualItem.item"
            :index="virtualItem.index"
          />
        </div>
      </div>

      <!-- 加载更多指示器 -->
      <div
        v-if="hasMore && !isLoading"
        ref="loadMoreRef"
        class="load-more-trigger"
        :style="{ height: '1px' }"
      />
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-indicator">
      <a-spin size="large">
        <template #indicator>
          <LoadingOutlined style="font-size: 24px" spin />
        </template>
      </a-spin>
      <p>加载中...</p>
    </div>

    <!-- 空状态 -->
    <div v-if="!isLoading && items.length === 0" class="empty-state">
      <slot name="empty">
        <a-empty description="暂无数据" />
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useVirtualizer } from '@tanstack/vue-virtual'
import { LoadingOutlined } from '@ant-design/icons-vue'

interface Props {
  items: any[]
  itemHeight?: number
  itemWidth?: number
  columnsCount?: number
  gap?: number
  containerHeight?: number
  hasMore?: boolean
  isLoading?: boolean
  overscan?: number
}

const props = withDefaults(defineProps<Props>(), {
  itemHeight: 280,
  itemWidth: 300,
  columnsCount: 3,
  gap: 16,
  containerHeight: 600,
  hasMore: false,
  isLoading: false,
  overscan: 5
})

const emit = defineEmits<{
  loadMore: []
}>()

// 引用
const containerRef = ref<HTMLElement>()
const scrollElementRef = ref<HTMLElement>()
const loadMoreRef = ref<HTMLElement>()

// 计算网格布局
const gridItems = computed(() => {
  const items = []
  for (let i = 0; i < props.items.length; i++) {
    const row = Math.floor(i / props.columnsCount)
    const col = i % props.columnsCount
    
    items.push({
      index: i,
      item: props.items[i],
      row,
      col,
      key: `item-${i}`
    })
  }
  return items
})

// 计算总高度
const totalHeight = computed(() => {
  const rowCount = Math.ceil(props.items.length / props.columnsCount)
  return rowCount * (props.itemHeight + props.gap) - props.gap
})

// 计算项目宽度（考虑间距）
const itemWidth = computed(() => {
  const totalGap = (props.columnsCount - 1) * props.gap
  const availableWidth = (containerRef.value?.clientWidth || 1200) - totalGap
  return Math.floor(availableWidth / props.columnsCount)
})

// 虚拟化器
const virtualizer = useVirtualizer({
  count: Math.ceil(props.items.length / props.columnsCount),
  getScrollElement: () => scrollElementRef.value,
  estimateSize: () => props.itemHeight + props.gap,
  overscan: props.overscan
})

// 虚拟项目
const virtualItems = computed(() => {
  const items = []
  
  for (const virtualRow of virtualizer.value.getVirtualItems()) {
    const startIndex = virtualRow.index * props.columnsCount
    const endIndex = Math.min(startIndex + props.columnsCount, props.items.length)
    
    for (let i = startIndex; i < endIndex; i++) {
      const col = i % props.columnsCount
      const left = col * (itemWidth.value + props.gap)
      
      items.push({
        key: `item-${i}`,
        index: i,
        item: props.items[i],
        start: virtualRow.start,
        left,
        row: virtualRow.index,
        col
      })
    }
  }
  
  return items
})

// 滚动处理
const handleScroll = () => {
  // 虚拟化器会自动处理滚动
}

// 无限滚动检测
let loadMoreObserver: IntersectionObserver | null = null

const setupLoadMoreObserver = () => {
  if (!loadMoreRef.value || !props.hasMore) return

  loadMoreObserver = new IntersectionObserver(
    (entries) => {
      const entry = entries[0]
      if (entry.isIntersecting && !props.isLoading) {
        emit('loadMore')
      }
    },
    {
      root: scrollElementRef.value,
      rootMargin: '100px'
    }
  )

  loadMoreObserver.observe(loadMoreRef.value)
}

const cleanupLoadMoreObserver = () => {
  if (loadMoreObserver) {
    loadMoreObserver.disconnect()
    loadMoreObserver = null
  }
}

// 响应式布局
const handleResize = () => {
  nextTick(() => {
    virtualizer.value.measure()
  })
}

let resizeObserver: ResizeObserver | null = null

const setupResizeObserver = () => {
  if (!containerRef.value) return

  resizeObserver = new ResizeObserver(handleResize)
  resizeObserver.observe(containerRef.value)
}

const cleanupResizeObserver = () => {
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    setupLoadMoreObserver()
    setupResizeObserver()
  })
})

onUnmounted(() => {
  cleanupLoadMoreObserver()
  cleanupResizeObserver()
})

// 监听数据变化
watch(
  () => [props.items.length, props.hasMore],
  () => {
    nextTick(() => {
      cleanupLoadMoreObserver()
      setupLoadMoreObserver()
    })
  }
)

watch(
  () => props.items,
  () => {
    nextTick(() => {
      virtualizer.value.measure()
    })
  }
)
</script>

<style scoped>
.virtual-resource-grid {
  width: 100%;
  height: 100%;
  position: relative;
}

.virtual-scroll-container {
  width: 100%;
  position: relative;
}

.virtual-list {
  width: 100%;
}

.virtual-item {
  padding: 0;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--ant-color-text-secondary);
}

.loading-indicator p {
  margin-top: 16px;
  margin-bottom: 0;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 40px;
}

.load-more-trigger {
  width: 100%;
  pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .virtual-resource-grid {
    --columns: 2;
  }
}

@media (max-width: 768px) {
  .virtual-resource-grid {
    --columns: 1;
  }
}
</style>
