import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import ImageGallery from '../ImageGallery.vue'

// Mock services
vi.mock('@/services/imageDataService', () => ({
  imageDataService: {
    getImages: vi.fn().mockResolvedValue([]),
  }
}))

vi.mock('@/services/imageLifecycleService', () => ({
  imageLifecycleService: {
    getStatistics: vi.fn().mockResolvedValue({
      total: 0,
      active: 0,
      expired: 0,
      failed: 0,
      expiring: 0,
      totalSize: 0
    }),
    startLifecycleManagement: vi.fn(),
    performLifecycleCheck: vi.fn(),
    deleteMultipleRecords: vi.fn(),
    updateImageTags: vi.fn()
  }
}))

// Mock Ant Design components
const mockAntdComponents = {
  'a-card': { template: '<div class="ant-card"><slot /></div>' },
  'a-button': { template: '<button class="ant-btn"><slot /></button>' },
  'a-select': { template: '<select class="ant-select"><slot /></select>' },
  'a-select-option': { template: '<option class="ant-select-option"><slot /></option>' },
  'a-tag': { template: '<span class="ant-tag"><slot /></span>' },
  'a-spin': { template: '<div class="ant-spin">Loading...</div>' },
  'a-empty': { template: '<div class="ant-empty"><slot /></div>' },
  'a-pagination': { template: '<div class="ant-pagination"></div>' }
}

describe('ImageGallery', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(ImageGallery, {
      global: {
        components: mockAntdComponents,
        stubs: {
          ImageDetailModal: true,
          TagEditModal: true,
          ImageUploadModal: true
        }
      }
    })
  })

  it('应该正确渲染主容器结构', () => {
    expect(wrapper.find('.image-gallery-container').exists()).toBe(true)
    expect(wrapper.find('.function-card').exists()).toBe(true)
  })

  it('应该显示统计信息', async () => {
    await nextTick()
    expect(wrapper.find('.stats-text').exists()).toBe(true)
    expect(wrapper.find('.stats-text').text()).toContain('共 0 张图片')
  })

  it('应该包含筛选控件', () => {
    expect(wrapper.find('.filter-controls-row').exists()).toBe(true)
    expect(wrapper.find('.status-selector').exists()).toBe(true)
    expect(wrapper.find('.sort-selector').exists()).toBe(true)
  })

  it('应该在空状态时显示正确的提示', async () => {
    await nextTick()
    expect(wrapper.find('.empty-state').exists()).toBe(true)
  })

  it('应该有刷新按钮', () => {
    const refreshBtn = wrapper.find('[data-testid="refresh-btn"]')
    expect(refreshBtn.exists()).toBe(true)
  })

  it('应该支持响应式布局', () => {
    expect(wrapper.find('.image-grid').exists()).toBe(true)
    
    // 检查CSS类是否正确应用
    const container = wrapper.find('.image-gallery-container')
    expect(container.element).toHaveClass('image-gallery-container')
  })

  it('应该正确处理标签筛选', async () => {
    const component = wrapper.vm
    
    // 测试标签切换功能
    expect(component.selectedTags).toEqual([])
    
    // 模拟标签点击
    component.toggleTag(1)
    expect(component.selectedTags).toContain(1)
    
    // 再次点击应该移除标签
    component.toggleTag(1)
    expect(component.selectedTags).not.toContain(1)
  })

  it('应该正确处理排序变更', () => {
    const component = wrapper.vm
    
    // 测试排序变更
    component.handleSortChange('size-desc')
    expect(component.filters.sortBy).toBe('size-desc')
  })

  it('应该支持批量操作', async () => {
    const component = wrapper.vm
    
    // 选择图片
    component.selectedImages = [1, 2, 3]
    await nextTick()
    
    // 应该显示批量操作栏
    expect(wrapper.find('.batch-actions-card').exists()).toBe(true)
    expect(wrapper.find('.batch-info').text()).toContain('已选择 3 张图片')
  })
})
