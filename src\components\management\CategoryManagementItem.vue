<template>
  <div>
    <!-- 当前分类项 -->
    <div :class="[
      'flex items-center justify-between px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors',
      level > 0 ? `ml-${level * 6}` : ''
    ]">
      <div class="flex items-center space-x-3">
        <!-- 拖拽手柄 -->
        <div class="drag-handle p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
          <div class="i-heroicons-bars-3 w-4 h-4"></div>
        </div>

        <!-- 展开/收起图标 -->
        <button v-if="hasChildren" class="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
          @click="toggleExpanded">
          <div :class="[
            'w-4 h-4 transition-transform duration-200',
            isExpanded ? 'i-heroicons-chevron-down' : 'i-heroicons-chevron-right'
          ]"></div>
        </button>
        <div v-else class="w-6"></div>

        <!-- 分类图标 -->
        <div :class="[
          'w-5 h-5',
          hasChildren ? 'i-heroicons-folder text-blue-500' : 'i-heroicons-document text-gray-400'
        ]"></div>

        <!-- 分类信息 -->
        <div>
          <div class="font-medium text-gray-900 dark:text-gray-100">
            {{ category.name }}
          </div>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            {{ category.resource_count }} 个资源
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex items-center space-x-2">
        <BaseButton variant="ghost" size="sm" @click="handleEdit">
          <div class="i-heroicons-pencil w-4 h-4"></div>
        </BaseButton>

        <BaseDropdown placement="bottom-end">
          <template #trigger>
            <BaseButton variant="ghost" size="sm">
              <div class="i-heroicons-ellipsis-horizontal w-4 h-4"></div>
            </BaseButton>
          </template>

          <div class="w-40">
            <DropdownItem text="编辑" icon="i-heroicons-pencil" @click="handleEdit" />
            <DropdownItem text="移动" icon="i-heroicons-arrows-right-left" @click="showMoveDialog = true" />
            <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
            <DropdownItem text="删除" icon="i-heroicons-trash" class="text-red-600 dark:text-red-400"
              @click="handleDelete" />
          </div>
        </BaseDropdown>
      </div>
    </div>

    <!-- 子分类 -->
    <div v-if="hasChildren && isExpanded">
      <draggable v-model="localChildren" group="categories" item-key="id" handle=".drag-handle" :animation="200"
        ghost-class="ghost" chosen-class="chosen" drag-class="drag" @end="handleChildDragEnd">
        <template #item="{ element: child }">
          <CategoryManagementItem :key="child.id" :category="child" :level="level + 1" @edit="$emit('edit', $event)"
            @delete="$emit('delete', $event)" @move="$emit('move', $event)" @reorder="$emit('reorder', $event)" />
        </template>
      </draggable>
    </div>

    <!-- 移动分类对话框 -->
    <BaseModal v-model="showMoveDialog" title="移动分类" size="sm">
      <div class="space-y-4">
        <p class="text-gray-600 dark:text-gray-400">
          将分类 "{{ category.name }}" 移动到：
        </p>

        <ParentCategorySelector v-model="newParentId" placeholder="选择目标父分类" :categories="allCategories"
          :exclude-id="category.id" />
      </div>

      <template #footer>
        <div class="flex justify-end space-x-3">
          <BaseButton variant="outline" @click="showMoveDialog = false">
            取消
          </BaseButton>
          <BaseButton @click="handleMove">
            移动
          </BaseButton>
        </div>
      </template>
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject, watch } from 'vue'
import draggable from 'vuedraggable'
import BaseButton from '@/components/ui/BaseButton.vue'
import BaseDropdown from '@/components/ui/BaseDropdown.vue'
import BaseModal from '@/components/ui/BaseModal.vue'
import DropdownItem from '@/components/ui/DropdownItem.vue'
import ParentCategorySelector from '@/components/knowledge/ParentCategorySelector.vue'
import type { Category, CategoryWithChildren } from '@/types'

interface Props {
  category: CategoryWithChildren
  level: number
}

interface Emits {
  (e: 'edit', category: CategoryWithChildren): void
  (e: 'delete', category: CategoryWithChildren): void
  (e: 'move', data: { categoryId: number; newParentId: number }): void
  (e: 'reorder', data: { categoryIds: number[]; parentId: number }): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 注入所有分类数据（用于移动时选择父分类）
const allCategories = inject<Category[]>('allCategories', [])

const isExpanded = ref(false)
const showMoveDialog = ref(false)
const newParentId = ref(0)

// 本地子分类数据，用于拖拽
const localChildren = ref<CategoryWithChildren[]>([])

// 监听子分类变化
watch(() => props.category.children, (newChildren) => {
  if (newChildren) {
    localChildren.value = [...newChildren]
  }
}, { immediate: true, deep: true })

const hasChildren = computed(() => {
  return props.category.children && props.category.children.length > 0
})

// 切换展开状态
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

// 编辑分类
const handleEdit = () => {
  emit('edit', props.category)
}

// 删除分类
const handleDelete = () => {
  emit('delete', props.category)
}

// 移动分类
const handleMove = () => {
  emit('move', {
    categoryId: props.category.id!,
    newParentId: newParentId.value
  })
  showMoveDialog.value = false
  newParentId.value = 0
}

// 处理子分类拖拽结束
const handleChildDragEnd = (evt: any) => {
  const { oldIndex, newIndex } = evt

  if (oldIndex !== newIndex) {
    // 发出重新排序事件
    const categoryIds = localChildren.value.map(cat => cat.id!)
    emit('reorder', { categoryIds, parentId: props.category.id! })
  }
}
</script>

<style scoped>
/* 动态缩进样式 */
.ml-6 {
  margin-left: 1.5rem;
}

.ml-12 {
  margin-left: 3rem;
}

.ml-18 {
  margin-left: 4.5rem;
}

.ml-24 {
  margin-left: 6rem;
}

.ml-30 {
  margin-left: 7.5rem;
}

/* 拖拽样式 */
.ghost {
  opacity: 0.5;
  background: #f3f4f6;
  border: 2px dashed #d1d5db;
}

.chosen {
  background: #eff6ff;
  border: 1px solid #3b82f6;
}

.drag {
  transform: rotate(2deg);
  opacity: 0.8;
}

/* 拖拽手柄样式 */
.drag-handle {
  cursor: grab;
  color: #9ca3af;
  transition: color 0.2s;
}

.drag-handle:hover {
  color: #6b7280;
}

.drag-handle:active {
  cursor: grabbing;
}
</style>
