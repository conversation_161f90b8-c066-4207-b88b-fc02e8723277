import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActive<PERSON>inia, createPinia } from 'pinia'
import { useImageHostStore } from '../imageHostStore'

// Mock services
vi.mock('@/services/imageHostService', () => ({
  imageHostService: {
    getAllConfigs: vi.fn().mockResolvedValue([
      {
        id: '1',
        name: '测试图床',
        provider: 'test',
        enabled: true,
        priority: 1,
        apiUrl: 'https://test.com/upload',
        method: 'POST',
        authType: 'none',
        fileField: 'file',
        responseType: 'json',
        urlField: 'url'
      }
    ]),
    saveConfig: vi.fn().mockResolvedValue({
      id: '1',
      name: '测试图床',
      provider: 'test',
      enabled: false,
      priority: 1,
      apiUrl: 'https://test.com/upload',
      method: 'POST',
      authType: 'none',
      fileField: 'file',
      responseType: 'json',
      urlField: 'url'
    }),
    testConfig: vi.fn().mockResolvedValue({
      success: true,
      message: '连接成功'
    }),
    deleteConfig: vi.fn().mockResolvedValue(undefined)
  }
}))

// Mock Ant Design Vue message
vi.mock('ant-design-vue', () => ({
  message: {
    loading: vi.fn(),
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn()
  }
}))

describe('ImageHostStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('应该正确初始化状态', () => {
    const store = useImageHostStore()
    
    expect(store.configs).toEqual([])
    expect(store.testResults).toEqual({})
    expect(store.testing).toEqual({})
    expect(store.loading).toBe(false)
    expect(store.error).toBe(null)
  })

  it('应该正确计算启用的配置', async () => {
    const store = useImageHostStore()
    
    // 加载配置
    await store.loadConfigs()
    
    expect(store.enabledConfigs).toHaveLength(1)
    expect(store.enabledConfigs[0].enabled).toBe(true)
  })

  it('应该正确计算配置统计', async () => {
    const store = useImageHostStore()
    
    // 加载配置
    await store.loadConfigs()
    
    expect(store.configsCount).toEqual({
      total: 1,
      enabled: 1,
      disabled: 0
    })
  })

  it('应该正确加载配置', async () => {
    const store = useImageHostStore()
    
    await store.loadConfigs()
    
    expect(store.configs).toHaveLength(1)
    expect(store.configs[0].name).toBe('测试图床')
    expect(store.loading).toBe(false)
  })

  it('应该正确保存配置', async () => {
    const store = useImageHostStore()
    
    const configForm = {
      name: '新图床',
      provider: 'new',
      enabled: true,
      priority: 2,
      apiUrl: 'https://new.com/upload',
      method: 'POST' as const,
      authType: 'none' as const,
      fileField: 'file',
      responseType: 'json' as const,
      urlField: 'url'
    }

    const result = await store.saveConfig(configForm)
    
    expect(result).toBeDefined()
    expect(store.configs).toHaveLength(1) // Mock 返回的是更新后的配置
  })

  it('应该正确删除配置', async () => {
    const store = useImageHostStore()
    
    // 先加载配置
    await store.loadConfigs()
    expect(store.configs).toHaveLength(1)
    
    // 删除配置
    await store.deleteConfig('1')
    
    expect(store.configs).toHaveLength(0)
  })

  it('应该正确测试配置', async () => {
    const store = useImageHostStore()
    
    const testConfig = {
      id: '1',
      name: '测试图床',
      provider: 'test',
      enabled: true,
      priority: 1,
      apiUrl: 'https://test.com/upload',
      method: 'POST' as const,
      authType: 'none' as const,
      fileField: 'file',
      responseType: 'json' as const,
      urlField: 'url'
    }

    const result = await store.testConfig(testConfig, true)
    
    expect(result.success).toBe(true)
    expect(store.testResults['1']).toBeDefined()
    expect(store.testResults['1'].success).toBe(true)
  })

  it('应该正确切换配置启用状态', async () => {
    const store = useImageHostStore()
    
    // 先加载配置
    await store.loadConfigs()
    expect(store.configs[0].enabled).toBe(true)
    
    // 切换为禁用
    await store.toggleConfigEnabled('1', false)
    
    expect(store.configs[0].enabled).toBe(false)
  })

  it('应该正确处理错误状态', async () => {
    const store = useImageHostStore()
    
    // Mock 加载失败
    const { imageHostService } = await import('@/services/imageHostService')
    vi.mocked(imageHostService.getAllConfigs).mockRejectedValueOnce(new Error('网络错误'))
    
    await store.loadConfigs()
    
    expect(store.error).toBe('网络错误')
    expect(store.loading).toBe(false)
  })

  it('应该正确清理状态', () => {
    const store = useImageHostStore()
    
    // 设置一些状态
    store.configs.push({
      id: '1',
      name: '测试',
      provider: 'test',
      enabled: true,
      priority: 1,
      apiUrl: 'https://test.com',
      method: 'POST',
      authType: 'none',
      fileField: 'file',
      responseType: 'json',
      urlField: 'url'
    })
    store.error = '测试错误'
    
    // 重置状态
    store.reset()
    
    expect(store.configs).toEqual([])
    expect(store.error).toBe(null)
    expect(store.testResults).toEqual({})
    expect(store.testing).toEqual({})
  })

  it('应该正确使用计算属性', async () => {
    const store = useImageHostStore()
    
    await store.loadConfigs()
    
    // 测试 getTestResult 计算属性
    store.testResults['1'] = { success: true, message: '成功' }
    expect(store.getTestResult('1')).toEqual({ success: true, message: '成功' })
    
    // 测试 isTesting 计算属性
    store.testing['1'] = true
    expect(store.isTesting('1')).toBe(true)
    
    // 测试 hasEnabledConfigs 计算属性
    expect(store.hasEnabledConfigs).toBe(true)
  })
})
