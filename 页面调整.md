# KnowlEdge 页面调整设计文档

## 整体设计规范

### 1. 网站宽度规范

- **整体宽度**: 1440px（最大宽度限制）
- **内容区域**: 使用 `max-width: 1440px` 并居中对齐
- **内边距**: 桌面端使用 2rem (32px)，移动端使用 1rem (16px)
- **响应式断点**:
  - 移动端: < 768px
  - 平板端: 768px - 1024px
  - 桌面端: 1024px - 1440px
  - 大屏幕: > 1440px（内容仍限制在1440px内）

### 2. 布局系统设计

#### 2.1 主布局结构

```css
/* 主容器 - 使用 Flexbox 垂直布局 */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  max-width: 1440px;
  margin: 0 auto;
  background: var(--color-page-background);
}

/* 顶部导航栏 */
.app-header {
  flex-shrink: 0;
  height: 64px; /* 固定高度 */
  position: sticky;
  top: 0;
  z-index: 40;
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
}

/* 主内容区域 */
.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 64px); /* 页面高度减去导航栏高度 */
  overflow: hidden;
  max-width: 1440px;
  margin: 0 auto;
  width: 100%;
}

/* 页面内容容器 */
.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
  width: 100%;
}

/* 页面内容滚动容器 */
.page-scroll-container {
  height: calc(100vh - 64px);
  overflow-y: auto;
  overflow-x: hidden;
}
```

#### 2.2 Grid 布局系统

```css
/* 网格容器基础类 */
.grid-container {
  display: grid;
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* 12列网格系统 */
.grid-12 {
  grid-template-columns: repeat(12, 1fr);
  gap: 1.5rem;
}

/* 响应式网格 */
@media (max-width: 768px) {
  .grid-12 {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .grid-12 {
    grid-template-columns: repeat(6, 1fr);
    gap: 1.25rem;
  }
}
```

### 3. 滚动区域设计

#### 3.1 统一滚动容器

```css
/* 滚动容器基础样式 */
.scroll-container {
  height: calc(100vh - 64px);
  overflow-y: auto;
  overflow-x: hidden;
}

/* 自定义滚动条样式 */
.scroll-container::-webkit-scrollbar {
  width: 6px;
}

.scroll-container::-webkit-scrollbar-track {
  background: var(--color-background-mute);
  border-radius: 3px;
}

.scroll-container::-webkit-scrollbar-thumb {
  background: var(--color-border-hover);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.scroll-container::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-strong);
}

/* Firefox 滚动条样式 */
.scroll-container {
  scrollbar-width: thin;
  scrollbar-color: var(--color-border-hover) var(--color-background-mute);
}
```

### 4. 页面特定布局

#### 4.1 首页布局

```css
.home-layout {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 64px);
}

.hero-section {
  flex-shrink: 0;
  padding: 5rem 2rem;
  text-align: center;
}

.stats-section {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}
```

#### 4.2 知识库页面布局

```css
.knowledge-layout {
  display: grid;
  grid-template-rows: auto 1fr;
  height: calc(100vh - 64px);
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 2rem;
}

.knowledge-header {
  padding: 2rem 0 1rem;
  border-bottom: 1px solid var(--color-border);
}

.knowledge-content {
  overflow-y: auto;
  padding: 2rem 0;
}
```

#### 4.3 图床管理页面布局

```css
.image-gallery-layout {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 64px);
  max-width: 1440px;
  margin: 0 auto;
}

.image-tabs {
  flex-shrink: 0;
  padding: 1rem 2rem 0;
  border-bottom: 1px solid var(--color-border);
}

.image-content {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}
```

### 5. 组件样式复用

#### 5.1 卡片组件基础样式

```css
.card-base {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-base:hover {
  border-color: var(--color-border-hover);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}
```

#### 5.2 按钮组件样式

```css
.btn-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  outline: none;
}

.btn-primary {
  background: var(--color-primary-500);
  color: white;
}

.btn-primary:hover {
  background: var(--color-primary-600);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--color-primary-500), 0.4);
}
```

### 6. 响应式设计

#### 6.1 移动端适配

```css
@media (max-width: 768px) {
  .app-container {
    max-width: 100%;
  }

  .page-content {
    padding: 1rem;
  }

  .grid-container {
    padding: 0 1rem;
  }
}
```

#### 6.2 大屏幕适配

```css
@media (min-width: 1441px) {
  .app-container {
    max-width: 1440px;
  }
}
```

## 实施计划

### 阶段一：基础布局调整

1. 修改 AppLayout.vue 主布局组件
2. 更新 main.css 和 base.css 基础样式
3. 调整导航栏 AppHeader.vue 的宽度限制

### 阶段二：页面组件调整

1. 调整 HomeView.vue 首页布局
2. 调整 KnowledgeView.vue 知识库页面
3. 调整 ImageGalleryView.vue 图床管理页面

### 阶段三：组件样式统一

1. 创建通用样式类
2. 更新卡片组件样式
3. 统一按钮和表单组件样式

### 阶段四：滚动优化

1. 实现统一的滚动容器
2. 优化滚动条样式
3. 确保所有页面滚动高度正确
