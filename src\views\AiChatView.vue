<template>
  <div class="h-[calc(100vh-4rem)] bg-gray-50 dark:bg-gray-900 flex overflow-hidden">
    <!-- 主要内容区域 - DeepSeek风格布局 -->
    <div class="flex-1 flex overflow-hidden">
      <!-- 左侧会话列表 -->
      <div
        class="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col flex-shrink-0">
        <!-- 会话列表头部 - DeepSeek风格 -->
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <!-- 顶部操作栏 -->
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-2">
              <div
                class="w-6 h-6 bg-gradient-to-br from-primary-400 to-primary-600 rounded flex items-center justify-center">
                <div class="i-heroicons-chat-bubble-left-right text-white w-3 h-3"></div>
              </div>
              <span class="text-sm font-medium text-gray-900 dark:text-gray-100">对话</span>
            </div>

            <div class="flex items-center space-x-1">
              <!-- 配置状态指示 -->
              <div :class="[
                'w-2 h-2 rounded-full',
                aiStore.isConfigured && aiStore.isEnabled
                  ? 'bg-green-500'
                  : aiStore.isConfigured
                    ? 'bg-yellow-500'
                    : 'bg-red-500'
              ]" :title="aiStore.isConfigured && aiStore.isEnabled ? '服务正常' : aiStore.isConfigured ? '服务未启用' : '未配置'">
              </div>

              <!-- 设置按钮 -->
              <BaseButton variant="ghost" size="sm" @click="handleOpenSettings" class="p-1">
                <div class="i-heroicons-cog-6-tooth w-4 h-4"></div>
              </BaseButton>
            </div>
          </div>

          <!-- 新建对话按钮 -->
          <div class="space-y-2 mb-3">
            <BaseButton size="sm" :disabled="aiStore.chatStatus !== 'idle'" class="w-full justify-center"
              @click="handleNewChat(false)">
              <div class="i-heroicons-plus w-4 h-4 mr-2"></div>
              新建对话
            </BaseButton>

            <BaseButton size="sm" variant="outline" :disabled="aiStore.chatStatus !== 'idle'"
              class="w-full justify-center" @click="handleNewChat(true)">
              <div class="i-heroicons-clock w-4 h-4 mr-2"></div>
              临时对话
            </BaseButton>
          </div>

          <!-- 搜索框 -->
          <BaseInput v-model="searchKeyword" placeholder="搜索对话..." size="sm">
            <template #prefix>
              <div class="i-heroicons-magnifying-glass w-4 h-4 text-gray-400"></div>
            </template>
          </BaseInput>
        </div>

        <!-- 会话列表 -->
        <div class="flex-1 overflow-y-auto p-4">
          <div v-if="filteredSessions.length === 0" class="text-center text-gray-500 dark:text-gray-400 py-8">
            <div class="i-heroicons-chat-bubble-left-ellipsis w-12 h-12 mx-auto mb-3 opacity-50"></div>
            <p class="text-sm text-chinese paragraph-chinese-no-indent">
              {{ searchKeyword ? '未找到匹配的对话' : '暂无对话历史' }}
            </p>
          </div>

          <div v-else class="space-y-2">
            <div v-for="session in filteredSessions" :key="session.id" @click="handleSelectSession(session.id)" :class="[
              'p-4 rounded-lg cursor-pointer transition-all duration-200 group',
              session.id === aiStore.currentSession?.id
                ? 'bg-primary-50 dark:bg-primary-900/20 border-2 border-primary-200 dark:border-primary-800 shadow-sm'
                : 'hover:bg-gray-50 dark:hover:bg-gray-700 border-2 border-transparent'
            ]">
              <div class="flex items-start justify-between">
                <div class="flex-1 min-w-0">
                  <div class="flex items-center mb-1">
                    <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate flex-1">
                      {{ session.title }}
                    </h3>
                    <div v-if="session.isTemporaryChat"
                      class="ml-2 px-1.5 py-0.5 bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 text-xs rounded-full flex items-center">
                      <div class="i-heroicons-clock w-2.5 h-2.5 mr-1"></div>
                      临时
                    </div>
                  </div>
                  <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                    <span>{{ formatDate(session.updatedAt) }}</span>

                    <!-- 模型标签 -->
                    <span v-if="getSessionModel(session)"
                      class="px-2 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs font-medium">
                      {{ getSessionModel(session) }}
                    </span>
                  </div>
                  <div v-if="session.isTemporaryChat && session.expiresAt"
                    class="text-xs text-orange-500 dark:text-orange-400 mt-1 flex items-center">
                    <div class="i-heroicons-clock w-3 h-3 mr-1"></div>
                    {{ formatTemporaryTime(session) }}
                  </div>
                </div>

                <BaseDropdown placement="bottom-end" class="opacity-0 group-hover:opacity-100 transition-opacity">
                  <template #trigger>
                    <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded">
                      <div class="i-heroicons-ellipsis-vertical w-4 h-4"></div>
                    </button>
                  </template>

                  <div class="w-40">
                    <DropdownItem text="重命名" icon="i-heroicons-pencil" @click="handleRenameSession(session)" />

                    <!-- 对话类型转换 -->
                    <DropdownItem v-if="session.isTemporaryChat" text="转为永久对话" icon="i-heroicons-bookmark"
                      @click="handleConvertToPermanent(session.id)" />
                    <DropdownItem v-else text="转为临时对话" icon="i-heroicons-clock"
                      @click="handleConvertToTemporary(session.id)" />

                    <DropdownItem text="删除" icon="i-heroicons-trash" @click="handleDeleteSession(session.id)"
                      class="text-red-600 dark:text-red-400" />
                  </div>
                </BaseDropdown>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部统计和操作 -->
        <div class="p-4 border-t border-gray-200 dark:border-gray-700 space-y-3">
          <div class="text-xs text-gray-500 dark:text-gray-400">
            <div class="flex justify-between">
              <span>总对话数</span>
              <span>{{ aiStore.sessions.length }}</span>
            </div>
            <div class="flex justify-between mt-1">
              <span>总消息数</span>
              <span>{{ totalMessages }}</span>
            </div>
          </div>

          <BaseButton variant="outline" size="sm" @click="handleClearHistory" :disabled="aiStore.sessions.length === 0"
            class="w-full">
            <div class="i-heroicons-trash mr-2"></div>
            清空历史
          </BaseButton>
        </div>
      </div>

      <!-- 右侧对话区域 - DeepSeek风格 -->
      <div class="flex-1 bg-white dark:bg-gray-900 flex flex-col relative">
        <!-- 消息列表 -->
        <div ref="messagesContainer" class="flex-1 overflow-y-auto pb-32">
          <!-- 空状态 -->
          <div v-if="aiStore.currentMessages.length === 0" class="flex items-center justify-center h-full px-6">
            <div class="text-center max-w-2xl">
              <div
                class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                <div class="i-heroicons-sparkles w-8 h-8 text-white"></div>
              </div>
              <h3 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                你好！我是AI助手
              </h3>
              <div class="reading-container-wide">
                <p class="text-gray-600 dark:text-gray-400 mb-8 text-lg text-chinese paragraph-chinese-no-indent">
                  我可以帮助您解答问题、提供建议、协助思考。请随时向我提问！
                </p>
              </div>

              <!-- 示例问题卡片 -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-3 max-w-2xl">
                <button v-for="example in exampleQuestions" :key="example" @click="handleExampleQuestion(example)"
                  class="group p-4 text-left bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl hover:border-primary-300 dark:hover:border-primary-600 hover:shadow-md transition-all duration-200">
                  <div class="flex items-start space-x-3">
                    <div
                      class="w-6 h-6 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5 group-hover:bg-primary-200 dark:group-hover:bg-primary-800/50 transition-colors">
                      <div class="i-heroicons-chat-bubble-left w-3 h-3 text-primary-600 dark:text-primary-400">
                      </div>
                    </div>
                    <span
                      class="text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-gray-100">
                      {{ example }}
                    </span>
                  </div>
                </button>
              </div>
            </div>
          </div>

          <!-- 消息列表 -->
          <div v-else class="max-w-4xl mx-auto">
            <div v-for="message in aiStore.currentMessages" :key="message.id" :class="[
              'py-6 px-6 border-b border-gray-100 dark:border-gray-800 last:border-b-0 group',
              message.role === 'user' ? 'bg-gray-50/50 dark:bg-gray-800/30' : 'bg-white dark:bg-gray-900'
            ]">
              <div class="flex items-start space-x-4 max-w-none">
                <!-- 头像 -->
                <div class="flex-shrink-0">
                  <div :class="[
                    'w-8 h-8 rounded-full flex items-center justify-center',
                    message.role === 'user'
                      ? 'bg-primary-500 text-white'
                      : 'bg-gradient-to-br from-emerald-400 to-emerald-600 text-white'
                  ]">
                    <div :class="[
                      'w-4 h-4',
                      message.role === 'user' ? 'i-heroicons-user' : 'i-heroicons-sparkles'
                    ]"></div>
                  </div>
                </div>

                <!-- 消息内容 -->
                <div class="flex-1 min-w-0">
                  <!-- 角色标签 -->
                  <div class="flex items-center mb-2">
                    <span class="text-sm font-semibold text-gray-900 dark:text-gray-100">
                      {{ message.role === 'user' ? '你' : 'AI助手' }}
                    </span>
                    <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">
                      {{ formatTime(message.timestamp) }}
                    </span>
                    <span v-if="message.tokens" class="ml-2 text-xs text-gray-400 dark:text-gray-500">
                      {{ message.tokens }} tokens
                    </span>
                  </div>

                  <!-- 消息内容 -->
                  <div class="text-gray-800 dark:text-gray-200">
                    <div v-if="message.error" class="text-red-600 dark:text-red-400">
                      <div
                        class="flex items-center mb-2 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                        <div class="i-heroicons-exclamation-triangle w-4 h-4 mr-2"></div>
                        <span class="text-sm font-medium">发送失败</span>
                      </div>
                      <p class="text-sm ml-3">{{ message.error }}</p>
                    </div>

                    <div v-else-if="message.content"
                      class="prose prose-gray dark:prose-invert max-w-none prose-pre:!bg-gray-900 prose-pre:!text-gray-100 prose-pre:border prose-pre:border-gray-200 dark:prose-pre:border-gray-700 prose-pre:rounded-lg prose-pre:p-4 prose-pre:shadow-sm">
                      <MarkdownPreview :content="message.content" />
                    </div>

                    <div v-else-if="message.role === 'assistant'"
                      class="flex items-center space-x-2 text-gray-500 dark:text-gray-400 py-2">
                      <LoadingSpinner size="sm" />
                      <span class="text-sm">正在思考...</span>
                    </div>
                  </div>

                  <!-- 操作按钮 -->
                  <div v-if="message.content && !message.error && message.role === 'assistant'"
                    class="flex items-center space-x-2 mt-3 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button @click="copyMessage(message.content)"
                      class="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                      title="复制回答">
                      <div class="i-heroicons-clipboard w-4 h-4"></div>
                    </button>
                    <button @click="regenerateResponse(message)"
                      class="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                      title="重新生成">
                      <div class="i-heroicons-arrow-path w-4 h-4"></div>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 固定悬浮输入区域 - DeepSeek风格 -->
        <div
          class="absolute bottom-0 left-0 right-0 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 z-10">
          <div class="max-w-4xl mx-auto p-4">
            <div class="relative">
              <!-- 配置选择器（仅在新会话时显示） -->
              <div v-if="aiStore.availableConfigs.length > 1 && aiStore.canChangeModel"
                class="mb-3 flex items-center justify-center">
                <div class="flex items-center space-x-2">
                  <span class="text-xs text-gray-500 dark:text-gray-400">AI配置:</span>
                  <BaseSelect :model-value="aiStore.selectedConfigId" @update:model-value="handleConfigChange" size="sm"
                    class="min-w-40">
                    <option v-for="config in aiStore.availableConfigs" :key="config.id" :value="config.id">
                      {{ config.name }}
                    </option>
                  </BaseSelect>
                </div>
              </div>

              <!-- 输入框容器 -->
              <div
                class="relative bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-2xl shadow-lg focus-within:border-primary-500 focus-within:ring-1 focus-within:ring-primary-500 transition-all"
                @click="handleContainerClick">
                <textarea ref="messageInput" v-model="aiStore.currentMessage" @keydown="handleKeyDown"
                  @click="handleInputClick" @focus="handleInputFocus" @input="handleInputChange" @blur="handleInputBlur"
                  placeholder="输入消息... (Shift+Enter换行，Enter发送)" :disabled="!aiStore.canUseInput"
                  class="w-full px-4 py-5 pr-16 bg-transparent border-0 resize-none focus:ring-0 focus:outline-none text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 cursor-text selection:bg-primary-100 selection:text-primary-900 font-sans"
                  rows="1"
                  style="min-height: 64px; max-height: 200px; z-index: 10; pointer-events: auto; user-select: text; font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;"
                  tabindex="0" autocomplete="off" spellcheck="false"></textarea>

                <!-- 发送按钮 -->
                <div class="absolute right-2 bottom-2">
                  <button @click="handleSendMessage" :disabled="!aiStore.canSendMessage" :class="[
                    'w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-200',
                    aiStore.canSendMessage
                      ? 'bg-primary-500 hover:bg-primary-600 text-white shadow-sm hover:shadow-md'
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                  ]">
                    <div v-if="aiStore.chatStatus === 'idle'" class="i-heroicons-paper-airplane w-5 h-5"></div>
                    <div v-else-if="aiStore.chatStatus === 'sending' || aiStore.chatStatus === 'receiving'"
                      class="i-heroicons-stop w-5 h-5"></div>
                    <LoadingSpinner v-else size="sm" />
                  </button>
                </div>
              </div>



              <!-- 底部提示信息 -->
              <div class="flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400">
                <div class="flex items-center space-x-3">
                  <span v-if="aiStore.currentConfig?.modelName" class="flex items-center">
                    <div class="w-1.5 h-1.5 bg-green-500 rounded-full mr-1.5"></div>
                    {{ aiStore.currentConfig.modelName }}
                  </span>

                  <!-- 显示当前启用的配置 -->
                  <span v-if="aiStore.currentConfig?.name" class="flex items-center">
                    <div class="i-heroicons-cog-6-tooth w-3 h-3 mr-1"></div>
                    {{ aiStore.currentConfig.name }}
                  </span>
                </div>
                <div class="flex items-center space-x-2">
                  <BaseButton v-if="aiStore.currentSession" size="sm" variant="ghost" @click="handleExportChat"
                    class="p-1">
                    <div class="i-heroicons-arrow-down-tray w-3 h-3"></div>
                  </BaseButton>
                </div>
              </div>
            </div>

            <!-- 状态提示 -->
            <div v-if="!aiStore.isConfigured || !aiStore.isEnabled"
              class="mt-4 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-xl">
              <div class="flex items-center">
                <div class="i-heroicons-exclamation-triangle w-5 h-5 text-amber-600 dark:text-amber-400 mr-3"></div>
                <div class="flex-1">
                  <p class="text-sm font-medium text-amber-800 dark:text-amber-200">
                    {{ !aiStore.isConfigured ? '需要配置AI服务' : 'AI服务未启用' }}
                  </p>
                  <p class="text-xs text-amber-700 dark:text-amber-300 mt-1">
                    {{ !aiStore.isConfigured ? '请先配置API密钥和服务端点' : '请在设置中启用AI服务' }}
                  </p>
                </div>
                <BaseButton size="sm" variant="outline" @click="handleOpenSettings" class="ml-4">
                  <div class="i-heroicons-cog-6-tooth w-4 h-4 mr-1"></div>
                  设置
                </BaseButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAiStore } from '@/stores/aiStore'
import type { AiChatSession } from '@/types'
import BaseInput from '@/components/ui/BaseInput.vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import BaseSelect from '@/components/ui/BaseSelect.vue'
import BaseDropdown from '@/components/ui/BaseDropdown.vue'
import DropdownItem from '@/components/ui/DropdownItem.vue'
import LoadingSpinner from '@/components/ui/LoadingSpinner.vue'
import MarkdownPreview from '@/components/knowledge/MarkdownPreview.vue'

// Store and Router
const aiStore = useAiStore()
const router = useRouter()

// 状态
const searchKeyword = ref('')
const messagesContainer = ref<HTMLElement>()
const messageInput = ref<HTMLTextAreaElement>()

// 示例问题
const exampleQuestions = [
  '请介绍一下Vue 3的新特性',
  '如何优化网站的SEO？',
  '解释一下什么是机器学习',
  '推荐一些学习编程的资源',
  '帮我写一个JavaScript函数',
  '什么是响应式设计？'
]

// 计算属性
const filteredSessions = computed(() => {
  if (!searchKeyword.value.trim()) {
    return aiStore.sessions
  }

  const keyword = searchKeyword.value.toLowerCase()
  return aiStore.sessions.filter(session =>
    session.title.toLowerCase().includes(keyword) ||
    session.messages.some(msg => msg.content.toLowerCase().includes(keyword))
  )
})

const totalMessages = computed(() => {
  return aiStore.sessions.reduce((total, session) => total + session.messages.length, 0)
})

// 监听消息变化，自动滚动到底部
watch(() => aiStore.currentMessages, () => {
  nextTick(() => {
    scrollToBottom()
  })
}, { deep: true })

// 生命周期
onMounted(async () => {
  console.log('开始初始化AI Store...')
  await aiStore.initialize()
  console.log('AI Store初始化完成')

  // 等待一段时间确保状态更新
  setTimeout(() => {
    console.log('延迟检查AI Store状态...')
    testInputBox()

    // 确保输入框能正常工作
    nextTick(() => {
      if (messageInput.value) {
        // 设置输入框属性确保可以交互
        messageInput.value.style.pointerEvents = 'auto'
        messageInput.value.style.userSelect = 'text'
        messageInput.value.style.cursor = 'text'
        messageInput.value.style.position = 'relative'
        messageInput.value.style.zIndex = '10'

        // 确保输入框可以接收事件
        messageInput.value.addEventListener('click', (e) => {
          e.stopPropagation()
          messageInput.value?.focus()
        })

        // 如果输入框可用，尝试聚焦
        if (aiStore.canUseInput) {
          messageInput.value.focus()
          console.log('输入框已聚焦')
        } else {
          console.log('输入框不可用，跳过聚焦')
        }
      }
    })
  }, 500)
})

// 方法
const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const formatDate = (date: Date) => {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMinutes < 1) {
    return '刚刚'
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`
  } else if (diffHours < 24) {
    return `${diffHours}小时前`
  } else if (diffDays < 30) {
    return `${diffDays}天前`
  } else {
    // 超过30天显示具体日期
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatTemporaryTime = (session: AiChatSession) => {
  if (!session.isTemporaryChat || !session.expiresAt) {
    return ''
  }

  try {
    const now = new Date()
    // 确保 expiresAt 是 Date 对象，如果是字符串则转换
    const expiresAt = session.expiresAt instanceof Date
      ? session.expiresAt
      : new Date(session.expiresAt)

    // 检查日期是否有效
    if (isNaN(expiresAt.getTime())) {
      return '时间格式错误'
    }

    const remaining = expiresAt.getTime() - now.getTime()

    if (remaining <= 0) {
      return '已过期'
    }

    const hours = Math.floor(remaining / (1000 * 60 * 60))
    const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60))

    if (hours > 0) {
      return `${hours}小时${minutes}分钟后过期`
    } else {
      return `${minutes}分钟后过期`
    }
  } catch (error) {
    console.error('格式化临时对话时间失败:', error)
    return '时间显示错误'
  }
}

const handleNewChat = async (isTemporaryChat: boolean = false) => {
  try {
    await aiStore.createNewSession(undefined, isTemporaryChat)
    searchKeyword.value = ''
    nextTick(() => {
      messageInput.value?.focus()
    })
  } catch (error) {
    console.error('创建新对话失败:', error)
  }
}

const handleSelectSession = async (sessionId: string) => {
  try {
    await aiStore.selectSession(sessionId)
    nextTick(() => {
      scrollToBottom()
      messageInput.value?.focus()
    })
  } catch (error) {
    console.error('选择对话失败:', error)
  }
}

const handleRenameSession = async (session: AiChatSession) => {
  const newTitle = prompt('请输入新的对话标题:', session.title)
  if (newTitle && newTitle.trim() !== session.title) {
    try {
      await aiStore.updateSessionTitle(session.id, newTitle.trim())
    } catch (error) {
      console.error('重命名对话失败:', error)
    }
  }
}

const handleDeleteSession = async (sessionId: string) => {
  if (confirm('确定要删除这个对话吗？此操作不可撤销。')) {
    try {
      await aiStore.deleteSession(sessionId)
    } catch (error) {
      console.error('删除对话失败:', error)
    }
  }
}

const handleClearHistory = async () => {
  if (confirm('确定要清空所有对话历史吗？此操作不可撤销。')) {
    try {
      await aiStore.clearAllHistory()
      searchKeyword.value = ''
    } catch (error) {
      console.error('清空历史失败:', error)
    }
  }
}

const handleExampleQuestion = (question: string) => {
  aiStore.currentMessage = question
  nextTick(() => {
    handleSendMessage()
  })
}

const handleSendMessage = async () => {
  if (!aiStore.canSendMessage) return

  const message = aiStore.currentMessage.trim()
  if (!message) return

  try {
    await aiStore.sendMessage(message)
    aiStore.currentMessage = ''

    // 自动调整输入框高度
    if (messageInput.value) {
      messageInput.value.style.height = '48px'
    }
  } catch (error) {
    console.error('发送消息失败:', error)
  }
}

const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    handleSendMessage()
  }

  // 自动调整输入框高度
  nextTick(() => {
    if (messageInput.value) {
      messageInput.value.style.height = '48px'
      messageInput.value.style.height = messageInput.value.scrollHeight + 'px'
    }
  })
}

const handleOpenSettings = () => {
  // 导航到设置页面的AI配置模块
  router.push('/settings?tab=ai')
}

// 处理AI配置切换
const handleConfigChange = (configId: string | number) => {
  aiStore.selectConfig(String(configId))
}

// 处理模型切换
const handleModelChange = (modelName: string | number) => {
  const success = aiStore.selectModel(String(modelName))
  if (!success) {
    // TODO: 显示错误提示
    console.warn('无法在对话进行中更改模型')
  }
}

// 获取当前模型的显示标签
const getCurrentModelLabel = () => {
  const currentModel = aiStore.currentModelName
  const model = aiStore.availableModels.find((m: any) => m.value === currentModel)
  return model ? model.label : currentModel
}

// 获取会话使用的模型名称
const getSessionModel = (session: AiChatSession) => {
  // 从会话的第一条AI消息中获取模型信息，或者使用默认模型
  const firstAiMessage = session.messages.find(m => m.role === 'assistant')
  if (firstAiMessage && (firstAiMessage as any).model) {
    return (firstAiMessage as any).model
  }

  // 如果没有存储模型信息，尝试从当前配置推断
  if (aiStore.currentConfig?.modelName) {
    return aiStore.currentConfig.modelName
  }

  return null
}

// 处理对话类型转换
const handleConvertToPermanent = async (sessionId: string) => {
  try {
    await aiStore.convertToPermanent(sessionId)
    // TODO: 显示成功通知
    console.log('已转为永久对话')
  } catch (error) {
    // TODO: 显示错误通知
    console.error('转换失败:', error)
  }
}

const handleConvertToTemporary = async (sessionId: string) => {
  try {
    await aiStore.convertToTemporary(sessionId)
    // TODO: 显示成功通知
    console.log('已转为临时对话')
  } catch (error) {
    // TODO: 显示错误通知
    console.error('转换失败:', error)
  }
}

// 处理输入框点击事件
const handleInputClick = (event: Event) => {
  console.log('输入框被点击', event)
  event.stopPropagation()

  const target = event.target as HTMLTextAreaElement

  // 确保输入框获得焦点
  if (target && !target.disabled) {
    target.focus()
    console.log('输入框已聚焦')
  }
}

// 处理输入框聚焦事件
const handleInputFocus = (event: FocusEvent) => {
  console.log('输入框获得焦点')
  event.stopPropagation()

  // 确保输入框可见
  if (messageInput.value) {
    messageInput.value.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
  }
}

// 处理输入框内容变化
const handleInputChange = (event: Event) => {
  const target = event.target as HTMLTextAreaElement

  // 自动调整高度
  target.style.height = 'auto'
  target.style.height = Math.min(target.scrollHeight, 200) + 'px'
}

// 处理输入框失去焦点事件
const handleInputBlur = (event: FocusEvent) => {
  console.log('输入框失去焦点')
}

// 处理容器点击事件
const handleContainerClick = (event: Event) => {
  console.log('容器被点击', event.target)
  // 如果点击的不是输入框本身，则聚焦到输入框
  if (event.target !== messageInput.value) {
    event.preventDefault()
    event.stopPropagation()

    if (messageInput.value && !messageInput.value.disabled) {
      messageInput.value.focus()
      console.log('通过容器点击聚焦输入框')
    }
  }
}

// 测试输入框功能
const testInputBox = () => {
  console.log('测试输入框功能')
  console.log('AI Store 状态:', {
    isConfigured: aiStore.isConfigured,
    isEnabled: aiStore.isEnabled,
    chatStatus: aiStore.chatStatus,
    canUseInput: aiStore.canUseInput,
    canSendMessage: aiStore.canSendMessage,
    currentMessage: aiStore.currentMessage
  })

  if (messageInput.value) {
    console.log('输入框元素:', messageInput.value)
    console.log('输入框样式:', {
      pointerEvents: getComputedStyle(messageInput.value).pointerEvents,
      zIndex: getComputedStyle(messageInput.value).zIndex,
      position: getComputedStyle(messageInput.value).position,
      disabled: messageInput.value.disabled,
      tabIndex: messageInput.value.tabIndex
    })

    // 尝试聚焦
    messageInput.value.focus()
    console.log('聚焦后的状态:', document.activeElement === messageInput.value)
  }
}

const handleExportChat = () => {
  if (!aiStore.currentSession) return

  const content = aiStore.currentSession.messages
    .map(msg => `**${msg.role === 'user' ? '用户' : 'AI'}**: ${msg.content}`)
    .join('\n\n')

  const blob = new Blob([content], { type: 'text/markdown' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${aiStore.currentSession.title}.md`
  a.click()
  URL.revokeObjectURL(url)
}

// 复制消息内容
const copyMessage = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content)
    // TODO: 显示复制成功提示
    console.log('消息已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    // 降级方案：使用传统方法复制
    const textArea = document.createElement('textarea')
    textArea.value = content
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
  }
}

// 重新生成回复
const regenerateResponse = async (message: any) => {
  if (!aiStore.currentSession) return

  try {
    // 找到这条消息的索引
    const messageIndex = aiStore.currentSession.messages.findIndex(m => m.id === message.id)
    if (messageIndex === -1) return

    // 找到对应的用户消息
    let userMessageIndex = messageIndex - 1
    while (userMessageIndex >= 0 && aiStore.currentSession.messages[userMessageIndex].role !== 'user') {
      userMessageIndex--
    }

    if (userMessageIndex >= 0) {
      const userMessage = aiStore.currentSession.messages[userMessageIndex]

      // 删除从用户消息之后的所有消息
      aiStore.currentSession.messages = aiStore.currentSession.messages.slice(0, userMessageIndex + 1)

      // 重新发送用户消息
      await aiStore.sendMessage(userMessage.content)
    }
  } catch (error) {
    console.error('重新生成回复失败:', error)
  }
}
</script>

<style scoped>
.prose {
  font-size: 0.875rem;
}

.prose p {
  margin: 0.5rem 0;
}

.prose p:first-child {
  margin-top: 0;
}

.prose p:last-child {
  margin-bottom: 0;
}
</style>
