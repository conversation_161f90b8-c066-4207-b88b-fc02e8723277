<template>
  <div class="image-host-test">
    <h2>图床管理测试页面</h2>

    <div class="test-buttons">
      <button @click="showModal = true" class="btn btn-primary">
        测试模态窗口
      </button>

      <button @click="showTutorial = true" class="btn btn-secondary">
        测试交互教程
      </button>
    </div>

    <!-- 测试模态窗口 -->
    <Teleport to="body">
      <div v-if="showModal" class="modal-overlay" @click="showModal = false">
        <div class="modal-container" @click.stop>
          <div class="modal-header">
            <h3>测试模态窗口</h3>
            <button @click="showModal = false">×</button>
          </div>
          <div class="modal-body">
            <p>这是一个测试模态窗口，用于验证Teleport是否正常工作。</p>

            <div class="form-field">
              <BaseInput :id="testInputId" v-model="testValue" label="测试输入框" type="text" placeholder="输入测试内容" size="sm"
                clearable />
            </div>
          </div>
          <div class="modal-footer">
            <button @click="showModal = false" class="btn btn-secondary">关闭</button>
          </div>
        </div>
      </div>
    </Teleport>

    <!-- 测试交互教程 -->
    <Teleport to="body">
      <div v-if="showTutorial" class="tutorial-overlay">
        <div class="tutorial-mask" @click="showTutorial = false"></div>

        <div class="tutorial-tooltip" style="left: 50%; top: 50%; transform: translate(-50%, -50%);">
          <div class="tooltip-header">
            <div class="step-indicator">测试教程</div>
            <button @click="showTutorial = false">×</button>
          </div>

          <div class="tooltip-content">
            <h3>测试交互教程</h3>
            <p>这是一个测试交互教程，用于验证教程组件是否正常工作。</p>
          </div>

          <div class="tooltip-actions">
            <button @click="showTutorial = false" class="btn btn-primary">关闭</button>
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BaseInput from '@/components/ui/BaseInput.vue'

const showModal = ref(false)
const showTutorial = ref(false)
const testValue = ref('')
const testInputId = ref(`test-input-${Math.random().toString(36).substring(2, 11)}`)
</script>

<style scoped>
.image-host-test {
  padding: 2rem;
}

.test-buttons {
  display: flex;
  gap: 1rem;
  margin: 2rem 0;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  font-weight: 500;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.modal-container {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-width: 32rem;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.form-field {
  margin: 1rem 0;
}

.form-field label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-field input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
}

.tutorial-overlay {
  position: fixed;
  inset: 0;
  z-index: 9999;
  pointer-events: none;
}

.tutorial-mask {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  pointer-events: auto;
}

.tutorial-tooltip {
  position: absolute;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid #e5e7eb;
  width: 24rem;
  max-width: 90vw;
  pointer-events: auto;
  z-index: 10001;
}

.tooltip-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.tooltip-content {
  padding: 1rem;
}

.tooltip-actions {
  display: flex;
  justify-content: flex-end;
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
}
</style>
