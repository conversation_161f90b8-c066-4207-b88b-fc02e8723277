<template>
  <BaseModal v-model="isVisible" title="分类管理" size="lg">
    <div class="space-y-6">
      <!-- 添加新分类 -->
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">添加新分类</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <BaseInput v-model="newCategory.name" label="分类名称" placeholder="请输入分类名称" required />
          <ParentCategorySelector v-model="newCategory.parent_id" label="父分类" placeholder="选择父分类（可选）"
            :categories="categories" />
        </div>
        <div class="mt-4 flex justify-end">
          <BaseButton @click="handleAddCategory" :disabled="!newCategory.name.trim()">
            <div class="i-heroicons-plus mr-2"></div>
            添加分类
          </BaseButton>
        </div>
      </div>

      <!-- 分类列表 -->
      <div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">分类列表</h3>
        <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg">
          <CategoryManagementTree :categories="categoryTree" @edit="handleEditCategory" @delete="handleDeleteCategory"
            @move="handleMoveCategory" @reorder="handleReorderCategories" />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-between">
        <BaseButton variant="outline" @click="handleRefresh">
          <div class="i-heroicons-arrow-path mr-2"></div>
          刷新
        </BaseButton>
        <BaseButton variant="outline" @click="isVisible = false">
          关闭
        </BaseButton>
      </div>
    </template>
  </BaseModal>
</template>

<script setup lang="ts">
import { ref, computed, watch, provide } from 'vue'
import BaseModal from '@/components/ui/BaseModal.vue'
import BaseInput from '@/components/ui/BaseInput.vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import ParentCategorySelector from '@/components/knowledge/ParentCategorySelector.vue'
import CategoryManagementTree from './CategoryManagementTree.vue'
import { categoryService } from '@/services/categoryService'
import type { Category, CategoryWithChildren } from '@/types'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 数据状态
const categories = ref<Category[]>([])
const categoryTree = ref<CategoryWithChildren[]>([])
const loading = ref(false)

// 新分类表单
const newCategory = ref({
  name: '',
  parent_id: 0
})

// 提供所有分类数据给子组件
provide('allCategories', categories)

// 加载分类数据
const loadCategories = async () => {
  try {
    loading.value = true
    const [allCategories, tree] = await Promise.all([
      categoryService.getAllCategories(),
      categoryService.getCategoryTree()
    ])
    categories.value = allCategories
    categoryTree.value = tree
  } catch (error) {
    console.error('加载分类失败:', error)
  } finally {
    loading.value = false
  }
}

// 添加分类
const handleAddCategory = async () => {
  try {
    await categoryService.createCategory({
      name: newCategory.value.name,
      parent_id: newCategory.value.parent_id,
      sort_order: 999
    })

    // 重置表单
    newCategory.value = {
      name: '',
      parent_id: 0
    }

    // 刷新数据
    await loadCategories()
  } catch (error) {
    console.error('添加分类失败:', error)
    alert('添加分类失败，请重试')
  }
}

// 编辑分类
const handleEditCategory = async (category: Category) => {
  const newName = prompt('请输入新的分类名称:', category.name)
  if (newName && newName.trim() && newName !== category.name) {
    try {
      await categoryService.updateCategory(category.id!, { name: newName.trim() })
      await loadCategories()
    } catch (error) {
      console.error('编辑分类失败:', error)
      alert('编辑分类失败，请重试')
    }
  }
}

// 删除分类
const handleDeleteCategory = async (category: Category) => {
  if (confirm(`确定要删除分类"${category.name}"吗？\n注意：删除后该分类下的资源将移动到"其他"分类。`)) {
    try {
      await categoryService.deleteCategory(category.id!)
      await loadCategories()
    } catch (error) {
      console.error('删除分类失败:', error)
      alert('删除分类失败：' + (error as Error).message)
    }
  }
}

// 移动分类
const handleMoveCategory = async (categoryId: number, newParentId: number) => {
  try {
    await categoryService.moveCategory(categoryId, newParentId)
    await loadCategories()
  } catch (error) {
    console.error('移动分类失败:', error)
    alert('移动分类失败：' + (error as Error).message)
  }
}

// 重新排序分类
const handleReorderCategories = async (data: { categoryIds: number[]; parentId: number }) => {
  try {
    await categoryService.reorderCategories(data.parentId, data.categoryIds)
    await loadCategories()
  } catch (error) {
    console.error('重新排序分类失败:', error)
    alert('重新排序分类失败，请重试')
  }
}

// 刷新数据
const handleRefresh = () => {
  loadCategories()
}

// 监听模态框显示状态
watch(isVisible, (visible) => {
  if (visible) {
    loadCategories()
  }
})
</script>
