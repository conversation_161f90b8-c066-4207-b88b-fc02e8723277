<template>
  <div class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h3 class="modal-title">
          {{ images.length === 1 ? '编辑标签' : `批量编辑标签 (${images.length} 张图片)` }}
        </h3>
        <button @click="$emit('close')" class="modal-close">
          <div class="i-heroicons-x-mark w-6 h-6"></div>
        </button>
      </div>

      <div class="modal-body">
        <!-- 当前标签 -->
        <div class="current-tags-section">
          <h4 class="section-title">当前标签</h4>
          <div v-if="currentTags.length > 0" class="tags-list">
            <div 
              v-for="tag in currentTags" 
              :key="tag"
              class="tag-item current-tag"
            >
              <span class="tag-text">{{ tag }}</span>
              <button 
                @click="removeTag(tag)"
                class="tag-remove"
                title="移除标签"
              >
                <div class="i-heroicons-x-mark w-3 h-3"></div>
              </button>
            </div>
          </div>
          <div v-else class="empty-tags">
            <span class="text-gray-500 dark:text-gray-400">暂无标签</span>
          </div>
        </div>

        <!-- 添加新标签 -->
        <div class="add-tag-section">
          <h4 class="section-title">添加标签</h4>
          <div class="add-tag-input">
            <BaseInput
              v-model="newTag"
              placeholder="输入标签名称..."
              @keyup.enter="addTag"
              class="tag-input"
            />
            <button 
              @click="addTag"
              :disabled="!newTag.trim()"
              class="btn btn-primary btn-sm"
            >
              <div class="i-heroicons-plus w-4 h-4 mr-1"></div>
              添加
            </button>
          </div>
          
          <!-- 标签建议 -->
          <div v-if="suggestedTags.length > 0" class="suggested-tags">
            <h5 class="suggested-title">建议标签</h5>
            <div class="tags-list">
              <button
                v-for="tag in suggestedTags"
                :key="tag"
                @click="addSuggestedTag(tag)"
                class="tag-item suggested-tag"
              >
                <span class="tag-text">{{ tag }}</span>
                <div class="i-heroicons-plus w-3 h-3 ml-1"></div>
              </button>
            </div>
          </div>
        </div>

        <!-- 常用标签 -->
        <div v-if="popularTags.length > 0" class="popular-tags-section">
          <h4 class="section-title">常用标签</h4>
          <div class="tags-list">
            <button
              v-for="tag in popularTags"
              :key="tag"
              @click="addSuggestedTag(tag)"
              class="tag-item popular-tag"
              :disabled="currentTags.includes(tag)"
            >
              <span class="tag-text">{{ tag }}</span>
              <div class="i-heroicons-plus w-3 h-3 ml-1"></div>
            </button>
          </div>
        </div>

        <!-- 批量操作提示 -->
        <div v-if="images.length > 1" class="batch-info">
          <div class="info-card">
            <div class="i-heroicons-information-circle w-5 h-5 text-blue-500 flex-shrink-0"></div>
            <div class="info-content">
              <p class="info-title">批量编辑说明</p>
              <p class="info-text">
                将为选中的 {{ images.length }} 张图片统一设置这些标签。
                现有标签将被完全替换。
              </p>
            </div>
          </div>
        </div>

        <!-- 预览图片 -->
        <div v-if="images.length <= 5" class="preview-images">
          <h4 class="section-title">预览图片</h4>
          <div class="images-grid">
            <div 
              v-for="image in images" 
              :key="image.id"
              class="preview-item"
            >
              <img 
                :src="getPrimaryImageUrl(image)" 
                :alt="image.originalName"
                @error="handleImageError"
                class="preview-thumbnail"
              />
              <span class="preview-name">{{ image.originalName }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="modal-footer">
        <button @click="$emit('close')" class="btn btn-secondary">
          取消
        </button>
        <button 
          @click="saveTags"
          :disabled="saving"
          class="btn btn-primary"
        >
          <div v-if="saving" class="i-heroicons-arrow-path w-4 h-4 animate-spin mr-2"></div>
          {{ saving ? '保存中...' : '保存' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { imageUploadService } from '@/services/imageUploadService'
import type { ImageRecord } from '@/types/imageHost'
import BaseInput from '@/components/ui/BaseInput.vue'

// Props
interface Props {
  images: ImageRecord[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  save: [imageIds: string[], tags: string[]]
}>()

// 响应式数据
const newTag = ref('')
const saving = ref(false)
const allTags = ref<string[]>([])

// 当前标签（如果是单张图片，显示其标签；如果是多张，显示交集）
const currentTags = ref<string[]>([])

// 计算属性
const suggestedTags = computed(() => {
  if (!newTag.value.trim()) return []
  
  const input = newTag.value.toLowerCase()
  return allTags.value
    .filter(tag => 
      tag.toLowerCase().includes(input) && 
      !currentTags.value.includes(tag)
    )
    .slice(0, 5)
})

const popularTags = computed(() => {
  // 统计标签使用频率
  const tagCount = new Map<string, number>()
  
  allTags.value.forEach(tag => {
    tagCount.set(tag, (tagCount.get(tag) || 0) + 1)
  })
  
  // 返回使用频率最高的标签（排除已选中的）
  return Array.from(tagCount.entries())
    .sort((a, b) => b[1] - a[1])
    .map(([tag]) => tag)
    .filter(tag => !currentTags.value.includes(tag))
    .slice(0, 10)
})

// 方法
const initializeTags = async () => {
  try {
    // 获取所有图片记录以统计标签
    const allImages = await imageUploadService.getAllImageRecords()
    const tagSet = new Set<string>()
    
    allImages.forEach(image => {
      image.tags.forEach(tag => tagSet.add(tag))
    })
    
    allTags.value = Array.from(tagSet)
    
    // 初始化当前标签
    if (props.images.length === 1) {
      // 单张图片，显示其标签
      currentTags.value = [...props.images[0].tags]
    } else {
      // 多张图片，显示标签交集
      const firstImageTags = new Set(props.images[0].tags)
      const commonTags = props.images.slice(1).reduce((common, image) => {
        return common.filter(tag => image.tags.includes(tag))
      }, Array.from(firstImageTags))
      
      currentTags.value = commonTags
    }
  } catch (error) {
    console.error('初始化标签失败:', error)
  }
}

const addTag = () => {
  const tag = newTag.value.trim()
  if (!tag || currentTags.value.includes(tag)) return
  
  currentTags.value.push(tag)
  newTag.value = ''
}

const addSuggestedTag = (tag: string) => {
  if (currentTags.value.includes(tag)) return
  
  currentTags.value.push(tag)
}

const removeTag = (tag: string) => {
  const index = currentTags.value.indexOf(tag)
  if (index > -1) {
    currentTags.value.splice(index, 1)
  }
}

const getPrimaryImageUrl = (image: ImageRecord): string => {
  const activeBackup = image.backups.find(backup => backup.status === 'active')
  return activeBackup?.url || image.backups[0]?.url || ''
}

const handleImageError = (e: Event) => {
  const img = e.target as HTMLImageElement
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMyNCA0IDI4IDggMjggMTJDMjggMTYgMjQgMjAgMjAgMjBDMTYgMjAgMTIgMTYgMTIgMTJDMTIgOCAxNiA0IDIwIDRaIiBmaWxsPSIjOUI5QkEwIi8+Cjwvc3ZnPgo='
}

const saveTags = async () => {
  saving.value = true
  
  try {
    const imageIds = props.images.map(image => image.id)
    emit('save', imageIds, currentTags.value)
  } catch (error) {
    console.error('保存标签失败:', error)
    alert('保存标签失败')
  } finally {
    saving.value = false
  }
}

const handleOverlayClick = () => {
  emit('close')
}

// 组件挂载时初始化
onMounted(() => {
  initializeTags()
})
</script>

<style scoped>
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4;
}

.modal-container {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700;
}

.modal-title {
  @apply text-lg font-semibold text-gray-900 dark:text-gray-100;
}

.modal-close {
  @apply p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors;
}

.modal-body {
  @apply p-6 overflow-y-auto max-h-[calc(90vh-200px)] space-y-6;
}

.modal-footer {
  @apply flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700;
}

.section-title {
  @apply text-base font-medium text-gray-900 dark:text-gray-100 mb-3;
}

.tags-list {
  @apply flex flex-wrap gap-2;
}

.tag-item {
  @apply inline-flex items-center px-3 py-1.5 text-sm rounded-lg transition-colors;
}

.current-tag {
  @apply bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300;
}

.suggested-tag {
  @apply bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-900/50;
}

.popular-tag {
  @apply bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600;
  @apply disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-gray-100 dark:disabled:hover:bg-gray-700;
}

.tag-text {
  @apply flex-1;
}

.tag-remove {
  @apply ml-2 p-0.5 hover:bg-primary-200 dark:hover:bg-primary-800 rounded transition-colors;
}

.empty-tags {
  @apply py-4 text-center;
}

.add-tag-input {
  @apply flex space-x-3 mb-4;
}

.tag-input {
  @apply flex-1;
}

.suggested-tags {
  @apply space-y-2;
}

.suggested-title {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}

.batch-info {
  @apply mt-6;
}

.info-card {
  @apply flex space-x-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg;
}

.info-content {
  @apply space-y-1;
}

.info-title {
  @apply text-sm font-medium text-blue-900 dark:text-blue-100;
}

.info-text {
  @apply text-sm text-blue-700 dark:text-blue-300;
}

.preview-images {
  @apply space-y-3;
}

.images-grid {
  @apply grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4;
}

.preview-item {
  @apply space-y-2;
}

.preview-thumbnail {
  @apply w-full aspect-square object-cover rounded-lg border border-gray-200 dark:border-gray-600;
}

.preview-name {
  @apply block text-xs text-gray-600 dark:text-gray-400 truncate;
}

.btn {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-primary {
  @apply text-white bg-primary-600 hover:bg-primary-700 focus:ring-primary-500;
}

.btn-secondary {
  @apply text-gray-700 bg-gray-100 hover:bg-gray-200 focus:ring-gray-500;
  @apply dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}
</style>
