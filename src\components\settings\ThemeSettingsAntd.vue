<template>
  <a-space direction="vertical" size="large" style="width: 100%">
    <!-- 说明信息 -->
    <a-alert
      message="主题配置"
      description="配置应用的外观主题，包括默认主题模式和自定义主题色。设置将在下次启动时生效。"
      type="info"
      show-icon
    />

    <!-- 默认主题模式 -->
    <a-card title="默认主题模式" size="small">
      <a-row :gutter="[16, 16]">
        <a-col
          v-for="mode in themeOptions"
          :key="mode.value"
          :span="8"
        >
          <a-card
            :class="[
              'cursor-pointer transition-all text-center',
              settings.defaultMode === mode.value
                ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                : 'hover:border-primary-300 dark:hover:border-primary-600'
            ]"
            size="small"
            @click="updateDefaultMode(mode.value)"
          >
            <div class="text-center">
              <div :class="[mode.icon, 'w-6 h-6 mx-auto mb-2']"></div>
              <div class="font-medium text-sm">{{ mode.label }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {{ mode.description }}
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </a-card>

    <!-- 自定义主题色 -->
    <a-card size="small">
      <template #title>
        <div class="flex items-center justify-between">
          <span>自定义主题色</span>
          <a-switch
            v-model:checked="settings.useCustomColor"
            @change="handleCustomColorToggle"
            checked-children="开启"
            un-checked-children="关闭"
          />
        </div>
      </template>

      <div v-if="settings.useCustomColor" class="space-y-6">
        <!-- 预设颜色 -->
        <div>
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            预设颜色
          </h4>
          <a-row :gutter="[12, 12]">
            <a-col
              v-for="color in presetColors"
              :key="color.value"
              :span="4"
            >
              <div
                :class="[
                  'w-12 h-12 rounded-lg cursor-pointer border-2 transition-all mx-auto',
                  settings.customPrimaryColor === color.value
                    ? 'border-gray-400 scale-110'
                    : 'border-gray-200 hover:border-gray-300'
                ]"
                :style="{ backgroundColor: color.value }"
                @click="updateCustomColor(color.value)"
                :title="color.name"
              >
                <CheckOutlined
                  v-if="settings.customPrimaryColor === color.value"
                  class="text-white text-lg flex items-center justify-center h-full"
                />
              </div>
            </a-col>
          </a-row>
        </div>

        <!-- 自定义颜色选择器 -->
        <div>
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            自定义颜色
          </h4>
          <a-row :gutter="16" align="middle">
            <a-col :span="12">
              <input
                type="color"
                v-model="settings.customPrimaryColor"
                @change="handleColorChange"
                class="w-full h-10 rounded-lg border border-gray-300 cursor-pointer"
              />
            </a-col>
            <a-col :span="12">
              <a-input
                v-model:value="settings.customPrimaryColor"
                placeholder="#1677ff"
                @change="handleColorInputChange"
              />
            </a-col>
          </a-row>
        </div>

        <!-- 颜色预览 -->
        <div>
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            预览效果
          </h4>
          <a-space>
            <a-button type="primary" :style="{ backgroundColor: settings.customPrimaryColor, borderColor: settings.customPrimaryColor }">
              主要按钮
            </a-button>
            <a-button :style="{ color: settings.customPrimaryColor, borderColor: settings.customPrimaryColor }">
              次要按钮
            </a-button>
            <a-tag :color="settings.customPrimaryColor">
              标签样式
            </a-tag>
          </a-space>
        </div>
      </div>

      <a-empty v-else description="开启自定义主题色以配置个性化颜色" />
    </a-card>

    <!-- 高级设置 -->
    <a-card title="高级设置" size="small">
      <a-space direction="vertical" size="middle" style="width: 100%">
        <a-form-item label="跟随系统主题">
          <a-switch
            v-model:checked="settings.followSystem"
            checked-children="是"
            un-checked-children="否"
          />
          <template #extra>
            <div class="text-sm text-gray-500">自动跟随系统的浅色/暗色模式设置</div>
          </template>
        </a-form-item>

        <a-form-item label="主题切换动画">
          <a-switch
            v-model:checked="settings.enableTransition"
            checked-children="开启"
            un-checked-children="关闭"
          />
          <template #extra>
            <div class="text-sm text-gray-500">主题切换时显示过渡动画效果</div>
          </template>
        </a-form-item>

        <a-form-item label="记住主题选择">
          <a-switch
            v-model:checked="settings.rememberChoice"
            checked-children="是"
            un-checked-children="否"
          />
          <template #extra>
            <div class="text-sm text-gray-500">下次启动时使用上次选择的主题</div>
          </template>
        </a-form-item>
      </a-space>
    </a-card>

    <!-- 操作按钮 -->
    <div class="flex justify-end">
      <a-space>
        <a-button @click="resetToDefaults">
          重置默认
        </a-button>
        <a-button type="primary" :loading="saving" @click="saveSettings">
          {{ saving ? '保存中...' : '保存设置' }}
        </a-button>
      </a-space>
    </div>
  </a-space>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { CheckOutlined } from '@ant-design/icons-vue'

// 状态
const saving = ref(false)

// 设置数据
const settings = reactive({
  defaultMode: 'system',
  useCustomColor: false,
  customPrimaryColor: '#1677ff',
  followSystem: true,
  enableTransition: true,
  rememberChoice: true
})

// 主题选项
const themeOptions = [
  {
    value: 'light',
    label: '浅色模式',
    description: '始终使用浅色主题',
    icon: 'i-heroicons-sun'
  },
  {
    value: 'dark',
    label: '暗色模式',
    description: '始终使用暗色主题',
    icon: 'i-heroicons-moon'
  },
  {
    value: 'system',
    label: '跟随系统',
    description: '根据系统设置自动切换',
    icon: 'i-heroicons-computer-desktop'
  }
]

// 预设颜色
const presetColors = [
  { name: '默认蓝', value: '#1677ff' },
  { name: '科技蓝', value: '#0066cc' },
  { name: '深海蓝', value: '#003d82' },
  { name: '翠绿色', value: '#00b96b' },
  { name: '橙黄色', value: '#fa8c16' },
  { name: '火红色', value: '#ff4d4f' },
  { name: '紫罗兰', value: '#722ed1' },
  { name: '玫瑰红', value: '#eb2f96' },
  { name: '青色', value: '#13c2c2' },
  { name: '金黄色', value: '#faad14' },
  { name: '石墨灰', value: '#434343' },
  { name: '深紫色', value: '#531dab' }
]

// 方法
const updateDefaultMode = (mode: string) => {
  settings.defaultMode = mode
  message.success(`已切换到${themeOptions.find(t => t.value === mode)?.label}`)
}

const handleCustomColorToggle = (checked: boolean) => {
  if (checked) {
    message.info('已开启自定义主题色')
  } else {
    message.info('已关闭自定义主题色')
  }
}

const updateCustomColor = (color: string) => {
  settings.customPrimaryColor = color
  message.success('主题色已更新')
}

const handleColorChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  settings.customPrimaryColor = target.value
}

const handleColorInputChange = () => {
  // 验证颜色格式
  const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
  if (!colorRegex.test(settings.customPrimaryColor)) {
    message.warning('请输入正确的颜色格式，如 #1677ff')
    settings.customPrimaryColor = '#1677ff'
  }
}

const resetToDefaults = () => {
  Object.assign(settings, {
    defaultMode: 'system',
    useCustomColor: false,
    customPrimaryColor: '#1677ff',
    followSystem: true,
    enableTransition: true,
    rememberChoice: true
  })
  message.info('已重置为默认设置')
}

const saveSettings = async () => {
  saving.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('主题设置保存成功!')
  } catch (error) {
    message.error('主题设置保存失败!')
  } finally {
    saving.value = false
  }
}

onMounted(() => {
  // 加载设置
  console.log('主题设置页面已加载')
})
</script>
