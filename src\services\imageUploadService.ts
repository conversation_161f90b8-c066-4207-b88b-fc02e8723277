import ky, { HTTPError, TimeoutError } from 'ky'
import { imageHostService } from './imageHostService'
import type {
  ImageHostConfig,
  UploadResult,
  ImageRecord,
  ImageBackup,
  ImageUploadSettings,
} from '@/types/imageHost'

// 上传进度回调类型
export type UploadProgressCallback = (progress: {
  loaded: number
  total: number
  percentage: number
  speed?: number
  remainingTime?: number
}) => void

class ImageUploadService {
  private readonly STORAGE_KEY = 'image_records'
  private readonly SETTINGS_KEY = 'image_upload_settings'

  // 创建 ky 实例用于上传
  private createUploadApi(config: ImageHostConfig) {
    return ky.create({
      timeout: 30000, // 30秒超时
      retry: {
        limit: 3,
        methods: ['post'],
        statusCodes: [408, 413, 429, 500, 502, 503, 504],
        delay: (attemptCount) => 0.3 * 2 ** (attemptCount - 1) * 1000, // 指数退避
      },
      hooks: {
        beforeRequest: [
          (request) => {
            // 添加通用请求头
            request.headers.set('User-Agent', 'KnowlEdge-ImageUploader/1.0')

            // 添加认证头
            if (config.authType === 'header' && config.authKey) {
              const authValue = config.authPrefix
                ? `${config.authPrefix}${config.authKey}`
                : config.authKey
              request.headers.set(config.authHeader!, authValue)
            }

            // 添加自定义头
            if (config.headers) {
              Object.entries(config.headers).forEach(([key, value]) => {
                request.headers.set(key, value)
              })
            }
          },
        ],
        beforeRetry: [
          ({ request, options, error, retryCount }) => {
            console.log(`🔄 重试上传到 ${config.name} (第${retryCount}次):`, error.message)
          },
        ],
        afterResponse: [
          (request, options, response) => {
            console.log(`✅ 上传到 ${config.name} 响应:`, response.status)
          },
        ],
      },
    })
  }

  // 默认上传设置
  private defaultSettings: ImageUploadSettings = {
    backupCount: 2, // 默认备份到2个图床
    defaultExpiration: 0, // 默认永久保存
    notifyBefore: 7, // 提前7天通知过期
    autoRetry: true, // 自动重试
    maxRetries: 3, // 最大重试3次
    retryDelay: 2, // 重试延迟2秒
  }

  // 获取上传设置
  getSettings(): ImageUploadSettings {
    try {
      const stored = localStorage.getItem(this.SETTINGS_KEY)
      if (!stored) return this.defaultSettings

      return { ...this.defaultSettings, ...JSON.parse(stored) }
    } catch (error) {
      console.error('获取上传设置失败:', error)
      return this.defaultSettings
    }
  }

  // 保存上传设置
  saveSettings(settings: ImageUploadSettings): void {
    try {
      localStorage.setItem(this.SETTINGS_KEY, JSON.stringify(settings))
    } catch (error) {
      console.error('保存上传设置失败:', error)
      throw new Error('保存设置失败')
    }
  }

  // 上传图片（支持故障转移和进度监控）
  async uploadImage(file: File, onProgress?: UploadProgressCallback): Promise<ImageRecord> {
    const settings = this.getSettings()
    const enabledConfigs = await imageHostService.getEnabledConfigs()

    if (enabledConfigs.length === 0) {
      throw new Error('没有可用的图床配置')
    }

    // 验证文件
    this.validateFile(file)

    // 随机选择起始配置，避免总是使用同一个
    const startIndex = Math.floor(Math.random() * enabledConfigs.length)
    const orderedConfigs = [
      ...enabledConfigs.slice(startIndex),
      ...enabledConfigs.slice(0, startIndex),
    ]

    // 计算需要备份的数量
    const backupCount = Math.min(settings.backupCount, enabledConfigs.length)
    const selectedConfigs = orderedConfigs.slice(0, backupCount)

    const backups: ImageBackup[] = []
    let primaryUpload: UploadResult | null = null
    let lastError: Error | null = null

    // 依次上传到选定的图床
    for (let i = 0; i < selectedConfigs.length; i++) {
      const config = selectedConfigs[i]
      let retryCount = 0

      while (retryCount <= settings.maxRetries) {
        try {
          console.log(`尝试上传到: ${config.name} (第${retryCount + 1}次)`)
          const result = await this.uploadToHost(file, config, onProgress)

          const backup: ImageBackup = {
            id: `backup_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
            hostConfigId: config.id,
            hostName: config.name,
            hostProvider: config.provider,
            url: result.url,
            uploadTime: result.uploadTime,
            status: 'active',
            lastChecked: new Date(),
          }

          backups.push(backup)

          // 第一个成功的作为主要上传
          if (!primaryUpload) {
            primaryUpload = result
          }

          console.log(`上传成功: ${config.name}`)
          break // 成功后跳出重试循环
        } catch (error) {
          console.warn(`上传失败 ${config.name} (第${retryCount + 1}次):`, error)
          lastError = error as Error
          retryCount++

          if (retryCount <= settings.maxRetries && settings.autoRetry) {
            // 等待后重试
            await new Promise((resolve) => setTimeout(resolve, settings.retryDelay * 1000))
          }
        }
      }
    }

    if (backups.length === 0) {
      throw new Error(`所有图床上传失败，最后错误: ${lastError?.message}`)
    }

    // 创建图片记录
    const imageRecord: ImageRecord = {
      id: `img_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      originalName: file.name,
      filename: primaryUpload!.url.split('/').pop() || file.name,
      size: file.size,
      format: file.type.split('/')[1] || file.name.split('.').pop() || 'unknown',
      uploadTime: new Date(),
      tags: [],
      backups,
      expirationSettings: {
        enabled: settings.defaultExpiration > 0,
        expiresAt:
          settings.defaultExpiration > 0
            ? new Date(Date.now() + settings.defaultExpiration * 24 * 60 * 60 * 1000)
            : undefined,
        notifyBefore: settings.notifyBefore,
      },
      status: 'active',
      lastChecked: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    // 获取图片尺寸
    try {
      const dimensions = await this.getImageDimensions(file)
      imageRecord.width = dimensions.width
      imageRecord.height = dimensions.height
    } catch (error) {
      console.warn('获取图片尺寸失败:', error)
    }

    // 保存记录
    await this.saveImageRecord(imageRecord)

    return imageRecord
  }

  // 批量上传图片
  async uploadMultiple(files: File[]): Promise<ImageRecord[]> {
    const results: ImageRecord[] = []
    const errors: string[] = []

    for (const file of files) {
      try {
        const result = await this.uploadImage(file)
        results.push(result)
      } catch (error) {
        console.error(`上传文件 ${file.name} 失败:`, error)
        errors.push(`${file.name}: ${(error as Error).message}`)
      }
    }

    if (errors.length > 0) {
      console.warn('部分文件上传失败:', errors)
    }

    return results
  }

  // 上传到指定图床（使用 ky 和进度监控）
  private async uploadToHost(
    file: File,
    config: ImageHostConfig,
    onProgress?: UploadProgressCallback,
  ): Promise<UploadResult> {
    // 文件验证
    this.validateFile(file, config)

    // 创建专用的 API 实例
    const api = this.createUploadApi(config)

    // 构建 FormData
    const formData = new FormData()
    formData.append(config.fileField, file)

    // 添加额外参数
    if (config.params) {
      Object.entries(config.params).forEach(([key, value]) => {
        formData.append(key, String(value))
      })
    }

    // 构建 URL
    let uploadUrl = config.apiUrl
    if (config.authType === 'query' && config.authKey) {
      uploadUrl += `/${config.authKey}`
    }

    try {
      console.log(`📤 开始上传到 ${config.name}:`, file.name)

      // 如果需要进度监控，使用 XMLHttpRequest
      if (onProgress) {
        return this.uploadWithProgress(uploadUrl, formData, config, file, onProgress)
      }

      // 否则使用 ky 进行快速上传
      const response = await api.post(uploadUrl, {
        body: formData,
      })

      console.log(`✅ 上传成功到 ${config.name}`)

      // 解析响应
      return this.parseKyResponse(response, config, file)
    } catch (error) {
      console.error(`❌ 上传失败到 ${config.name}:`, error)

      if (error instanceof HTTPError) {
        throw new Error(`HTTP ${error.response.status}: ${error.response.statusText}`)
      } else if (error instanceof TimeoutError) {
        throw new Error(`上传超时: ${config.name}`)
      } else {
        throw error
      }
    }
  }

  // 使用 XMLHttpRequest 进行带进度监控的上传
  private uploadWithProgress(
    url: string,
    formData: FormData,
    config: ImageHostConfig,
    file: File,
    onProgress: UploadProgressCallback,
  ): Promise<UploadResult> {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest()
      const startTime = Date.now()
      let lastLoaded = 0
      let lastTime = startTime

      // 上传进度监控
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const currentTime = Date.now()
          const timeDiff = currentTime - lastTime
          const loadedDiff = event.loaded - lastLoaded

          // 计算上传速度 (bytes/second)
          const speed = timeDiff > 0 ? (loadedDiff / timeDiff) * 1000 : 0

          // 计算剩余时间 (seconds)
          const remainingBytes = event.total - event.loaded
          const remainingTime = speed > 0 ? remainingBytes / speed : 0

          const progress = {
            loaded: event.loaded,
            total: event.total,
            percentage: Math.round((event.loaded / event.total) * 100),
            speed: Math.round(speed),
            remainingTime: Math.round(remainingTime),
          }

          console.log(`📊 上传进度 ${config.name}: ${progress.percentage}% (${progress.speed} B/s)`)
          onProgress(progress)

          lastLoaded = event.loaded
          lastTime = currentTime
        }
      })

      // 上传完成
      xhr.addEventListener('load', async () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const responseText = xhr.responseText
            const data = config.responseType === 'json' ? JSON.parse(responseText) : responseText

            // 解析响应获取URL
            const result = this.parseUploadResponse(data, config, file)
            console.log(`✅ 上传完成到 ${config.name}:`, result.url)
            resolve(result)
          } catch (error) {
            console.error(`❌ 解析响应失败 ${config.name}:`, error)
            reject(
              new Error(`解析响应失败: ${error instanceof Error ? error.message : '未知错误'}`),
            )
          }
        } else {
          console.error(`❌ 上传失败 ${config.name}: HTTP ${xhr.status}`)
          reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`))
        }
      })

      // 上传错误
      xhr.addEventListener('error', () => {
        console.error(`❌ 网络错误 ${config.name}`)
        reject(new Error(`网络错误: ${config.name}`))
      })

      // 上传超时
      xhr.addEventListener('timeout', () => {
        console.error(`❌ 上传超时 ${config.name}`)
        reject(new Error(`上传超时: ${config.name}`))
      })

      // 配置请求
      xhr.open('POST', url)
      xhr.timeout = 30000 // 30秒超时

      // 添加请求头
      if (config.authType === 'header' && config.authKey) {
        const authValue = config.authPrefix
          ? `${config.authPrefix}${config.authKey}`
          : config.authKey
        xhr.setRequestHeader(config.authHeader!, authValue)
      }

      if (config.headers) {
        Object.entries(config.headers).forEach(([key, value]) => {
          xhr.setRequestHeader(key, value)
        })
      }

      // 发送请求
      xhr.send(formData)
    })
  }

  // 解析上传响应
  private parseUploadResponse(data: any, config: ImageHostConfig, file: File): UploadResult {
    if (config.responseType === 'json') {
      // 检查是否成功
      if (config.successField) {
        const success = this.getNestedValue(data, config.successField)
        if (success !== config.successValue) {
          const error = config.errorField
            ? this.getNestedValue(data, config.errorField)
            : '上传失败'
          throw new Error(error)
        }
      }

      // 提取图片URL
      const url = this.getNestedValue(data, config.urlField)
      if (!url) {
        throw new Error('响应中未找到图片URL')
      }

      return {
        url,
        originalName: file.name,
        size: file.size,
        uploadTime: new Date(),
        hostName: config.name,
        hostProvider: config.provider,
        rawResponse: data,
      }
    } else {
      // 文本响应直接作为URL
      return {
        url: data.trim(),
        originalName: file.name,
        size: file.size,
        uploadTime: new Date(),
        hostName: config.name,
        hostProvider: config.provider,
        rawResponse: data,
      }
    }
  }

  // 构建请求
  private buildRequest(file: File, config: ImageHostConfig) {
    const formData = new FormData()
    formData.append(config.fileField, file)

    // 添加额外参数
    if (config.params) {
      Object.entries(config.params).forEach(([key, value]) => {
        formData.append(key, String(value))
      })
    }

    // 构建URL
    let url = config.apiUrl
    if (config.authType === 'query' && config.authKey) {
      url += `/${config.authKey}`
    }

    // 构建请求头
    const headers: Record<string, string> = {
      ...config.headers,
    }

    // 添加认证头
    if (config.authType === 'header' && config.authKey) {
      const authValue = config.authPrefix ? `${config.authPrefix}${config.authKey}` : config.authKey
      headers[config.authHeader!] = authValue
    }

    return {
      url,
      options: {
        method: config.method,
        headers,
        body: formData,
      },
    }
  }

  // 解析 ky 响应
  private async parseKyResponse(
    response: Response,
    config: ImageHostConfig,
    file: File,
  ): Promise<UploadResult> {
    const data = config.responseType === 'json' ? await response.json() : await response.text()

    if (config.responseType === 'json') {
      // 检查是否成功
      if (config.successField) {
        const success = this.getNestedValue(data, config.successField)
        if (success !== config.successValue) {
          const error = config.errorField
            ? this.getNestedValue(data, config.errorField)
            : '上传失败'
          throw new Error(error)
        }
      }

      // 提取图片URL
      const url = this.getNestedValue(data, config.urlField)
      if (!url) {
        throw new Error('响应中未找到图片URL')
      }

      return {
        url,
        originalName: file.name,
        size: file.size,
        uploadTime: new Date(),
        hostName: config.name,
        hostProvider: config.provider,
        rawResponse: data,
      }
    } else {
      // 文本响应直接作为URL
      return {
        url: data.trim(),
        originalName: file.name,
        size: file.size,
        uploadTime: new Date(),
        hostName: config.name,
        hostProvider: config.provider,
        rawResponse: data,
      }
    }
  }

  // 解析响应（保留原方法用于兼容）
  private async parseResponse(
    response: Response,
    config: ImageHostConfig,
    file: File,
  ): Promise<UploadResult> {
    const data = config.responseType === 'json' ? await response.json() : await response.text()

    if (config.responseType === 'json') {
      // 检查是否成功
      if (config.successField) {
        const success = this.getNestedValue(data, config.successField)
        if (success !== config.successValue) {
          const error = config.errorField
            ? this.getNestedValue(data, config.errorField)
            : '上传失败'
          throw new Error(error)
        }
      }

      // 提取图片URL
      const url = this.getNestedValue(data, config.urlField)
      if (!url) {
        throw new Error('响应中未找到图片URL')
      }

      return {
        url,
        originalName: file.name,
        size: file.size,
        uploadTime: new Date(),
        hostName: config.name,
        hostProvider: config.provider,
        rawResponse: data,
      }
    } else {
      // 文本响应直接作为URL
      return {
        url: data.trim(),
        originalName: file.name,
        size: file.size,
        uploadTime: new Date(),
        hostName: config.name,
        hostProvider: config.provider,
        rawResponse: data,
      }
    }
  }

  // 获取嵌套对象值
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }

  // 验证文件
  private validateFile(file: File, config?: ImageHostConfig) {
    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      throw new Error('只支持图片文件')
    }

    // 检查文件大小（通用限制：100MB）
    const maxSize = config?.maxFileSize || 100
    if (file.size > maxSize * 1024 * 1024) {
      throw new Error(`文件大小超过限制 ${maxSize}MB`)
    }

    // 检查文件格式
    if (config?.allowedFormats) {
      const extension = file.name.split('.').pop()?.toLowerCase()
      if (!extension || !config.allowedFormats.includes(extension)) {
        throw new Error(`不支持的文件格式，支持: ${config.allowedFormats.join(', ')}`)
      }
    }
  }

  // 获取图片尺寸
  private getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image()
      const url = URL.createObjectURL(file)

      img.onload = () => {
        URL.revokeObjectURL(url)
        resolve({ width: img.naturalWidth, height: img.naturalHeight })
      }

      img.onerror = () => {
        URL.revokeObjectURL(url)
        reject(new Error('无法获取图片尺寸'))
      }

      img.src = url
    })
  }

  // 保存图片记录
  private async saveImageRecord(record: ImageRecord): Promise<void> {
    try {
      const records = await this.getAllImageRecords()
      records.push(record)
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(records))
    } catch (error) {
      console.error('保存图片记录失败:', error)
      throw new Error('保存图片记录失败')
    }
  }

  // 获取所有图片记录
  async getAllImageRecords(): Promise<ImageRecord[]> {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (!stored) return []

      const records = JSON.parse(stored) as ImageRecord[]
      return records.map((record) => ({
        ...record,
        uploadTime: new Date(record.uploadTime),
        expirationSettings: {
          ...record.expirationSettings,
          expiresAt: record.expirationSettings.expiresAt
            ? new Date(record.expirationSettings.expiresAt)
            : undefined,
        },
        lastChecked: record.lastChecked ? new Date(record.lastChecked) : undefined,
        createdAt: new Date(record.createdAt),
        updatedAt: new Date(record.updatedAt),
      }))
    } catch (error) {
      console.error('获取图片记录失败:', error)
      return []
    }
  }
}

export const imageUploadService = new ImageUploadService()
