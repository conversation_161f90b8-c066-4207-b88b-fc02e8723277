<template>
  <button type="button" :class="itemClasses" :disabled="disabled" @click="handleClick">
    <div v-if="icon" :class="[icon, 'mr-3']"></div>
    <span class="flex-1 text-left">{{ text }}</span>
    <div v-if="shortcut" class="text-xs text-gray-400 ml-3">{{ shortcut }}</div>
    <div v-if="hasSubmenu" class="i-heroicons-chevron-right ml-3"></div>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  text: string
  icon?: string
  disabled?: boolean
  danger?: boolean
  shortcut?: string
  hasSubmenu?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  danger: false,
  hasSubmenu: false
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const itemClasses = computed(() => {
  const baseClasses = [
    'w-full flex items-center px-4 py-2 text-sm transition-colors duration-150',
    'focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700',
    'disabled:opacity-50 disabled:cursor-not-allowed'
  ]

  if (props.disabled) {
    baseClasses.push('cursor-not-allowed')
  } else if (props.danger) {
    baseClasses.push(
      'text-red-600 dark:text-red-400',
      'hover:bg-red-50 dark:hover:bg-red-900/20',
      'focus:bg-red-50 dark:focus:bg-red-900/20'
    )
  } else {
    baseClasses.push(
      'text-gray-700 dark:text-gray-300',
      'hover:bg-gray-100 dark:hover:bg-gray-700'
    )
  }

  return baseClasses
})

const handleClick = (event: MouseEvent) => {
  if (!props.disabled) {
    emit('click', event)
  }
}
</script>
