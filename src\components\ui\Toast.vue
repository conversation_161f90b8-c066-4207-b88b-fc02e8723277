<template>
  <Teleport to="body">
    <Transition enter-active-class="transition-all duration-300 ease-out" enter-from-class="opacity-0 translate-x-full"
      enter-to-class="opacity-100 translate-x-0" leave-active-class="transition-all duration-300 ease-in"
      leave-from-class="opacity-100 translate-x-0" leave-to-class="opacity-0 translate-x-full">
      <div v-if="visible" :class="[
        'fixed top-4 right-4 z-50 max-w-sm w-full',
        'bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700',
        'p-4 flex items-start space-x-3'
      ]">
        <!-- 图标 -->
        <div :class="[
          'flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center',
          typeClasses.bg
        ]">
          <div :class="[typeClasses.icon, 'w-4 h-4']"></div>
        </div>

        <!-- 内容 -->
        <div class="flex-1 min-w-0">
          <p :class="['text-sm font-medium', typeClasses.title]">
            {{ title }}
          </p>
          <p v-if="message" class="mt-1 text-sm text-gray-500 dark:text-gray-400 break-all">
            {{ message }}
          </p>

          <!-- 操作按钮 -->
          <div v-if="actions && actions.length > 0" class="mt-3 flex space-x-2">
            <button v-for="action in actions" :key="action.text" @click="action.action" :class="[
              'px-3 py-1 text-xs font-medium rounded transition-colors',
              action.variant === 'primary'
                ? 'bg-primary-600 text-white hover:bg-primary-700'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            ]">
              {{ action.text }}
            </button>
          </div>

          <!-- 进度条 -->
          <div v-if="showProgress && !persistent" class="mt-2">
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
              <div class="bg-current h-1 rounded-full transition-all duration-100"
                :style="{ width: `${progressPercent}%` }"></div>
            </div>
          </div>
        </div>

        <!-- 关闭按钮 -->
        <button @click="close"
          class="flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
          <div class="i-heroicons-x-mark w-4 h-4"></div>
        </button>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface ActionButton {
  text: string
  action: () => void
  variant?: 'primary' | 'secondary'
}

interface Props {
  type?: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  autoClose?: boolean
  showProgress?: boolean
  persistent?: boolean
  actions?: ActionButton[]
}

const props = withDefaults(defineProps<Props>(), {
  type: 'info',
  duration: 4000,
  autoClose: true,
  showProgress: false,
  persistent: false
})

const emit = defineEmits<{
  close: []
}>()

const visible = ref(false)
const startTime = ref(0)
const currentTime = ref(0)
let timer: ReturnType<typeof setTimeout> | null = null
let progressTimer: ReturnType<typeof setInterval> | null = null

const typeClasses = computed(() => {
  const classes = {
    success: {
      bg: 'bg-green-100 dark:bg-green-900/30',
      icon: 'i-heroicons-check text-green-600 dark:text-green-400',
      title: 'text-green-800 dark:text-green-200'
    },
    error: {
      bg: 'bg-red-100 dark:bg-red-900/30',
      icon: 'i-heroicons-x-mark text-red-600 dark:text-red-400',
      title: 'text-red-800 dark:text-red-200'
    },
    warning: {
      bg: 'bg-yellow-100 dark:bg-yellow-900/30',
      icon: 'i-heroicons-exclamation-triangle text-yellow-600 dark:text-yellow-400',
      title: 'text-yellow-800 dark:text-yellow-200'
    },
    info: {
      bg: 'bg-blue-100 dark:bg-blue-900/30',
      icon: 'i-heroicons-information-circle text-blue-600 dark:text-blue-400',
      title: 'text-blue-800 dark:text-blue-200'
    }
  }
  return classes[props.type]
})

const progressPercent = computed(() => {
  if (!props.showProgress || props.persistent || !props.autoClose) return 0
  if (startTime.value === 0) return 100

  const elapsed = currentTime.value - startTime.value
  const remaining = Math.max(0, props.duration - elapsed)
  return Math.round((remaining / props.duration) * 100)
})

const show = () => {
  visible.value = true

  if (props.autoClose && props.duration > 0 && !props.persistent) {
    startTime.value = Date.now()
    currentTime.value = startTime.value

    // 设置自动关闭定时器
    timer = setTimeout(() => {
      close()
    }, props.duration)

    // 设置进度更新定时器
    if (props.showProgress) {
      progressTimer = setInterval(() => {
        currentTime.value = Date.now()
      }, 50)
    }
  }
}

const close = () => {
  visible.value = false

  if (timer) {
    clearTimeout(timer)
    timer = null
  }

  if (progressTimer) {
    clearInterval(progressTimer)
    progressTimer = null
  }

  emit('close')
}

onMounted(() => {
  show()
})
</script>
