<template>
  <div style="padding: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 80vh; color: white;">
    <div style="max-width: 1200px; margin: 0 auto; text-align: center;">
      <h1 style="font-size: 48px; font-weight: bold; margin-bottom: 20px;">
        欢迎使用 KnowlEdge
      </h1>
      <p style="font-size: 20px; margin-bottom: 40px; opacity: 0.9;">
        您的个人知识管理系统
      </p>
      
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; margin-top: 60px;">
        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 12px; backdrop-filter: blur(10px);">
          <h3 style="font-size: 24px; margin-bottom: 15px;">📚 知识库</h3>
          <p style="opacity: 0.8; margin-bottom: 20px;">管理和浏览您的知识资源</p>
          <button 
            @click="goToKnowledge" 
            style="background: white; color: #667eea; border: none; padding: 12px 24px; border-radius: 6px; font-weight: bold; cursor: pointer;"
          >
            进入知识库
          </button>
        </div>
        
        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 12px; backdrop-filter: blur(10px);">
          <h3 style="font-size: 24px; margin-bottom: 15px;">🖼️ 图床管理</h3>
          <p style="opacity: 0.8; margin-bottom: 20px;">管理您的图片资源</p>
          <button 
            @click="goToImageGallery" 
            style="background: white; color: #667eea; border: none; padding: 12px 24px; border-radius: 6px; font-weight: bold; cursor: pointer;"
          >
            图床管理
          </button>
        </div>
        
        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 12px; backdrop-filter: blur(10px);">
          <h3 style="font-size: 24px; margin-bottom: 15px;">🎨 样式展示</h3>
          <p style="opacity: 0.8; margin-bottom: 20px;">查看组件样式展示</p>
          <button 
            @click="goToShowcase" 
            style="background: white; color: #667eea; border: none; padding: 12px 24px; border-radius: 6px; font-weight: bold; cursor: pointer;"
          >
            样式展示
          </button>
        </div>
      </div>
      
      <div style="margin-top: 60px; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 8px;">
        <p style="font-size: 14px; opacity: 0.7;">
          当前时间: {{ currentTime }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const currentTime = ref('')
let timer: number | null = null

onMounted(() => {
  console.log('SimpleHomeView 组件已挂载')
  updateTime()
  timer = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

const goToKnowledge = () => {
  console.log('导航到知识库')
  router.push('/knowledge')
}

const goToImageGallery = () => {
  console.log('导航到图床管理')
  router.push('/image-gallery')
}

const goToShowcase = () => {
  console.log('导航到样式展示')
  router.push('/component-showcase')
}
</script>
