<template>
  <a-modal v-model:open="isOpen" :title="configId ? '编辑AI配置' : '新建AI配置'" width="600px" @cancel="handleClose"
    :footer="null">
    <a-form :model="form" :rules="formRules" layout="vertical" @finish="handleSubmit">
      <!-- 配置名称 -->
      <a-form-item label="配置名称" name="name">
        <a-input v-model:value="form.name" placeholder="请输入配置名称，如：OpenAI GPT-4" />
        <template #extra>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            用于识别不同的AI服务配置
          </div>
        </template>
      </a-form-item>

      <!-- 服务提供商选择 -->
      <a-form-item label="服务提供商" name="provider">
        <a-select v-model:value="form.provider" :options="providerOptions" @change="handleProviderChange" />
      </a-form-item>

      <!-- API Key -->
      <a-form-item label="API Key" name="apiKey">
        <a-input-password v-model:value="form.apiKey" placeholder="请输入API Key" />
        <template #extra>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            您的API密钥将安全存储在本地浏览器中
          </div>
        </template>
      </a-form-item>

      <!-- Base URL -->
      <a-form-item label="API端点" name="baseUrl">
        <a-input v-model:value="form.baseUrl" placeholder="https://api.openai.com/v1" />
        <template #extra>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            API服务的基础URL地址
          </div>
        </template>
      </a-form-item>

      <!-- 模型名称 -->
      <a-form-item label="模型名称" name="model">
        <a-select v-model:value="form.model" :options="modelOptions" show-search placeholder="选择或输入模型名称"
          :filter-option="filterOption" />
        <template #extra>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            选择要使用的AI模型，如 gpt-4、gpt-3.5-turbo 等
          </div>
        </template>
      </a-form-item>

      <!-- 高级设置 -->
      <a-collapse ghost>
        <a-collapse-panel key="advanced" header="高级设置">
          <a-space direction="vertical" size="middle" style="width: 100%">
            <!-- 温度设置 -->
            <a-form-item label="温度 (Temperature)" name="temperature">
              <a-row :gutter="16">
                <a-col :span="16">
                  <a-slider v-model:value="form.temperature" :min="0" :max="2" :step="0.1"
                    :tooltip-formatter="(value) => `${value}`" />
                </a-col>
                <a-col :span="8">
                  <a-input-number v-model:value="form.temperature" :min="0" :max="2" :step="0.1" style="width: 100%" />
                </a-col>
              </a-row>
              <template #extra>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  控制输出的随机性，0表示确定性输出，2表示高随机性
                </div>
              </template>
            </a-form-item>

            <!-- 最大令牌数 -->
            <a-form-item label="最大令牌数" name="maxTokens">
              <a-input-number v-model:value="form.maxTokens" :min="1" :max="8192" style="width: 100%"
                placeholder="4096" />
              <template #extra>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  限制AI响应的最大长度，留空使用模型默认值
                </div>
              </template>
            </a-form-item>

            <!-- 系统提示词 -->
            <a-form-item label="系统提示词" name="systemPrompt">
              <a-textarea v-model:value="form.systemPrompt" :rows="4" placeholder="你是一个有用的AI助手..." show-count
                :maxlength="1000" />
              <template #extra>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  定义AI的角色和行为方式
                </div>
              </template>
            </a-form-item>

            <!-- 超时设置 -->
            <a-form-item label="请求超时" name="timeout">
              <a-space>
                <a-input-number v-model:value="form.timeout" :min="5" :max="300" style="width: 120px" />
                <span class="text-sm text-gray-500 dark:text-gray-400">秒</span>
              </a-space>
              <template #extra>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  API请求的超时时间
                </div>
              </template>
            </a-form-item>
          </a-space>
        </a-collapse-panel>
      </a-collapse>

      <!-- 设为默认配置 -->
      <a-form-item>
        <a-checkbox v-model:checked="form.isDefault">
          设为默认配置
        </a-checkbox>
        <template #extra>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            将此配置设为默认的AI服务配置
          </div>
        </template>
      </a-form-item>

      <!-- 操作按钮 -->
      <a-form-item>
        <a-space>
          <a-button @click="handleClose">
            取消
          </a-button>
          <a-button @click="handleTest" :loading="testing">
            {{ testing ? '测试中...' : '测试连接' }}
          </a-button>
          <a-button type="primary" html-type="submit" :loading="saving">
            {{ saving ? '保存中...' : '保存配置' }}
          </a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'

interface Props {
  modelValue?: boolean
  configId?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  close: []
  save: [config: any]
  saved: []
}>()

// 状态
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})
const saving = ref(false)
const testing = ref(false)

// 表单数据
const form = reactive({
  name: '',
  provider: 'openai',
  apiKey: '',
  baseUrl: 'https://api.openai.com/v1',
  model: 'gpt-3.5-turbo',
  temperature: 0.7,
  maxTokens: 4096,
  systemPrompt: '你是一个有用的AI助手，请用中文回答问题。',
  timeout: 30,
  isDefault: false
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入配置名称', trigger: 'blur' },
    { min: 2, max: 50, message: '配置名称长度应为2-50个字符', trigger: 'blur' }
  ],
  apiKey: [
    { required: true, message: '请输入API Key', trigger: 'blur' }
  ],
  baseUrl: [
    { required: true, message: '请输入API端点', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ],
  model: [
    { required: true, message: '请选择模型', trigger: 'change' }
  ]
}

// 选项数据
const providerOptions = [
  { label: 'OpenAI', value: 'openai' },
  { label: 'Azure OpenAI', value: 'azure' },
  { label: 'Anthropic Claude', value: 'anthropic' },
  { label: 'Google Gemini', value: 'google' },
  { label: '其他', value: 'custom' }
]

const modelOptions = computed(() => {
  switch (form.provider) {
    case 'openai':
      return [
        { label: 'GPT-4', value: 'gpt-4' },
        { label: 'GPT-4 Turbo', value: 'gpt-4-turbo-preview' },
        { label: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo' },
        { label: 'GPT-3.5 Turbo 16K', value: 'gpt-3.5-turbo-16k' }
      ]
    case 'anthropic':
      return [
        { label: 'Claude 3 Opus', value: 'claude-3-opus-20240229' },
        { label: 'Claude 3 Sonnet', value: 'claude-3-sonnet-20240229' },
        { label: 'Claude 3 Haiku', value: 'claude-3-haiku-20240307' }
      ]
    case 'google':
      return [
        { label: 'Gemini Pro', value: 'gemini-pro' },
        { label: 'Gemini Pro Vision', value: 'gemini-pro-vision' }
      ]
    default:
      return []
  }
})

// 方法
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const handleProviderChange = (value: string) => {
  // 根据提供商更新默认配置
  switch (value) {
    case 'openai':
      form.baseUrl = 'https://api.openai.com/v1'
      form.model = 'gpt-3.5-turbo'
      break
    case 'azure':
      form.baseUrl = 'https://your-resource.openai.azure.com'
      form.model = 'gpt-35-turbo'
      break
    case 'anthropic':
      form.baseUrl = 'https://api.anthropic.com'
      form.model = 'claude-3-sonnet-20240229'
      break
    case 'google':
      form.baseUrl = 'https://generativelanguage.googleapis.com'
      form.model = 'gemini-pro'
      break
  }
}

const handleTest = async () => {
  testing.value = true
  try {
    // 模拟测试连接
    await new Promise(resolve => setTimeout(resolve, 2000))
    message.success('连接测试成功!')
  } catch (error) {
    message.error('连接测试失败，请检查配置!')
  } finally {
    testing.value = false
  }
}

const handleSubmit = async () => {
  saving.value = true
  try {
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('配置保存成功!')
    emit('save', { ...form })
    emit('saved')
    handleClose()
  } catch (error) {
    message.error('配置保存失败!')
  } finally {
    saving.value = false
  }
}

const handleClose = () => {
  isOpen.value = false
  emit('close')
}

// 暴露方法
const open = () => {
  isOpen.value = true
}

defineExpose({
  open
})
</script>
