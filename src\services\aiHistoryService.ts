import type { AiChatSession, AiMessage } from '@/types'
import { compressJson, decompressJson, smartParseJson } from '@/utils/compression'

/**
 * AI历史记录服务
 * 负责对话历史的本地存储、检索和管理
 */
class AiHistoryService {
  private readonly STORAGE_KEY = 'ai_chat_sessions'
  private readonly COMPRESSED_STORAGE_KEY = 'ai_chat_sessions_compressed'
  private readonly MAX_SESSIONS = 100 // 最大保存会话数
  private readonly MAX_MESSAGES_PER_SESSION = 1000 // 每个会话最大消息数
  private readonly USE_COMPRESSION = true // 启用压缩存储

  /**
   * 获取所有对话会话
   */
  async getAllSessions(): Promise<AiChatSession[]> {
    try {
      let stored: string | null = null

      // 优先尝试从压缩存储读取
      if (this.USE_COMPRESSION) {
        stored = localStorage.getItem(this.COMPRESSED_STORAGE_KEY)
      }

      // 如果压缩存储不存在，尝试从旧的未压缩存储读取
      if (!stored) {
        stored = localStorage.getItem(this.STORAGE_KEY)
        if (stored && this.USE_COMPRESSION) {
          // 迁移到压缩存储
          const sessions = JSON.parse(stored)
          this.saveSessions(sessions)
          localStorage.removeItem(this.STORAGE_KEY) // 删除旧数据
        }
      }

      if (!stored) {
        return []
      }

      let sessions: AiChatSession[]

      if (this.USE_COMPRESSION) {
        sessions = decompressJson<AiChatSession[]>(stored)
      } else {
        sessions = JSON.parse(stored)
      }

      // 转换日期字符串为Date对象
      return sessions
        .map((session) => ({
          ...session,
          createdAt: new Date(session.createdAt),
          updatedAt: new Date(session.updatedAt),
          expiresAt: session.expiresAt ? new Date(session.expiresAt) : undefined,
          messages: session.messages.map((msg) => ({
            ...msg,
            timestamp: new Date(msg.timestamp),
          })),
        }))
        .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
    } catch (error) {
      console.error('获取对话历史失败:', error)
      return []
    }
  }

  /**
   * 获取指定会话
   */
  async getSession(sessionId: string): Promise<AiChatSession | null> {
    const sessions = await this.getAllSessions()
    return sessions.find((session) => session.id === sessionId) || null
  }

  /**
   * 创建新会话
   */
  async createSession(title?: string, isTemporaryChat?: boolean): Promise<AiChatSession> {
    const now = new Date()
    const session: AiChatSession = {
      id: this.generateSessionId(),
      title: title || (isTemporaryChat ? '临时对话' : '新对话'),
      messages: [],
      createdAt: now,
      updatedAt: now,
      totalTokens: 0,
      isTemporaryChat,
      expiresAt: isTemporaryChat ? new Date(now.getTime() + 24 * 60 * 60 * 1000) : undefined, // 24小时后过期
    }

    await this.saveSession(session)
    return session
  }

  /**
   * 保存会话
   */
  async saveSession(session: AiChatSession): Promise<void> {
    try {
      const sessions = await this.getAllSessions()
      const existingIndex = sessions.findIndex((s) => s.id === session.id)

      if (existingIndex >= 0) {
        // 更新现有会话
        sessions[existingIndex] = {
          ...session,
          updatedAt: new Date(),
        }
      } else {
        // 添加新会话
        sessions.unshift({
          ...session,
          updatedAt: new Date(),
        })
      }

      // 限制会话数量
      if (sessions.length > this.MAX_SESSIONS) {
        sessions.splice(this.MAX_SESSIONS)
      }

      this.saveSessions(sessions)
    } catch (error) {
      console.error('保存会话失败:', error)
      throw error
    }
  }

  /**
   * 删除会话
   */
  async deleteSession(sessionId: string): Promise<void> {
    try {
      const sessions = await this.getAllSessions()
      const filteredSessions = sessions.filter((session) => session.id !== sessionId)

      this.saveSessions(filteredSessions)
    } catch (error) {
      console.error('删除会话失败:', error)
      throw error
    }
  }

  /**
   * 清空所有历史记录
   */
  async clearAllHistory(): Promise<void> {
    try {
      localStorage.removeItem(this.STORAGE_KEY)
    } catch (error) {
      console.error('清空历史记录失败:', error)
      throw error
    }
  }

  /**
   * 向会话添加消息
   */
  async addMessage(sessionId: string, message: AiMessage): Promise<void> {
    const session = await this.getSession(sessionId)
    if (!session) {
      throw new Error(`会话 ${sessionId} 不存在`)
    }

    // 限制消息数量
    if (session.messages.length >= this.MAX_MESSAGES_PER_SESSION) {
      // 删除最旧的消息（保留第一条系统消息）
      const systemMessages = session.messages.filter((msg) => msg.role === 'system')
      const otherMessages = session.messages.filter((msg) => msg.role !== 'system')

      if (otherMessages.length > 0) {
        otherMessages.shift() // 删除最旧的非系统消息
      }

      session.messages = [...systemMessages, ...otherMessages]
    }

    session.messages.push(message)

    // 更新token统计
    if (message.tokens) {
      session.totalTokens = (session.totalTokens || 0) + message.tokens
    }

    // 自动更新会话标题（基于第一条用户消息）
    if (message.role === 'user' && session.messages.filter((m) => m.role === 'user').length === 1) {
      session.title = this.generateTitleFromMessage(message.content)
    }

    await this.saveSession(session)
  }

  /**
   * 更新会话标题
   */
  async updateSessionTitle(sessionId: string, title: string): Promise<void> {
    const session = await this.getSession(sessionId)
    if (!session) {
      throw new Error(`会话 ${sessionId} 不存在`)
    }

    session.title = title.trim() || this.generateSessionTitle()
    await this.saveSession(session)
  }

  /**
   * 搜索历史记录
   */
  async searchHistory(keyword: string): Promise<AiChatSession[]> {
    const sessions = await this.getAllSessions()
    const lowerKeyword = keyword.toLowerCase()

    return sessions.filter((session) => {
      // 搜索标题
      if (session.title.toLowerCase().includes(lowerKeyword)) {
        return true
      }

      // 搜索消息内容
      return session.messages.some((message) =>
        message.content.toLowerCase().includes(lowerKeyword),
      )
    })
  }

  /**
   * 获取会话统计信息
   */
  async getStatistics(): Promise<{
    totalSessions: number
    totalMessages: number
    totalTokens: number
    oldestSession?: Date
    newestSession?: Date
  }> {
    const sessions = await this.getAllSessions()

    if (sessions.length === 0) {
      return {
        totalSessions: 0,
        totalMessages: 0,
        totalTokens: 0,
      }
    }

    const totalMessages = sessions.reduce((sum, session) => sum + session.messages.length, 0)
    const totalTokens = sessions.reduce((sum, session) => sum + (session.totalTokens || 0), 0)
    const dates = sessions.map((s) => s.createdAt).sort((a, b) => a.getTime() - b.getTime())

    return {
      totalSessions: sessions.length,
      totalMessages,
      totalTokens,
      oldestSession: dates[0],
      newestSession: dates[dates.length - 1],
    }
  }

  /**
   * 导出历史记录
   */
  async exportHistory(): Promise<string> {
    const sessions = await this.getAllSessions()
    return JSON.stringify(sessions, null, 2)
  }

  /**
   * 导入历史记录
   */
  async importHistory(data: string): Promise<void> {
    try {
      const importedSessions = JSON.parse(data) as AiChatSession[]

      // 验证数据格式
      if (!Array.isArray(importedSessions)) {
        throw new Error('导入数据格式不正确')
      }

      // 转换日期格式并验证
      const validSessions = importedSessions.map((session) => {
        if (!session.id || !session.title || !Array.isArray(session.messages)) {
          throw new Error('会话数据格式不正确')
        }

        return {
          ...session,
          createdAt: new Date(session.createdAt),
          updatedAt: new Date(session.updatedAt),
          expiresAt: session.expiresAt ? new Date(session.expiresAt) : undefined,
          messages: session.messages.map((msg) => ({
            ...msg,
            timestamp: new Date(msg.timestamp),
          })),
        }
      })

      // 合并现有会话
      const existingSessions = await this.getAllSessions()
      const existingIds = new Set(existingSessions.map((s) => s.id))

      const newSessions = validSessions.filter((s) => !existingIds.has(s.id))
      const allSessions = [...existingSessions, ...newSessions]

      // 限制总数量
      if (allSessions.length > this.MAX_SESSIONS) {
        allSessions.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
        allSessions.splice(this.MAX_SESSIONS)
      }

      this.saveSessions(allSessions)
    } catch (error) {
      console.error('导入历史记录失败:', error)
      throw error
    }
  }

  /**
   * 清理过期的临时对话
   */
  async cleanupExpiredTemporaryChats(): Promise<number> {
    try {
      const sessions = await this.getAllSessions()
      const now = new Date()

      const validSessions = sessions.filter((session) => {
        // 如果是临时对话且已过期，则删除
        if (session.isTemporaryChat && session.expiresAt && session.expiresAt < now) {
          console.log(`清理过期临时对话: ${session.title}`)
          return false
        }
        return true
      })

      const removedCount = sessions.length - validSessions.length

      if (removedCount > 0) {
        this.saveSessions(validSessions)
        console.log(`已清理 ${removedCount} 个过期临时对话`)
      }

      return removedCount
    } catch (error) {
      console.error('清理过期临时对话失败:', error)
      return 0
    }
  }

  /**
   * 获取临时对话的剩余时间（毫秒）
   */
  getTemporaryChatRemainingTime(session: AiChatSession): number {
    if (!session.isTemporaryChat || !session.expiresAt) {
      return 0
    }

    const now = new Date()
    const remaining = session.expiresAt.getTime() - now.getTime()
    return Math.max(0, remaining)
  }

  /**
   * 格式化剩余时间为可读字符串
   */
  formatRemainingTime(remainingMs: number): string {
    if (remainingMs <= 0) {
      return '已过期'
    }

    const hours = Math.floor(remainingMs / (1000 * 60 * 60))
    const minutes = Math.floor((remainingMs % (1000 * 60 * 60)) / (1000 * 60))

    if (hours > 0) {
      return `${hours}小时${minutes}分钟后过期`
    } else {
      return `${minutes}分钟后过期`
    }
  }

  /**
   * 将临时对话转为永久对话
   */
  async convertToPermanent(sessionId: string): Promise<void> {
    try {
      const sessions = await this.getAllSessions()
      const sessionIndex = sessions.findIndex((s) => s.id === sessionId)

      if (sessionIndex === -1) {
        throw new Error('会话不存在')
      }

      const session = sessions[sessionIndex]
      if (!session.isTemporaryChat) {
        throw new Error('该会话已经是永久对话')
      }

      // 更新会话属性
      session.isTemporaryChat = false
      session.expiresAt = undefined
      session.updatedAt = new Date()

      this.saveSessions(sessions)
      console.log(`会话 ${session.title} 已转为永久对话`)
    } catch (error) {
      console.error('转为永久对话失败:', error)
      throw error
    }
  }

  /**
   * 将永久对话转为临时对话
   */
  async convertToTemporary(sessionId: string): Promise<void> {
    try {
      const sessions = await this.getAllSessions()
      const sessionIndex = sessions.findIndex((s) => s.id === sessionId)

      if (sessionIndex === -1) {
        throw new Error('会话不存在')
      }

      const session = sessions[sessionIndex]
      if (session.isTemporaryChat) {
        throw new Error('该会话已经是临时对话')
      }

      // 更新会话属性
      const now = new Date()
      session.isTemporaryChat = true
      session.expiresAt = new Date(now.getTime() + 24 * 60 * 60 * 1000) // 24小时后过期
      session.updatedAt = now

      this.saveSessions(sessions)
      console.log(`会话 ${session.title} 已转为临时对话`)
    } catch (error) {
      console.error('转为临时对话失败:', error)
      throw error
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 生成默认会话标题
   */
  private generateSessionTitle(): string {
    const now = new Date()
    return `对话 ${now.getMonth() + 1}/${now.getDate()} ${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`
  }

  /**
   * 根据消息内容生成标题
   */
  private generateTitleFromMessage(content: string): string {
    const trimmedContent = content.trim()

    // 如果内容很短，直接使用
    if (trimmedContent.length <= 40) {
      return trimmedContent
    }

    // 尝试找到第一个句子（以句号、问号、感叹号结尾）
    const sentenceMatch = trimmedContent.match(/^[^。？！.?!]*[。？！.?!]/)
    if (sentenceMatch && sentenceMatch[0].length <= 50) {
      return sentenceMatch[0]
    }

    // 尝试找到第一个逗号前的内容
    const commaMatch = trimmedContent.match(/^[^，,]*/)
    if (commaMatch && commaMatch[0].length > 10 && commaMatch[0].length <= 40) {
      return commaMatch[0]
    }

    // 默认截取前40个字符
    const title = trimmedContent.substring(0, 40)
    return title.length < trimmedContent.length ? `${title}...` : title
  }

  /**
   * 保存会话数组到存储（支持压缩）
   */
  private saveSessions(sessions: AiChatSession[]): void {
    try {
      if (this.USE_COMPRESSION) {
        const compressed = compressJson(sessions)
        localStorage.setItem(this.COMPRESSED_STORAGE_KEY, compressed)
        // 清理旧的未压缩数据
        localStorage.removeItem(this.STORAGE_KEY)
      } else {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(sessions))
      }
    } catch (error) {
      console.error('保存会话失败:', error)
      throw error
    }
  }
}

// 导出单例实例
export const aiHistoryService = new AiHistoryService()
export default aiHistoryService
