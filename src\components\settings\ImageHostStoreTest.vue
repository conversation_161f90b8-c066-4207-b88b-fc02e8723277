<template>
  <div class="store-test-container">
    <h3>图床 Store 状态测试</h3>

    <div class="test-section">
      <h4>基本状态</h4>
      <p>配置数量: {{ configs.length }}</p>
      <p>启用配置数量: {{ enabledConfigs.length }}</p>
      <p>加载状态: {{ loading ? '加载中' : '已完成' }}</p>
      <p>错误信息: {{ error || '无' }}</p>
    </div>

    <div class="test-section">
      <h4>配置列表</h4>
      <div v-if="configs.length === 0" class="empty-state">
        暂无配置
      </div>
      <div v-else>
        <div v-for="config in configs" :key="config.id" class="config-item">
          <span>{{ config.name }}</span>
          <span :class="{ enabled: config.enabled, disabled: !config.enabled }">
            {{ config.enabled ? '已启用' : '已禁用' }}
          </span>
          <span v-if="isTesting(config.id)">测试中...</span>
          <span v-else-if="getTestResult(config.id)">
            {{ getTestResult(config.id)?.success ? '✅' : '❌' }}
          </span>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h4>操作测试</h4>
      <button @click="handleLoadConfigs" :disabled="loading">
        {{ loading ? '加载中...' : '重新加载配置' }}
      </button>
      <button @click="handleTestFirstConfig" :disabled="configs.length === 0">
        测试第一个配置
      </button>
      <button @click="handleToggleFirstConfig" :disabled="configs.length === 0">
        切换第一个配置状态
      </button>
      <button @click="handleTestEnhancedNotification" :disabled="configs.length === 0">
        测试增强版通知
      </button>
    </div>

    <div class="test-section">
      <h4>计算属性测试</h4>
      <p>配置统计: {{ JSON.stringify(configsCount) }}</p>
      <p>有启用的配置: {{ hasEnabledConfigs ? '是' : '否' }}</p>
      <p>排序后的配置: {{sortedConfigs.map(c => c.name).join(', ')}}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useImageHostStore } from '@/stores/imageHostStore'

// 使用 store
const imageHostStore = useImageHostStore()

// 获取响应式状态
const {
  configs,
  loading,
  error,
  enabledConfigs,
  sortedConfigs,
  configsCount,
  hasEnabledConfigs,
  getTestResult,
  isTesting
} = storeToRefs(imageHostStore)

// 获取方法
const {
  loadConfigs,
  testConfig,
  testConfigWithEnhancedNotification,
  toggleConfigEnabled
} = imageHostStore

// 操作处理
const handleLoadConfigs = async () => {
  try {
    await loadConfigs()
    console.log('配置加载完成')
  } catch (error) {
    console.error('加载配置失败:', error)
  }
}

const handleTestFirstConfig = async () => {
  if (configs.value.length > 0) {
    try {
      await testConfig(configs.value[0], true)
      console.log('测试完成')
    } catch (error) {
      console.error('测试失败:', error)
    }
  }
}

const handleToggleFirstConfig = async () => {
  if (configs.value.length > 0) {
    const config = configs.value[0]
    try {
      await toggleConfigEnabled(config.id, !config.enabled)
      console.log('状态切换完成')
    } catch (error) {
      console.error('状态切换失败:', error)
    }
  }
}

const handleTestEnhancedNotification = async () => {
  if (configs.value.length > 0) {
    try {
      await testConfigWithEnhancedNotification(configs.value[0])
      console.log('增强版通知测试完成')
    } catch (error) {
      console.error('增强版通知测试失败:', error)
    }
  }
}

// 组件挂载时自动加载
import { onMounted } from 'vue'
onMounted(() => {
  handleLoadConfigs()
})
</script>

<style scoped>
.store-test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
}

.test-section h4 {
  margin: 0 0 12px 0;
  color: #1890ff;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.config-item:last-child {
  border-bottom: none;
}

.enabled {
  color: #52c41a;
  font-weight: 500;
}

.disabled {
  color: #ff4d4f;
  font-weight: 500;
}

.empty-state {
  color: #999;
  text-align: center;
  padding: 20px;
}

button {
  margin-right: 12px;
  margin-bottom: 8px;
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s;
}

button:hover:not(:disabled) {
  border-color: #1890ff;
  color: #1890ff;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

p {
  margin: 8px 0;
}
</style>
