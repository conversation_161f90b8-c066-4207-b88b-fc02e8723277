<template>
  <div class="resource-preview-container">
    <div class="resource-preview-content">
      <!-- 顶部操作栏 -->
      <div class="preview-header">
        <div class="header-left">
          <a-button type="text" @click="handleBack" class="back-btn">
            <template #icon>
              <ArrowLeftOutlined />
            </template>
            返回编辑
          </a-button>
          <a-divider type="vertical" />
          <span class="preview-label">
            <EyeOutlined class="preview-icon" />
            预览模式
          </span>
        </div>
        <div class="header-right">
          <a-space>
            <a-button @click="handleBack">
              继续编辑
            </a-button>
            <a-button type="primary" @click="handleSave" :loading="saving">
              保存资源
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 使用复用组件 -->
      <ResourceDetailDisplay :resource="displayResource" :show-actions="false" :show-related-resources="false" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined
} from '@ant-design/icons-vue'
import ResourceDetailDisplay from '@/components/common/ResourceDetailDisplay.vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const previewData = ref<any>({})
const saving = ref(false)

// 将预览数据转换为 ResourceDetailDisplay 组件需要的格式
const displayResource = computed(() => {
  return {
    id: previewData.value.previewId,
    title: previewData.value.title || '',
    description: previewData.value.description || '',
    url: previewData.value.url || '',
    cover_image_url: previewData.value.cover_image_url || '',
    view_count: 0,
    created_at: new Date(),
    updated_at: new Date(),
    category: previewData.value.selectedCategory || null,
    tags: previewData.value.selectedTags || []
  }
})

// 格式化 URL 显示
const formatUrl = (url: string) => {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname
  } catch {
    return url
  }
}

// 返回编辑
const handleBack = () => {
  router.back()
}

// 保存资源
const handleSave = async () => {
  try {
    saving.value = true

    // 从 sessionStorage 获取完整的表单数据
    const formData = JSON.parse(sessionStorage.getItem('resource-preview-data') || '{}')

    // 这里应该调用实际的保存 API
    // await resourceService.create(formData)

    message.success('资源保存成功！')

    // 清除临时数据
    sessionStorage.removeItem('resource-preview-data')

    // 跳转到资源列表或详情页
    router.push({ name: 'ResourceList' })

  } catch (error) {
    console.error('保存资源失败:', error)
    message.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

// 组件挂载时加载预览数据
onMounted(() => {
  const data = sessionStorage.getItem('resource-preview-data')
  if (data) {
    previewData.value = JSON.parse(data)
  } else {
    message.error('预览数据不存在')
    router.back()
  }
})
</script>

<style scoped>
.resource-preview-container {
  padding: 24px;
  background: var(--ant-color-bg-layout);
  min-height: calc(100vh - 48px);
}

.resource-preview-content {
  max-width: 1000px;
  margin: 0 auto;
}

/* 预览头部 */
.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding: 16px 24px;
  background: var(--ant-color-bg-container);
  border: 1px solid var(--ant-color-border);
  border-radius: 8px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--ant-color-text-secondary);
  transition: all 0.2s;
}

.back-btn:hover {
  color: var(--ant-color-primary);
}

.preview-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--ant-color-text-secondary);
  font-size: 14px;
}

.preview-icon {
  color: var(--ant-color-primary);
}

/* 资源卡片 */
.resource-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--ant-color-border);
}

.resource-header {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
}

.resource-cover {
  flex-shrink: 0;
  width: 200px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--ant-color-border);
}

.cover-image {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--ant-color-bg-layout);
  color: var(--ant-color-text-tertiary);
}

.placeholder-icon {
  font-size: 32px;
}

.resource-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.resource-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.category-tag {
  margin: 0;
}

.resource-date {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--ant-color-text-tertiary);
  font-size: 13px;
}

.resource-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--ant-color-text);
  margin: 0;
  line-height: 1.3;
}

.resource-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: auto;
}

.visit-btn {
  border-radius: 6px;
}

.stats-btn {
  color: var(--ant-color-text-secondary);
}

/* 标签区域 */
.resource-tags {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  padding: 16px;
  background: var(--ant-color-bg-layout);
  border-radius: 8px;
}

.tags-label {
  font-weight: 500;
  color: var(--ant-color-text-secondary);
  white-space: nowrap;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

/* 描述内容 */
.resource-description {
  margin-bottom: 24px;
}

.description-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  color: var(--ant-color-text-tertiary);
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.3;
}

/* 底部信息 */
.resource-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 16px;
  border-top: 1px solid var(--ant-color-border);
}

.footer-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--ant-color-text-secondary);
  font-size: 13px;
}

.resource-link {
  color: var(--ant-color-primary);
  text-decoration: none;
}

.resource-link:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .resource-header {
    flex-direction: column;
    gap: 16px;
  }

  .resource-cover {
    width: 100%;
    height: 160px;
  }

  .preview-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-left,
  .header-right {
    justify-content: center;
  }
}
</style>
