<template>
  <div class="filepond-upload-container">
    <!-- FilePond 组件 -->
    <file-pond
      ref="pond"
      name="files"
      :allow-multiple="allowMultiple"
      :max-files="maxFiles"
      :accepted-file-types="acceptedFileTypes"
      :max-file-size="maxFileSize"
      :server="serverConfig"
      :files="files"
      :label-idle="labelIdle"
      :credits="false"
      @processfile="onProcessFile"
      @processfiles="onProcessFiles"
      @addfile="onAddFile"
      @removefile="onRemoveFile"
      @error="onError"
      @warning="onWarning"
      class="filepond-custom"
    />
    
    <!-- 配置面板 -->
    <div v-if="showConfig" class="config-panel">
      <div class="config-header">
        <h3>上传设置</h3>
        <button @click="showConfig = false" class="close-btn">×</button>
      </div>
      
      <div class="config-body">
        <!-- 图床选择 -->
        <div class="setting-group">
          <label>选择图床</label>
          <div class="host-grid">
            <div 
              v-for="host in availableHosts" 
              :key="host.id"
              class="host-card"
              :class="{ selected: selectedHosts.includes(host.id) }"
              @click="toggleHost(host.id)"
            >
              <div class="host-info">
                <span class="host-name">{{ host.name }}</span>
                <span class="host-provider">{{ host.provider }}</span>
              </div>
              <div class="host-status" :class="host.enabled ? 'online' : 'offline'">
                {{ host.enabled ? '●' : '○' }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- 批量设置 -->
        <div class="setting-group">
          <label>批量设置</label>
          <input 
            v-model="batchSettings.namePrefix" 
            placeholder="名称前缀（可选）"
            class="setting-input"
          />
          <input 
            v-model="batchSettings.tags" 
            placeholder="标签（逗号分隔）"
            class="setting-input"
          />
          <textarea 
            v-model="batchSettings.description" 
            placeholder="描述信息（可选）"
            class="setting-textarea"
          ></textarea>
        </div>
        
        <!-- 快速操作 -->
        <div class="setting-group">
          <button @click="selectAllHosts" class="action-btn">全选图床</button>
          <button @click="clearHostSelection" class="action-btn">清空选择</button>
          <button @click="randomSelectHosts" class="action-btn">随机选择</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import vueFilePond from 'vue-filepond'
import 'filepond/dist/filepond.min.css'

// 导入 FilePond 插件
import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type'
import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size'
import FilePondPluginImagePreview from 'filepond-plugin-image-preview'
import FilePondPluginImageCrop from 'filepond-plugin-image-crop'
import FilePondPluginImageResize from 'filepond-plugin-image-resize'
import FilePondPluginImageTransform from 'filepond-plugin-image-transform'
import FilePondPluginImageEdit from 'filepond-plugin-image-edit'

// 导入插件样式
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css'
import 'filepond-plugin-image-edit/dist/filepond-plugin-image-edit.css'

import { imageHostService } from '@/services/imageHostService'
import { imageDataService } from '@/services/imageDataService'
import type { ImageHostConfig } from '@/types/imageHost'

// 创建 FilePond 组件
const FilePond = vueFilePond(
  FilePondPluginFileValidateType,
  FilePondPluginFileValidateSize,
  FilePondPluginImagePreview,
  FilePondPluginImageCrop,
  FilePondPluginImageResize,
  FilePondPluginImageTransform,
  FilePondPluginImageEdit
)

interface Props {
  allowMultiple?: boolean
  maxFiles?: number
  maxFileSize?: string
  acceptedFileTypes?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  allowMultiple: true,
  maxFiles: 10,
  maxFileSize: '10MB',
  acceptedFileTypes: () => ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
})

const emit = defineEmits<{
  uploaded: [results: any[]]
  error: [error: string]
  progress: [progress: number]
}>()

// 响应式数据
const pond = ref()
const files = ref([])
const showConfig = ref(false)
const availableHosts = ref<ImageHostConfig[]>([])
const selectedHosts = ref<string[]>([])
const batchSettings = ref({
  namePrefix: '',
  tags: '',
  description: ''
})

// 计算属性
const labelIdle = computed(() => 
  `拖拽图片到这里或 <span class="filepond--label-action">选择文件</span><br>
   支持 ${props.acceptedFileTypes.map(type => type.split('/')[1].toUpperCase()).join(', ')}，
   单个文件最大 ${props.maxFileSize}`
)

// FilePond 服务器配置
const serverConfig = {
  // 自定义处理函数
  process: (fieldName: string, file: File, metadata: any, load: Function, error: Function, progress: Function, abort: Function) => {
    // 使用你的自定义上传逻辑
    handleCustomUpload(file, progress, load, error)
    
    // 返回中止函数
    return {
      abort: () => {
        console.log('上传已中止')
        abort()
      }
    }
  },
  
  // 可选：恢复上传
  restore: null,
  
  // 可选：删除文件
  remove: (source: string, load: Function, error: Function) => {
    // 这里可以实现删除逻辑
    load()
  }
}

// 自定义上传处理
const handleCustomUpload = async (
  file: File, 
  progress: Function, 
  load: Function, 
  error: Function
) => {
  try {
    // 获取可用图床
    const hostsToUse = selectedHosts.value.length > 0 
      ? availableHosts.value.filter(h => selectedHosts.value.includes(h.id))
      : availableHosts.value.filter(h => h.enabled)
    
    if (hostsToUse.length === 0) {
      throw new Error('没有可用的图床')
    }
    
    // 随机选择图床
    const randomHost = hostsToUse[Math.floor(Math.random() * hostsToUse.length)]
    
    // 上传到选中的图床
    const result = await uploadToHost(file, randomHost, (progressData: any) => {
      // 更新 FilePond 进度
      progress(true, progressData.loaded, progressData.total)
    })
    
    // 保存到数据库
    await saveToDatabase(file, result)
    
    // 通知 FilePond 上传完成
    load(result.url)
    
    // 触发上传完成事件
    emit('uploaded', [result])
    
  } catch (err) {
    console.error('上传失败:', err)
    error(err instanceof Error ? err.message : '上传失败')
    emit('error', err instanceof Error ? err.message : '上传失败')
  }
}

// 上传到指定图床
const uploadToHost = async (file: File, hostConfig: ImageHostConfig, onProgress?: Function) => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()
    const formData = new FormData()
    
    // 构建表单数据
    formData.append(hostConfig.fileField || 'file', file)
    
    // 添加额外参数
    if (hostConfig.params) {
      Object.entries(hostConfig.params).forEach(([key, value]) => {
        formData.append(key, String(value))
      })
    }
    
    // 进度监听
    if (onProgress) {
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          onProgress({
            loaded: event.loaded,
            total: event.total,
            percentage: Math.round((event.loaded / event.total) * 100)
          })
        }
      })
    }
    
    // 完成监听
    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText)
          
          // 根据图床配置解析URL
          let url: string
          if (hostConfig.responseType === 'json') {
            url = getNestedValue(response, hostConfig.urlField)
          } else {
            url = xhr.responseText.trim()
          }
          
          resolve({
            url,
            hostId: hostConfig.id,
            hostName: hostConfig.name,
            deleteUrl: response.delete_url || undefined
          })
        } catch (error) {
          reject(new Error('解析响应失败'))
        }
      } else {
        reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`))
      }
    })
    
    // 错误监听
    xhr.addEventListener('error', () => {
      reject(new Error('网络请求失败'))
    })
    
    // 发送请求
    xhr.open(hostConfig.method || 'POST', hostConfig.apiUrl)
    
    // 设置认证头
    if (hostConfig.authType === 'header' && hostConfig.authKey) {
      const authValue = hostConfig.authPrefix 
        ? `${hostConfig.authPrefix}${hostConfig.authKey}`
        : hostConfig.authKey
      xhr.setRequestHeader(hostConfig.authHeader!, authValue)
    }
    
    if (hostConfig.headers) {
      Object.entries(hostConfig.headers).forEach(([key, value]) => {
        xhr.setRequestHeader(key, String(value))
      })
    }
    
    xhr.send(formData)
  })
}

// 保存到数据库
const saveToDatabase = async (file: File, uploadResult: any) => {
  const imageRecord = {
    name: batchSettings.value.namePrefix + file.name.replace(/\.[^/.]+$/, ''),
    originalName: file.name,
    size: file.size,
    type: file.type,
    uploadTime: new Date(),
    description: batchSettings.value.description,
    tags: batchSettings.value.tags.split(',').map(t => t.trim()).filter(Boolean),
    urls: [{
      hostId: uploadResult.hostId,
      hostName: uploadResult.hostName,
      url: uploadResult.url,
      deleteUrl: uploadResult.deleteUrl,
      uploadTime: new Date(),
      status: 'active' as const
    }]
  }
  
  await imageDataService.saveImage(imageRecord)
}

// 获取嵌套对象值
const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}

// 图床选择相关方法
const toggleHost = (hostId: string) => {
  const index = selectedHosts.value.indexOf(hostId)
  if (index > -1) {
    selectedHosts.value.splice(index, 1)
  } else {
    selectedHosts.value.push(hostId)
  }
}

const selectAllHosts = () => {
  selectedHosts.value = availableHosts.value.map(h => h.id)
}

const clearHostSelection = () => {
  selectedHosts.value = []
}

const randomSelectHosts = () => {
  const enabledHosts = availableHosts.value.filter(h => h.enabled)
  const count = Math.min(Math.floor(Math.random() * 3) + 1, enabledHosts.length)
  const shuffled = [...enabledHosts].sort(() => Math.random() - 0.5)
  selectedHosts.value = shuffled.slice(0, count).map(h => h.id)
}

// FilePond 事件处理
const onProcessFile = (error: any, file: any) => {
  if (error) {
    console.error('处理文件失败:', error)
    return
  }
  console.log('文件处理完成:', file.filename)
}

const onProcessFiles = () => {
  console.log('所有文件处理完成')
}

const onAddFile = (error: any, file: any) => {
  if (error) {
    console.error('添加文件失败:', error)
    return
  }
  console.log('文件已添加:', file.filename)
}

const onRemoveFile = (error: any, file: any) => {
  if (error) {
    console.error('移除文件失败:', error)
    return
  }
  console.log('文件已移除:', file.filename)
}

const onError = (error: any) => {
  console.error('FilePond 错误:', error)
  emit('error', error.body || '未知错误')
}

const onWarning = (error: any) => {
  console.warn('FilePond 警告:', error)
}

// 生命周期
onMounted(async () => {
  // 加载图床配置
  availableHosts.value = await imageHostService.getEnabledConfigs()
})

// 暴露方法
defineExpose({
  showConfig: () => { showConfig.value = true },
  hideConfig: () => { showConfig.value = false },
  addFiles: (newFiles: File[]) => {
    newFiles.forEach(file => {
      pond.value?.addFile(file)
    })
  },
  removeAllFiles: () => {
    pond.value?.removeFiles()
  },
  processFiles: () => {
    pond.value?.processFiles()
  }
})
</script>

<style scoped>
.filepond-upload-container {
  position: relative;
  width: 100%;
}

/* 自定义 FilePond 样式 */
:deep(.filepond--root) {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

:deep(.filepond--drop-label) {
  color: #374151;
  font-size: 16px;
}

:deep(.filepond--label-action) {
  color: #3b82f6;
  text-decoration: underline;
}

:deep(.filepond--panel-root) {
  background-color: #f9fafb;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
}

:deep(.filepond--item-panel) {
  background-color: #ffffff;
  border-radius: 6px;
}

/* 暗黑模式适配 */
.dark :deep(.filepond--drop-label) {
  color: #f3f4f6;
}

.dark :deep(.filepond--panel-root) {
  background-color: #1f2937;
  border-color: #4b5563;
}

.dark :deep(.filepond--item-panel) {
  background-color: #374151;
}

/* 配置面板样式 */
.config-panel {
  position: absolute;
  top: 0;
  right: -320px;
  width: 300px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 500px;
  overflow: hidden;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.config-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
  border-radius: 4px;
}

.close-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.config-body {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.setting-group {
  margin-bottom: 20px;
}

.setting-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.host-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.host-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.host-card:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.host-card.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.host-info {
  display: flex;
  flex-direction: column;
}

.host-name {
  font-weight: 500;
  color: #111827;
  font-size: 13px;
}

.host-provider {
  font-size: 11px;
  color: #6b7280;
}

.host-status {
  font-size: 12px;
}

.host-status.online {
  color: #10b981;
}

.host-status.offline {
  color: #ef4444;
}

.setting-input, .setting-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 8px;
}

.setting-input:focus, .setting-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.setting-textarea {
  resize: vertical;
  min-height: 60px;
}

.action-btn {
  display: inline-block;
  padding: 6px 12px;
  margin-right: 8px;
  margin-bottom: 8px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

/* 暗黑模式配置面板 */
.dark .config-panel {
  background: #1f2937;
  border-color: #374151;
}

.dark .config-header {
  background: #111827;
  border-color: #374151;
}

.dark .config-header h3 {
  color: #f9fafb;
}

.dark .setting-group label {
  color: #e5e7eb;
}

.dark .host-card {
  border-color: #4b5563;
  background: #374151;
}

.dark .host-card:hover {
  background: #4b5563;
}

.dark .host-card.selected {
  background: #1e3a8a;
  border-color: #3b82f6;
}

.dark .host-name {
  color: #f9fafb;
}

.dark .setting-input, .dark .setting-textarea {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.dark .action-btn {
  background: #374151;
  border-color: #4b5563;
  color: #e5e7eb;
}

.dark .action-btn:hover {
  background: #4b5563;
}
</style>
