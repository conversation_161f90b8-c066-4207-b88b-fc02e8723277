<template>
  <!-- 知识库页面 - Ant Design Vue重设计 -->
  <div class="knowledge-view-container">
    <!-- 紧凑型功能区 - 集成筛选和操作功能 -->
    <a-card class="function-card" size="small">
      <div class="function-content">
        <!-- 第一行：筛选控件和操作按钮 -->
        <div class="function-row function-controls">
          <!-- 左侧筛选区 -->
          <div class="filter-controls">
            <!-- 分类筛选 -->
            <div class="filter-item">
              <span class="filter-label">分类：</span>
              <a-tree-select v-model:value="selectedCategoryId" :tree-data="categoryTreeData" placeholder="选择分类"
                allow-clear class="category-selector" :popup-match-select-width="true" @change="handleCategoryChange" />
            </div>

          </div>

          <!-- 右侧操作区 -->
          <div class="action-controls">
            <!-- 排序选择 -->
            <div class="filter-item">
              <span class="filter-label">排序：</span>
              <a-select v-model:value="filterCurrentSortKey" @change="handleSortChange" class="sort-selector"
                placeholder="排序方式">
                <template #suffixIcon>
                  <SortAscendingOutlined />
                </template>
                <a-select-option v-for="option in localSortOptions" :key="option.key" :value="option.key">
                  <div class="sort-option">
                    <component :is="option.icon" class="sort-icon" />
                    <span>{{ option.label }}</span>
                  </div>
                </a-select-option>
              </a-select>
            </div>

            <!-- 清除筛选按钮 -->
            <a-button v-if="hasActiveFilters" size="small" @click="clearFilters" class="clear-filters-btn">
              <template #icon>
                <ClearOutlined />
              </template>
              清除筛选
            </a-button>


          </div>
        </div>

        <!-- 第二行：标签筛选区（可滚动） -->
        <div class="function-row function-tags">
          <div class="tags-section">
            <span class="filter-label">标签：</span>
            <div class="tags-container">
              <div class="tags-scroll-area">
                <div v-for="tag in displayedTags" :key="tag.id"
                  :class="['modern-tag', { 'modern-tag-selected': isTagSelected(tag.id || 0) }]"
                  :style="getTagStyle(tag, isTagSelected(tag.id || 0))" @click="toggleTag(tag)">
                  <span class="tag-name">{{ tag.name }}</span>
                  <span class="tag-count">{{ tag.resource_count }}</span>
                </div>

                <!-- 显示更多标签按钮 -->
                <div v-if="hasMoreTags" class="modern-tag more-tags-btn" @click="loadMoreTags">
                  <span class="tag-name">更多标签</span>
                  <span class="tag-count">+{{ remainingTagsCount }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 资源列表 -->
    <div class="resource-list-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <a-spin size="large" tip="加载中...">
          <div class="loading-placeholder"></div>
        </a-spin>
      </div>

      <!-- 空状态 -->
      <a-empty v-else-if="resources.length === 0" class="empty-state">
        <template #image>
          <FileTextOutlined class="empty-icon" />
        </template>
        <template #description>
          <span class="empty-description">暂无资源</span>
          <p class="empty-subtitle">开始添加您的第一个知识资源吧</p>
        </template>
        <a-button type="primary" @click="$router.push('/knowledge/create')">
          <template #icon>
            <PlusOutlined />
          </template>
          添加资源
        </a-button>
      </a-empty>

      <!-- 资源网格 -->
      <div v-else class="resource-grid" :class="getGridClasses()">
        <ResourceCard v-for="resource in resources" :key="resource.id" :id="`resource-${resource.id}`"
          :resource="resource" @click="handleResourceClick(resource)" @edit="handleResourceEdit(resource)"
          @delete="handleResourceDelete(resource)" />
      </div>

      <!-- 分页导航 -->
      <div v-if="knowledgeSettings.loading.type === 'pagination' && totalResources > 0" class="pagination-container">
        <a-pagination v-model:current="currentPage" :total="totalResources"
          :page-size="knowledgeSettings.loading.pageSize" :show-size-changer="false" :show-quick-jumper="true"
          :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`" @change="handlePageChange"
          class="resource-pagination" />
      </div>
    </div>

    <!-- 无限滚动加载指示器 -->
    <div v-if="loading && knowledgeSettings.loading.type === 'infinite'" class="infinite-loading">
      <a-spin size="small" tip="加载更多...">
        <div class="infinite-loading-placeholder"></div>
      </a-spin>
    </div>

    <!-- 悬浮按钮组 - 重新设计 -->
    <div class="float-buttons-container">
      <!-- 添加资源按钮 -->
      <a-tooltip title="添加资源" placement="left">
        <div class="float-button add-resource-btn" @click="$router.push('/knowledge/create')">
          <PlusOutlined class="float-btn-icon" />
          <span class="float-btn-text">添加</span>
        </div>
      </a-tooltip>

      <!-- 回到顶部按钮 -->
      <a-tooltip title="回到顶部" placement="left">
        <div class="float-button back-to-top-btn" @click="scrollToTop" v-show="showBackToTop">
          <UpOutlined class="float-btn-icon" />
          <span class="float-btn-text">顶部</span>
        </div>
      </a-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  PlusOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
  FileTextOutlined,
  ClearOutlined,
  UpOutlined,
  ClockCircleOutlined,
  EyeOutlined,
  FontSizeOutlined
} from '@ant-design/icons-vue'
import ResourceCard from '@/components/knowledge/ResourceCard.vue'
import { knowledgeSettingsService, type KnowledgeSettings } from '@/services/knowledgeSettingsService'
import { resourceService } from '@/services/resourceService'
import { useResourceList } from '@/composables/useResourceList'
import { useFilters } from '@/composables/useFilters'
import type { ResourceWithDetails, Tag, CategoryWithChildren, SortOption, SortOrder } from '@/types'

const route = useRoute()
const router = useRouter()

// 知识库设置
const knowledgeSettings = ref<KnowledgeSettings>(knowledgeSettingsService.getSettings())

// 使用新的 Composables
const {
  selectedCategoryId,
  selectedTags,
  currentSortKey: filterCurrentSortKey,
  searchKeyword,
  viewMode,
  categories,
  categoryTree,
  popularTags,
  hasActiveFilters,
  filterSummary,
  sortOptions,
  setCategory,
  addTag,
  removeTag,
  toggleTag,
  setSortKey,
  setSearchKeyword,
  setViewMode,
  clearFilters,
  clearAll,
  loadCategories,
  loadTags,
  syncFromUrl,
  syncToUrl
} = useFilters({ syncWithUrl: true })

const {
  resources,
  total: totalResources,
  isLoading: loading,
  hasMore,
  currentPage,
  loadMore,
  refresh,
  reset
} = useResourceList({
  pageSize: computed(() => knowledgeSettings.value.loading.pageSize),
  enableInfiniteScroll: computed(() => knowledgeSettings.value.loading.type === 'infinite')
})

// 标签分页状态（从设置中获取）
const displayedTagsCount = ref(knowledgeSettings.value.tags.initialLoad)
const tagPageSize = computed(() => knowledgeSettings.value.tags.loadMore)

// 辅助方法：检查标签是否被选中
const isTagSelected = (tagId: number) => {
  return selectedTags.value.some(tag => tag.id === tagId)
}

// 滚动状态
const showBackToTop = ref(false)

// 分类树数据转换为Ant Design TreeSelect格式
const categoryTreeData = computed(() => {
  const convertToTreeData = (categories: CategoryWithChildren[]): any[] => {
    return categories.map(category => ({
      title: category.name,
      value: category.id,
      key: category.id,
      children: category.children && category.children.length > 0
        ? convertToTreeData(category.children)
        : undefined
    }))
  }
  return convertToTreeData(categoryTree.value)
})

// 从设置中获取默认排序
const getDefaultSorting = () => {
  const defaultSorting = knowledgeSettings.value.sorting.default
  const parts = defaultSorting.split('_')

  // 映射排序字段
  const sortFieldMap: Record<string, SortOption> = {
    'created': 'created_at',
    'updated': 'updated_at',
    'title': 'title',
    'visits': 'view_count'
  }

  const sortField = sortFieldMap[parts[0]] || 'created_at'
  const sortOrder = parts[1] as SortOrder || 'desc'

  return { sortField, sortOrder }
}

const { sortField, sortOrder: defaultSortOrder } = getDefaultSorting()
const currentSort = ref<SortOption>(sortField)
const sortOrder = ref<SortOrder>(defaultSortOrder)
const highlightResourceId = ref<string | null>(null)

// 本地排序选项 - 包含图标和方向（用于显示）
const localSortOptions = [
  { key: 'created_at_desc', field: 'created_at', order: 'desc', label: '创建时间 ↓', icon: ClockCircleOutlined },
  { key: 'created_at_asc', field: 'created_at', order: 'asc', label: '创建时间 ↑', icon: ClockCircleOutlined },
  { key: 'updated_at_desc', field: 'updated_at', order: 'desc', label: '更新时间 ↓', icon: ClockCircleOutlined },
  { key: 'updated_at_asc', field: 'updated_at', order: 'asc', label: '更新时间 ↑', icon: ClockCircleOutlined },
  { key: 'view_count_desc', field: 'view_count', order: 'desc', label: '浏览次数 ↓', icon: EyeOutlined },
  { key: 'view_count_asc', field: 'view_count', order: 'asc', label: '浏览次数 ↑', icon: EyeOutlined },
  { key: 'title_asc', field: 'title', order: 'asc', label: '标题 A-Z', icon: FontSizeOutlined },
  { key: 'title_desc', field: 'title', order: 'desc', label: '标题 Z-A', icon: FontSizeOutlined }
]

// 计算属性
const displayedTags = computed(() => {
  return popularTags.value.slice(0, displayedTagsCount.value)
})

const hasMoreTags = computed(() => {
  return popularTags.value.length > displayedTagsCount.value
})

const remainingTagsCount = computed(() => {
  return popularTags.value.length - displayedTagsCount.value
})



// 处理URL查询参数
const handleUrlQuery = async () => {
  const { highlight } = route.query

  // 处理资源高亮
  if (highlight && typeof highlight === 'string') {
    highlightResourceId.value = highlight
    // 等待资源加载完成后再滚动
    setTimeout(() => {
      const element = document.getElementById(`resource-${highlight}`)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' })
        // 添加高亮效果
        element.classList.add('ring-2', 'ring-primary-500', 'ring-opacity-50')
        setTimeout(() => {
          element.classList.remove('ring-2', 'ring-primary-500', 'ring-opacity-50')
        }, 3000)
      }
    }, 1000)
  }
}

// 排序变更
const handleSortChange = (sortKey: string) => {
  const option = localSortOptions.find(opt => opt.key === sortKey)
  if (option) {
    setSortKey(sortKey)
  }
}

// 页面变更处理
const handlePageChange = (page: number) => {
  currentPage.value = page
}

// 加载更多标签
const loadMoreTags = () => {
  displayedTagsCount.value = Math.min(
    displayedTagsCount.value + tagPageSize.value,
    popularTags.value.length
  )
}

// 分类变更处理
const handleCategoryChange = (categoryId: number | null) => {
  if (categoryId) {
    const category = categories.value.find(cat => cat.id === categoryId) || null
    setCategory(categoryId, category)
  } else {
    setCategory(null, null)
  }
}

// 滚动相关方法
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}



// 资源点击
const handleResourceClick = (resource: ResourceWithDetails) => {
  // 增加浏览次数
  resourceService.incrementViewCount(resource.id!)

  // 跳转到详情页
  router.push(`/knowledge/${resource.id}`)
}

// 编辑资源
const handleResourceEdit = (resource: ResourceWithDetails) => {
  router.push(`/knowledge/create?id=${resource.id}`)
}

// 删除资源
const handleResourceDelete = async (resource: ResourceWithDetails) => {
  if (confirm(`确定要删除资源"${resource.title}"吗？`)) {
    try {
      await resourceService.deleteResource(resource.id!)
      await refresh()
    } catch (error) {
      console.error('删除资源失败:', error)
      alert('删除失败，请重试')
    }
  }
}



// 获取网格布局类名 - 动态响应式布局
const getGridClasses = () => {
  // 使用动态响应式布局，不再依赖固定的cardsPerRow设置
  return 'grid-responsive-auto'
}

// 获取标签样式 - 根据标签颜色和选中状态
const getTagStyle = (tag: Tag, isSelected: boolean) => {
  const tagColor = tag.color || '#1677ff'

  if (isSelected) {
    // 选中状态：背景色为标签颜色，文字为白色
    return {
      backgroundColor: tagColor,
      borderColor: tagColor,
      color: '#ffffff'
    }
  } else {
    // 未选中状态：边框和文字为标签颜色，背景透明
    return {
      backgroundColor: 'transparent',
      borderColor: tagColor,
      color: tagColor
    }
  }
}

// 设置变更监听器
const handleSettingsChange = (newSettings: KnowledgeSettings) => {
  knowledgeSettings.value = newSettings

  // 应用新的排序设置
  const parts = newSettings.sorting.default.split('_')
  const sortFieldMap: Record<string, SortOption> = {
    'created': 'created_at',
    'updated': 'updated_at',
    'title': 'title',
    'visits': 'view_count'
  }

  const sortField = sortFieldMap[parts[0]] || 'created_at'
  const sortOrderValue = parts[1] as SortOrder || 'desc'

  currentSort.value = sortField
  sortOrder.value = sortOrderValue
  const newSortKey = `${sortField}_${sortOrderValue}`
  setSortKey(newSortKey)

  // 重新加载标签以应用新的标签排序
  loadTags()

  // 应用CSS变量
  knowledgeSettingsService.applyCSSVariables()
}

// 滚动处理 - 同时处理无限滚动和回到顶部
const handleScroll = () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop

  // 回到顶部按钮显示逻辑
  showBackToTop.value = scrollTop > 300 // 滚动超过300px时显示回到顶部按钮

  // 无限滚动处理
  if (knowledgeSettings.value.loading.type !== 'infinite') return
  if (loading.value || !hasMore.value) return

  const windowHeight = window.innerHeight
  const documentHeight = document.documentElement.scrollHeight
  const preloadDistance = knowledgeSettings.value.loading.preloadDistance

  if (scrollTop + windowHeight >= documentHeight - preloadDistance) {
    loadMore()
  }
}

// 初始化
onMounted(async () => {
  // 添加设置变更监听器
  knowledgeSettingsService.addListener(handleSettingsChange)

  // 添加滚动监听器（用于无限滚动）
  window.addEventListener('scroll', handleScroll)

  // 应用当前CSS变量
  knowledgeSettingsService.applyCSSVariables()

  await Promise.all([
    loadCategories(),
    loadTags()
  ])

  // 处理URL查询参数
  await syncFromUrl()
  await handleUrlQuery()
})

// 清理
onUnmounted(() => {
  knowledgeSettingsService.removeListener(handleSettingsChange)
  window.removeEventListener('scroll', handleScroll)
})

// 监听路由查询参数变化
watch(() => route.query, handleUrlQuery)
</script>

<style>
/* 全局样式 - 选择器选中项统一颜色 */

/* 明亮模式下的选中项 */
.ant-select-dropdown .ant-select-item-option-selected {
  background: #1677ff !important;
  color: #ffffff !important;
}

.ant-select-dropdown .ant-select-item-option-selected .sort-icon {
  color: #ffffff !important;
}

.ant-select-dropdown .ant-select-item-option-selected .sort-option span {
  color: #ffffff !important;
}

/* 暗黑模式下的选中项 */
.dark .ant-select-dropdown .ant-select-item-option-selected {
  background: #1677ff !important;
  color: #ffffff !important;
}

.dark .ant-select-dropdown .ant-select-item-option-selected .sort-icon {
  color: #ffffff !important;
}

.dark .ant-select-dropdown .ant-select-item-option-selected .sort-option span {
  color: #ffffff !important;
}

/* 分类选择器选中项背景色 */
.ant-tree-select-dropdown .ant-select-tree-node-selected {
  background: #1677ff !important;
}

.dark .ant-tree-select-dropdown .ant-select-tree-node-selected {
  background: #1677ff !important;
}
</style>

<style scoped>
/* 知识库页面 - Ant Design Vue重设计样式 */
.knowledge-view-container {
  padding: var(--knowledge-container-padding, 24px);
  background: var(--knowledge-bg, var(--ant-color-bg-layout, #f5f5f5));
  min-height: calc(100vh - 48px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 紧凑型功能区 */
.function-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--ant-color-border, #d9d9d9);
}

.function-content {
  padding: 0;
  /* 移除默认内边距，由内部元素控制 */
}

/* 功能行布局 */
.function-row {
  padding: 12px 16px;
}

.function-row+.function-row {
  border-top: 1px solid var(--ant-color-border-secondary, #f0f0f0);
  padding-top: 12px;
}

/* 第一行：控件和操作按钮 */
.function-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

/* 第二行：标签区域 */
.function-tags {
  padding-top: 8px !important;
  /* 减少顶部间距 */
}

.tags-section {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.tags-container {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.tags-scroll-area {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  max-height: 120px;
  overflow-y: auto;
  padding: 2px 0;
  align-items: center;
}

/* 组件样式优化 */
.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--ant-color-text, #000000d9);
  white-space: nowrap;
  min-width: fit-content;
}

/* 选择器优化 - 参考 Ant Design 官网设计 */
.category-selector {
  min-width: 200px !important;
  width: 200px !important;
  height: 32px !important;
  border-radius: 6px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.category-selector :deep(.ant-tree-select-selector) {
  width: 200px !important;
  min-width: 200px !important;
  height: 32px !important;
  line-height: 30px !important;
}

/* 更强制的宽度设置 */
.filter-item .category-selector,
.filter-controls .category-selector {
  min-width: 200px !important;
  width: 200px !important;
}

.category-selector:hover {
  border-color: #4096ff;
}

.category-selector:focus-within {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

.sort-selector {
  min-width: 140px;
  width: 140px;
  height: 32px;
  border-radius: 6px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.sort-selector :deep(.ant-select-selector) {
  height: 32px !important;
  line-height: 30px !important;
}

.sort-selector:hover {
  border-color: #4096ff;
}

.sort-selector:focus-within {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

/* 排序选项样式 - 优化对齐和紧凑度 */
.sort-option {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 6px;
  padding: 2px 0;
  line-height: 1.2;
  min-height: 20px;
}

.sort-icon {
  font-size: 13px;
  color: var(--ant-color-text-secondary, #666);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

/* 优化排序选择器下拉菜单的紧凑度 */
.sort-selector .ant-select-dropdown {
  padding: 4px 0 !important;
}

.sort-selector .ant-select-item {
  padding: 4px 12px !important;
  line-height: 1.3 !important;
  min-height: 28px !important;
  display: flex !important;
  align-items: center !important;
}

.sort-selector .ant-select-item-option-content {
  flex: 1;
  display: flex;
  align-items: center;
}

/* 亮色模式下的选项悬浮和选中效果 */
.sort-selector .ant-select-item:hover {
  background: rgba(0, 0, 0, 0.04) !important;
}

.sort-selector .ant-select-item-option-selected {
  background: rgba(22, 119, 255, 0.15) !important;
  color: #1677ff !important;
  font-weight: 500;
  border-left: 3px solid #1677ff !important;
}

.sort-selector .ant-select-item-option-selected .sort-icon {
  color: #1677ff !important;
}

/* 确保排序选项文字垂直居中和不换行 */
.sort-option span {
  white-space: nowrap;
  font-size: 14px;
  line-height: 1.2;
  display: flex;
  align-items: center;
  height: 16px;
}

/* 亮色模式下的选项悬浮和选中效果 */
.sort-selector .ant-select-item:hover {
  background: rgba(0, 0, 0, 0.04) !important;
}

.sort-selector .ant-select-item-option-selected {
  background: rgba(22, 119, 255, 0.1) !important;
  color: #1677ff !important;
  font-weight: 500;
}

.sort-selector .ant-select-item-option-selected .sort-icon {
  color: #1677ff !important;
}

/* 确保排序选项文字不换行 */
.sort-option span {
  white-space: nowrap;
  font-size: 14px;
  line-height: 1.2;
}

.add-resource-btn,
.clear-filters-btn {
  height: 24px;
  /* 使用 small 尺寸 */
  border-radius: 4px;
  font-weight: 400;
}

.add-resource-btn {
  min-width: 80px;
}

/* 选择器下拉面板优化 - 参考 Ant Design 官网设计 */
:deep(.ant-select-dropdown) {
  border-radius: 8px !important;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
  border: 1px solid rgba(5, 5, 5, 0.06) !important;
  padding: 4px 0 !important;
}

:deep(.ant-select-item) {
  border-radius: 4px !important;
  margin: 0 4px !important;
  padding: 5px 12px !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

:deep(.ant-select-item-option-active) {
  background-color: rgba(0, 0, 0, 0.04) !important;
}

:deep(.ant-select-item-option-selected) {
  background-color: #e6f4ff !important;
  color: rgba(0, 0, 0, 0.88) !important;
  font-weight: 600 !important;
}

:deep(.ant-tree-select-dropdown) {
  border-radius: 8px !important;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
  border: 1px solid rgba(5, 5, 5, 0.06) !important;
  padding: 4px 0 !important;
}

:deep(.ant-tree-treenode) {
  border-radius: 4px !important;
  margin: 0 4px !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  white-space: nowrap !important;
  overflow: visible !important;
}

:deep(.ant-tree-node-content-wrapper) {
  padding: 4px 8px !important;
  width: 100% !important;
  overflow: hidden !important;
}

:deep(.ant-tree-node-content-wrapper:hover) {
  background-color: rgba(0, 0, 0, 0.04) !important;
}

:deep(.ant-tree-node-selected) {
  background-color: #e6f4ff !important;
}

:deep(.ant-tree-title) {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 100% !important;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: var(--knowledge-filter-section-gap, 8px);
}

.filter-label {
  font-size: var(--knowledge-filter-label-size, 14px);
  font-weight: var(--knowledge-filter-label-weight, 500);
  color: var(--knowledge-filter-label-color, var(--ant-color-text, #000000d9));
  white-space: nowrap;
}

.category-selector {
  min-width: var(--knowledge-category-width, 200px);
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--knowledge-tag-gap, 8px);
  align-items: center;
}

/* 紧凑型标签样式 - 无图标，显示标签颜色 */
.modern-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: 1px solid;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 12px;
  user-select: none;
  white-space: nowrap;
}

.modern-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modern-tag .tag-name {
  font-weight: 500;
  line-height: 1.2;
}

.modern-tag .tag-count {
  background: rgba(255, 255, 255, 0.8);
  color: inherit;
  padding: 1px 4px;
  border-radius: 6px;
  font-size: 10px;
  font-weight: 600;
  min-width: 16px;
  text-align: center;
  line-height: 1.2;
}

/* 选中状态的标签计数 */
.modern-tag-selected .tag-count {
  background: rgba(255, 255, 255, 0.3);
  color: white;
}

/* 更多标签按钮特殊样式 */
.more-tags-btn {
  border-style: dashed !important;
  border-color: var(--ant-color-border-secondary) !important;
  background: var(--ant-color-bg-layout) !important;
  color: var(--ant-color-text-secondary) !important;
}

.more-tags-btn:hover {
  border-color: var(--ant-color-primary) !important;
  background: var(--ant-color-primary-bg) !important;
  color: var(--ant-color-primary) !important;
}

.more-tags-btn .tag-count {
  background: var(--ant-color-primary) !important;
  color: white !important;
}

.clear-filters-btn {
  border-radius: var(--knowledge-btn-radius, 8px);
}

/* 资源列表容器 */
.resource-list-container {
  position: relative;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--knowledge-loading-padding, 80px 20px);
}

.loading-placeholder {
  width: 100%;
  height: var(--knowledge-loading-height, 200px);
}

/* 空状态 */
.empty-state {
  padding: var(--knowledge-empty-padding, 80px 20px);
}

.empty-icon {
  font-size: var(--knowledge-empty-icon-size, 64px);
  color: var(--knowledge-empty-icon-color, var(--ant-color-text-quaternary, #00000025));
}

.empty-description {
  font-size: var(--knowledge-empty-desc-size, 16px);
  font-weight: var(--knowledge-empty-desc-weight, 500);
  color: var(--knowledge-empty-desc-color, var(--ant-color-text-secondary, #00000073));
  margin-bottom: var(--knowledge-empty-desc-margin, 8px);
}

.empty-subtitle {
  font-size: var(--knowledge-empty-sub-size, 14px);
  color: var(--knowledge-empty-sub-color, var(--ant-color-text-tertiary, #00000040));
  margin: 0 0 var(--knowledge-empty-sub-margin, 24px) 0;
}

/* 资源网格 */
.resource-grid {
  display: grid;
  gap: var(--knowledge-grid-gap, 16px);
  margin-bottom: var(--knowledge-grid-margin, 24px);
}

/* 动态响应式网格布局 - 智能边距调整，避免过多留白 */
.grid-responsive-auto {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--knowledge-grid-gap, 16px);
  justify-content: start;
  /* 左对齐，避免居中造成的留白 */
  align-content: start;
  width: 100%;
}

/* 针对不同屏幕尺寸的智能调整 */
@media (max-width: 640px) {
  .grid-responsive-auto {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--knowledge-grid-gap, 12px);
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .grid-responsive-auto {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--knowledge-grid-gap, 14px);
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .grid-responsive-auto {
    grid-template-columns: repeat(auto-fill, minmax(290px, 1fr));
    gap: var(--knowledge-grid-gap, 16px);
  }
}

@media (min-width: 1025px) and (max-width: 1280px) {
  .grid-responsive-auto {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--knowledge-grid-gap, 18px);
  }
}

@media (min-width: 1281px) and (max-width: 1440px) {
  .grid-responsive-auto {
    grid-template-columns: repeat(auto-fill, minmax(310px, 1fr));
    gap: var(--knowledge-grid-gap, 20px);
  }
}

@media (min-width: 1441px) {
  .grid-responsive-auto {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--knowledge-grid-gap, 22px);
    max-width: 1440px;
    margin: 0 auto;
  }
}

/* 分页容器 */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: var(--knowledge-pagination-padding, 24px 0);
}

.resource-pagination {
  border-radius: var(--knowledge-pagination-radius, 8px);
}

/* 无限滚动加载 */
.infinite-loading {
  display: flex;
  justify-content: center;
  padding: var(--knowledge-infinite-padding, 24px 0);
}

.infinite-loading-placeholder {
  width: 100%;
  height: var(--knowledge-infinite-height, 60px);
}

/* 悬浮按钮容器 - 重新设计 */
.float-buttons-container {
  position: fixed;
  right: 24px;
  bottom: 24px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 悬浮按钮基础样式 */
.float-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 72px;
  height: 72px;
  background: var(--ant-color-primary, #1677ff);
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(22, 119, 255, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  outline: none;
  user-select: none;
}

.float-button:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 12px 32px rgba(22, 119, 255, 0.4),
    0 6px 16px rgba(0, 0, 0, 0.2);
}

.float-button:active {
  transform: translateY(-2px) scale(1.02);
}

/* 按钮图标 */
.float-btn-icon {
  font-size: 20px;
  color: white;
  margin-bottom: 2px;
}

/* 按钮文字 */
.float-btn-text {
  font-size: 10px;
  color: white;
  font-weight: 500;
  line-height: 1;
}

/* 特定按钮样式 */
.add-resource-btn {
  background: var(--ant-color-primary, #1677ff);
}

.back-to-top-btn {
  background: var(--ant-color-text-secondary, #666);
}

.back-to-top-btn:hover {
  background: var(--ant-color-text, #333);
  box-shadow: 0 12px 32px rgba(102, 102, 102, 0.4),
    0 6px 16px rgba(0, 0, 0, 0.2);
}

/* 响应式悬浮按钮定位 - 大屏幕下更靠近内容区域 */
@media (min-width: 1500px) {
  .float-buttons-container {
    /* 在大屏幕上，按钮容器距离内容区域右边缘固定距离 */
    right: calc(50vw - 720px + 48px);
    /* 720px = 1440px/2, 48px = 24px内边距 + 24px间距 */
  }
}

@media (min-width: 1920px) {
  .float-buttons-container {
    /* 在超大屏幕上，限制最大距离 */
    right: calc(50vw - 720px + 72px);
    /* 增加到72px间距 */
  }
}

/* 移动端悬浮按钮优化 */
@media (max-width: 768px) {
  .float-buttons-container {
    right: 16px;
    bottom: 16px;
    gap: 8px;
  }

  .float-button {
    width: 56px;
    height: 56px;
    border-radius: 12px;
  }

  .float-btn-icon {
    font-size: 18px;
  }

  .float-btn-text {
    font-size: 9px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .knowledge-view-container {
    padding: 16px;
  }

  .function-controls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .filter-controls {
    width: 100%;
  }

  .filter-item {
    flex-direction: column;
    align-items: stretch;
    gap: 6px;
  }

  .filter-label {
    font-size: 13px;
  }

  .category-selector,
  .sort-selector {
    min-width: auto;
    width: 100%;
  }

  .action-controls {
    flex-direction: column;
    gap: 12px;
    width: 100%;
    margin-top: 12px;
  }

  .action-controls .filter-item {
    width: 100%;
  }

  .tags-section {
    flex-direction: column;
    align-items: stretch;
    gap: 6px;
  }

  .tags-scroll-area {
    max-height: 80px;
    /* 移动端减少高度 */
  }
}

.filter-section {
  flex-direction: column;
  align-items: stretch;
  gap: var(--knowledge-filter-section-gap-mobile, 4px);
}

.category-selector {
  min-width: auto;
  width: 100%;
}

/* 暗色主题适配 - 完全对标 Ant Design 官网标准 */
.dark {
  /* 知识库页面暗色主题变量 - 基于 Ant Design 官网标准 */
  --knowledge-bg: #000000;
  --knowledge-title-color: rgba(255, 255, 255, 0.85);
  --knowledge-desc-color: rgba(255, 255, 255, 0.65);
  --knowledge-filter-label-color: rgba(255, 255, 255, 0.85);
  --knowledge-card-border: #424242;
  --knowledge-card-shadow: 0 2px 8px rgba(0, 0, 0, 0.45);
  --knowledge-empty-icon-color: rgba(255, 255, 255, 0.45);
  --knowledge-empty-desc-color: rgba(255, 255, 255, 0.85);
  --knowledge-empty-sub-color: rgba(255, 255, 255, 0.65);

  /* Ant Design 组件暗色模式变量覆盖 */
  --ant-color-bg-container: #141414;
  --ant-color-bg-elevated: #1f1f1f;
  --ant-color-border: #424242;
  --ant-color-text: rgba(255, 255, 255, 0.85);
  --ant-color-text-secondary: rgba(255, 255, 255, 0.65);
  --ant-color-text-placeholder: rgba(255, 255, 255, 0.45);
}



.dark .knowledge-view-container {
  background: #000000 !important;
  color: rgba(255, 255, 255, 0.85);
}

/* 暗色模式下的操作栏样式已通过全局变量控制，无需额外样式 */

.dark .function-card {
  background: #141414 !important;
  border-color: #424242 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.45);
}

.dark .function-card .ant-card-body {
  background: #141414 !important;
}

.dark .function-row+.function-row {
  border-top-color: #424242;
}

/* 暗色模式下的筛选标签 */
.dark .filter-label {
  color: rgba(255, 255, 255, 0.85) !important;
}

.dark .function-content {
  color: rgba(255, 255, 255, 0.85);
}

.dark .empty-state {
  color: rgba(255, 255, 255, 0.85);
}

/* 暗黑模式下的空状态元素强制样式 */
.dark .empty-icon {
  color: rgba(255, 255, 255, 0.45) !important;
}

.dark .empty-description {
  color: rgba(255, 255, 255, 0.85) !important;
}

.dark .empty-subtitle {
  color: rgba(255, 255, 255, 0.65) !important;
}

/* 暗色模式下的选择器优化 - 最高优先级覆盖 */
.dark .category-selector,
.dark .sort-selector,
.dark .ant-select,
.dark .ant-tree-select {
  background-color: #141414 !important;
  background: #141414 !important;
  border-color: #424242 !important;
  color: rgba(255, 255, 255, 0.85) !important;
}

/* 选择器内部元素强制覆盖 */
.dark .category-selector .ant-tree-select-selector,
.dark .sort-selector .ant-select-selector,
.dark .ant-tree-select .ant-tree-select-selector,
.dark .ant-select .ant-select-selector {
  background-color: #141414 !important;
  background: #141414 !important;
  border-color: #424242 !important;
  color: rgba(255, 255, 255, 0.85) !important;
  box-shadow: none !important;
}

/* 选择器状态覆盖 */
.dark .category-selector:hover .ant-tree-select-selector,
.dark .sort-selector:hover .ant-select-selector,
.dark .ant-tree-select:hover .ant-tree-select-selector,
.dark .ant-select:hover .ant-select-selector {
  background-color: #141414 !important;
  background: #141414 !important;
  border-color: #4096ff !important;
}

.dark .category-selector:hover,
.dark .sort-selector:hover {
  border-color: #4096ff !important;
  background-color: #141414 !important;
}

.dark .category-selector:focus-within,
.dark .sort-selector:focus-within {
  border-color: #1677ff !important;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.2) !important;
  background-color: #141414 !important;
}

/* 暗黑模式下的排序选项样式 - 提高文字可见度 */
.dark .sort-icon {
  color: rgba(255, 255, 255, 0.75) !important;
}

/* 暗黑模式下的排序选择器下拉菜单优化 */
.dark .sort-selector .ant-select-dropdown {
  background: #1f1f1f !important;
  border-color: #424242 !important;
}

.dark .sort-selector .ant-select-item {
  color: rgba(255, 255, 255, 0.9) !important;
  background: transparent !important;
}

.dark .sort-selector .ant-select-item:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.95) !important;
}

/* 暗黑模式下排序选项文字增强 */
.dark .sort-option span {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* 暗色模式下选择器内部样式 - 强制覆盖 */
.dark :deep(.ant-select-selector),
.dark :deep(.ant-tree-select-selector) {
  background-color: #141414 !important;
  background: #141414 !important;
  border-color: #424242 !important;
  color: rgba(255, 255, 255, 0.85) !important;
}

.dark :deep(.ant-select-selector:hover),
.dark :deep(.ant-tree-select-selector:hover) {
  background-color: #141414 !important;
  background: #141414 !important;
  border-color: #4096ff !important;
}

.dark :deep(.ant-select-selector:focus),
.dark :deep(.ant-tree-select-selector:focus) {
  background-color: #141414 !important;
  background: #141414 !important;
  border-color: #1677ff !important;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.2) !important;
}

.dark :deep(.ant-select-selection-placeholder),
.dark :deep(.ant-tree-select-selection-placeholder) {
  color: rgba(255, 255, 255, 0.45) !important;
}

.dark :deep(.ant-select-selection-item),
.dark :deep(.ant-tree-select-selection-item) {
  color: rgba(255, 255, 255, 0.85) !important;
}

.dark :deep(.ant-select-arrow),
.dark :deep(.ant-tree-select-arrow) {
  color: rgba(255, 255, 255, 0.45) !important;
}



/* 暗色模式下的下拉面板 - 完全对标官网效果 */
.dark :deep(.ant-select-dropdown) {
  background: #1f1f1f !important;
  border-color: #424242 !important;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.45),
    0 3px 6px -4px rgba(0, 0, 0, 0.5),
    0 9px 28px 8px rgba(0, 0, 0, 0.3) !important;
}

.dark :deep(.ant-select-item) {
  color: rgba(255, 255, 255, 0.85) !important;
  background: transparent !important;
}

.dark :deep(.ant-select-item-option-active) {
  background-color: rgba(255, 255, 255, 0.08) !important;
  color: rgba(255, 255, 255, 0.85) !important;
}

.dark :deep(.ant-select-item-option-selected) {
  background-color: #111b26 !important;
  color: #1677ff !important;
  font-weight: 600 !important;
}

.dark :deep(.ant-tree-select-dropdown) {
  background: #1f1f1f !important;
  border-color: #424242 !important;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.45),
    0 3px 6px -4px rgba(0, 0, 0, 0.5),
    0 9px 28px 8px rgba(0, 0, 0, 0.3) !important;
  min-width: 280px !important;
  width: auto !important;
}

.dark :deep(.ant-tree-treenode) {
  color: rgba(255, 255, 255, 0.85) !important;
}

.dark :deep(.ant-tree-node-content-wrapper) {
  background: transparent !important;
  color: rgba(255, 255, 255, 0.85) !important;
}

.dark :deep(.ant-tree-node-content-wrapper:hover) {
  background-color: rgba(255, 255, 255, 0.08) !important;
  color: rgba(255, 255, 255, 0.85) !important;
}

.dark :deep(.ant-tree-node-selected .ant-tree-node-content-wrapper) {
  background-color: #111b26 !important;
  color: #1677ff !important;
}

/* 暗色模式下的清空按钮 */
.dark :deep(.ant-select-clear),
.dark :deep(.ant-tree-select-clear) {
  color: rgba(255, 255, 255, 0.45) !important;
}

.dark :deep(.ant-select-clear:hover),
.dark :deep(.ant-tree-select-clear:hover) {
  color: rgba(255, 255, 255, 0.65) !important;
}

/* 暗色模式下的悬浮按钮优化 */
.dark .float-button {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.6),
    0 4px 12px rgba(0, 0, 0, 0.4);
}

.dark .float-button:hover {
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.7),
    0 6px 16px rgba(0, 0, 0, 0.5);
}

.dark .add-resource-btn {
  background: var(--ant-color-primary, #1677ff);
  box-shadow: 0 8px 24px rgba(22, 119, 255, 0.4),
    0 4px 12px rgba(0, 0, 0, 0.4);
}

.dark .add-resource-btn:hover {
  box-shadow: 0 12px 32px rgba(22, 119, 255, 0.5),
    0 6px 16px rgba(0, 0, 0, 0.5);
}

.dark .back-to-top-btn {
  background: rgba(255, 255, 255, 0.15);
}

.dark .back-to-top-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 12px 32px rgba(255, 255, 255, 0.1),
    0 6px 16px rgba(0, 0, 0, 0.5);
}





/* 暗色模式下的按钮 */
.dark .ant-btn-primary {
  background: #1677ff !important;
  border-color: #1677ff !important;
}

.dark .ant-btn-default {
  background: #1f1f1f !important;
  border-color: #424242 !important;
  color: rgba(255, 255, 255, 0.85) !important;
}

.dark .ant-btn-default:hover {
  background: #262626 !important;
  border-color: #595959 !important;
}



/* 暗色模式下的标签 */
.dark .ant-tag {
  background: rgba(255, 255, 255, 0.08) !important;
  border-color: rgba(255, 255, 255, 0.18) !important;
  color: rgba(255, 255, 255, 0.85) !important;
}

.dark .ant-tag.ant-tag-blue {
  background: rgba(22, 119, 255, 0.15) !important;
  border-color: rgba(22, 119, 255, 0.3) !important;
  color: #1677ff !important;
}
</style>
