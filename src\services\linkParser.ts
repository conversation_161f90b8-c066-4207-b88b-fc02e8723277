interface LinkMetadata {
  title?: string
  description?: string
  image?: string
  keywords?: string[]
  url: string
}

interface ParseResponse {
  success: boolean
  data?: LinkMetadata
  error?: string
}

class LinkParserService {
  private defaultApiUrl = 'https://meta-thief.itea.dev/api/meta'
  
  // 从localStorage获取用户配置的API URL
  private getApiUrl(): string {
    const customUrl = localStorage.getItem('linkParserApiUrl')
    if (customUrl) {
      return customUrl
    }
    
    // 在开发环境中使用代理
    if (import.meta.env.DEV) {
      return '/api/meta'
    }
    
    return this.defaultApiUrl
  }
  
  // 设置API URL
  setApiUrl(url: string): void {
    localStorage.setItem('linkParserApiUrl', url)
  }
  
  // 获取当前API URL
  getCurrentApiUrl(): string {
    return this.getApiUrl()
  }
  
  // 解析链接元数据
  async parseLink(url: string): Promise<ParseResponse> {
    try {
      // 验证URL格式
      if (!this.isValidUrl(url)) {
        return {
          success: false,
          error: '请输入有效的URL'
        }
      }
      
      const apiUrl = this.getApiUrl()
      // 指定需要的meta字段，提高API效率
      const metaFields = [
        'title', 'description', 'keywords', 'favicon', 'author',
        'ogTitle', 'ogDescription', 'ogImage', 'ogSiteName',
        'twitterTitle', 'twitterDescription', 'twitterImage', 'twitterCreator'
      ].join(',')

      const response = await fetch(`${apiUrl}?url=${encodeURIComponent(url)}&meta=${metaFields}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      // 调试：打印API响应数据
      console.log('MetaThief API 响应:', data)

      // 处理API响应数据
      const metadata: LinkMetadata = {
        url,
        title: data.title || data.ogTitle || data.twitterTitle || '',
        description: data.description || data.ogDescription || data.twitterDescription || '',
        image: data.ogImage || data.twitterImage || data.favicon || '',
        keywords: this.extractKeywords(data)
      }

      console.log('解析后的元数据:', metadata)

      return {
        success: true,
        data: metadata
      }
      
    } catch (error) {
      console.error('链接解析失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '解析失败，请检查网络连接'
      }
    }
  }
  
  // 提取关键词
  private extractKeywords(data: any): string[] {
    const keywords: string[] = []
    
    // 从keywords字段提取
    if (data.keywords) {
      if (typeof data.keywords === 'string') {
        keywords.push(...data.keywords.split(',').map((k: string) => k.trim()))
      } else if (Array.isArray(data.keywords)) {
        keywords.push(...data.keywords)
      }
    }
    
    // 从网站名称提取
    if (data.ogSiteName) {
      keywords.push(data.ogSiteName)
    }
    
    // 从作者信息提取
    if (data.author) {
      keywords.push(data.author)
    }
    
    // 从Twitter创建者提取
    if (data.twitterCreator) {
      keywords.push(data.twitterCreator.replace('@', ''))
    }
    
    // 从标题中提取关键词（智能分词）
    const titleSources = [data.title, data.ogTitle, data.twitterTitle].filter(Boolean)
    titleSources.forEach(title => {
      if (title) {
        const titleWords = title
          .split(/[\s\-_|:：]+/)
          .filter((word: string) => word.length > 2 && word.length < 20)
          .filter((word: string) => !/^\d+$/.test(word)) // 过滤纯数字
          .slice(0, 3) // 最多取3个
        keywords.push(...titleWords)
      }
    })
    
    // 从描述中提取关键词（简单处理）
    const descSources = [data.description, data.ogDescription, data.twitterDescription].filter(Boolean)
    descSources.forEach(desc => {
      if (desc && desc.length < 100) { // 只处理较短的描述
        const descWords = desc
          .split(/[\s,，。.]+/)
          .filter((word: string) => word.length > 3 && word.length < 15)
          .slice(0, 2) // 最多取2个
        keywords.push(...descWords)
      }
    })
    
    // 去重并过滤
    return [...new Set(keywords)]
      .filter(keyword => keyword && keyword.length > 1 && keyword.length < 30)
      .filter(keyword => !keyword.match(/^(http|www|com|org|net|cn)$/i)) // 过滤常见无意义词
      .slice(0, 10) // 最多返回10个关键词
  }
  
  // 验证URL格式
  private isValidUrl(url: string): boolean {
    try {
      new URL(url)
      return url.startsWith('http://') || url.startsWith('https://')
    } catch {
      return false
    }
  }
  
  // 测试API连接
  async testApiConnection(apiUrl?: string): Promise<boolean> {
    try {
      const testUrl = apiUrl || this.getApiUrl()
      const response = await fetch(`${testUrl}?url=https://example.com`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      })
      
      return response.ok || response.status === 400 // 400也算正常，说明API在工作
    } catch {
      return false
    }
  }
}

export const linkParserService = new LinkParserService()
export type { LinkMetadata, ParseResponse }
