import { createApp, type App } from 'vue'
import BaseNotification from '@/components/ui/BaseNotification.vue'

// 通知类型
type NotificationType = 'success' | 'error' | 'warning' | 'info'

// 通知位置
type NotificationPosition = 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center'

// 通知选项
interface NotificationOptions {
  type?: NotificationType
  title: string
  message?: string
  duration?: number // 自动关闭时间（毫秒），0表示不自动关闭
  position?: NotificationPosition
  showProgress?: boolean
  clickToClose?: boolean
}

// 通知实例管理
class NotificationManager {
  private instances: App[] = []

  // 显示通知
  show(options: NotificationOptions) {
    // 创建容器元素
    const container = document.createElement('div')
    document.body.appendChild(container)

    // 创建Vue应用实例
    const app = createApp(BaseNotification, {
      ...options,
      onClose: () => {
        // 关闭时清理
        this.cleanup(app, container)
      }
    })

    // 挂载应用
    app.mount(container)

    // 保存实例引用
    this.instances.push(app)

    return app
  }

  // 清理实例
  private cleanup(app: App, container: HTMLElement) {
    // 从实例列表中移除
    const index = this.instances.indexOf(app)
    if (index > -1) {
      this.instances.splice(index, 1)
    }

    // 卸载应用
    app.unmount()

    // 移除容器元素
    if (container.parentNode) {
      container.parentNode.removeChild(container)
    }
  }

  // 显示成功通知
  success(title: string, message?: string, options?: Partial<NotificationOptions>) {
    return this.show({
      type: 'success',
      title,
      message,
      ...options
    })
  }

  // 显示错误通知
  error(title: string, message?: string, options?: Partial<NotificationOptions>) {
    return this.show({
      type: 'error',
      title,
      message,
      ...options
    })
  }

  // 显示警告通知
  warning(title: string, message?: string, options?: Partial<NotificationOptions>) {
    return this.show({
      type: 'warning',
      title,
      message,
      ...options
    })
  }

  // 显示信息通知
  info(title: string, message?: string, options?: Partial<NotificationOptions>) {
    return this.show({
      type: 'info',
      title,
      message,
      ...options
    })
  }

  // 清除所有通知
  clearAll() {
    this.instances.forEach(app => {
      app.unmount()
    })
    this.instances = []

    // 清理所有通知容器
    const containers = document.querySelectorAll('[data-notification-container]')
    containers.forEach(container => {
      if (container.parentNode) {
        container.parentNode.removeChild(container)
      }
    })
  }
}

// 创建全局实例
const notification = new NotificationManager()

// 导出便捷方法
export const showNotification = (options: NotificationOptions) => notification.show(options)
export const showSuccess = (title: string, message?: string, options?: Partial<NotificationOptions>) => 
  notification.success(title, message, options)
export const showError = (title: string, message?: string, options?: Partial<NotificationOptions>) => 
  notification.error(title, message, options)
export const showWarning = (title: string, message?: string, options?: Partial<NotificationOptions>) => 
  notification.warning(title, message, options)
export const showInfo = (title: string, message?: string, options?: Partial<NotificationOptions>) => 
  notification.info(title, message, options)

export default notification
