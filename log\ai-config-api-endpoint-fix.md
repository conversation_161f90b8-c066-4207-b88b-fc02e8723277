# AI配置API端点路径修复日志

## 2024-12-19 修复API端点路径问题

### 问题描述
用户按照官方文档配置AI服务，但测试连接时出现404错误。错误信息显示：
```
POST https://api.chatanywhere.tech/v1 404 (Not Found)
API请求失败 (404): {"timestamp":1753091799884,"status":404,"error":"Not Found","path":"/"}
```

### 问题根本原因
1. **API端点路径不完整**
   - 用户配置的baseUrl是 `https://api.chatanywhere.tech/v1`
   - 但实际的OpenAI API端点应该是 `https://api.chatanywhere.tech/v1/chat/completions`
   - 我们的代码直接向baseUrl发送请求，缺少具体的API路径

2. **不同服务商的端点差异**
   - OpenAI: `/chat/completions`
   - Claude: `/messages`
   - Gemini: `/generateContent`
   - 我们没有根据服务商类型构建正确的端点

3. **认证方式差异**
   - OpenAI: `Authorization: Bearer {apiKey}`
   - Claude: `x-api-key: {apiKey}` + `anthropic-version: 2023-06-01`
   - Gemini: `x-goog-api-key: {apiKey}`
   - 我们使用了统一的Bearer认证，不适用于所有服务商

### 修复内容

#### 1. 新增API端点构建方法
**文件**: `src/services/aiConfigDatabaseService.ts`

```typescript
private buildApiUrl(baseUrl: string, provider: string): string {
  // 移除baseUrl末尾的斜杠
  const cleanBaseUrl = baseUrl.replace(/\/+$/, '')
  
  switch (provider) {
    case 'openai':
      return `${cleanBaseUrl}/chat/completions`
    
    case 'claude':
      return `${cleanBaseUrl}/messages`
    
    case 'gemini':
      return `${cleanBaseUrl}/generateContent`
    
    default:
      return `${cleanBaseUrl}/chat/completions`
  }
}
```

#### 2. 新增请求头构建方法
```typescript
private buildRequestHeaders(provider: string, apiKey: string): Record<string, string> {
  const baseHeaders = {
    'Content-Type': 'application/json',
  }

  switch (provider) {
    case 'openai':
      return {
        ...baseHeaders,
        'Authorization': `Bearer ${apiKey}`,
      }

    case 'claude':
      return {
        ...baseHeaders,
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01',
      }

    case 'gemini':
      return {
        ...baseHeaders,
        'x-goog-api-key': apiKey,
      }

    default:
      return {
        ...baseHeaders,
        'Authorization': `Bearer ${apiKey}`,
      }
  }
}
```

#### 3. 修改测试请求逻辑
```typescript
// 修复前：直接使用baseUrl
const response = await fetch(config.baseUrl, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${config.apiKey}`,
    ...this.getProviderHeaders(config.provider),
  },
  // ...
})

// 修复后：构建完整端点和正确请求头
const apiUrl = this.buildApiUrl(config.baseUrl, config.provider)
const headers = this.buildRequestHeaders(config.provider, config.apiKey)

const response = await fetch(apiUrl, {
  method: 'POST',
  headers,
  // ...
})
```

### 支持的API端点格式

#### 1. OpenAI兼容服务
- **端点**: `{baseUrl}/chat/completions`
- **认证**: `Authorization: Bearer {apiKey}`
- **示例**: 
  - ChatAnywhere: `https://api.chatanywhere.tech/v1/chat/completions`
  - OpenAI官方: `https://api.openai.com/v1/chat/completions`

#### 2. Claude API
- **端点**: `{baseUrl}/messages`
- **认证**: `x-api-key: {apiKey}` + `anthropic-version: 2023-06-01`
- **示例**: `https://api.anthropic.com/v1/messages`

#### 3. Gemini API
- **端点**: `{baseUrl}/generateContent`
- **认证**: `x-goog-api-key: {apiKey}`
- **示例**: `https://generativelanguage.googleapis.com/v1/models/gemini-pro/generateContent`

### 用户配置指南

#### OpenAI兼容服务配置
```
服务商: openai
API端点: https://api.chatanywhere.tech/v1
API Key: sk-xxxxxxxxxxxxxxxx
模型: gpt-3.5-turbo
```

#### Claude配置
```
服务商: claude
API端点: https://api.anthropic.com/v1
API Key: sk-ant-xxxxxxxxxxxxxxxx
模型: claude-3-5-sonnet-20241022
```

#### Gemini配置
```
服务商: gemini
API端点: https://generativelanguage.googleapis.com/v1/models/gemini-pro
API Key: AIzaxxxxxxxxxxxxxxxx
模型: gemini-pro
```

### 错误处理改进

#### 1. 更详细的调试信息
```typescript
console.log('完整的API端点:', apiUrl)
console.log('请求头:', headers)
console.log('构建的测试请求:', requestBody)
```

#### 2. 端点验证
- 自动移除baseUrl末尾的多余斜杠
- 根据服务商类型构建正确的完整端点
- 验证端点格式的正确性

#### 3. 认证方式验证
- 根据服务商使用正确的认证头
- 添加必要的版本信息和特殊头部

### 常见配置错误和解决方案

#### 1. 404 Not Found
- **原因**: API端点路径不完整
- **解决**: 确保baseUrl不包含具体的API路径，系统会自动添加

#### 2. 401 Unauthorized
- **原因**: API Key错误或认证方式不正确
- **解决**: 检查API Key格式和服务商认证方式

#### 3. 403 Forbidden
- **原因**: API Key权限不足或配额用完
- **解决**: 检查API Key权限和账户余额

#### 4. 500 Internal Server Error
- **原因**: 服务商服务器问题
- **解决**: 稍后重试或联系服务商

### 测试验证
修复后需要验证：
1. ✅ OpenAI兼容服务（如ChatAnywhere）能正常连接
2. ✅ Claude API能正常连接
3. ✅ Gemini API能正常连接
4. ✅ 错误的端点会显示正确的错误信息
5. ✅ 错误的API Key会显示认证失败

### 相关文件修改
- `src/services/aiConfigDatabaseService.ts`
  - 新增 `buildApiUrl()` 方法
  - 新增 `buildRequestHeaders()` 方法
  - 修改 `testConfig()` 方法的请求逻辑

### 向后兼容性
- 保持原有的配置格式不变
- 用户只需要配置baseUrl，不需要包含具体的API路径
- 系统自动根据服务商类型构建正确的端点

## 修复状态：✅ 完成
- API端点路径问题已解决
- 支持多种服务商的正确端点格式
- 认证方式已针对不同服务商优化
- 用户可以正常测试连接
