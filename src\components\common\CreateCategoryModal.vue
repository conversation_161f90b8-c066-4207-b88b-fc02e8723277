<template>
  <a-modal v-model:open="visible" title="添加新分类" width="600px" :footer="null" @cancel="handleCancel">
    <div class="add-form-container">
      <div class="add-category-form">
        <a-form :model="formData" :rules="formRules" layout="vertical" @finish="handleSubmit" ref="formRef"
          class="form-content">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="分类名称" name="name">
                <a-input v-model:value="formData.name" placeholder="请输入分类名称" @press-enter="handleSubmit" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="父分类" name="parent_id">
                <a-tree-select v-model:value="formData.parent_id" :tree-data="categoryTreeData" placeholder="选择父分类（可选）"
                  allow-clear tree-default-expand-all
                  :field-names="{ children: 'children', label: 'title', value: 'value', key: 'key' }">
                  <template #title="{ title }">
                    <span>{{ title }}</span>
                  </template>
                </a-tree-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit" :loading="loading">
                <PlusOutlined v-if="!loading" />
                {{ loading ? '添加中...' : '添加分类' }}
              </a-button>
              <a-button @click="handleReset">
                重置
              </a-button>
              <a-button @click="handleCancel">
                取消
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { categoryService } from '@/services/categoryService'
import type { Category } from '@/types'

interface Props {
  open: boolean
  categories: Category[]
  initialName?: string
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'success', category: Category): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref()
const loading = ref(false)
const formData = ref({
  name: '',
  parent_id: null as number | null
})

// 计算属性
const visible = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

// 转换分类数据为树形结构
const categoryTreeData = computed(() => {
  const buildTreeData = (parentId: number = 0): any[] => {
    return props.categories
      .filter(cat => cat.parent_id === parentId)
      .map(category => ({
        key: category.id!,
        value: category.id!,
        title: category.name,
        children: buildTreeData(category.id!)
      }))
      .filter(node => node.children.length > 0 || node.value)
  }

  return buildTreeData()
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 50, message: '分类名称长度应在1-50个字符之间', trigger: 'blur' },
    {
      validator: async (rule: any, value: string) => {
        if (value && props.categories.some(cat => cat.name === value.trim())) {
          throw new Error('分类名称已存在')
        }
      },
      trigger: 'blur'
    }
  ]
}

// 监听初始名称变化
watch(() => props.initialName, (newName) => {
  if (newName) {
    formData.value.name = newName
  }
}, { immediate: true })

// 监听对话框打开状态
watch(() => props.open, (isOpen) => {
  if (isOpen && props.initialName) {
    formData.value.name = props.initialName
  }
})

// 方法
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()

    loading.value = true

    const categoryId = await categoryService.createCategory({
      name: formData.value.name.trim(),
      parent_id: formData.value.parent_id || 0,
      sort_order: props.categories.length + 1
    })

    // 获取新创建的分类信息
    const newCategory = await categoryService.getCategoryById(categoryId)
    if (!newCategory) {
      throw new Error('创建分类后无法获取分类信息')
    }

    message.success('分类创建成功！')
    emit('success', newCategory)
    handleReset()
    visible.value = false

  } catch (error) {
    console.error('创建分类失败:', error)
    message.error('创建分类失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleReset = () => {
  formData.value.name = props.initialName || ''
  formData.value.parent_id = null
  formRef.value?.clearValidate()
}

const handleCancel = () => {
  handleReset()
  emit('cancel')
  visible.value = false
}
</script>

<style scoped>
/* 添加分类表单样式 - 对话框内简洁样式 */
.add-form-container {
  margin-bottom: 0;
}

.add-category-form {
  padding: 0;
}

.form-content {
  margin: 0;
}
</style>
