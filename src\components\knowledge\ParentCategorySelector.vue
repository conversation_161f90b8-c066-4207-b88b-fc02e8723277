<template>
  <div>
    <BaseDropdown ref="dropdownRef" width="trigger" class="relative z-[10000]">
      <template #trigger>
        <button type="button" :class="[
          'w-full flex items-center justify-between px-3 py-2 rounded-xl transition-all duration-200',
          'focus:outline-none text-left',
          'bg-gray-50 border-0 hover:bg-gray-100 focus:bg-white focus:border focus:border-primary-500 dark:bg-gray-700 dark:hover:bg-gray-650 dark:focus:bg-gray-800 dark:focus:border-primary-400'
        ]">
          <span :class="selectedCategoryPath ? 'text-gray-900 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'">
            {{ selectedCategoryPath || placeholder }}
          </span>
          <div class="i-heroicons-chevron-down w-4 h-4 text-gray-400"></div>
        </button>
      </template>

      <div
        class="w-full max-h-96 overflow-hidden bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg">
        <!-- 搜索框 -->
        <div class="p-3 border-b border-gray-200 dark:border-gray-600">
          <BaseInput v-model="searchQuery" placeholder="搜索分类..." prefix-icon="i-heroicons-magnifying-glass" size="sm"
            clearable />
        </div>

        <!-- 树形分类列表 -->
        <div class="max-h-80 overflow-y-auto">
          <div class="py-1">
            <!-- 无父分类选项 -->
            <DropdownItem text="无父分类（根分类）" :icon="!modelValue ? 'i-heroicons-check' : ''"
              @click.stop="selectCategory(null)" />

            <!-- 分类树 -->
            <ParentCategoryTreeNode v-for="category in filteredCategories" :key="category.id" :category="category"
              :selected-id="modelValue" :search-query="searchQuery" :level="0" :all-categories="availableCategories"
              @select="selectCategory" />
          </div>
        </div>

        <!-- 无匹配结果 -->
        <div v-if="filteredCategories.length === 0 && searchQuery"
          class="px-4 py-8 text-sm text-gray-500 dark:text-gray-400 text-center">
          未找到匹配的分类
        </div>
      </div>
    </BaseDropdown>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import BaseDropdown from '@/components/ui/BaseDropdown.vue'
import BaseInput from '@/components/ui/BaseInput.vue'
import DropdownItem from '@/components/ui/DropdownItem.vue'
import ParentCategoryTreeNode from './ParentCategoryTreeNode.vue'
import type { Category } from '@/types'

interface Props {
  modelValue: number | null
  categories: Category[]
  placeholder?: string
  excludeId?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '选择父分类'
})

const emit = defineEmits<{
  'update:modelValue': [value: number | null]
}>()

// 响应式数据
const dropdownRef = ref()
const searchQuery = ref('')

// 计算属性
const selectedCategory = computed(() =>
  props.categories.find(cat => cat.id === props.modelValue)
)

const selectedCategoryPath = computed(() => {
  if (!selectedCategory.value) return ''
  return getCategoryPath(selectedCategory.value)
})

// 可用的分类（排除指定的分类）
const availableCategories = computed(() => {
  if (!props.excludeId) {
    return props.categories
  }

  const excludeIds = getDescendantIds(props.excludeId)
  excludeIds.push(props.excludeId)
  return props.categories.filter(cat => !excludeIds.includes(cat.id!))
})

const filteredCategories = computed(() => {
  const categories = availableCategories.value

  if (!searchQuery.value) {
    return categories.filter(cat => cat.parent_id === 0)
  }

  return categories.filter(category =>
    category.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// 方法
const getDescendantIds = (categoryId: number): number[] => {
  const descendants: number[] = []
  const children = props.categories.filter(cat => cat.parent_id === categoryId)

  for (const child of children) {
    descendants.push(child.id!)
    descendants.push(...getDescendantIds(child.id!))
  }

  return descendants
}

const getCategoryPath = (category: Category): string => {
  const path = [category.name]
  let current = category

  while (current.parent_id && current.parent_id !== 0) {
    const parent = props.categories.find(cat => cat.id === current.parent_id)
    if (parent) {
      path.unshift(parent.name)
      current = parent
    } else {
      break
    }
  }

  return path.join(' > ')
}

const selectCategory = (categoryId: number | null) => {
  emit('update:modelValue', categoryId)
  dropdownRef.value?.close()
}
</script>
