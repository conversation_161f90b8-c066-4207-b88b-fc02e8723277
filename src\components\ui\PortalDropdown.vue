<template>
  <div class="relative" ref="dropdownRef">
    <!-- 触发器 -->
    <div @click="toggle" class="cursor-pointer">
      <slot name="trigger" :isOpen="isOpen">
        <button class="btn-secondary">
          <div v-if="triggerIcon" :class="[triggerIcon, 'mr-2']"></div>
          {{ triggerText }}
          <div :class="['i-heroicons-chevron-down ml-2 transition-transform duration-200', { 'rotate-180': isOpen }]">
          </div>
        </button>
      </slot>
    </div>

    <!-- 下拉菜单 - 使用 Portal 渲染到 body -->
    <Teleport to="body">
      <Transition enter-active-class="transition-all duration-200" enter-from-class="opacity-0 scale-95 translate-y-1"
        enter-to-class="opacity-100 scale-100 translate-y-0" leave-active-class="transition-all duration-200"
        leave-from-class="opacity-100 scale-100 translate-y-0" leave-to-class="opacity-0 scale-95 translate-y-1">
        <div v-if="isOpen" :class="menuClasses" :style="menuStyle" @click.stop>
          <slot :close="close"></slot>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

interface Props {
  triggerText?: string
  triggerIcon?: string
  placement?: 'bottom-start' | 'bottom-end' | 'top-start' | 'top-end'
  width?: 'auto' | 'trigger' | 'full'
}

const props = withDefaults(defineProps<Props>(), {
  placement: 'bottom-start',
  width: 'auto'
})

const emit = defineEmits<{
  open: []
  close: []
}>()

const dropdownRef = ref<HTMLElement>()
const isOpen = ref(false)
const menuPosition = ref({ top: 0, left: 0, width: 0 })

const menuClasses = computed(() => {
  const baseClasses = [
    'fixed z-[10000] bg-white dark:bg-gray-800 rounded-xl shadow-lg shadow-primary-200/50 dark:shadow-primary-900/30 border border-primary-200 dark:border-primary-700',
    'py-2 animate-slide-down'
  ]

  // 宽度样式
  const widthClasses = {
    auto: 'min-w-48',
    trigger: '',
    full: 'w-screen max-w-sm'
  }

  return [
    ...baseClasses,
    widthClasses[props.width]
  ]
})

const menuStyle = computed(() => {
  const style: Record<string, string> = {
    top: `${menuPosition.value.top}px`,
    left: `${menuPosition.value.left}px`
  }

  if (props.width === 'trigger') {
    style.width = `${menuPosition.value.width}px`
  }

  return style
})

const updatePosition = () => {
  if (!dropdownRef.value) return

  const rect = dropdownRef.value.getBoundingClientRect()
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft

  let top = rect.bottom + scrollTop + 8 // 8px margin
  let left = rect.left + scrollLeft

  // 根据placement调整位置
  switch (props.placement) {
    case 'bottom-end':
      left = rect.right + scrollLeft - (props.width === 'trigger' ? rect.width : 192) // 192px = min-w-48
      break
    case 'top-start':
      top = rect.top + scrollTop - 8 // 向上偏移
      break
    case 'top-end':
      top = rect.top + scrollTop - 8
      left = rect.right + scrollLeft - (props.width === 'trigger' ? rect.width : 192)
      break
  }

  // 确保不超出视口
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight
  const menuWidth = props.width === 'trigger' ? rect.width : 192

  if (left + menuWidth > viewportWidth) {
    left = viewportWidth - menuWidth - 16 // 16px margin
  }
  if (left < 16) {
    left = 16
  }

  // 如果下方空间不足，尝试显示在上方
  if (top + 300 > viewportHeight + scrollTop && rect.top > 300) {
    top = rect.top + scrollTop - 8
  }

  menuPosition.value = {
    top,
    left,
    width: rect.width
  }
}

const toggle = () => {
  if (isOpen.value) {
    close()
  } else {
    open()
  }
}

const open = () => {
  isOpen.value = true
  nextTick(() => {
    updatePosition()
  })
  emit('open')
}

const close = () => {
  isOpen.value = false
  emit('close')
}

// 点击外部关闭
const handleClickOutside = (event: MouseEvent) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    close()
  }
}

// ESC键关闭
const handleEscape = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isOpen.value) {
    close()
  }
}

// 窗口大小变化时更新位置
const handleResize = () => {
  if (isOpen.value) {
    updatePosition()
  }
}

// 滚动时更新位置
const handleScroll = () => {
  if (isOpen.value) {
    updatePosition()
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  document.addEventListener('keydown', handleEscape)
  window.addEventListener('resize', handleResize)
  window.addEventListener('scroll', handleScroll, true)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('keydown', handleEscape)
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('scroll', handleScroll, true)
})

defineExpose({
  open,
  close,
  toggle,
  isOpen
})
</script>
