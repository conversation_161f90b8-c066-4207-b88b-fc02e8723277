<template>
  <div>
    <div
      class="border border-primary-200 dark:border-primary-600 rounded-xl overflow-hidden shadow-none bg-white dark:bg-gray-800">
      <!-- 工具栏 -->
      <div
        class="px-2 sm:px-4 py-2 sm:py-3 bg-primary-50 dark:bg-primary-900/20 border-b border-primary-200 dark:border-primary-600">
        <!-- 第一行：主要格式化工具 -->
        <div class="flex items-center justify-between mb-2">
          <div class="flex items-center space-x-1 overflow-x-auto scrollbar-hide flex-1 min-w-0">
            <!-- 基础格式化按钮 -->
            <div class="flex items-center space-x-1 flex-shrink-0">
              <button type="button" v-for="tool in basicFormatTools" :key="tool.name" :title="tool.title"
                class="p-1.5 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 hover:bg-primary-100 dark:hover:bg-primary-800/50 rounded-lg transition-all duration-200 border-0 flex-shrink-0"
                @click="insertFormat(tool)">
                <div :class="[tool.icon, 'w-4 h-4']"></div>
              </button>
            </div>

            <div class="w-px h-4 bg-primary-300 dark:bg-primary-600 flex-shrink-0 mx-1"></div>

            <!-- 高级格式化按钮 -->
            <div class="flex items-center space-x-1 flex-shrink-0">
              <button type="button" v-for="tool in advancedFormatTools" :key="tool.name" :title="tool.title"
                class="p-1.5 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 hover:bg-primary-100 dark:hover:bg-primary-800/50 rounded-lg transition-all duration-200 border-0 flex-shrink-0"
                @click="insertFormat(tool)">
                <div :class="[tool.icon, 'w-4 h-4']"></div>
              </button>
            </div>
          </div>

          <!-- 预览切换和帮助 -->
          <div class="flex items-center space-x-1 flex-shrink-0 ml-2">
            <button type="button" :class="[
              'px-2 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 border-0 flex-shrink-0 whitespace-nowrap',
              showPreview
                ? 'bg-primary-500 text-white shadow-sm'
                : 'text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 hover:bg-primary-100 dark:hover:bg-primary-800/50'
            ]" @click="showPreview = !showPreview">
              <div class="i-heroicons-eye mr-1"></div>
              预览
            </button>

            <button type="button"
              class="p-1.5 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 hover:bg-primary-100 dark:hover:bg-primary-800/50 rounded-lg transition-all duration-200 border-0 flex-shrink-0"
              @click="showShortcutHelp" title="快捷键帮助 (Ctrl+/)">
              <div class="i-heroicons-question-mark-circle w-4 h-4"></div>
            </button>
          </div>
        </div>

        <!-- 第二行：结构化工具 -->
        <div class="flex items-center space-x-1 overflow-x-auto scrollbar-hide pb-1">
          <button type="button" v-for="tool in structureTools" :key="tool.name" :title="tool.title"
            class="p-1.5 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 hover:bg-primary-100 dark:hover:bg-primary-800/50 rounded-lg transition-all duration-200 border-0 flex-shrink-0"
            @click="insertFormat(tool)">
            <div :class="[tool.icon, 'w-4 h-4']"></div>
          </button>
        </div>

        <!-- 字数统计 -->
        <div class="text-xs text-gray-500 dark:text-gray-400">
          {{ wordCount }} 字
        </div>
      </div>

      <!-- 编辑区域 -->
      <div class="flex">
        <!-- 编辑器 -->
        <div :class="['flex-1', showPreview ? 'border-r border-primary-200 dark:border-primary-600' : '']">
          <textarea ref="textareaRef" :value="modelValue" :placeholder="placeholder" :class="[
            'w-full h-80 p-4 resize-none focus:outline-none transition-all duration-200 rounded-none',
            'font-mono text-sm leading-relaxed text-gray-900 dark:text-gray-100',
            error
              ? 'bg-red-50 border border-red-300 focus:bg-white focus:border-red-500 dark:bg-red-900/10 dark:border-red-600 dark:focus:bg-gray-800 dark:focus:border-red-400'
              : 'bg-gray-50 border-0 focus:bg-white focus:border focus:border-primary-500 dark:bg-gray-700 dark:focus:bg-gray-800 dark:focus:border-primary-400'
          ]" @input="handleInput" @keydown="handleKeydown"></textarea>
        </div>

        <!-- 预览区域 -->
        <div v-if="showPreview" class="flex-1 h-80 overflow-y-auto bg-primary-25 dark:bg-primary-950/30">
          <div class="p-4">
            <MarkdownPreview :content="modelValue" />
          </div>
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <p v-if="error" class="mt-1 text-sm text-red-600">
      {{ error }}
    </p>

    <!-- 帮助提示 -->
    <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
      支持 Markdown 格式。使用工具栏快速插入格式，或按 Ctrl+/ 查看快捷键。
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import MarkdownPreview from './MarkdownPreview.vue'

interface Props {
  modelValue: string
  placeholder?: string
  error?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '输入内容...'
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

// 状态
const textareaRef = ref<HTMLTextAreaElement>()
const showPreview = ref(false)

// 基础格式化工具
const basicFormatTools = [
  { name: 'bold', title: '粗体 (Ctrl+B)', icon: 'i-heroicons-bold', format: '**', suffix: '**' },
  { name: 'italic', title: '斜体 (Ctrl+I)', icon: 'i-heroicons-italic', format: '*', suffix: '*' },
  { name: 'strikethrough', title: '删除线', icon: 'i-heroicons-minus', format: '~~', suffix: '~~' },
  { name: 'mark', title: '高亮文本', icon: 'i-heroicons-paint-brush', format: '==', suffix: '==' },
  { name: 'insert', title: '插入文本', icon: 'i-heroicons-plus-circle', format: '++', suffix: '++' }
]

// 高级格式化工具
const advancedFormatTools = [
  { name: 'code', title: '行内代码 (Ctrl+`)', icon: 'i-heroicons-code-bracket', format: '`', suffix: '`' },
  { name: 'codeblock', title: '代码块', icon: 'i-heroicons-code-bracket-square', format: '```\n', suffix: '\n```' },
  { name: 'link', title: '链接 (Ctrl+K)', icon: 'i-heroicons-link', format: '[', suffix: '](url)' },
  { name: 'image', title: '图片', icon: 'i-heroicons-photo', format: '![', suffix: '](url)' },
  { name: 'quote', title: '引用', icon: 'i-heroicons-chat-bubble-left-right', format: '> ', suffix: '' }
]

// 结构化工具
const structureTools = [
  { name: 'heading1', title: '一级标题', icon: 'i-heroicons-hashtag', format: '# ', suffix: '' },
  { name: 'heading2', title: '二级标题', icon: 'i-heroicons-hashtag', format: '## ', suffix: '' },
  { name: 'heading3', title: '三级标题', icon: 'i-heroicons-hashtag', format: '### ', suffix: '' },
  { name: 'list', title: '无序列表', icon: 'i-heroicons-list-bullet', format: '- ', suffix: '' },
  { name: 'ordered-list', title: '有序列表', icon: 'i-heroicons-numbered-list', format: '1. ', suffix: '' },
  { name: 'task-list', title: '任务列表', icon: 'i-heroicons-check-circle', format: '- [ ] ', suffix: '' },
  { name: 'table', title: '表格', icon: 'i-heroicons-table-cells', format: '| 列1 | 列2 | 列3 |\n|-----|-----|-----|\n| 内容1 | 内容2 | 内容3 |\n| 内容4 | 内容5 | 内容6 |', suffix: '' },
  { name: 'info-box', title: '信息框', icon: 'i-heroicons-information-circle', format: '::: info 信息\n', suffix: '\n:::\n' },
  { name: 'warning-box', title: '警告框', icon: 'i-heroicons-exclamation-triangle', format: '::: warning 警告\n', suffix: '\n:::\n' },
  { name: 'footnote', title: '脚注', icon: 'i-heroicons-document-text', format: '[^1]', suffix: '' },
  { name: 'hr', title: '分割线', icon: 'i-heroicons-minus', format: '\n---\n', suffix: '' }
]

// 所有格式化工具（用于快捷键处理）
const formatTools = [...basicFormatTools, ...advancedFormatTools, ...structureTools]

// 计算属性
const wordCount = computed(() => {
  return props.modelValue.replace(/\s/g, '').length
})

// 处理输入
const handleInput = (event: Event) => {
  const target = event.target as HTMLTextAreaElement
  emit('update:modelValue', target.value)
}

// 处理键盘快捷键
const handleKeydown = (event: KeyboardEvent) => {
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 'b':
        event.preventDefault()
        insertFormat(formatTools.find(t => t.name === 'bold')!)
        break
      case 'i':
        event.preventDefault()
        insertFormat(formatTools.find(t => t.name === 'italic')!)
        break
      case '`':
        event.preventDefault()
        insertFormat(formatTools.find(t => t.name === 'code')!)
        break
      case 'k':
        event.preventDefault()
        insertFormat(formatTools.find(t => t.name === 'link')!)
        break
      case '1':
        event.preventDefault()
        insertFormat(formatTools.find(t => t.name === 'heading1')!)
        break
      case '2':
        event.preventDefault()
        insertFormat(formatTools.find(t => t.name === 'heading2')!)
        break
      case '3':
        event.preventDefault()
        insertFormat(formatTools.find(t => t.name === 'heading3')!)
        break
      case 'l':
        event.preventDefault()
        if (event.shiftKey) {
          insertFormat(formatTools.find(t => t.name === 'ordered-list')!)
        } else {
          insertFormat(formatTools.find(t => t.name === 'list')!)
        }
        break
      case 'q':
        event.preventDefault()
        insertFormat(formatTools.find(t => t.name === 'quote')!)
        break
      case '/':
        event.preventDefault()
        showShortcutHelp()
        break
    }
  }

  // Tab键缩进
  if (event.key === 'Tab') {
    event.preventDefault()
    insertText('  ')
  }
}

// 插入格式
const insertFormat = (tool: typeof formatTools[0]) => {
  const textarea = textareaRef.value
  if (!textarea) return

  const start = textarea.selectionStart
  const end = textarea.selectionEnd
  const selectedText = props.modelValue.substring(start, end)

  let newText = ''
  let cursorOffset = 0

  if (['heading1', 'heading2', 'heading3', 'list', 'ordered-list', 'quote', 'task-list'].includes(tool.name)) {
    // 行首格式：在行首添加
    const lineStart = props.modelValue.lastIndexOf('\n', start - 1) + 1
    newText = props.modelValue.substring(0, lineStart) +
      tool.format +
      props.modelValue.substring(lineStart)
    cursorOffset = tool.format.length
  } else if (tool.name === 'table') {
    // 表格：插入完整表格模板
    newText = props.modelValue.substring(0, start) +
      '\n' + tool.format + '\n' +
      props.modelValue.substring(end)
    cursorOffset = tool.format.length + 2
  } else if (tool.name === 'hr') {
    // 分割线：插入分割线
    newText = props.modelValue.substring(0, start) +
      tool.format +
      props.modelValue.substring(end)
    cursorOffset = tool.format.length
  } else if (tool.name === 'codeblock') {
    // 代码块：特殊处理
    newText = props.modelValue.substring(0, start) +
      tool.format +
      selectedText +
      tool.suffix +
      props.modelValue.substring(end)
    cursorOffset = tool.format.length + (selectedText ? 0 : -4) // 将光标放在代码块内部
  } else if (['info-box', 'warning-box'].includes(tool.name)) {
    // 信息框和警告框：块级格式
    newText = props.modelValue.substring(0, start) +
      '\n' + tool.format +
      (selectedText || '在这里输入内容') +
      tool.suffix +
      props.modelValue.substring(end)
    cursorOffset = tool.format.length + 1
  } else if (tool.name === 'footnote') {
    // 脚注：插入引用和定义
    const footnoteNum = (props.modelValue.match(/\[\^\d+\]/g) || []).length + 1
    const footnoteRef = `[^${footnoteNum}]`
    const footnoteDef = `\n\n[^${footnoteNum}]: 脚注内容`

    newText = props.modelValue.substring(0, start) +
      footnoteRef +
      props.modelValue.substring(end) +
      footnoteDef
    cursorOffset = footnoteRef.length
  } else {
    // 其他格式：包围选中文本
    newText = props.modelValue.substring(0, start) +
      tool.format +
      selectedText +
      tool.suffix +
      props.modelValue.substring(end)
    cursorOffset = tool.format.length + (selectedText ? selectedText.length + tool.suffix.length : 0)
  }

  emit('update:modelValue', newText)

  // 设置光标位置
  nextTick(() => {
    if (textarea) {
      const newCursorPos = start + cursorOffset
      textarea.setSelectionRange(newCursorPos, newCursorPos)
      textarea.focus()
    }
  })
}

// 插入文本
const insertText = (text: string) => {
  const textarea = textareaRef.value
  if (!textarea) return

  const start = textarea.selectionStart
  const end = textarea.selectionEnd

  const newText = props.modelValue.substring(0, start) +
    text +
    props.modelValue.substring(end)

  emit('update:modelValue', newText)

  nextTick(() => {
    if (textarea) {
      const newCursorPos = start + text.length
      textarea.setSelectionRange(newCursorPos, newCursorPos)
      textarea.focus()
    }
  })
}

// 显示快捷键帮助
const showShortcutHelp = () => {
  const shortcuts = [
    'Ctrl+B: 粗体',
    'Ctrl+I: 斜体',
    'Ctrl+`: 行内代码',
    'Ctrl+K: 链接',
    'Ctrl+1: 一级标题',
    'Ctrl+2: 二级标题',
    'Ctrl+3: 三级标题',
    'Ctrl+L: 无序列表',
    'Ctrl+Shift+L: 有序列表',
    'Ctrl+Q: 引用',
    'Tab: 缩进',
    'Ctrl+/: 显示帮助'
  ]
  alert('Markdown 快捷键:\n\n' + shortcuts.join('\n'))
}
</script>
