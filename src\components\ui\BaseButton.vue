<template>
  <button :type="type" :class="buttonClasses" :disabled="disabled || loading" @click="handleClick">
    <div v-if="loading" class="i-heroicons-arrow-path animate-spin mr-2"></div>
    <div v-if="icon && !loading" :class="[icon, { 'mr-2': $slots.default }]"></div>
    <slot></slot>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  icon?: string
  block?: boolean
  type?: 'button' | 'submit' | 'reset'
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  disabled: false,
  loading: false,
  block: false,
  type: 'button'
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const buttonClasses = computed(() => {
  const baseClasses = [
    'inline-flex items-center justify-center font-semibold rounded-xl transition-all duration-200 border-0',
    'focus:outline-none focus:ring-3 focus:ring-offset-1',
    'disabled:opacity-60 disabled:cursor-not-allowed disabled:transform-none',
    'transform hover:scale-[1.02] active:scale-[0.98]'
  ]

  // 尺寸样式 - 增加内边距提高可点击性
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm min-h-[36px]',
    md: 'px-4 py-2.5 text-base min-h-[44px]',
    lg: 'px-6 py-3 text-lg min-h-[52px]'
  }

  // 变体样式 - 优化对比度和可访问性
  const variantClasses = {
    primary: [
      'bg-primary-600 hover:bg-primary-700 active:bg-primary-800 text-white',
      'shadow-sm hover:shadow-md',
      'focus:ring-primary-300 dark:focus:ring-primary-600',
      'disabled:bg-primary-400 disabled:text-white'
    ].join(' '),
    secondary: [
      'bg-primary-50 hover:bg-primary-100 active:bg-primary-200 text-primary-800',
      'dark:bg-primary-900/40 dark:hover:bg-primary-800/50 dark:active:bg-primary-700/60 dark:text-primary-200',
      'border border-primary-200 dark:border-primary-700',
      'hover:border-primary-300 dark:hover:border-primary-600',
      'focus:ring-primary-300 dark:focus:ring-primary-600',
      'disabled:bg-primary-25 disabled:text-primary-400 disabled:border-primary-100'
    ].join(' '),
    outline: [
      'bg-transparent hover:bg-primary-50 active:bg-primary-100 text-primary-700',
      'dark:text-primary-300 dark:hover:bg-primary-900/20 dark:active:bg-primary-800/30',
      'border-2 border-primary-300 dark:border-primary-600',
      'hover:border-primary-400 dark:hover:border-primary-500',
      'focus:ring-primary-300 dark:focus:ring-primary-600',
      'disabled:text-primary-300 disabled:border-primary-200'
    ].join(' '),
    ghost: [
      'bg-transparent hover:bg-primary-50 active:bg-primary-100 text-primary-600',
      'dark:text-primary-400 dark:hover:bg-primary-900/20 dark:active:bg-primary-800/30',
      'focus:ring-primary-300 dark:focus:ring-primary-600',
      'disabled:text-primary-300'
    ].join(' '),
    danger: [
      'bg-red-600 hover:bg-red-700 active:bg-red-800 text-white',
      'shadow-sm hover:shadow-md',
      'focus:ring-red-300 dark:focus:ring-red-600',
      'disabled:bg-red-400 disabled:text-white'
    ].join(' ')
  }

  const classes = [
    ...baseClasses,
    sizeClasses[props.size],
    variantClasses[props.variant]
  ]

  if (props.block) {
    classes.push('w-full')
  }

  return classes
})

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>
