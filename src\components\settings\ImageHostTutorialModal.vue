<template>
  <Teleport to="body">
    <Transition enter-active-class="transition-all duration-300 ease-out" enter-from-class="opacity-0 scale-95"
      enter-to-class="opacity-100 scale-100" leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 scale-100" leave-to-class="opacity-0 scale-95">
      <div v-if="true" class="modal-overlay" @click="handleOverlayClick"
        style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.5); display: flex; align-items: center; justify-content: center; padding: 1rem; z-index: 9999; backdrop-filter: blur(4px);">
        <div class="modal-container" @click.stop
          style="background-color: white; border-radius: 0.75rem; box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); max-width: 56rem; width: 100%; max-height: 90vh; overflow: hidden;">
          <div class="modal-header">
            <div class="flex items-center">
              <div class="i-heroicons-academic-cap w-6 h-6 text-blue-600 mr-3"></div>
              <h3 class="modal-title">图床配置教程</h3>
            </div>
            <button @click="$emit('close')" class="modal-close">
              <div class="i-heroicons-x-mark w-6 h-6"></div>
            </button>
          </div>

          <div class="modal-body">
            <div class="tutorial-content">
              <div class="tutorial-step">
                <div class="step-number">1</div>
                <div class="step-content">
                  <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">选择图床服务</h4>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    推荐使用以下免费图床服务：
                  </p>
                  <div class="service-list">
                    <div class="service-item">
                      <div class="service-info">
                        <div class="service-name">PICUI图床</div>
                        <div class="service-desc">支持游客上传，可选Token认证</div>
                      </div>
                      <a href="https://picui.cn" target="_blank" class="service-link">
                        访问官网
                      </a>
                    </div>
                    <div class="service-item">
                      <div class="service-info">
                        <div class="service-name">SM.MS图床</div>
                        <div class="service-desc">老牌免费图床，需要注册获取Token</div>
                      </div>
                      <a href="https://sm.ms" target="_blank" class="service-link">
                        访问官网
                      </a>
                    </div>
                    <div class="service-item">
                      <div class="service-info">
                        <div class="service-name">ImgBB图床</div>
                        <div class="service-desc">国外免费图床，需要API Key</div>
                      </div>
                      <a href="https://imgbb.com" target="_blank" class="service-link">
                        访问官网
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <div class="tutorial-step">
                <div class="step-number">2</div>
                <div class="step-content">
                  <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">获取API密钥</h4>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    大部分图床服务需要API密钥来进行身份验证：
                  </p>
                  <ul class="list-disc list-inside text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    <li>注册图床服务账号</li>
                    <li>在用户设置或API设置中生成密钥</li>
                    <li>复制密钥备用（请妥善保管）</li>
                  </ul>
                </div>
              </div>

              <div class="tutorial-step">
                <div class="step-number">3</div>
                <div class="step-content">
                  <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">配置图床参数</h4>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    点击"添加图床"按钮，填写以下信息：
                  </p>
                  <div class="config-params">
                    <div class="param-item">
                      <div class="param-name">配置名称</div>
                      <div class="param-desc">为图床起一个容易识别的名称</div>
                    </div>
                    <div class="param-item">
                      <div class="param-name">API接口地址</div>
                      <div class="param-desc">图床服务的上传API地址</div>
                    </div>
                    <div class="param-item">
                      <div class="param-name">认证密钥</div>
                      <div class="param-desc">从图床服务获取的API Key或Token</div>
                    </div>
                    <div class="param-item">
                      <div class="param-name">URL字段路径</div>
                      <div class="param-desc">从API响应中提取图片URL的字段路径</div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="tutorial-step">
                <div class="step-number">4</div>
                <div class="step-content">
                  <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">测试和使用</h4>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    配置完成后：
                  </p>
                  <ul class="list-disc list-inside text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    <li>系统会自动进行连接测试</li>
                    <li>测试成功后即可开始上传图片</li>
                    <li>可以配置多个图床实现备份和故障转移</li>
                    <li>通过优先级控制图床的使用顺序</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div class="modal-footer">
            <button @click="$emit('close')" class="btn btn-secondary">
              关闭教程
            </button>
            <button @click="$emit('start-config')" class="btn btn-primary">
              开始配置
            </button>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
// 定义组件名称
defineOptions({
  name: 'ImageHostTutorialModal'
})

// Emits
const emit = defineEmits<{
  close: []
  'start-config': []
}>()

// 处理遮罩点击
const handleOverlayClick = () => {
  emit('close')
}
</script>

<style scoped>
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4;
  z-index: 9999;
  backdrop-filter: blur(4px);
}

.modal-container {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700;
}

.modal-title {
  @apply text-lg font-semibold text-gray-900 dark:text-gray-100;
}

.modal-close {
  @apply p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors;
}

.modal-body {
  @apply p-6 overflow-y-auto max-h-[calc(90vh-140px)];
}

.modal-footer {
  @apply flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700;
}

.tutorial-content {
  @apply space-y-6;
}

.tutorial-step {
  @apply flex items-start space-x-4;
}

.step-number {
  @apply flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium;
}

.step-content {
  @apply flex-1;
}

.service-list {
  @apply space-y-3;
}

.service-item {
  @apply flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg;
}

.service-info {
  @apply flex-1;
}

.service-name {
  @apply font-medium text-gray-900 dark:text-gray-100;
}

.service-desc {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.service-link {
  @apply text-blue-600 hover:text-blue-700 text-sm font-medium;
}

.config-params {
  @apply space-y-3;
}

.param-item {
  @apply flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg;
}

.param-name {
  @apply flex-shrink-0 w-24 font-medium text-gray-900 dark:text-gray-100 text-sm;
}

.param-desc {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.btn {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-primary {
  @apply text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply text-gray-700 bg-gray-100 hover:bg-gray-200 focus:ring-gray-500;
  @apply dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600;
}
</style>
