<template>
  <a-modal
    v-model:open="isVisible"
    title="搜索引擎设置"
    width="600px"
    @cancel="handleClose"
    :footer="null"
  >
    <a-space direction="vertical" size="large" style="width: 100%">
      <!-- 默认搜索引擎 -->
      <a-card title="默认搜索引擎" size="small">
        <a-row :gutter="[12, 12]">
          <a-col
            v-for="engine in settings.engines"
            :key="engine.id"
            :span="12"
          >
            <a-card
              :class="[
                'cursor-pointer transition-all',
                settings.defaultEngine === engine.id
                  ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                  : 'hover:border-gray-300 dark:hover:border-gray-600'
              ]"
              size="small"
              @click="setDefaultEngine(engine.id)"
            >
              <div class="flex items-center">
                <div :class="[engine.icon, 'w-6 h-6 mr-3']"></div>
                <span class="font-medium">{{ engine.name }}</span>
                <CheckOutlined
                  v-if="settings.defaultEngine === engine.id"
                  class="ml-auto text-primary-500"
                />
              </div>
            </a-card>
          </a-col>
        </a-row>
      </a-card>

      <!-- 自定义搜索引擎 -->
      <a-card size="small">
        <template #title>
          <div class="flex items-center justify-between">
            <span>自定义搜索引擎</span>
            <a-button type="primary" @click="showAddEngine = true">
              <PlusOutlined />
              添加
            </a-button>
          </div>
        </template>

        <a-space direction="vertical" size="middle" style="width: 100%">
          <a-card
            v-for="engine in customEngines"
            :key="engine.id"
            size="small"
            class="bg-gray-50 dark:bg-gray-800"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div :class="[engine.icon, 'w-5 h-5 mr-3']"></div>
                <div>
                  <div class="font-medium">{{ engine.name }}</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
                    {{ engine.url }}
                  </div>
                </div>
              </div>
              <a-space>
                <a-button type="text" @click="editEngine(engine)">
                  <EditOutlined />
                </a-button>
                <a-button type="text" danger @click="removeEngine(engine.id)">
                  <DeleteOutlined />
                </a-button>
              </a-space>
            </div>
          </a-card>

          <a-empty v-if="customEngines.length === 0" description="暂无自定义搜索引擎" />
        </a-space>
      </a-card>

      <!-- 搜索设置 -->
      <a-card title="搜索设置" size="small">
        <a-space direction="vertical" size="middle" style="width: 100%">
          <a-form-item label="搜索建议">
            <a-switch
              v-model:checked="settings.suggestions.enabled"
              checked-children="开启"
              un-checked-children="关闭"
            />
            <template #extra>
              <div class="text-sm text-gray-500">显示搜索建议和历史记录</div>
            </template>
          </a-form-item>

          <a-form-item label="新标签页打开">
            <a-switch
              v-model:checked="settings.openInNewTab"
              checked-children="是"
              un-checked-children="否"
            />
            <template #extra>
              <div class="text-sm text-gray-500">在新标签页中打开搜索结果</div>
            </template>
          </a-form-item>

          <a-form-item label="搜索历史">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-input-number
                  v-model:value="settings.suggestions.maxHistory"
                  :min="0"
                  :max="100"
                  style="width: 100%"
                  addon-after="条"
                />
              </a-col>
              <a-col :span="12">
                <a-button @click="clearSearchHistory">
                  清空历史
                </a-button>
              </a-col>
            </a-row>
            <template #extra>
              <div class="text-sm text-gray-500">保存的搜索历史记录数量</div>
            </template>
          </a-form-item>
        </a-space>
      </a-card>

      <!-- 操作按钮 -->
      <div class="flex justify-end">
        <a-space>
          <a-button @click="handleClose">
            取消
          </a-button>
          <a-button type="primary" :loading="saving" @click="saveSettings">
            {{ saving ? '保存中...' : '保存设置' }}
          </a-button>
        </a-space>
      </div>
    </a-space>

    <!-- 添加/编辑搜索引擎模态框 -->
    <a-modal
      v-model:open="showAddEngine"
      :title="editingEngine ? '编辑搜索引擎' : '添加搜索引擎'"
      @cancel="resetEngineForm"
      :footer="null"
    >
      <a-form
        :model="engineForm"
        :rules="engineRules"
        layout="vertical"
        @finish="saveEngine"
      >
        <a-form-item label="名称" name="name">
          <a-input
            v-model:value="engineForm.name"
            placeholder="请输入搜索引擎名称"
          />
        </a-form-item>

        <a-form-item label="搜索URL" name="url">
          <a-input
            v-model:value="engineForm.url"
            placeholder="例如：https://www.google.com/search?q={query}"
          />
          <template #extra>
            <div class="text-sm text-gray-500">使用 {query} 作为搜索关键词的占位符</div>
          </template>
        </a-form-item>

        <a-form-item label="图标" name="icon">
          <a-select
            v-model:value="engineForm.icon"
            placeholder="选择图标"
            :options="iconOptions"
          />
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button @click="resetEngineForm">
              取消
            </a-button>
            <a-button type="primary" html-type="submit" :loading="saving">
              {{ saving ? '保存中...' : '保存' }}
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  CheckOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 状态
const isVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const saving = ref(false)
const showAddEngine = ref(false)
const editingEngine = ref<any>(null)

// 设置数据
const settings = reactive({
  defaultEngine: 'google',
  engines: [
    { id: 'google', name: 'Google', icon: 'i-logos-google', url: 'https://www.google.com/search?q={query}' },
    { id: 'bing', name: 'Bing', icon: 'i-logos-microsoft', url: 'https://www.bing.com/search?q={query}' },
    { id: 'baidu', name: '百度', icon: 'i-simple-icons-baidu', url: 'https://www.baidu.com/s?wd={query}' },
    { id: 'duckduckgo', name: 'DuckDuckGo', icon: 'i-simple-icons-duckduckgo', url: 'https://duckduckgo.com/?q={query}' }
  ],
  customEngines: [] as any[],
  suggestions: {
    enabled: true,
    maxHistory: 10
  },
  openInNewTab: true
})

// 表单数据
const engineForm = reactive({
  name: '',
  url: '',
  icon: 'i-heroicons-magnifying-glass'
})

// 表单验证规则
const engineRules = {
  name: [
    { required: true, message: '请输入搜索引擎名称', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入搜索URL', trigger: 'blur' },
    { pattern: /\{query\}/, message: 'URL中必须包含 {query} 占位符', trigger: 'blur' }
  ]
}

// 图标选项
const iconOptions = [
  { value: 'i-heroicons-magnifying-glass', label: '搜索图标' },
  { value: 'i-heroicons-globe-alt', label: '地球图标' },
  { value: 'i-heroicons-link', label: '链接图标' },
  { value: 'i-heroicons-star', label: '星星图标' }
]

// 计算属性
const customEngines = computed(() => settings.customEngines)

// 方法
const setDefaultEngine = (engineId: string) => {
  settings.defaultEngine = engineId
  message.success('默认搜索引擎已更新')
}

const editEngine = (engine: any) => {
  editingEngine.value = engine
  engineForm.name = engine.name
  engineForm.url = engine.url
  engineForm.icon = engine.icon
  showAddEngine.value = true
}

const removeEngine = (engineId: string) => {
  const index = settings.customEngines.findIndex(e => e.id === engineId)
  if (index > -1) {
    settings.customEngines.splice(index, 1)
    message.success('搜索引擎已删除')
  }
}

const saveEngine = async () => {
  saving.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    if (editingEngine.value) {
      // 编辑现有引擎
      const index = settings.customEngines.findIndex(e => e.id === editingEngine.value.id)
      if (index > -1) {
        settings.customEngines[index] = {
          ...settings.customEngines[index],
          name: engineForm.name,
          url: engineForm.url,
          icon: engineForm.icon
        }
      }
      message.success('搜索引擎已更新')
    } else {
      // 添加新引擎
      const newEngine = {
        id: `custom_${Date.now()}`,
        name: engineForm.name,
        url: engineForm.url,
        icon: engineForm.icon
      }
      settings.customEngines.push(newEngine)
      message.success('搜索引擎已添加')
    }
    
    resetEngineForm()
  } catch (error) {
    message.error('保存失败')
  } finally {
    saving.value = false
  }
}

const resetEngineForm = () => {
  showAddEngine.value = false
  editingEngine.value = null
  engineForm.name = ''
  engineForm.url = ''
  engineForm.icon = 'i-heroicons-magnifying-glass'
}

const clearSearchHistory = () => {
  // 模拟清空搜索历史
  message.success('搜索历史已清空')
}

const saveSettings = async () => {
  saving.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('设置保存成功!')
    handleClose()
  } catch (error) {
    message.error('设置保存失败!')
  } finally {
    saving.value = false
  }
}

const handleClose = () => {
  isVisible.value = false
}

onMounted(() => {
  // 加载设置
  console.log('搜索引擎设置页面已加载')
})
</script>
