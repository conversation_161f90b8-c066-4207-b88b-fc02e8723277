<template>
  <div class="markdown-preview-container">
    <MdPreview :model-value="content" :theme="theme" :preview-theme="props.previewTheme" :code-theme="props.codeTheme"
      :no-mermaid="false" :no-katex="false" :no-highlight="false" :sanitize="sanitizeHtml" class="md-preview" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { MdPreview } from 'md-editor-v3'
import 'md-editor-v3/lib/style.css'

interface Props {
  content: string
  theme?: 'light' | 'dark'
  previewTheme?: 'default' | 'github' | 'vuepress' | 'mk-cute' | 'smart-blue' | 'cyanosis'
  codeTheme?: 'atom' | 'a11y' | 'github' | 'gradient' | 'kimbie' | 'paraiso' | 'qtcreator' | 'stackoverflow'
}

const props = withDefaults(defineProps<Props>(), {
  content: '',
  theme: 'light',
  previewTheme: 'github',
  codeTheme: 'github'
})

// HTML 清理配置
const sanitizeHtml = (html: string) => {
  // 这里可以添加自定义的 HTML 清理逻辑
  return html
}

// 监听主题变化
const isDark = computed(() => {
  return document.documentElement.classList.contains('dark')
})

const theme = computed(() => {
  return isDark.value ? 'dark' : 'light'
})
</script>

<style scoped>
.markdown-preview-container {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--ant-color-border);
  background: var(--ant-color-bg-container);
}

.md-preview {
  --md-color: var(--ant-color-text);
  --md-hover-color: var(--ant-color-text-secondary);
  --md-bk-color: var(--ant-color-bg-container);
  --md-bk-color-outstand: var(--ant-color-bg-layout);
  --md-bk-hover-color: var(--ant-color-fill-tertiary);
  --md-border-color: var(--ant-color-border);
  --md-border-hover-color: var(--ant-color-primary);
  --md-border-active-color: var(--ant-color-primary);
  --md-scrollbar-bg-color: var(--ant-color-fill-quaternary);
  --md-scrollbar-thumb-color: var(--ant-color-fill-secondary);
  --md-scrollbar-thumb-hover-color: var(--ant-color-fill);
  --md-scrollbar-thumb-active-color: var(--ant-color-fill);
  border: none;
  border-radius: 8px;
}

/* 预览内容样式 */
.md-preview :deep(.md-editor-preview) {
  color: var(--ant-color-text);
  font-size: 14px;
  line-height: 1.6;
  padding: 16px;
  background: var(--ant-color-bg-container);
}

.md-preview :deep(.md-editor-preview h1),
.md-preview :deep(.md-editor-preview h2),
.md-preview :deep(.md-editor-preview h3),
.md-preview :deep(.md-editor-preview h4),
.md-preview :deep(.md-editor-preview h5),
.md-preview :deep(.md-editor-preview h6) {
  color: var(--ant-color-text);
  margin: 16px 0 8px 0;
  font-weight: 600;
}

.md-preview :deep(.md-editor-preview h1) {
  font-size: 24px;
  border-bottom: 2px solid var(--ant-color-border);
  padding-bottom: 8px;
}

.md-preview :deep(.md-editor-preview h2) {
  font-size: 20px;
  border-bottom: 1px solid var(--ant-color-border-secondary);
  padding-bottom: 4px;
}

.md-preview :deep(.md-editor-preview h3) {
  font-size: 18px;
}

.md-preview :deep(.md-editor-preview h4) {
  font-size: 16px;
}

.md-preview :deep(.md-editor-preview h5) {
  font-size: 14px;
}

.md-preview :deep(.md-editor-preview h6) {
  font-size: 13px;
  color: var(--ant-color-text-secondary);
}

.md-preview :deep(.md-editor-preview p) {
  margin: 12px 0;
  color: var(--ant-color-text);
  line-height: 1.6;
}

.md-preview :deep(.md-editor-preview code) {
  background: var(--ant-color-bg-layout);
  color: var(--ant-color-error);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.9em;
  border: 1px solid var(--ant-color-border-secondary);
}

.md-preview :deep(.md-editor-preview pre) {
  background: var(--ant-color-bg-layout);
  border: 1px solid var(--ant-color-border);
  border-radius: 8px;
  padding: 16px;
  overflow-x: auto;
  margin: 16px 0;
}

.md-preview :deep(.md-editor-preview pre code) {
  background: none;
  padding: 0;
  border: none;
  color: var(--ant-color-text);
  font-size: 13px;
  line-height: 1.5;
}

.md-preview :deep(.md-editor-preview blockquote) {
  border-left: 4px solid var(--ant-color-primary);
  background: var(--ant-color-primary-bg);
  margin: 16px 0;
  padding: 12px 16px;
  color: var(--ant-color-text-secondary);
  border-radius: 0 6px 6px 0;
  font-style: italic;
}

.md-preview :deep(.md-editor-preview blockquote p) {
  margin: 0;
}

.md-preview :deep(.md-editor-preview table) {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
  font-size: 14px;
  border: 1px solid var(--ant-color-border);
  border-radius: 6px;
  overflow: hidden;
}

.md-preview :deep(.md-editor-preview th),
.md-preview :deep(.md-editor-preview td) {
  border: 1px solid var(--ant-color-border);
  padding: 8px 12px;
  text-align: left;
}

.md-preview :deep(.md-editor-preview th) {
  background: var(--ant-color-bg-layout);
  font-weight: 600;
  color: var(--ant-color-text);
}

.md-preview :deep(.md-editor-preview tr:nth-child(even)) {
  background: var(--ant-color-fill-quaternary);
}

.md-preview :deep(.md-editor-preview img) {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  margin: 12px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.md-preview :deep(.md-editor-preview img:hover) {
  transform: scale(1.02);
  cursor: zoom-in;
}

.md-preview :deep(.md-editor-preview a) {
  color: var(--ant-color-primary);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
}

.md-preview :deep(.md-editor-preview a:hover) {
  color: var(--ant-color-primary-hover);
  border-bottom-color: var(--ant-color-primary);
}

.md-preview :deep(.md-editor-preview hr) {
  border: none;
  border-top: 2px solid var(--ant-color-border);
  margin: 24px 0;
  background: none;
}

.md-preview :deep(.md-editor-preview ul),
.md-preview :deep(.md-editor-preview ol) {
  margin: 12px 0;
  padding-left: 24px;
}

.md-preview :deep(.md-editor-preview li) {
  margin: 6px 0;
  line-height: 1.5;
}

.md-preview :deep(.md-editor-preview strong) {
  font-weight: 600;
  color: var(--ant-color-text);
}

.md-preview :deep(.md-editor-preview em) {
  font-style: italic;
  color: var(--ant-color-text-secondary);
}

.md-preview :deep(.md-editor-preview del) {
  text-decoration: line-through;
  color: var(--ant-color-text-tertiary);
}

/* 暗黑模式适配 */
.dark .md-preview {
  --md-color: rgba(255, 255, 255, 0.85);
  --md-hover-color: rgba(255, 255, 255, 0.65);
  --md-bk-color: #141414;
  --md-bk-color-outstand: #1f1f1f;
  --md-bk-hover-color: rgba(255, 255, 255, 0.04);
  --md-border-color: #424242;
  --md-border-hover-color: #1677ff;
  --md-border-active-color: #1677ff;
}

.dark .markdown-preview-container {
  border-color: #424242;
  background: #141414;
}

.dark .md-preview :deep(.md-editor-preview) {
  background: #141414;
}

.dark .md-preview :deep(.md-editor-preview code) {
  background: #1f1f1f;
  border-color: #424242;
  color: #ff7875;
}

.dark .md-preview :deep(.md-editor-preview pre) {
  background: #1f1f1f;
  border-color: #424242;
}

.dark .md-preview :deep(.md-editor-preview th) {
  background: #1f1f1f;
}

.dark .md-preview :deep(.md-editor-preview tr:nth-child(even)) {
  background: rgba(255, 255, 255, 0.02);
}
</style>
