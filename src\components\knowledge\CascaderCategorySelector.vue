<template>
  <div>
    <BaseDropdown ref="dropdownRef" width="full">
      <template #trigger>
        <button type="button" :class="[
          'w-full flex items-center justify-between px-3 py-2 rounded-xl transition-all duration-200',
          'focus:outline-none text-left',
          error
            ? 'bg-red-50 border border-red-300 focus:bg-white focus:border-red-500 dark:bg-red-900/10 dark:border-red-600 dark:focus:bg-gray-800 dark:focus:border-red-400'
            : 'bg-gray-50 border-0 hover:bg-gray-100 focus:bg-white focus:border focus:border-primary-500 dark:bg-gray-700 dark:hover:bg-gray-650 dark:focus:bg-gray-800 dark:focus:border-primary-400'
        ]">
          <span :class="selectedCategoryPath ? 'text-primary' : 'text-gray-500 dark:text-gray-400'">
            {{ selectedCategoryPath || '选择分类' }}
          </span>
          <div class="i-heroicons-chevron-down w-4 h-4 text-gray-400"></div>
        </button>
      </template>

      <div
        class="w-full max-h-80 overflow-hidden bg-white dark:bg-gray-800 border border-primary-200 dark:border-primary-600 rounded-xl shadow-lg">
        <!-- 搜索框 -->
        <div class="p-3 border-b border-primary-200 dark:border-primary-600">
          <BaseInput v-model="searchQuery" placeholder="搜索分类..." prefix-icon="i-heroicons-magnifying-glass" size="sm"
            clearable />
        </div>

        <!-- 级联选择区域 -->
        <div class="flex max-h-64">
          <!-- 根分类列表 -->
          <div class="flex-1 border-r border-primary-200 dark:border-primary-600 overflow-y-auto">
            <div class="py-1">
              <div
                class="px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide bg-primary-25 dark:bg-primary-950/30">
                根分类
              </div>

              <DropdownItem text="无分类" :icon="!modelValue ? 'i-heroicons-check' : ''"
                @click.stop="selectCategory(null)" />

              <template v-for="category in filteredRootCategories" :key="category.id">
                <DropdownItem :text="`${category.name} (${category.resource_count})`"
                  :icon="modelValue === category.id ? 'i-heroicons-check' : (hasChildren(category.id) ? 'i-heroicons-chevron-right' : 'i-heroicons-folder')"
                  :active="selectedRootCategory?.id === category.id" @click.stop="selectRootCategory(category)" />
              </template>
            </div>
          </div>

          <!-- 子分类列表 -->
          <div v-if="selectedRootCategory && getChildren(selectedRootCategory.id).length > 0"
            class="flex-1 overflow-y-auto">
            <div class="py-1">
              <div
                class="px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide bg-primary-25 dark:bg-primary-950/30">
                {{ selectedRootCategory.name }}
              </div>

              <template v-for="child in getChildren(selectedRootCategory.id)" :key="child.id">
                <DropdownItem :text="`${child.name} (${child.resource_count})`"
                  :icon="modelValue === child.id ? 'i-heroicons-check' : 'i-heroicons-folder'"
                  @click.stop="selectCategory(child.id)" />
              </template>
            </div>
          </div>
        </div>

        <!-- 无匹配结果 -->
        <div v-if="filteredRootCategories.length === 0 && searchQuery"
          class="px-4 py-8 text-sm text-gray-500 dark:text-gray-400 text-center">
          未找到匹配的分类
        </div>

        <!-- 创建新分类 -->
        <div class="border-t border-primary-200 dark:border-primary-600 p-3">
          <BaseButton variant="ghost" size="sm" block @click="showCreateDialog = true">
            <div class="i-heroicons-plus mr-2"></div>
            创建新分类
          </BaseButton>
        </div>
      </div>
    </BaseDropdown>

    <!-- 错误提示 -->
    <p v-if="error" class="mt-1 text-sm text-red-600">
      {{ error }}
    </p>

    <!-- 创建分类对话框 -->
    <BaseModal v-model="showCreateDialog" title="创建新分类" size="sm" :allow-overflow="true">
      <form @submit.prevent="createCategory" class="space-y-4">
        <BaseInput v-model="newCategoryName" label="分类名称" placeholder="输入分类名称" required :error="createError" />

        <div>
          <label class="block text-sm font-medium text-primary mb-2">
            父分类
          </label>
          <ParentCategorySelector v-model="newCategoryParent" :categories="categories" placeholder="选择父分类（可选）" />
        </div>
      </form>

      <template #footer>
        <BaseButton variant="secondary" @click="showCreateDialog = false">
          取消
        </BaseButton>
        <BaseButton type="submit" :loading="creating" @click="createCategory">
          创建
        </BaseButton>
      </template>
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import BaseDropdown from '@/components/ui/BaseDropdown.vue'
import BaseInput from '@/components/ui/BaseInput.vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import BaseModal from '@/components/ui/BaseModal.vue'
import DropdownItem from '@/components/ui/DropdownItem.vue'
import ParentCategorySelector from './ParentCategorySelector.vue'
import { categoryService } from '@/services/categoryService'
import type { Category } from '@/types'

interface Props {
  modelValue: number | null
  categories: Category[]
  error?: string
  readonly?: boolean // 是否为只读模式（编辑时不自动保存）
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: number | null]
}>()

// 状态
const dropdownRef = ref()
const searchQuery = ref('')
const selectedRootCategory = ref<Category | null>(null)
const showCreateDialog = ref(false)
const newCategoryName = ref('')
const newCategoryParent = ref(0)
const creating = ref(false)
const createError = ref('')

// 计算属性
const selectedCategory = computed(() =>
  props.categories.find(cat => cat.id === props.modelValue)
)

const selectedCategoryPath = computed(() => {
  if (!selectedCategory.value) return ''
  return getCategoryPath(selectedCategory.value)
})

const rootCategories = computed(() =>
  props.categories.filter(cat => cat.parent_id === 0)
)

const filteredRootCategories = computed(() => {
  if (!searchQuery.value) return rootCategories.value

  const query = searchQuery.value.toLowerCase()
  return rootCategories.value.filter(category =>
    category.name.toLowerCase().includes(query)
  )
})

// 方法
const hasChildren = (categoryId: number) => {
  return props.categories.some(cat => cat.parent_id === categoryId)
}

const getChildren = (parentId: number) => {
  return props.categories.filter(cat => cat.parent_id === parentId)
}

const getCategoryPath = (category: Category): string => {
  const path = [category.name]
  let current = category

  while (current.parent_id && current.parent_id !== 0) {
    const parent = props.categories.find(cat => cat.id === current.parent_id)
    if (parent) {
      path.unshift(parent.name)
      current = parent
    } else {
      break
    }
  }

  return path.join(' > ')
}

const selectRootCategory = (category: Category) => {
  selectedRootCategory.value = category

  // 如果没有子分类，直接选择
  if (!hasChildren(category.id!)) {
    selectCategory(category.id!)
  }
}

const selectCategory = (categoryId: number | null) => {
  emit('update:modelValue', categoryId)
  dropdownRef.value?.close()
  selectedRootCategory.value = null
}

// 创建新分类
const createCategory = async () => {
  if (!newCategoryName.value.trim()) {
    createError.value = '请输入分类名称'
    return
  }

  try {
    creating.value = true
    createError.value = ''

    let categoryId: number
    let newCategory: Category

    if (props.readonly) {
      // 只读模式：创建临时分类，不保存到数据库
      categoryId = Date.now() // 使用时间戳作为临时ID
      newCategory = {
        id: categoryId,
        name: newCategoryName.value.trim(),
        parent_id: newCategoryParent.value,
        resource_count: 0,
        sort_order: props.categories.length + 1,
        created_at: new Date(),
        isTemporary: true // 标记为临时分类
      }
    } else {
      // 正常模式：保存到数据库
      categoryId = await categoryService.createCategory({
        name: newCategoryName.value.trim(),
        parent_id: newCategoryParent.value,
        sort_order: props.categories.length + 1
      })

      // 重新加载分类信息
      const loadedCategory = await categoryService.getCategoryById(categoryId)
      if (!loadedCategory) {
        throw new Error('创建分类后无法加载分类信息')
      }
      newCategory = loadedCategory
    }

    // 添加到分类列表
    props.categories.push(newCategory)
    emit('update:modelValue', categoryId)

    // 重置表单
    newCategoryName.value = ''
    newCategoryParent.value = 0
    showCreateDialog.value = false
    dropdownRef.value?.close()
  } catch (error) {
    console.error('创建分类失败:', error)
    createError.value = '创建失败，请重试'
  } finally {
    creating.value = false
  }
}
</script>
