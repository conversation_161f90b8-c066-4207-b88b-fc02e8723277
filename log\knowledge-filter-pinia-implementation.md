# 知识库筛选状态持久化 - Pinia 实现

## 🎯 问题描述

用户在知识库界面进行筛选（分类、标签、排序）后，如果点击其他界面再返回，所有筛选状态都会丢失，用户需要重新设置筛选条件，体验很差。

## 🔧 解决方案

使用 Pinia 状态管理库 + localStorage 实现筛选状态的持久化存储。

## 📁 文件结构

### 1. **新建 Pinia Store**
**文件**：`src/stores/knowledgeFilter.ts`

**核心功能**：
- ✅ **状态管理**：集中管理所有筛选相关状态
- ✅ **持久化存储**：自动保存到 localStorage
- ✅ **状态恢复**：页面加载时自动恢复状态
- ✅ **数据验证**：确保恢复的数据仍然有效

### 2. **状态定义**
```typescript
interface FilterState {
  selectedCategoryId: number | null    // 选中的分类ID
  selectedTags: Tag[]                  // 选中的标签列表
  currentSortKey: string              // 当前排序方式
  searchKeyword: string               // 搜索关键词
  viewMode: 'grid' | 'list'           // 视图模式
}
```

### 3. **核心方法**
```typescript
// 状态操作
setCategory(categoryId, category)     // 设置分类
addTag(tag) / removeTag(tagId)       // 添加/移除标签
setSortKey(sortKey)                  // 设置排序
clearFilters()                       // 清除筛选

// 持久化
persistState()                       // 保存状态到 localStorage
restoreState()                       // 从 localStorage 恢复状态

// 数据验证
updateCategoryInfo(categories)       // 验证分类是否仍然存在
validateTags(availableTags)          // 验证标签是否仍然存在
```

## 🔄 实现细节

### 1. **KnowledgeView.vue 重构**

**修改前**：使用本地 ref 状态
```typescript
const selectedCategory = ref<Category | null>(null)
const selectedCategoryId = ref<number | null>(null)
const selectedTags = ref<number[]>([])
const currentSortKey = ref<string>('created_at_desc')
```

**修改后**：使用 Pinia store
```typescript
const filterStore = useKnowledgeFilterStore()

// 直接使用 store 中的状态
// filterStore.selectedCategoryId
// filterStore.selectedTags
// filterStore.currentSortKey
```

### 2. **模板绑定更新**

**修改前**：
```vue
<a-tree-select v-model:value="selectedCategoryId" />
<a-select v-model:value="currentSortKey" />
<div v-if="selectedCategory || selectedTags.length > 0">
```

**修改后**：
```vue
<a-tree-select v-model:value="filterStore.selectedCategoryId" />
<a-select v-model:value="filterStore.currentSortKey" />
<div v-if="filterStore.hasActiveFilters">
```

### 3. **方法重构**

**筛选操作**：
```typescript
// 修改前
const toggleTag = (tagId: number) => {
  const index = selectedTags.value.indexOf(tagId)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else {
    selectedTags.value.push(tagId)
  }
}

// 修改后
const toggleTag = (tagId: number) => {
  const tag = popularTags.value.find(t => t.id === tagId)
  if (!tag) return
  
  const isSelected = filterStore.selectedTags.some(t => t.id === tagId)
  if (isSelected) {
    filterStore.removeTag(tagId)
  } else {
    filterStore.addTag(tag)
  }
}
```

**API 调用**：
```typescript
// 修改前
const searchOptions = {
  category_id: selectedCategory.value?.id,
  tag_ids: selectedTags.value.length > 0 ? selectedTags.value : undefined,
  sort_by: currentSort.value,
  sort_order: sortOrder.value
}

// 修改后
const searchOptions = {
  category_id: filterStore.selectedCategoryId || undefined,
  tag_ids: filterStore.selectedTags.length > 0 ? 
    filterStore.selectedTags.map(tag => tag.id) : undefined,
  sort_by: currentSort.value,
  sort_order: sortOrder.value
}
```

### 4. **初始化流程**

```typescript
onMounted(async () => {
  // 1. 先恢复筛选状态
  filterStore.restoreState()
  
  // 2. 加载基础数据
  await Promise.all([
    loadCategories(),
    loadCategoryTree(),
    loadPopularTags()
  ])

  // 3. 验证恢复的数据是否仍然有效
  filterStore.updateCategoryInfo(categories.value)
  filterStore.validateTags(popularTags.value)

  // 4. 同步排序状态
  const sortOption = sortOptions.find(opt => opt.key === filterStore.currentSortKey)
  if (sortOption) {
    currentSort.value = sortOption.field
    sortOrder.value = sortOption.order
  }

  // 5. 处理URL参数（可能覆盖恢复的状态）
  await handleUrlQuery()

  // 6. 加载资源
  if (resources.value.length === 0) {
    await loadResources(true)
  }
})
```

## ✨ 功能特性

### 1. **自动持久化**
- 每次状态变更都会自动保存到 localStorage
- 页面刷新或重新访问时自动恢复状态
- 支持跨浏览器标签页的状态同步

### 2. **数据验证**
- 恢复状态时验证分类是否仍然存在
- 验证标签是否仍然有效
- 自动清理无效的筛选条件

### 3. **智能合并**
- URL 参数优先级高于恢复的状态
- 支持通过 URL 分享特定的筛选状态
- 保持向后兼容性

### 4. **用户体验优化**
- 无感知的状态恢复
- 筛选状态摘要显示
- 一键清除所有筛选

## 🔍 核心代码示例

### Store 定义
```typescript
export const useKnowledgeFilterStore = defineStore('knowledgeFilter', () => {
  const selectedCategoryId = ref<number | null>(null)
  const selectedTags = ref<Tag[]>([])
  const currentSortKey = ref<string>('created_at_desc')
  
  const hasActiveFilters = computed(() => {
    return selectedCategoryId.value !== null || 
           selectedTags.value.length > 0
  })
  
  const persistState = () => {
    const state = {
      selectedCategoryId: selectedCategoryId.value,
      selectedTags: selectedTags.value,
      currentSortKey: currentSortKey.value
    }
    localStorage.setItem('knowledge-filter-state', JSON.stringify(state))
  }
  
  const restoreState = () => {
    try {
      const savedState = localStorage.getItem('knowledge-filter-state')
      if (savedState) {
        const state = JSON.parse(savedState)
        selectedCategoryId.value = state.selectedCategoryId || null
        selectedTags.value = state.selectedTags || []
        currentSortKey.value = state.currentSortKey || 'created_at_desc'
        return true
      }
    } catch (error) {
      console.warn('恢复筛选状态失败:', error)
    }
    return false
  }
  
  return {
    selectedCategoryId,
    selectedTags,
    currentSortKey,
    hasActiveFilters,
    persistState,
    restoreState
  }
})
```

### 组件使用
```typescript
const filterStore = useKnowledgeFilterStore()

// 恢复状态
onMounted(() => {
  filterStore.restoreState()
})

// 使用状态
const searchOptions = {
  category_id: filterStore.selectedCategoryId,
  tag_ids: filterStore.selectedTags.map(tag => tag.id)
}
```

## 🎉 实现效果

### 1. **状态持久化**
- ✅ 用户设置的筛选条件会自动保存
- ✅ 离开页面再返回时筛选状态完整保持
- ✅ 浏览器刷新后筛选状态不丢失

### 2. **数据一致性**
- ✅ 分类被删除时自动清除相关筛选
- ✅ 标签被删除时自动从筛选中移除
- ✅ 确保恢复的状态始终有效

### 3. **用户体验**
- ✅ 无需重新设置筛选条件
- ✅ 提供筛选状态摘要
- ✅ 支持一键清除所有筛选

### 4. **开发体验**
- ✅ 集中的状态管理
- ✅ 类型安全的 API
- ✅ 易于扩展和维护

## 🚀 扩展可能

1. **更多筛选条件**：可以轻松添加更多筛选维度
2. **云端同步**：可以扩展为云端状态同步
3. **筛选历史**：可以保存筛选历史记录
4. **智能推荐**：基于筛选历史推荐相关内容

这次实现完美解决了筛选状态丢失的问题，大大提升了用户体验！🎯
