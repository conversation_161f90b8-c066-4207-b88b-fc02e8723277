<template>
  <div>
    <BaseDropdown ref="dropdownRef" width="full">
      <template #trigger>
        <button :class="[
          'w-full flex items-center justify-between px-3 py-2 rounded-xl transition-all duration-200',
          'focus:outline-none text-left',
          error
            ? 'bg-red-50 border border-red-300 focus:bg-white focus:border-red-500 dark:bg-red-900/10 dark:border-red-600 dark:focus:bg-gray-800 dark:focus:border-red-400'
            : 'bg-gray-50 border-0 hover:bg-gray-100 focus:bg-white focus:border focus:border-primary-500 dark:bg-gray-700 dark:hover:bg-gray-650 dark:focus:bg-gray-800 dark:focus:border-primary-400'
        ]">
          <span :class="selectedCategory ? 'text-primary' : 'text-gray-500 dark:text-gray-400'">
            {{ selectedCategory ? selectedCategory.name : '选择分类' }}
          </span>
          <div class="i-heroicons-chevron-down w-4 h-4 text-gray-400"></div>
        </button>
      </template>

      <div class="w-full max-h-64 overflow-y-auto">
        <!-- 搜索框 -->
        <div class="p-3 border-b border-gray-200 dark:border-gray-700">
          <BaseInput v-model="searchQuery" placeholder="搜索分类..." prefix-icon="i-heroicons-magnifying-glass" size="sm"
            clearable />
        </div>

        <!-- 分类列表 -->
        <div class="py-1">
          <DropdownItem text="无分类" :icon="!modelValue ? 'i-heroicons-check' : ''" @click="selectCategory(null)" />

          <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>

          <template v-for="category in filteredCategories" :key="category.id">
            <DropdownItem :text="`${category.name} (${category.resource_count})`"
              :icon="modelValue === category.id ? 'i-heroicons-check' : 'i-heroicons-folder'"
              @click="selectCategory(category.id)" />
          </template>

          <!-- 无匹配结果 -->
          <div v-if="filteredCategories.length === 0 && searchQuery"
            class="px-4 py-3 text-sm text-gray-500 dark:text-gray-400 text-center">
            未找到匹配的分类
          </div>
        </div>

        <!-- 创建新分类 -->
        <div class="border-t border-gray-200 dark:border-gray-700 p-3">
          <BaseButton variant="ghost" size="sm" block @click="showCreateDialog = true">
            <div class="i-heroicons-plus mr-2"></div>
            创建新分类
          </BaseButton>
        </div>
      </div>
    </BaseDropdown>

    <!-- 错误提示 -->
    <p v-if="error" class="mt-1 text-sm text-red-600">
      {{ error }}
    </p>

    <!-- 创建分类对话框 -->
    <BaseModal v-model="showCreateDialog" title="创建新分类" size="sm">
      <form @submit.prevent="createCategory" class="space-y-4">
        <BaseInput v-model="newCategoryName" label="分类名称" placeholder="输入分类名称" required :error="createError" />

        <BaseSelect v-model="newCategoryParent" label="父分类" :options="[
          { label: '根分类', value: 0 },
          ...categories.map(cat => ({ label: cat.name, value: cat.id }))
        ]" />
      </form>

      <template #footer>
        <BaseButton variant="secondary" @click="showCreateDialog = false">
          取消
        </BaseButton>
        <BaseButton :loading="creating" @click="createCategory">
          创建
        </BaseButton>
      </template>
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import BaseDropdown from '@/components/ui/BaseDropdown.vue'
import BaseInput from '@/components/ui/BaseInput.vue'
import BaseSelect from '@/components/ui/BaseSelect.vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import BaseModal from '@/components/ui/BaseModal.vue'
import DropdownItem from '@/components/ui/DropdownItem.vue'
import { categoryService } from '@/services/categoryService'
import type { Category } from '@/types'

interface Props {
  modelValue: number | null
  categories: Category[]
  error?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: number | null]
}>()

// 状态
const dropdownRef = ref()
const searchQuery = ref('')
const showCreateDialog = ref(false)
const newCategoryName = ref('')
const newCategoryParent = ref(0)
const creating = ref(false)
const createError = ref('')

// 计算属性
const selectedCategory = computed(() =>
  props.categories.find(cat => cat.id === props.modelValue)
)

const filteredCategories = computed(() => {
  if (!searchQuery.value) return props.categories

  const query = searchQuery.value.toLowerCase()
  return props.categories.filter(category =>
    category.name.toLowerCase().includes(query)
  )
})

// 选择分类
const selectCategory = (categoryId: number | null) => {
  emit('update:modelValue', categoryId)
  dropdownRef.value?.close()
}

// 创建新分类
const createCategory = async () => {
  if (!newCategoryName.value.trim()) {
    createError.value = '请输入分类名称'
    return
  }

  try {
    creating.value = true
    createError.value = ''

    const categoryId = await categoryService.createCategory({
      name: newCategoryName.value.trim(),
      parent_id: newCategoryParent.value,
      sort_order: props.categories.length + 1
    })

    // 重新加载分类列表
    const newCategory = await categoryService.getCategoryById(categoryId)
    if (newCategory) {
      props.categories.push(newCategory)
      emit('update:modelValue', categoryId)
    }

    // 重置表单
    newCategoryName.value = ''
    newCategoryParent.value = 0
    showCreateDialog.value = false
    dropdownRef.value?.close()
  } catch (error) {
    console.error('创建分类失败:', error)
    createError.value = '创建失败，请重试'
  } finally {
    creating.value = false
  }
}
</script>
