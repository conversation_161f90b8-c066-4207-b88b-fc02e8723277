import { computed, ref, watch, type ComputedRef } from 'vue'
import { useQuery, useQueryClient } from '@tanstack/vue-query'
import { resourceService } from '@/services/resourceService'
import { useKnowledgeFilterStore } from '@/stores/knowledgeFilter'
import type { ResourceWithDetails, SearchOptions } from '@/types'
import { debounce } from 'lodash-es'

export interface UseResourceListOptions {
  pageSize?: number | ComputedRef<number>
  enableInfiniteScroll?: boolean | ComputedRef<boolean>
  autoRefetch?: boolean
}

export interface ResourceListResult {
  resources: Ref<ResourceWithDetails[]>
  total: Ref<number>
  isLoading: Ref<boolean>
  isError: Ref<boolean>
  error: Ref<Error | null>
  hasMore: Ref<boolean>
  currentPage: Ref<number>
  loadMore: () => Promise<void>
  refresh: () => Promise<void>
  reset: () => void
}

/**
 * 资源列表管理 Hook
 * 统一处理资源列表的加载、筛选、分页等逻辑
 */
export function useResourceList(options: UseResourceListOptions = {}): ResourceListResult {
  const { pageSize = 12, enableInfiniteScroll = true, autoRefetch = true } = options

  const filterStore = useKnowledgeFilterStore()
  const queryClient = useQueryClient()

  // 本地状态
  const currentPage = ref(1)
  const allResources = ref<ResourceWithDetails[]>([])

  // 计算实际的页面大小和无限滚动设置
  const actualPageSize = computed(() => {
    return typeof pageSize === 'number' ? pageSize : pageSize.value
  })

  const actualEnableInfiniteScroll = computed(() => {
    return typeof enableInfiniteScroll === 'boolean'
      ? enableInfiniteScroll
      : enableInfiniteScroll.value
  })

  // 构建查询参数
  const queryParams = computed((): SearchOptions => {
    const params = filterStore.getFilterParams()
    return {
      category_id: params.categoryId || undefined,
      tag_ids:
        params.tagIds.length > 0
          ? params.tagIds.filter((id): id is number => id !== undefined)
          : undefined,
      keyword: params.keyword || undefined,
      sort_by: params.sortKey.split('_').slice(0, -1).join('_') as any,
      sort_order: params.sortKey.split('_').slice(-1)[0] as any,
      limit: actualPageSize.value,
      offset: (currentPage.value - 1) * actualPageSize.value,
    }
  })

  // 查询键，用于缓存管理
  const queryKey = computed(() => ['resources', queryParams.value])

  // 使用 TanStack Query 管理数据
  const { data, isLoading, isError, error, refetch, isFetching } = useQuery({
    queryKey,
    queryFn: async () => {
      console.log('useResourceList: 开始加载资源数据，查询参数:', queryParams.value)
      const result = await resourceService.searchResourcesWithCount(queryParams.value)
      console.log('useResourceList: 资源数据加载完成:', result)
      return result
    },
    staleTime: 5 * 60 * 1000, // 5分钟内数据被认为是新鲜的
    gcTime: 10 * 60 * 1000, // 10分钟后清理缓存
    enabled: autoRefetch,
  })

  // 计算属性
  const resources = computed(() => {
    if (actualEnableInfiniteScroll.value) {
      return allResources.value
    }
    return data.value?.resources || []
  })

  const total = computed(() => data.value?.total || 0)

  const hasMore = computed(() => {
    if (!actualEnableInfiniteScroll.value) return false
    return allResources.value.length < total.value
  })

  // 加载更多数据（无限滚动）
  const loadMore = async () => {
    if (!hasMore.value || isLoading.value) return

    currentPage.value += 1

    try {
      const result = await resourceService.searchResourcesWithCount(queryParams.value)
      if (result.resources.length > 0) {
        allResources.value.push(...result.resources)
      }
    } catch (error) {
      console.error('加载更多资源失败:', error)
      currentPage.value -= 1 // 回滚页码
    }
  }

  // 刷新数据
  const refresh = async () => {
    await refetch()
    if (actualEnableInfiniteScroll.value) {
      // 重置无限滚动状态
      currentPage.value = 1
      allResources.value = data.value?.resources || []
    }
  }

  // 重置状态
  const reset = () => {
    currentPage.value = 1
    allResources.value = []
    queryClient.removeQueries({ queryKey: ['resources'] })
  }

  // 防抖的刷新函数
  const debouncedRefresh = debounce(refresh, 300)

  // 监听筛选条件变化
  watch(
    () => filterStore.getFilterParams(),
    () => {
      if (autoRefetch) {
        reset()
        debouncedRefresh()
      }
    },
    { deep: true },
  )

  // 监听数据变化，更新无限滚动列表
  watch(
    () => data.value,
    (newData) => {
      if (actualEnableInfiniteScroll.value && newData && currentPage.value === 1) {
        allResources.value = newData.resources
      }
    },
    { immediate: true },
  )

  return {
    resources,
    total,
    isLoading: computed(() => isLoading.value || isFetching.value),
    isError,
    error,
    hasMore,
    currentPage,
    loadMore,
    refresh,
    reset,
  }
}

/**
 * 资源详情管理 Hook
 */
export function useResourceDetail(resourceId: Ref<number | undefined>) {
  const queryKey = computed(() => ['resource', resourceId.value])

  const {
    data: resource,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey,
    queryFn: async () => {
      if (!resourceId.value) return null
      return await resourceService.getResourceById(resourceId.value)
    },
    enabled: computed(() => !!resourceId.value),
    staleTime: 2 * 60 * 1000, // 2分钟
    gcTime: 5 * 60 * 1000, // 5分钟
  })

  return {
    resource,
    isLoading,
    isError,
    error,
    refresh: refetch,
  }
}

/**
 * 资源操作 Hook（创建、更新、删除）
 */
export function useResourceMutations() {
  const queryClient = useQueryClient()

  const invalidateResourceQueries = () => {
    queryClient.invalidateQueries({ queryKey: ['resources'] })
    queryClient.invalidateQueries({ queryKey: ['resource'] })
  }

  const createResource = async (resourceData: any) => {
    try {
      const result = await resourceService.createResource(resourceData)
      invalidateResourceQueries()
      return result
    } catch (error) {
      console.error('创建资源失败:', error)
      throw error
    }
  }

  const updateResource = async (id: number, resourceData: any) => {
    try {
      await resourceService.updateResource(id, resourceData)
      invalidateResourceQueries()
    } catch (error) {
      console.error('更新资源失败:', error)
      throw error
    }
  }

  const deleteResource = async (id: number) => {
    try {
      await resourceService.deleteResource(id)
      invalidateResourceQueries()
    } catch (error) {
      console.error('删除资源失败:', error)
      throw error
    }
  }

  return {
    createResource,
    updateResource,
    deleteResource,
  }
}
