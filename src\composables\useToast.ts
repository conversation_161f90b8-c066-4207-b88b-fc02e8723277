import { ref, createApp, h } from 'vue'
import Toast from '@/components/ui/Toast.vue'

interface ToastOptions {
  type?: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  autoClose?: boolean
}

const toasts = ref<Array<{ id: string; component: any }>>([])

let toastId = 0

export function useToast() {
  const show = (options: ToastOptions) => {
    const id = `toast-${++toastId}`
    
    // 创建容器元素
    const container = document.createElement('div')
    document.body.appendChild(container)
    
    // 创建Toast组件实例
    const app = createApp({
      render() {
        return h(Toast, {
          ...options,
          onClose: () => {
            // 清理组件和DOM
            app.unmount()
            document.body.removeChild(container)
            
            // 从toasts数组中移除
            const index = toasts.value.findIndex(toast => toast.id === id)
            if (index > -1) {
              toasts.value.splice(index, 1)
            }
          }
        })
      }
    })
    
    // 挂载组件
    app.mount(container)
    
    // 添加到toasts数组
    toasts.value.push({ id, component: app })
    
    return id
  }

  const success = (title: string, message?: string, duration = 4000) => {
    return show({
      type: 'success',
      title,
      message,
      duration
    })
  }

  const error = (title: string, message?: string, duration = 5000) => {
    return show({
      type: 'error',
      title,
      message,
      duration
    })
  }

  const warning = (title: string, message?: string, duration = 4000) => {
    return show({
      type: 'warning',
      title,
      message,
      duration
    })
  }

  const info = (title: string, message?: string, duration = 4000) => {
    return show({
      type: 'info',
      title,
      message,
      duration
    })
  }

  const clear = () => {
    toasts.value.forEach(toast => {
      toast.component.unmount()
    })
    toasts.value = []
  }

  return {
    show,
    success,
    error,
    warning,
    info,
    clear,
    toasts: toasts.value
  }
}
