<template>
  <div class="resource-detail-container">
    <div class="resource-detail-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-wrapper">
        <a-spin size="large" tip="加载中...">
          <div class="loading-content"></div>
        </a-spin>
      </div>

      <!-- 资源不存在 -->
      <div v-else-if="!resource" class="not-found-wrapper">
        <a-result status="404" title="资源不存在" sub-title="您访问的资源可能已被删除或不存在">
          <template #extra>
            <a-button type="primary" @click="$router.push('/knowledge')">
              返回知识库
            </a-button>
          </template>
        </a-result>
      </div>

      <!-- 主要内容区域 -->
      <div v-else class="resource-detail-main">
        <!-- 顶部操作栏 -->
        <div class="top-actions">
          <a-button type="text" @click="$router.back()" class="back-btn">
            <template #icon>
              <ArrowLeftOutlined />
            </template>
            返回
          </a-button>
        </div>

        <!-- 使用复用组件 -->
        <ResourceDetailDisplay :resource="resource" :show-actions="true" :show-related-resources="true"
          :related-resources="relatedResources" @edit="editResource" @delete="deleteResource"
          @go-to-resource="goToResource" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import {
  ArrowLeftOutlined
} from '@ant-design/icons-vue'
import ResourceDetailDisplay from '@/components/common/ResourceDetailDisplay.vue'
import { resourceService } from '@/services/resourceService'
import type { ResourceWithDetails } from '@/types'

const route = useRoute()
const router = useRouter()

// 状态
const loading = ref(true)
const resource = ref<ResourceWithDetails | null>(null)
const relatedResources = ref<ResourceWithDetails[]>([])

// 初始化
onMounted(async () => {
  await loadResource()
})

// 加载资源
const loadResource = async () => {
  try {
    loading.value = true
    const resourceId = Number(route.params.id)

    if (!resourceId) {
      router.push('/knowledge')
      return
    }

    const [resourceData, related] = await Promise.all([
      resourceService.getResourceById(resourceId),
      loadRelatedResources(resourceId)
    ])

    if (!resourceData) {
      resource.value = null
      return
    }

    resource.value = resourceData
    relatedResources.value = related

    // 增加浏览次数
    await resourceService.incrementViewCount(resourceId)
  } catch (error) {
    console.error('加载资源失败:', error)
    resource.value = null
  } finally {
    loading.value = false
  }
}

// 加载相关资源
const loadRelatedResources = async (currentResourceId: number) => {
  try {
    // 获取当前资源信息用于推荐
    const currentResource = await resourceService.getResourceById(currentResourceId)
    if (!currentResource) return []

    // 基于分类和标签推荐相关资源
    const searchOptions = {
      category_id: currentResource.category_id,
      tag_ids: currentResource.tags?.map((tag: any) => tag.id!) || [],
      limit: 6
    }

    const related = await resourceService.searchResources(searchOptions)

    // 排除当前资源
    return related.filter(r => r.id !== currentResourceId).slice(0, 3)
  } catch (error) {
    console.error('加载相关资源失败:', error)
    return []
  }
}

// 编辑资源
const editResource = () => {
  if (resource.value?.id) {
    router.push(`/knowledge/create?id=${resource.value.id}`)
  }
}

// 删除资源
const deleteResource = async () => {
  if (!resource.value?.id) return

  // 使用 Ant Design 的确认对话框
  const confirmed = await new Promise((resolve) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除资源"${resource.value?.title}"吗？此操作不可撤销。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => resolve(true),
      onCancel: () => resolve(false)
    })
  })

  if (confirmed) {
    try {
      await resourceService.deleteResource(resource.value.id)
      message.success('资源删除成功')
      router.push('/knowledge')
    } catch (error) {
      console.error('删除资源失败:', error)
      message.error('删除失败，请重试')
    }
  }
}

// 跳转到相关资源
const goToResource = (id: number | undefined) => {
  if (id) {
    router.push(`/knowledge/${id}`)
  }
}
</script>

<style scoped>
/* 资源详情页面 - 参考分类管理器的卡片式设计 */
.resource-detail-container {
  padding: 24px;
  background: var(--ant-color-bg-layout);
  min-height: calc(100vh - 48px);
}

.resource-detail-content {
  max-width: 1200px;
  margin: 0 auto;
}

/* 加载和错误状态 */
.loading-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-content {
  width: 100px;
  height: 100px;
}

.not-found-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* 顶部操作栏 */
.top-actions {
  margin-bottom: 16px;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--ant-color-text-secondary);
  transition: all 0.2s;
}

.back-btn:hover {
  color: var(--ant-color-primary);
}

/* 卡片标题样式 - 参考分类管理器 */
.card-title-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--ant-color-text);
}

.title-icon {
  font-size: 16px;
  color: var(--ant-color-primary);
}

/* 卡片样式 */
.resource-info-card,
.description-card,
.related-resources-card {
  border: 1px solid var(--ant-color-border);
  border-radius: 8px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  transition: all 0.2s;
}

.resource-info-card:hover,
.description-card:hover,
.related-resources-card:hover {
  border-color: var(--ant-color-border-secondary);
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);
}

/* 资源封面 */
.resource-cover {
  width: 100%;
  height: 160px;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid var(--ant-color-border);
}

.cover-image {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--ant-color-bg-layout);
  color: var(--ant-color-text-tertiary);
}

.placeholder-icon {
  font-size: 48px;
  opacity: 0.3;
}

/* 资源详情 */
.resource-details {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.resource-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--ant-color-text);
  margin: 0;
  line-height: 1.3;
}

.resource-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--ant-color-text-secondary);
  font-size: 13px;
}

.meta-item .anticon {
  font-size: 14px;
}

/* 标签区域 */
.resource-tags-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tags-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.tags-label {
  font-weight: 500;
  color: var(--ant-color-text-secondary);
  white-space: nowrap;
  font-size: 13px;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

/* 详情页紧凑型标签样式 */
.detail-tag {
  display: inline-flex;
  align-items: center;
  padding: 3px 8px;
  border: 1px solid;
  border-radius: 10px;
  font-size: 12px;
  user-select: none;
  white-space: nowrap;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.detail-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.detail-tag .tag-name {
  font-weight: 500;
  line-height: 1.2;
}

.category-tag {
  margin: 0;
}

.no-category {
  color: var(--ant-color-text-tertiary);
  font-size: 13px;
}

/* 资源链接 */
.resource-url {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: auto;
}

.url-link {
  color: var(--ant-color-primary);
  text-decoration: none;
  font-size: 13px;
  flex: 1;
  display: flex;
  align-items: center;
  gap: 4px;
}

.url-link:hover {
  text-decoration: underline;
}

/* 链接内的操作图标 */
.url-actions {
  display: inline-flex;
  align-items: center;
  gap: 3px;
  margin-left: 4px;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.url-link:hover .url-actions {
  opacity: 1;
}

.action-icon {
  font-size: 10px !important;
  color: var(--ant-color-text-tertiary);
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-icon:hover {
  color: var(--ant-color-primary);
  background: var(--ant-color-primary-bg);
  transform: scale(1.1);
}

/* 操作按钮样式已移至 .action-btn */

.danger-item {
  color: var(--ant-color-error) !important;
}

.danger-item:hover {
  background: var(--ant-color-error-bg) !important;
}

/* 描述内容样式 */
.description-content {
  min-height: 200px;
}

.markdown-content {
  padding: 8px 0;
}

.description-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  color: var(--ant-color-text-tertiary);
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.3;
}

/* 相关资源样式 */
.related-resources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.related-resource-item {
  cursor: pointer;
}

.related-resource-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.related-resource-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--ant-color-text);
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.related-resource-desc {
  font-size: 13px;
  color: var(--ant-color-text-secondary);
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

.related-resource-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
}

.view-count {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--ant-color-text-tertiary);
  font-size: 12px;
}

.no-related-resources {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  color: var(--ant-color-text-tertiary);
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .resource-detail-container {
    padding: 16px;
  }

  .resource-info-card .ant-row {
    flex-direction: column;
  }

  .resource-cover {
    height: 200px;
  }

  .resource-title {
    font-size: 18px;
  }

  .related-resources-grid {
    grid-template-columns: 1fr;
  }

  .resource-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .tags-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .resource-detail-container {
    padding: 12px;
  }

  .card-title-wrapper {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .resource-title {
    font-size: 16px;
  }
}
</style>
