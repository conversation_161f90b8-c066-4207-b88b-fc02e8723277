<template>
  <div class="tag-management-container">
    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon total">
            <div class="i-heroicons-tag w-5 h-5"></div>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ tagStats.total }}</div>
            <div class="stat-label">总标签</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon used">
            <div class="i-heroicons-check-circle w-5 h-5"></div>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ tagStats.used }}</div>
            <div class="stat-label">已使用</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon unused">
            <div class="i-heroicons-x-circle w-5 h-5"></div>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ tagStats.unused }}</div>
            <div class="stat-label">未使用</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加新标签 -->
    <div class="add-section">
      <div class="add-form">
        <div class="form-header">
          <h4>添加新标签</h4>
        </div>
        <div class="form-body">
          <div class="input-row">
            <div class="name-input-wrapper">
              <input v-model="newTag.name" type="text" placeholder="输入标签名称..." class="name-input"
                @keyup.enter="addTag" />
            </div>
            <div class="color-input-wrapper">
              <input v-model="newTag.color" type="color" class="color-input" title="选择颜色" />
            </div>
            <button @click="addTag" :disabled="!newTag.name.trim() || adding" class="add-button">
              <div v-if="adding" class="i-heroicons-arrow-path w-4 h-4 animate-spin"></div>
              <div v-else class="i-heroicons-plus w-4 h-4"></div>
              <span>{{ adding ? '添加中...' : '添加' }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 标签列表 -->
    <div class="tags-section">
      <div class="section-header">
        <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100">标签列表</h4>
        <div class="section-actions">
          <div class="search-box">
            <div class="i-heroicons-magnifying-glass w-4 h-4 text-gray-400"></div>
            <input v-model="searchQuery" type="text" placeholder="搜索标签..." class="search-input" />
          </div>
          <button @click="cleanupUnusedTags" :disabled="tagStats.unused === 0 || cleaning"
            class="btn btn-outline btn-sm">
            <div v-if="cleaning" class="i-heroicons-arrow-path w-4 h-4 animate-spin mr-2"></div>
            <div v-else class="i-heroicons-trash w-4 h-4 mr-2"></div>
            清理未使用
          </button>
        </div>
      </div>

      <div class="tags-list">
        <div v-if="loading" class="loading-state">
          <div class="i-heroicons-arrow-path w-6 h-6 animate-spin text-gray-400"></div>
          <span class="text-gray-500 dark:text-gray-400">加载中...</span>
        </div>

        <div v-else-if="filteredTags.length === 0" class="empty-state">
          <div class="i-heroicons-tag w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-3"></div>
          <p class="text-gray-500 dark:text-gray-400">
            {{ searchQuery ? '没有找到匹配的标签' : '还没有创建任何标签' }}
          </p>
        </div>

        <div v-else class="tags-grid">
          <div v-for="tag in filteredTags" :key="tag.id" class="tag-card">
            <div class="tag-header">
              <div class="tag-color" :style="{ backgroundColor: tag.color }"></div>
              <div class="tag-info">
                <div class="tag-name">{{ tag.name }}</div>
                <div class="tag-count">{{ tag.imageCount }} 张图片</div>
              </div>
              <div class="tag-actions">
                <button @click="editTag(tag)" class="action-btn" title="编辑标签">
                  <div class="i-heroicons-pencil w-4 h-4"></div>
                </button>
                <button @click="deleteTag(tag)" class="action-btn text-red-500 hover:text-red-600" title="删除标签">
                  <div class="i-heroicons-trash w-4 h-4"></div>
                </button>
              </div>
            </div>
            <div class="tag-meta">
              <span class="tag-date">{{ formatDate(tag.createdTime) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { imageDataService, type ImageTagRecord } from '@/services/imageDataService'

// 状态
const loading = ref(false)
const adding = ref(false)
const cleaning = ref(false)
const tags = ref<ImageTagRecord[]>([])
const searchQuery = ref('')

// 新标签表单
const newTag = ref({
  name: '',
  color: '#3B82F6'
})

// 标签统计
const tagStats = computed(() => {
  const total = tags.value.length
  const used = tags.value.filter(tag => tag.imageCount > 0).length
  const unused = total - used

  return { total, used, unused }
})

// 过滤后的标签
const filteredTags = computed(() => {
  if (!searchQuery.value.trim()) {
    return tags.value
  }

  const query = searchQuery.value.toLowerCase()
  return tags.value.filter(tag =>
    tag.name && typeof tag.name === 'string' &&
    tag.name.toLowerCase().includes(query)
  )
})

// 加载标签数据
const loadTags = async () => {
  try {
    loading.value = true
    const loadedTags = await imageDataService.getAllTags()

    // 验证数据格式
    tags.value = loadedTags.filter((tag: any) =>
      tag &&
      typeof tag === 'object' &&
      tag.name &&
      typeof tag.name === 'string'
    )


  } catch (error) {
    console.error('加载图片标签失败:', error)
    tags.value = []
  } finally {
    loading.value = false
  }
}

// 添加标签
const addTag = async () => {
  if (!newTag.value.name.trim()) return

  try {
    adding.value = true

    // 检查标签是否已存在
    const existingTag = tags.value.find(tag =>
      tag.name && typeof tag.name === 'string' &&
      tag.name.toLowerCase() === newTag.value.name.toLowerCase()
    )

    if (existingTag) {
      alert('标签名称已存在')
      return
    }

    // 调用imageDataService添加标签
    await imageDataService.addImageTag(newTag.value.name, newTag.value.color)

    // 重置表单
    newTag.value = {
      name: '',
      color: '#3B82F6'
    }

    // 刷新数据
    await loadTags()
  } catch (error) {
    console.error('添加标签失败:', error)
    alert('添加标签失败：' + (error as Error).message)
  } finally {
    adding.value = false
  }
}

// 编辑标签
const editTag = async (tag: ImageTagRecord) => {
  const newName = prompt('请输入新的标签名称:', tag.name)
  if (newName && newName.trim() && newName !== tag.name) {
    try {
      await imageDataService.updateImageTag(tag.id!, { name: newName.trim() })
      await loadTags()
    } catch (error) {
      console.error('编辑标签失败:', error)
      alert('编辑标签失败，请重试')
    }
  }
}

// 删除标签
const deleteTag = async (tag: ImageTagRecord) => {
  if (tag.imageCount > 0) {
    if (!confirm(`标签"${tag.name}"正在被 ${tag.imageCount} 张图片使用，确定要删除吗？删除后这些图片将失去该标签。`)) {
      return
    }
  } else {
    if (!confirm(`确定要删除标签"${tag.name}"吗？`)) {
      return
    }
  }

  try {
    await imageDataService.deleteImageTag(tag.id!)
    await loadTags()
  } catch (error) {
    console.error('删除标签失败:', error)
    alert('删除标签失败，请重试')
  }
}

// 清理未使用的标签
const cleanupUnusedTags = async () => {
  const unusedTags = tags.value.filter(tag => tag.imageCount === 0)

  if (unusedTags.length === 0) {
    alert('没有未使用的标签需要清理')
    return
  }

  if (!confirm(`确定要删除 ${unusedTags.length} 个未使用的标签吗？`)) {
    return
  }

  try {
    cleaning.value = true

    for (const tag of unusedTags) {
      await imageDataService.deleteImageTag(tag.id!)
    }

    await loadTags()
    alert(`已清理 ${unusedTags.length} 个未使用的标签`)
  } catch (error) {
    console.error('清理标签失败:', error)
    alert('清理标签失败，请重试')
  } finally {
    cleaning.value = false
  }
}

// 格式化日期
const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

// 这些方法现在已经在imageDataService中实现了

// 初始化
onMounted(() => {
  loadTags()
})
</script>

<style scoped>
.tag-management-container {
  padding: 0;
  max-width: 100%;
}

/* 统计卡片样式 */
.stats-section {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.stat-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.used {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.unused {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

/* 添加标签样式 */
.add-section {
  margin-bottom: 24px;
}

.add-form {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
}

.form-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.input-row {
  display: flex;
  gap: 12px;
  align-items: center;
}

.name-input-wrapper {
  flex: 1;
}

.name-input {
  width: 100%;
  padding: 10px 14px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  color: #1f2937;
  transition: all 0.2s ease;
}

.name-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.color-input-wrapper {
  display: flex;
  align-items: center;
}

.color-input {
  width: 40px;
  height: 40px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  cursor: pointer;
  background: none;
}

.add-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-button:hover:not(:disabled) {
  background: #2563eb;
}

.add-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 标签列表样式 */
.tags-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.section-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 240px;
  padding: 8px 12px 8px 36px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  color: #1f2937;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-box .i-heroicons-magnifying-glass {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  pointer-events: none;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 20px;
  text-align: center;
  color: #6b7280;
}

.tags-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.tag-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.2s ease;
}

.tag-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.tag-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tag-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.tag-info {
  flex: 1;
  min-width: 0;
}

.tag-name {
  font-weight: 500;
  color: #1f2937;
  font-size: 14px;
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tag-count {
  font-size: 12px;
  color: #6b7280;
}

.tag-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-btn {
  padding: 6px;
  border-radius: 6px;
  color: #9ca3af;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  color: #6b7280;
  background: #f3f4f6;
}

.action-btn.text-red-500 {
  color: #ef4444;
}

.action-btn.text-red-500:hover {
  color: #dc2626;
  background: #fef2f2;
}

.tag-meta {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f3f4f6;
}

.tag-date {
  font-size: 11px;
  color: #9ca3af;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  border: 1px solid transparent;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn-primary {
  color: white;
  background: #3b82f6;
  border-color: #3b82f6;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
  border-color: #2563eb;
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-outline {
  color: #6b7280;
  background: white;
  border-color: #d1d5db;
}

.btn-outline:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 13px;
}

/* 清理按钮样式 */
.cleanup-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cleanup-button:hover:not(:disabled) {
  background: #e5e7eb;
  color: #374151;
}

.cleanup-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .input-row {
    flex-direction: column;
    gap: 12px;
  }

  .name-input-wrapper,
  .color-input-wrapper,
  .add-button {
    width: 100%;
  }

  .section-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .search-input {
    width: 100%;
  }

  .tags-grid {
    grid-template-columns: 1fr;
  }
}
</style>
