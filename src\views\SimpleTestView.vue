<template>
  <div style="padding: 20px; background: #f0f0f0; margin: 20px; border-radius: 8px;">
    <h1 style="color: #333; font-size: 24px; margin-bottom: 16px;">简单测试页面</h1>
    <p style="color: #666; font-size: 16px; margin-bottom: 12px;">
      这是一个简单的测试页面，用于验证路由是否正常工作。
    </p>
    <p style="color: #666; font-size: 14px;">
      当前时间: {{ currentTime }}
    </p>
    <div style="margin-top: 20px;">
      <button 
        @click="goHome" 
        style="padding: 8px 16px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;"
      >
        返回首页
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const currentTime = ref('')

onMounted(() => {
  console.log('SimpleTestView 组件已挂载')
  updateTime()
  setInterval(updateTime, 1000)
})

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

const goHome = () => {
  router.push('/')
}
</script>
