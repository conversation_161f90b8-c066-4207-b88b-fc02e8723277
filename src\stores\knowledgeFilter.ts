import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Category, Tag } from '@/types'

export interface FilterState {
  selectedCategoryId: number | null
  selectedTags: Tag[]
  currentSortKey: string
  searchKeyword: string
  viewMode: 'grid' | 'list'
}

export const useKnowledgeFilterStore = defineStore('knowledgeFilter', () => {
  // 状态定义
  const selectedCategoryId = ref<number | null>(null)
  const selectedTags = ref<Tag[]>([])
  const currentSortKey = ref<string>('created_at_desc')
  const searchKeyword = ref<string>('')
  const viewMode = ref<'grid' | 'list'>('grid')
  
  // 分类和标签数据（用于显示）
  const selectedCategory = ref<Category | null>(null)
  
  // 计算属性
  const hasActiveFilters = computed(() => {
    return selectedCategoryId.value !== null || 
           selectedTags.value.length > 0 || 
           searchKeyword.value.trim() !== ''
  })
  
  const filterSummary = computed(() => {
    const summary: string[] = []
    
    if (selectedCategory.value) {
      summary.push(`分类: ${selectedCategory.value.name}`)
    }
    
    if (selectedTags.value.length > 0) {
      summary.push(`标签: ${selectedTags.value.map(tag => tag.name).join(', ')}`)
    }
    
    if (searchKeyword.value.trim()) {
      summary.push(`搜索: ${searchKeyword.value}`)
    }
    
    return summary.join(' | ')
  })
  
  // Actions
  const setCategory = (categoryId: number | null, category: Category | null = null) => {
    selectedCategoryId.value = categoryId
    selectedCategory.value = category
    persistState()
  }
  
  const addTag = (tag: Tag) => {
    if (!selectedTags.value.find(t => t.id === tag.id)) {
      selectedTags.value.push(tag)
      persistState()
    }
  }
  
  const removeTag = (tagId: number) => {
    const index = selectedTags.value.findIndex(tag => tag.id === tagId)
    if (index > -1) {
      selectedTags.value.splice(index, 1)
      persistState()
    }
  }
  
  const setTags = (tags: Tag[]) => {
    selectedTags.value = [...tags]
    persistState()
  }
  
  const setSortKey = (sortKey: string) => {
    currentSortKey.value = sortKey
    persistState()
  }
  
  const setSearchKeyword = (keyword: string) => {
    searchKeyword.value = keyword
    persistState()
  }
  
  const setViewMode = (mode: 'grid' | 'list') => {
    viewMode.value = mode
    persistState()
  }
  
  const clearFilters = () => {
    selectedCategoryId.value = null
    selectedCategory.value = null
    selectedTags.value = []
    searchKeyword.value = ''
    persistState()
  }
  
  const clearAll = () => {
    selectedCategoryId.value = null
    selectedCategory.value = null
    selectedTags.value = []
    searchKeyword.value = ''
    currentSortKey.value = 'created_at_desc'
    viewMode.value = 'grid'
    persistState()
  }
  
  // 持久化状态到 localStorage
  const persistState = () => {
    const state: FilterState = {
      selectedCategoryId: selectedCategoryId.value,
      selectedTags: selectedTags.value,
      currentSortKey: currentSortKey.value,
      searchKeyword: searchKeyword.value,
      viewMode: viewMode.value
    }
    
    try {
      localStorage.setItem('knowledge-filter-state', JSON.stringify(state))
    } catch (error) {
      console.warn('无法保存筛选状态到 localStorage:', error)
    }
  }
  
  // 从 localStorage 恢复状态
  const restoreState = () => {
    try {
      const savedState = localStorage.getItem('knowledge-filter-state')
      if (savedState) {
        const state: FilterState = JSON.parse(savedState)
        
        selectedCategoryId.value = state.selectedCategoryId || null
        selectedTags.value = state.selectedTags || []
        currentSortKey.value = state.currentSortKey || 'created_at_desc'
        searchKeyword.value = state.searchKeyword || ''
        viewMode.value = state.viewMode || 'grid'
        
        console.log('已恢复筛选状态:', state)
        return true
      }
    } catch (error) {
      console.warn('无法从 localStorage 恢复筛选状态:', error)
    }
    return false
  }
  
  // 更新分类信息（当分类数据加载后调用）
  const updateCategoryInfo = (categories: Category[]) => {
    if (selectedCategoryId.value && !selectedCategory.value) {
      const category = categories.find(c => c.id === selectedCategoryId.value)
      if (category) {
        selectedCategory.value = category
      } else {
        // 如果分类不存在了，清除选择
        selectedCategoryId.value = null
        selectedCategory.value = null
        persistState()
      }
    }
  }
  
  // 验证并清理无效的标签
  const validateTags = (availableTags: Tag[]) => {
    const validTags = selectedTags.value.filter(selectedTag => 
      availableTags.find(tag => tag.id === selectedTag.id)
    )
    
    if (validTags.length !== selectedTags.value.length) {
      selectedTags.value = validTags
      persistState()
    }
  }
  
  // 获取筛选参数（用于 API 调用）
  const getFilterParams = () => {
    return {
      categoryId: selectedCategoryId.value,
      tagIds: selectedTags.value.map(tag => tag.id),
      sortKey: currentSortKey.value,
      keyword: searchKeyword.value.trim(),
      viewMode: viewMode.value
    }
  }
  
  return {
    // 状态
    selectedCategoryId,
    selectedTags,
    selectedCategory,
    currentSortKey,
    searchKeyword,
    viewMode,
    
    // 计算属性
    hasActiveFilters,
    filterSummary,
    
    // Actions
    setCategory,
    addTag,
    removeTag,
    setTags,
    setSortKey,
    setSearchKeyword,
    setViewMode,
    clearFilters,
    clearAll,
    restoreState,
    updateCategoryInfo,
    validateTags,
    getFilterParams
  }
})
