<template>
  <div class="relative">
    <label v-if="label" :for="textareaId" class="block text-sm font-medium text-secondary mb-2 text-chinese">
      {{ label }}
      <span v-if="required" class="text-red-500 ml-1">*</span>
    </label>

    <div class="relative">
      <textarea :id="textareaId" :value="modelValue" :placeholder="placeholder" :disabled="disabled"
        :readonly="readonly" :rows="rows" :class="textareaClasses" @input="handleInput" @blur="handleBlur"
        @focus="handleFocus"></textarea>

      <button v-if="clearable && modelValue" @click="handleClear" class="custom-textarea-clear-button" type="button"
        title="清空">
        <div class="i-heroicons-x-mark w-4 h-4"></div>
      </button>
    </div>

    <p v-if="error" class="mt-1 text-sm text-red-600 animate-slide-down text-chinese">
      {{ error }}
    </p>

    <p v-else-if="hint" class="mt-1 text-sm text-secondary text-chinese">
      {{ hint }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'

interface Props {
  modelValue?: string
  label?: string
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  error?: string
  hint?: string
  clearable?: boolean
  rows?: number
  size?: 'sm' | 'md' | 'lg'
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  readonly: false,
  required: false,
  clearable: false,
  rows: 4,
  size: 'md'
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  blur: [event: FocusEvent]
  focus: [event: FocusEvent]
}>()

const textareaId = ref(`textarea-${Math.random().toString(36).substring(2, 11)}`)
const isFocused = ref(false)

const textareaClasses = computed(() => {
  const classes = ['custom-textarea-field']
  if (props.error) {
    classes.push('error')
  }
  if (props.size === 'sm') {
    classes.push('compact')
  }
  return classes
})

const handleInput = (event: Event) => {
  const target = event.target as HTMLTextAreaElement
  emit('update:modelValue', target.value)
}

const handleBlur = (event: FocusEvent) => {
  isFocused.value = false
  emit('blur', event)
}

const handleFocus = (event: FocusEvent) => {
  isFocused.value = true
  emit('focus', event)
}

const handleClear = () => {
  emit('update:modelValue', '')
}
</script>

<style scoped>
/* 文本域样式 */
.custom-textarea-field {
  width: 100%;
  background-color: #f9fafb;
  border: none;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 16px;
  color: #111827;
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei', '微软雅黑', sans-serif;
  line-height: 1.75;
  letter-spacing: 0.02em;
  resize: vertical;
  min-height: 100px;
  transition: all 0.2s ease;
  outline: none;
}

/* 紧凑模式文本域样式 */
.custom-textarea-field.compact {
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.5;
  border-radius: 8px;
  min-height: 80px;
}

.custom-textarea-field::placeholder {
  color: #6b7280;
}

.custom-textarea-field:hover {
  background-color: #f3f4f6;
}

.custom-textarea-field:focus {
  background-color: #ffffff;
  border: 1px solid var(--primary-500, #3b82f6);
  box-shadow: none;
}

.custom-textarea-field:disabled {
  background-color: #f3f4f6;
  color: #9ca3af;
  cursor: not-allowed;
}

/* 暗色模式 */
:root.dark .custom-textarea-field {
  background-color: #374151;
  color: #f9fafb;
}

:root.dark .custom-textarea-field::placeholder {
  color: #9ca3af;
}

:root.dark .custom-textarea-field:hover {
  background-color: #4b5563;
}

:root.dark .custom-textarea-field:focus {
  background-color: #1f2937;
  border: 1px solid var(--primary-400, #60a5fa);
  box-shadow: none;
}

:root.dark .custom-textarea-field:disabled {
  background-color: #4b5563;
  color: #6b7280;
}

/* 错误状态 */
.custom-textarea-field.error {
  background-color: #fef2f2;
  border: 1px solid #fca5a5;
}

.custom-textarea-field.error:focus {
  background-color: #ffffff;
  border: 1px solid #ef4444;
}

:root.dark .custom-textarea-field.error {
  background-color: #450a0a;
  border: 1px solid #dc2626;
}

:root.dark .custom-textarea-field.error:focus {
  background-color: #1f2937;
  border: 1px solid #ef4444;
}

/* 圆形清空按钮 */
.custom-textarea-clear-button {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: #9ca3af;
  background: transparent;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.custom-textarea-clear-button:hover {
  color: #6b7280;
  background-color: rgba(0, 0, 0, 0.08);
  opacity: 1;
  transform: scale(1.1);
}

:root.dark .custom-textarea-clear-button {
  color: #6b7280;
}

:root.dark .custom-textarea-clear-button:hover {
  color: #9ca3af;
  background-color: rgba(255, 255, 255, 0.15);
  transform: scale(1.1);
}

/* 紧凑模式清空按钮 */
.custom-textarea-field.compact+.custom-textarea-clear-button {
  top: 8px;
  right: 8px;
  width: 18px;
  height: 18px;
}

/* 设置界面优化 - 增强对比度 */
.settings-page .custom-textarea-field {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
}

.settings-page .custom-textarea-field:hover {
  background-color: #ffffff;
  border: 1px solid #d1d5db;
}

.settings-page .custom-textarea-field:focus {
  background-color: #ffffff;
  border: 1px solid var(--primary-500, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

:root.dark .settings-page .custom-textarea-field {
  background-color: #1f2937;
  border: 1px solid #374151;
}

:root.dark .settings-page .custom-textarea-field:hover {
  background-color: #1f2937;
  border: 1px solid #4b5563;
}

:root.dark .settings-page .custom-textarea-field:focus {
  background-color: #1f2937;
  border: 1px solid var(--primary-400, #60a5fa);
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}
</style>
