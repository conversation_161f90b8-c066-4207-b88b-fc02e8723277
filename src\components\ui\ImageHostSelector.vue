<template>
  <div class="space-y-3">
    <div class="flex items-center justify-between">
      <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
        图床选择
      </label>
      <button
        @click="selectAll"
        class="text-xs text-primary-600 hover:text-primary-700 transition-colors"
      >
        {{ allSelected ? '取消全选' : '全选' }}
      </button>
    </div>

    <div class="space-y-2">
      <div
        v-for="host in imageHosts"
        :key="host.id"
        @click="toggleHost(host.id)"
        :class="[
          'p-3 border rounded-lg cursor-pointer transition-all duration-200',
          selectedHosts.includes(host.id)
            ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
            : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
        ]"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <!-- 选择状态 -->
            <div :class="[
              'w-4 h-4 rounded border-2 flex items-center justify-center transition-colors',
              selectedHosts.includes(host.id)
                ? 'border-primary-500 bg-primary-500'
                : 'border-gray-300 dark:border-gray-600'
            ]">
              <div
                v-if="selectedHosts.includes(host.id)"
                class="i-heroicons-check w-3 h-3 text-white"
              ></div>
            </div>

            <!-- 图床信息 -->
            <div class="flex items-center space-x-2">
              <div :class="[
                'w-2 h-2 rounded-full',
                host.status === 'online' ? 'bg-green-500' : 'bg-red-500'
              ]"></div>
              <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ host.name }}
              </span>
            </div>
          </div>

          <div class="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
            <span>{{ host.speed }}</span>
            <span v-if="host.quota">{{ host.quota }}</span>
          </div>
        </div>

        <!-- 图床详细信息 -->
        <div v-if="selectedHosts.includes(host.id)" class="mt-2 pt-2 border-t border-gray-200 dark:border-gray-600">
          <div class="grid grid-cols-2 gap-2 text-xs text-gray-600 dark:text-gray-400">
            <div>
              <span class="font-medium">类型:</span> {{ host.type }}
            </div>
            <div>
              <span class="font-medium">限制:</span> {{ host.sizeLimit }}
            </div>
            <div>
              <span class="font-medium">格式:</span> {{ host.supportedFormats.join(', ') }}
            </div>
            <div>
              <span class="font-medium">CDN:</span> {{ host.cdnEnabled ? '已启用' : '未启用' }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 智能选择提示 -->
    <div v-if="selectedHosts.length === 0" class="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
      <div class="flex items-start space-x-2">
        <div class="i-heroicons-exclamation-triangle w-4 h-4 text-yellow-600 dark:text-yellow-400 mt-0.5"></div>
        <div class="text-xs text-yellow-700 dark:text-yellow-300">
          <p class="font-medium mb-1">智能选择模式</p>
          <p>未选择图床时，系统将从可用图床中随机选择以实现负载均衡。</p>
        </div>
      </div>
    </div>

    <!-- 多图床备份提示 -->
    <div v-if="selectedHosts.length > 1" class="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
      <div class="flex items-start space-x-2">
        <div class="i-heroicons-information-circle w-4 h-4 text-blue-600 dark:text-blue-400 mt-0.5"></div>
        <div class="text-xs text-blue-700 dark:text-blue-300">
          <p class="font-medium mb-1">多图床备份</p>
          <p>已选择 {{ selectedHosts.length }} 个图床，图片将同时上传到所有选中的图床实现备份。</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface ImageHost {
  id: string
  name: string
  type: string
  status: 'online' | 'offline'
  speed: string
  quota?: string
  sizeLimit: string
  supportedFormats: string[]
  cdnEnabled: boolean
  enabled: boolean
}

interface Props {
  modelValue: string[]
  availableHosts: ImageHost[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: string[]]
}>()

const selectedHosts = ref<string[]>([...props.modelValue])

// 过滤出已启用的图床
const imageHosts = computed(() => {
  return props.availableHosts.filter(host => host.enabled && host.status === 'online')
})

const allSelected = computed(() => {
  return imageHosts.value.length > 0 && selectedHosts.value.length === imageHosts.value.length
})

const toggleHost = (hostId: string) => {
  const index = selectedHosts.value.indexOf(hostId)
  if (index > -1) {
    selectedHosts.value.splice(index, 1)
  } else {
    selectedHosts.value.push(hostId)
  }
}

const selectAll = () => {
  if (allSelected.value) {
    selectedHosts.value = []
  } else {
    selectedHosts.value = imageHosts.value.map(host => host.id)
  }
}

// 监听选择变化
watch(selectedHosts, (newValue) => {
  emit('update:modelValue', newValue)
}, { deep: true })

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedHosts.value = [...newValue]
}, { deep: true })
</script>
