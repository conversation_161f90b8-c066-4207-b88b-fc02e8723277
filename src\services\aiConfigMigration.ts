import { aiConfigService } from './aiConfigService'
import { aiConfigDatabaseService } from './aiConfigDatabaseService'
import type { AiConfigForm } from '@/types'

/**
 * AI配置数据迁移服务
 * 将localStorage中的AI配置数据迁移到数据库中
 */
export class AiConfigMigration {
  private readonly MIGRATION_KEY = 'ai_config_migration_completed'

  /**
   * 检查是否需要迁移
   */
  needsMigration(): boolean {
    // 检查是否已经完成迁移
    const migrationCompleted = localStorage.getItem(this.MIGRATION_KEY)
    if (migrationCompleted === 'true') {
      return false
    }

    // 检查localStorage中是否有AI配置数据
    const hasOldData = this.hasOldConfigData()
    return hasOldData
  }

  /**
   * 检查localStorage中是否有旧的配置数据
   */
  private hasOldConfigData(): boolean {
    try {
      const configs = aiConfigService.getAllConfigs()
      return configs.length > 0
    } catch (error) {
      console.error('检查旧配置数据失败:', error)
      return false
    }
  }

  /**
   * 执行数据迁移
   */
  async migrate(): Promise<{ success: boolean; migratedCount: number; errors: string[] }> {
    const result = {
      success: false,
      migratedCount: 0,
      errors: [] as string[]
    }

    try {
      console.log('开始AI配置数据迁移...')

      // 获取localStorage中的所有配置
      const oldConfigs = aiConfigService.getAllConfigs()
      console.log(`发现 ${oldConfigs.length} 个配置需要迁移`)

      if (oldConfigs.length === 0) {
        result.success = true
        this.markMigrationCompleted()
        return result
      }

      // 逐个迁移配置
      for (const oldConfig of oldConfigs) {
        try {
          // 转换为新的配置格式
          const newConfig: AiConfigForm = {
            name: oldConfig.name,
            provider: oldConfig.provider,
            apiKey: oldConfig.apiKey,
            baseUrl: oldConfig.baseUrl,
            modelName: oldConfig.modelName || '',
            temperature: oldConfig.temperature || 0.7,
            maxTokens: oldConfig.maxTokens || 4000,
            timeout: oldConfig.timeout || 30000,
            systemPrompt: oldConfig.systemPrompt || '',
            customHeaders: oldConfig.customHeaders || {},
            customParams: oldConfig.customParams || {},
            enabled: oldConfig.enabled !== false,
            isDefault: oldConfig.isDefault || false,
          }

          // 保存到数据库
          await aiConfigDatabaseService.saveConfig(newConfig)
          result.migratedCount++
          
          console.log(`成功迁移配置: ${oldConfig.name}`)
        } catch (error: any) {
          const errorMsg = `迁移配置 "${oldConfig.name}" 失败: ${error.message}`
          console.error(errorMsg)
          result.errors.push(errorMsg)
        }
      }

      // 迁移自定义模型
      await this.migrateCustomModels()

      // 标记迁移完成
      this.markMigrationCompleted()
      result.success = true

      console.log(`AI配置数据迁移完成，成功迁移 ${result.migratedCount} 个配置`)

    } catch (error: any) {
      const errorMsg = `数据迁移失败: ${error.message}`
      console.error(errorMsg)
      result.errors.push(errorMsg)
    }

    return result
  }

  /**
   * 迁移自定义模型
   */
  private async migrateCustomModels(): Promise<void> {
    try {
      const customModels = aiConfigService.getCustomModels()
      console.log(`发现 ${customModels.length} 个自定义模型需要迁移`)

      for (const model of customModels) {
        try {
          // TODO: 实现自定义模型迁移
          // await aiConfigDatabaseService.addCustomModel(model.provider, model.name, model.label)
          console.log(`自定义模型迁移暂未实现: ${model.name}`)
        } catch (error: any) {
          console.error(`迁移自定义模型 "${model.name}" 失败:`, error)
        }
      }
    } catch (error) {
      console.error('迁移自定义模型失败:', error)
    }
  }

  /**
   * 标记迁移完成
   */
  private markMigrationCompleted(): void {
    localStorage.setItem(this.MIGRATION_KEY, 'true')
  }

  /**
   * 重置迁移状态（用于测试）
   */
  resetMigrationStatus(): void {
    localStorage.removeItem(this.MIGRATION_KEY)
  }

  /**
   * 清理旧数据（可选，谨慎使用）
   */
  async cleanupOldData(): Promise<void> {
    try {
      // 清理localStorage中的AI配置数据
      const keys = [
        'ai_configs',
        'custom_models',
        'ai_default_config_id'
      ]

      for (const key of keys) {
        localStorage.removeItem(key)
      }

      console.log('旧数据清理完成')
    } catch (error) {
      console.error('清理旧数据失败:', error)
    }
  }

  /**
   * 自动迁移（在应用启动时调用）
   */
  async autoMigrate(): Promise<void> {
    if (!this.needsMigration()) {
      console.log('无需进行AI配置数据迁移')
      return
    }

    console.log('检测到需要进行AI配置数据迁移')
    
    try {
      const result = await this.migrate()
      
      if (result.success) {
        console.log(`✅ AI配置数据迁移成功，迁移了 ${result.migratedCount} 个配置`)
        
        if (result.errors.length > 0) {
          console.warn('⚠️ 迁移过程中出现以下错误:')
          result.errors.forEach(error => console.warn(`  - ${error}`))
        }
      } else {
        console.error('❌ AI配置数据迁移失败')
        result.errors.forEach(error => console.error(`  - ${error}`))
      }
    } catch (error) {
      console.error('❌ AI配置数据迁移异常:', error)
    }
  }
}

// 导出单例实例
export const aiConfigMigration = new AiConfigMigration()
export default aiConfigMigration
