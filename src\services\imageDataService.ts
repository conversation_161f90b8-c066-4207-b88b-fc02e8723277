// 图片数据服务 - 集成到现有的KnowledgeDatabase系统
import { db, type Image, type ImageUrl, type ImageTag, type ImageTagRelation } from '@/database'

export interface ImageRecord {
  id?: number
  name: string
  originalName: string
  size: number
  type: string
  uploadTime: Date
  width?: number
  height?: number
  description?: string
  urls: ImageUrlRecord[]
  tags: string[]
  thumbnail?: string
}

export interface ImageUrlRecord {
  id?: number
  hostId: string
  hostName: string
  url: string
  deleteUrl?: string
  uploadTime: Date
  status: 'active' | 'inactive' | 'failed'
}

export interface ImageTagRecord {
  id?: number
  name: string
  color?: string
  imageCount: number
  createdTime: Date
}

class ImageDataService {
  // 保存图片信息
  async saveImage(imageData: Omit<ImageRecord, 'id'>): Promise<number> {
    try {
      return await db.transaction(
        'rw',
        [db.images, db.image_urls, db.image_tags, db.image_tag_relations],
        async () => {
          // 1. 保存图片基本信息
          const imageId = await db.images.add({
            name: imageData.name,
            original_name: imageData.originalName,
            size: imageData.size,
            type: imageData.type,
            upload_time: imageData.uploadTime,
            width: imageData.width,
            height: imageData.height,
            description: imageData.description,
          })

          // 2. 保存图片URL信息
          for (const urlData of imageData.urls) {
            await db.image_urls.add({
              image_id: imageId,
              url: urlData.url,
              host_id: urlData.hostId,
              host_name: urlData.hostName,
              delete_url: urlData.deleteUrl,
              upload_time: urlData.uploadTime,
              status: urlData.status,
            })
          }

          // 3. 处理标签
          if (imageData.tags && imageData.tags.length > 0) {
            for (const tagName of imageData.tags) {
              // 查找或创建标签
              let tag = await db.image_tags.where('name').equals(tagName).first()
              if (!tag) {
                const tagId = await db.image_tags.add({
                  name: tagName,
                  color: this.generateTagColor(tagName),
                  image_count: 0,
                  created_time: new Date(),
                })
                tag = { id: tagId, name: tagName, image_count: 0, created_time: new Date() }
              }

              // 创建标签关系
              await db.image_tag_relations.add({
                tag_id: tag.id!,
                image_id: imageId,
              })

              // 更新标签使用计数
              await db.image_tags.update(tag.id!, { image_count: tag.image_count + 1 })
            }
          }

          return imageId
        },
      )
    } catch (error) {
      console.error('保存图片失败:', error)
      throw error
    }
  }

  // 获取图片列表
  async getImages(options?: {
    limit?: number
    offset?: number
    sortBy?: 'upload_time' | 'name' | 'size'
    sortOrder?: 'asc' | 'desc'
    tags?: string[]
  }): Promise<ImageRecord[]> {
    try {
      let query = db.images.orderBy(options?.sortBy || 'upload_time')

      if (options?.sortOrder === 'asc') {
        query = query.reverse()
      }

      if (options?.offset) {
        query = query.offset(options.offset)
      }

      if (options?.limit) {
        query = query.limit(options.limit)
      }

      const images = await query.toArray()
      const result: ImageRecord[] = []

      for (const image of images) {
        // 获取图片的URL信息
        const urls = await db.image_urls.where('image_id').equals(image.id!).toArray()

        // 获取图片的标签信息
        const tagRelations = await db.image_tag_relations
          .where('image_id')
          .equals(image.id!)
          .toArray()
        const tagIds = tagRelations.map((rel) => rel.tag_id)
        const tags =
          tagIds.length > 0 ? await db.image_tags.where('id').anyOf(tagIds).toArray() : []
        const tagNames = tags.map((tag) => tag.name)

        // 如果指定了标签过滤，检查是否匹配
        if (options?.tags && options.tags.length > 0) {
          const hasMatchingTag = options.tags.some((filterTag) => tagNames.includes(filterTag))
          if (!hasMatchingTag) {
            continue
          }
        }

        const imageRecord: ImageRecord = {
          id: image.id,
          name: image.name,
          originalName: image.original_name,
          size: image.size,
          type: image.type,
          uploadTime: image.upload_time,
          width: image.width,
          height: image.height,
          description: image.description,
          urls: urls.map((url) => ({
            id: url.id,
            hostId: url.host_id,
            hostName: url.host_name,
            url: url.url,
            deleteUrl: url.delete_url,
            uploadTime: url.upload_time,
            status: url.status,
          })),
          tags: tagNames,
        }

        result.push(imageRecord)
      }

      return result
    } catch (error) {
      console.error('获取图片列表失败:', error)
      return []
    }
  }

  // 获取单个图片
  async getImage(id: number): Promise<ImageRecord | null> {
    try {
      const image = await db.images.get(id)
      if (!image) return null

      const urls = await db.image_urls.where('image_id').equals(id).toArray()
      const tagRelations = await db.image_tag_relations.where('image_id').equals(id).toArray()
      const tagIds = tagRelations.map((rel) => rel.tag_id)
      const tags = tagIds.length > 0 ? await db.image_tags.where('id').anyOf(tagIds).toArray() : []

      return {
        id: image.id,
        name: image.name,
        originalName: image.original_name,
        size: image.size,
        type: image.type,
        uploadTime: image.upload_time,
        width: image.width,
        height: image.height,
        description: image.description,
        urls: urls.map((url) => ({
          hostId: url.host_id,
          hostName: url.host_name,
          url: url.url,
          deleteUrl: url.delete_url,
          uploadTime: url.upload_time,
          status: url.status,
        })),
        tags: tags.map((tag) => tag.name),
      }
    } catch (error) {
      console.error('获取图片失败:', error)
      return null
    }
  }

  // 删除图片
  async deleteImage(id: number): Promise<void> {
    try {
      await db.transaction(
        'rw',
        [db.images, db.image_urls, db.image_tags, db.image_tag_relations],
        async () => {
          // 获取图片的标签关系
          const tagRelations = await db.image_tag_relations.where('image_id').equals(id).toArray()

          // 删除标签关系并更新标签计数
          for (const relation of tagRelations) {
            await db.image_tag_relations.delete([relation.tag_id, relation.image_id])

            const tag = await db.image_tags.get(relation.tag_id)
            if (tag && tag.image_count > 0) {
              await db.image_tags.update(relation.tag_id, { image_count: tag.image_count - 1 })
            }
          }

          // 删除图片URL
          await db.image_urls.where('image_id').equals(id).delete()

          // 删除图片记录
          await db.images.delete(id)
        },
      )
    } catch (error) {
      console.error('删除图片失败:', error)
      throw error
    }
  }

  // 获取所有标签
  async getAllTags(): Promise<ImageTagRecord[]> {
    try {
      const tags = await db.image_tags.orderBy('name').toArray()
      const mappedTags = tags
        .filter((tag) => tag && tag.name) // 过滤掉无效数据
        .map((tag) => ({
          id: tag.id,
          name: String(tag.name), // 确保name是字符串
          color: tag.color || '#3B82F6', // 提供默认颜色
          imageCount: tag.image_count || 0, // 提供默认值
          createdTime: tag.created_time || new Date(), // 提供默认时间
        }))
      return mappedTags
    } catch (error) {
      console.error('获取标签失败:', error)
      return []
    }
  }

  // 获取统计信息
  async getStatistics(): Promise<{
    imageCount: number
    tagCount: number
    totalSize: number
    hostCount: number
  }> {
    try {
      const imageCount = await db.images.count()
      const tagCount = await db.image_tags.count()

      const images = await db.images.toArray()
      const totalSize = images.reduce((sum, img) => sum + img.size, 0)

      const urls = await db.image_urls.toArray()
      const hostIds = new Set(urls.map((url) => url.host_id))
      const hostCount = hostIds.size

      return {
        imageCount,
        tagCount,
        totalSize,
        hostCount,
      }
    } catch (error) {
      console.error('获取统计信息失败:', error)
      return {
        imageCount: 0,
        tagCount: 0,
        totalSize: 0,
        hostCount: 0,
      }
    }
  }

  // 添加图片URL
  async addImageUrl(imageId: number, urlRecord: Omit<ImageUrlRecord, 'id'>): Promise<number> {
    try {
      return await db.transaction('rw', [db.image_urls], async () => {
        const urlId = await db.image_urls.add({
          image_id: imageId,
          host_id: urlRecord.hostId,
          host_name: urlRecord.hostName,
          url: urlRecord.url,
          delete_url: urlRecord.deleteUrl,
          upload_time: urlRecord.uploadTime,
          status: urlRecord.status,
        })
        return urlId as number
      })
    } catch (error) {
      console.error('添加图片URL失败:', error)
      throw error
    }
  }

  // 删除图片的单个URL
  async deleteImageUrl(imageId: number, urlId: number): Promise<void> {
    try {
      await db.transaction('rw', [db.image_urls], async () => {
        await db.image_urls.delete(urlId)
      })
    } catch (error) {
      console.error('删除图片URL失败:', error)
      throw error
    }
  }

  // 更新图片URL状态
  async updateImageUrlStatus(
    urlId: number,
    status: 'active' | 'inactive' | 'failed',
  ): Promise<void> {
    try {
      await db.image_urls.update(urlId, { status })
    } catch (error) {
      console.error('更新图片URL状态失败:', error)
      throw error
    }
  }

  // 添加图片标签
  async addImageTag(name: string, color?: string): Promise<number> {
    try {
      // 检查标签是否已存在
      const existingTag = await db.image_tags.where('name').equals(name).first()
      if (existingTag) {
        throw new Error('标签名称已存在')
      }

      const tagId = await db.image_tags.add({
        name,
        color: color || this.generateTagColor(name),
        image_count: 0,
        created_time: new Date(),
      })

      return tagId as number
    } catch (error) {
      console.error('添加图片标签失败:', error)
      throw error
    }
  }

  // 更新图片标签
  async updateImageTag(id: number, data: { name?: string; color?: string }): Promise<void> {
    try {
      // 如果更新名称，检查是否与其他标签重复
      if (data.name) {
        const existingTag = await db.image_tags.where('name').equals(data.name).first()
        if (existingTag && existingTag.id !== id) {
          throw new Error('标签名称已存在')
        }
      }

      await db.image_tags.update(id, data)
    } catch (error) {
      console.error('更新图片标签失败:', error)
      throw error
    }
  }

  // 删除图片标签
  async deleteImageTag(id: number): Promise<void> {
    try {
      await db.transaction('rw', [db.image_tags, db.image_tag_relations], async () => {
        // 删除标签关系
        await db.image_tag_relations.where('tag_id').equals(id).delete()

        // 删除标签
        await db.image_tags.delete(id)
      })
    } catch (error) {
      console.error('删除图片标签失败:', error)
      throw error
    }
  }

  // 更新图片信息
  async updateImage(id: string, data: { originalName?: string }): Promise<void> {
    try {
      await db.images.update(id, data)
    } catch (error) {
      console.error('更新图片信息失败:', error)
      throw error
    }
  }

  // 更新图片标签
  async updateImageTags(imageId: string, tags: string[]): Promise<void> {
    try {
      await db.transaction('rw', [db.image_tags, db.image_tag_relations], async () => {
        // 删除现有的标签关系
        await db.image_tag_relations.where('image_id').equals(imageId).delete()

        // 为每个标签创建或获取标签记录，并建立关系
        for (const tagName of tags) {
          // 查找或创建标签
          let tag = await db.image_tags.where('name').equals(tagName).first()

          if (!tag) {
            // 创建新标签
            const tagId = await db.image_tags.add({
              name: tagName,
              color: this.generateTagColor(tagName),
              image_count: 0,
              created_time: new Date(),
            })
            tag = { id: tagId, name: tagName }
          }

          // 创建标签关系
          await db.image_tag_relations.add({
            tag_id: tag.id!,
            image_id: imageId,
          })
        }

        // 更新所有标签的图片计数
        await this.updateTagCounts()
      })
    } catch (error) {
      console.error('更新图片标签失败:', error)
      throw error
    }
  }

  // 更新标签的图片计数
  private async updateTagCounts(): Promise<void> {
    try {
      const tags = await db.image_tags.toArray()

      for (const tag of tags) {
        const count = await db.image_tag_relations.where('tag_id').equals(tag.id!).count()
        await db.image_tags.update(tag.id!, { image_count: count })
      }
    } catch (error) {
      console.error('更新标签计数失败:', error)
    }
  }

  // 获取标签统计信息
  async getImageTagStats(): Promise<{
    total: number
    used: number
    unused: number
    mostUsed: ImageTagRecord | null
  }> {
    try {
      const tags = await this.getAllTags()
      const total = tags.length
      const used = tags.filter((tag) => tag.imageCount > 0).length
      const unused = total - used
      const mostUsed = tags.reduce(
        (prev, current) => (prev.imageCount > current.imageCount ? prev : current),
        tags[0] || null,
      )

      return { total, used, unused, mostUsed }
    } catch (error) {
      console.error('获取标签统计失败:', error)
      return { total: 0, used: 0, unused: 0, mostUsed: null }
    }
  }

  // 生成标签颜色
  private generateTagColor(tagName: string): string {
    const colors = [
      '#EF4444',
      '#F97316',
      '#F59E0B',
      '#EAB308',
      '#84CC16',
      '#22C55E',
      '#10B981',
      '#14B8A6',
      '#06B6D4',
      '#0EA5E9',
      '#3B82F6',
      '#6366F1',
      '#8B5CF6',
      '#A855F7',
      '#D946EF',
      '#EC4899',
      '#F43F5E',
    ]

    let hash = 0
    for (let i = 0; i < tagName.length; i++) {
      hash = tagName.charCodeAt(i) + ((hash << 5) - hash)
    }

    return colors[Math.abs(hash) % colors.length]
  }
}

// 创建单例实例
export const imageDataService = new ImageDataService()
