<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-6xl mx-auto px-4">
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-8">Ant Design Vue 组件演示</h1>
        
        <div class="space-y-12">
          <!-- 下拉框演示 -->
          <section>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">下拉框 (Select)</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <!-- 基础选择框 -->
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">基础选择框</label>
                <a-select
                  v-model:value="basicSelect"
                  placeholder="请选择城市"
                  style="width: 100%"
                  :options="cityOptions"
                />
                <p class="text-xs text-gray-500">选中值: {{ basicSelect || '未选择' }}</p>
              </div>

              <!-- 可搜索选择框 -->
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">可搜索选择框</label>
                <a-select
                  v-model:value="searchSelect"
                  show-search
                  placeholder="搜索并选择"
                  style="width: 100%"
                  :filter-option="filterOption"
                  :options="languageOptions"
                />
                <p class="text-xs text-gray-500">选中值: {{ searchSelect || '未选择' }}</p>
              </div>

              <!-- 多选选择框 -->
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">多选选择框</label>
                <a-select
                  v-model:value="multiSelect"
                  mode="multiple"
                  placeholder="选择多个选项"
                  style="width: 100%"
                  :options="colorOptions"
                />
                <p class="text-xs text-gray-500">选中值: {{ multiSelect.join(', ') || '未选择' }}</p>
              </div>

              <!-- 带清空功能 -->
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">可清空选择框</label>
                <a-select
                  v-model:value="clearableSelect"
                  placeholder="可清空的选择框"
                  style="width: 100%"
                  allow-clear
                  :options="frameworkOptions"
                />
                <p class="text-xs text-gray-500">选中值: {{ clearableSelect || '未选择' }}</p>
              </div>

              <!-- 禁用状态 -->
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">禁用状态</label>
                <a-select
                  v-model:value="disabledSelect"
                  placeholder="禁用状态"
                  style="width: 100%"
                  disabled
                  :options="cityOptions"
                />
                <p class="text-xs text-gray-500">选中值: {{ disabledSelect || '未选择' }}</p>
              </div>

              <!-- 加载状态 -->
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">加载状态</label>
                <a-select
                  v-model:value="loadingSelect"
                  placeholder="加载中..."
                  style="width: 100%"
                  loading
                  :options="cityOptions"
                />
                <p class="text-xs text-gray-500">选中值: {{ loadingSelect || '未选择' }}</p>
              </div>
            </div>
          </section>

          <!-- 输入框演示 -->
          <section>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">输入框 (Input)</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <!-- 基础输入框 -->
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">基础输入框</label>
                <a-input v-model:value="basicInput" placeholder="请输入内容" />
              </div>

              <!-- 带前缀图标 -->
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">带前缀图标</label>
                <a-input v-model:value="prefixInput" placeholder="用户名">
                  <template #prefix>
                    <UserOutlined style="color: rgba(0,0,0,.25)" />
                  </template>
                </a-input>
              </div>

              <!-- 密码输入框 -->
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">密码输入框</label>
                <a-input-password v-model:value="passwordInput" placeholder="请输入密码" />
              </div>

              <!-- 文本域 -->
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">文本域</label>
                <a-textarea v-model:value="textareaInput" placeholder="请输入多行文本" :rows="3" />
              </div>

              <!-- 搜索框 -->
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">搜索框</label>
                <a-input-search
                  v-model:value="searchInput"
                  placeholder="搜索内容"
                  enter-button="搜索"
                  @search="onSearch"
                />
              </div>

              <!-- 数字输入框 -->
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">数字输入框</label>
                <a-input-number
                  v-model:value="numberInput"
                  :min="1"
                  :max="100"
                  placeholder="请输入数字"
                  style="width: 100%"
                />
              </div>
            </div>
          </section>

          <!-- 按钮演示 -->
          <section>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">按钮 (Button)</h2>
            <div class="space-y-4">
              <div class="flex flex-wrap gap-4">
                <a-button type="primary">主要按钮</a-button>
                <a-button>默认按钮</a-button>
                <a-button type="dashed">虚线按钮</a-button>
                <a-button type="text">文本按钮</a-button>
                <a-button type="link">链接按钮</a-button>
              </div>
              
              <div class="flex flex-wrap gap-4">
                <a-button type="primary" size="large">大按钮</a-button>
                <a-button type="primary">中按钮</a-button>
                <a-button type="primary" size="small">小按钮</a-button>
              </div>
              
              <div class="flex flex-wrap gap-4">
                <a-button type="primary" :loading="loading" @click="handleLoading">
                  {{ loading ? '加载中' : '点击加载' }}
                </a-button>
                <a-button type="primary" disabled>禁用按钮</a-button>
                <a-button type="primary" danger>危险按钮</a-button>
              </div>
            </div>
          </section>

          <!-- 表单演示 -->
          <section>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">表单 (Form)</h2>
            <div class="max-w-md">
              <a-form
                :model="formData"
                :rules="rules"
                @finish="onFinish"
                @finishFailed="onFinishFailed"
                layout="vertical"
              >
                <a-form-item label="用户名" name="username">
                  <a-input v-model:value="formData.username" placeholder="请输入用户名" />
                </a-form-item>

                <a-form-item label="邮箱" name="email">
                  <a-input v-model:value="formData.email" placeholder="请输入邮箱" />
                </a-form-item>

                <a-form-item label="城市" name="city">
                  <a-select
                    v-model:value="formData.city"
                    placeholder="请选择城市"
                    :options="cityOptions"
                  />
                </a-form-item>

                <a-form-item label="描述" name="description">
                  <a-textarea v-model:value="formData.description" placeholder="请输入描述" :rows="3" />
                </a-form-item>

                <a-form-item>
                  <a-button type="primary" html-type="submit">提交</a-button>
                  <a-button style="margin-left: 10px;" @click="resetForm">重置</a-button>
                </a-form-item>
              </a-form>
            </div>
          </section>

          <!-- 操作按钮 -->
          <section>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">操作</h2>
            <div class="flex flex-wrap gap-4">
              <a-button @click="clearAllValues">清空所有值</a-button>
              <a-button type="primary" @click="setRandomValues">设置随机值</a-button>
              <a-button type="dashed" @click="showMessage">显示消息</a-button>
            </div>
          </section>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { UserOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 基础数据
const basicSelect = ref('')
const searchSelect = ref('')
const multiSelect = ref([])
const clearableSelect = ref('')
const disabledSelect = ref('beijing')
const loadingSelect = ref('')

// 输入框数据
const basicInput = ref('')
const prefixInput = ref('')
const passwordInput = ref('')
const textareaInput = ref('')
const searchInput = ref('')
const numberInput = ref(1)

// 按钮状态
const loading = ref(false)

// 表单数据
const formData = reactive({
  username: '',
  email: '',
  city: '',
  description: ''
})

// 选项数据
const cityOptions = [
  { value: 'beijing', label: '北京' },
  { value: 'shanghai', label: '上海' },
  { value: 'guangzhou', label: '广州' },
  { value: 'shenzhen', label: '深圳' },
  { value: 'hangzhou', label: '杭州' },
  { value: 'nanjing', label: '南京' },
  { value: 'wuhan', label: '武汉' },
  { value: 'chengdu', label: '成都' }
]

const languageOptions = [
  { value: 'javascript', label: 'JavaScript' },
  { value: 'typescript', label: 'TypeScript' },
  { value: 'python', label: 'Python' },
  { value: 'java', label: 'Java' },
  { value: 'csharp', label: 'C#' },
  { value: 'cpp', label: 'C++' },
  { value: 'go', label: 'Go' },
  { value: 'rust', label: 'Rust' }
]

const colorOptions = [
  { value: 'red', label: '红色' },
  { value: 'blue', label: '蓝色' },
  { value: 'green', label: '绿色' },
  { value: 'yellow', label: '黄色' },
  { value: 'purple', label: '紫色' }
]

const frameworkOptions = [
  { value: 'vue', label: 'Vue.js' },
  { value: 'react', label: 'React' },
  { value: 'angular', label: 'Angular' },
  { value: 'svelte', label: 'Svelte' }
]

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 15, message: '用户名长度应为3-15个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  city: [
    { required: true, message: '请选择城市', trigger: 'change' }
  ]
}

// 事件处理
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const onSearch = (value: string) => {
  console.log('搜索:', value)
  message.info(`搜索: ${value}`)
}

const handleLoading = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('加载完成!')
  }, 2000)
}

const onFinish = (values: any) => {
  console.log('表单提交:', values)
  message.success('表单提交成功!')
}

const onFinishFailed = (errorInfo: any) => {
  console.log('表单验证失败:', errorInfo)
  message.error('表单验证失败，请检查输入!')
}

const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = ''
  })
  message.info('表单已重置')
}

const clearAllValues = () => {
  basicSelect.value = ''
  searchSelect.value = ''
  multiSelect.value = []
  clearableSelect.value = ''
  loadingSelect.value = ''
  basicInput.value = ''
  prefixInput.value = ''
  passwordInput.value = ''
  textareaInput.value = ''
  searchInput.value = ''
  numberInput.value = 1
  resetForm()
  message.info('所有值已清空')
}

const setRandomValues = () => {
  const getRandomOption = (options: any[]) => {
    return options[Math.floor(Math.random() * options.length)].value
  }
  
  basicSelect.value = getRandomOption(cityOptions)
  searchSelect.value = getRandomOption(languageOptions)
  multiSelect.value = [getRandomOption(colorOptions), getRandomOption(colorOptions)]
  clearableSelect.value = getRandomOption(frameworkOptions)
  basicInput.value = '随机文本 ' + Math.random().toString(36).substring(7)
  prefixInput.value = 'user_' + Math.random().toString(36).substring(7)
  textareaInput.value = '这是一段随机生成的多行文本内容。\n包含换行符和多行内容。'
  numberInput.value = Math.floor(Math.random() * 100) + 1
  
  formData.username = 'user_' + Math.random().toString(36).substring(7)
  formData.email = '<EMAIL>'
  formData.city = getRandomOption(cityOptions)
  formData.description = '随机生成的描述内容'
  
  message.success('随机值设置完成!')
}

const showMessage = () => {
  message.info('这是一条信息提示')
}
</script>
