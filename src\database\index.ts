import Dexie, { type Table } from 'dexie'
import type { Resource, Category, Tag, ResourceTag } from '@/types'

// 图片相关类型定义
export interface Image {
  id?: number
  name: string
  original_name: string
  size: number
  type: string
  upload_time: Date
  width?: number
  height?: number
  description?: string
}

export interface ImageUrl {
  id?: number
  image_id: number
  url: string
  host_id: string
  host_name: string
  delete_url?: string
  upload_time: Date
  status: 'active' | 'inactive' | 'failed'
}

export interface ImageTag {
  id?: number
  name: string
  color?: string
  image_count: number
  created_time: Date
}

export interface ImageTagRelation {
  tag_id: number
  image_id: number
}

// AI配置相关类型定义
export interface AiProvider {
  id?: number
  name: string
  display_name: string
  type: 'builtin' | 'custom'
  base_url?: string
  color?: string
  icon?: string
  description?: string
  is_active: boolean
  sort_order: number
  created_at: Date
  updated_at: Date
}

export interface AiModel {
  id?: number
  provider_id: number
  name: string
  display_name: string
  type: 'builtin' | 'custom'
  max_tokens?: number
  description?: string
  is_active: boolean
  sort_order: number
  created_at: Date
  updated_at: Date
}

export interface AiConfig {
  id?: number
  name: string
  provider_id: number
  model_id?: number
  api_key?: string
  base_url?: string
  temperature: number
  max_tokens: number
  timeout: number
  system_prompt?: string
  custom_headers?: string // JSON格式存储
  custom_params?: string // JSON格式存储
  is_default: boolean
  is_enabled: boolean
  created_at: Date
  updated_at: Date
}

export class KnowledgeDatabase extends Dexie {
  resources!: Table<Resource>
  categories!: Table<Category>
  tags!: Table<Tag>
  resource_tags!: Table<ResourceTag>

  // 图片相关表
  images!: Table<Image>
  image_urls!: Table<ImageUrl>
  image_tags!: Table<ImageTag>
  image_tag_relations!: Table<ImageTagRelation>

  // AI配置相关表
  ai_providers!: Table<AiProvider>
  ai_models!: Table<AiModel>
  ai_configs!: Table<AiConfig>

  constructor() {
    super('KnowledgeDatabase')

    this.version(1).stores({
      resources: '++id, url, title, category_id, view_count, created_at, updated_at',
      categories: '++id, name, parent_id, sort_order, created_at',
      tags: '++id, name, color, resource_count, created_at',
      resource_tags: '[resource_id+tag_id], resource_id, tag_id',
    })

    // 版本2：修复排序字段索引
    this.version(2).stores({
      resources: '++id, url, title, category_id, view_count, created_at, updated_at',
      categories: '++id, name, parent_id, sort_order, created_at',
      tags: '++id, name, color, resource_count, created_at',
      resource_tags: '[resource_id+tag_id], resource_id, tag_id',
    })

    // 版本3：添加图片管理功能
    this.version(3).stores({
      resources: '++id, url, title, category_id, view_count, created_at, updated_at',
      categories: '++id, name, parent_id, sort_order, created_at',
      tags: '++id, name, color, resource_count, created_at',
      resource_tags: '[resource_id+tag_id], resource_id, tag_id',
      // 图片相关表
      images: '++id, name, original_name, size, type, upload_time, width, height',
      image_urls: '++id, image_id, url, host_id, host_name, upload_time, status',
      image_tags: '++id, name, color, image_count, created_time',
      image_tag_relations: '[tag_id+image_id], tag_id, image_id',
    })

    // 版本4：添加AI配置管理功能
    this.version(4).stores({
      resources: '++id, url, title, category_id, view_count, created_at, updated_at',
      categories: '++id, name, parent_id, sort_order, created_at',
      tags: '++id, name, color, resource_count, created_at',
      resource_tags: '[resource_id+tag_id], resource_id, tag_id',
      // 图片相关表
      images: '++id, name, original_name, size, type, upload_time, width, height',
      image_urls: '++id, image_id, url, host_id, host_name, upload_time, status',
      image_tags: '++id, name, color, image_count, created_time',
      image_tag_relations: '[tag_id+image_id], tag_id, image_id',
      // AI配置相关表
      ai_providers: '++id, name, display_name, type, is_active, sort_order, created_at, updated_at',
      ai_models:
        '++id, provider_id, name, display_name, type, is_active, sort_order, created_at, updated_at',
      ai_configs:
        '++id, name, provider_id, model_id, is_default, is_enabled, created_at, updated_at',
    })

    // 版本42：修复版本冲突问题
    this.version(42).stores({
      resources: '++id, url, title, category_id, view_count, created_at, updated_at',
      categories: '++id, name, parent_id, sort_order, created_at',
      tags: '++id, name, color, resource_count, created_at',
      resource_tags: '[resource_id+tag_id], resource_id, tag_id',
      // 图片相关表
      images: '++id, name, original_name, size, type, upload_time, width, height',
      image_urls: '++id, image_id, url, host_id, host_name, upload_time, status',
      image_tags: '++id, name, color, image_count, created_time',
      image_tag_relations: '[tag_id+image_id], tag_id, image_id',
      // AI配置相关表
      ai_providers: '++id, name, display_name, type, is_active, sort_order, created_at, updated_at',
      ai_models:
        '++id, provider_id, name, display_name, type, is_active, sort_order, created_at, updated_at',
      ai_configs:
        '++id, name, provider_id, model_id, is_default, is_enabled, created_at, updated_at',
    })

    // 添加钩子函数，自动设置时间戳
    this.resources.hook('creating', function (primKey, obj, trans) {
      obj.created_at = new Date()
      obj.updated_at = new Date()
      obj.view_count = obj.view_count || 0
    })

    this.resources.hook('updating', function (modifications, primKey, obj, trans) {
      modifications.updated_at = new Date()
    })

    this.categories.hook('creating', function (primKey, obj, trans) {
      obj.created_at = new Date()
      obj.resource_count = obj.resource_count || 0
    })

    this.tags.hook('creating', function (primKey, obj, trans) {
      obj.created_at = new Date()
      obj.resource_count = obj.resource_count || 0
    })

    // 图片相关钩子函数
    this.images.hook('creating', function (primKey, obj, trans) {
      obj.upload_time = obj.upload_time || new Date()
    })

    this.image_urls.hook('creating', function (primKey, obj, trans) {
      obj.upload_time = obj.upload_time || new Date()
      obj.status = obj.status || 'active'
    })

    this.image_tags.hook('creating', function (primKey, obj, trans) {
      obj.created_time = obj.created_time || new Date()
      obj.image_count = obj.image_count || 0
    })

    // AI配置相关钩子函数
    this.ai_providers.hook('creating', function (primKey, obj, trans) {
      obj.created_at = obj.created_at || new Date()
      obj.updated_at = obj.updated_at || new Date()
      obj.is_active = obj.is_active !== undefined ? obj.is_active : true
      obj.sort_order = obj.sort_order || 0
    })

    this.ai_providers.hook('updating', function (modifications, primKey, obj, trans) {
      modifications.updated_at = new Date()
    })

    this.ai_models.hook('creating', function (primKey, obj, trans) {
      obj.created_at = obj.created_at || new Date()
      obj.updated_at = obj.updated_at || new Date()
      obj.is_active = obj.is_active !== undefined ? obj.is_active : true
      obj.sort_order = obj.sort_order || 0
    })

    this.ai_models.hook('updating', function (modifications, primKey, obj, trans) {
      modifications.updated_at = new Date()
    })

    this.ai_configs.hook('creating', function (primKey, obj, trans) {
      obj.created_at = obj.created_at || new Date()
      obj.updated_at = obj.updated_at || new Date()
      obj.is_default = obj.is_default !== undefined ? obj.is_default : false
      obj.is_enabled = obj.is_enabled !== undefined ? obj.is_enabled : true
      obj.temperature = obj.temperature !== undefined ? obj.temperature : 0.7
      obj.max_tokens = obj.max_tokens || 4000
      obj.timeout = obj.timeout || 30000
    })

    this.ai_configs.hook('updating', function (modifications, primKey, obj, trans) {
      modifications.updated_at = new Date()
    })
  }

  // 初始化默认数据
  async initializeDefaultData() {
    const categoryCount = await this.categories.count()
    if (categoryCount === 0) {
      // 创建默认分类
      const categories = await this.categories.bulkAdd([
        {
          name: '前端开发',
          parent_id: 0,
          resource_count: 0,
          sort_order: 1,
          created_at: new Date(),
        },
        {
          name: '后端开发',
          parent_id: 0,
          resource_count: 0,
          sort_order: 2,
          created_at: new Date(),
        },
        {
          name: '数据库',
          parent_id: 0,
          resource_count: 0,
          sort_order: 3,
          created_at: new Date(),
        },
        {
          name: '工具软件',
          parent_id: 0,
          resource_count: 0,
          sort_order: 4,
          created_at: new Date(),
        },
        {
          name: '其他',
          parent_id: 0,
          resource_count: 0,
          sort_order: 5,
          created_at: new Date(),
        },
      ])

      // 添加子分类
      await this.categories.bulkAdd([
        // 前端开发子分类
        {
          name: 'Vue.js',
          parent_id: 1, // 前端开发的ID
          resource_count: 0,
          sort_order: 1,
          created_at: new Date(),
        },
        {
          name: 'React',
          parent_id: 1,
          resource_count: 0,
          sort_order: 2,
          created_at: new Date(),
        },
        {
          name: 'Angular',
          parent_id: 1,
          resource_count: 0,
          sort_order: 3,
          created_at: new Date(),
        },
        // 后端开发子分类
        {
          name: 'Node.js',
          parent_id: 2, // 后端开发的ID
          resource_count: 0,
          sort_order: 1,
          created_at: new Date(),
        },
        {
          name: 'Python',
          parent_id: 2,
          resource_count: 0,
          sort_order: 2,
          created_at: new Date(),
        },
        {
          name: 'Java',
          parent_id: 2,
          resource_count: 0,
          sort_order: 3,
          created_at: new Date(),
        },
        // 数据库子分类
        {
          name: 'MySQL',
          parent_id: 3, // 数据库的ID
          resource_count: 0,
          sort_order: 1,
          created_at: new Date(),
        },
        {
          name: 'MongoDB',
          parent_id: 3,
          resource_count: 0,
          sort_order: 2,
          created_at: new Date(),
        },
        {
          name: 'Redis',
          parent_id: 3,
          resource_count: 0,
          sort_order: 3,
          created_at: new Date(),
        },
      ])
    }

    const tagCount = await this.tags.count()
    if (tagCount === 0) {
      // 创建默认标签
      await this.tags.bulkAdd([
        { name: 'Vue.js', color: '#4FC08D', resource_count: 0, created_at: new Date() },
        { name: 'React', color: '#61DAFB', resource_count: 0, created_at: new Date() },
        { name: 'JavaScript', color: '#F7DF1E', resource_count: 0, created_at: new Date() },
        { name: 'TypeScript', color: '#3178C6', resource_count: 0, created_at: new Date() },
        { name: 'Node.js', color: '#339933', resource_count: 0, created_at: new Date() },
        { name: 'Python', color: '#3776AB', resource_count: 0, created_at: new Date() },
        { name: 'Java', color: '#ED8B00', resource_count: 0, created_at: new Date() },
        { name: 'MySQL', color: '#4479A1', resource_count: 0, created_at: new Date() },
        { name: 'MongoDB', color: '#47A248', resource_count: 0, created_at: new Date() },
        { name: '工具', color: '#6B7280', resource_count: 0, created_at: new Date() },
      ])
    }

    // 初始化AI默认数据
    await this.initializeAiDefaultData()
  }

  // 初始化AI默认数据
  async initializeAiDefaultData() {
    const providerCount = await this.ai_providers.count()
    if (providerCount === 0) {
      // 创建默认AI服务商
      const providers = await this.ai_providers.bulkAdd([
        {
          name: 'openai',
          display_name: 'OpenAI',
          type: 'builtin',
          base_url: 'https://api.openai.com/v1',
          color: '#10a37f',
          icon: 'openai-icon',
          description: 'OpenAI GPT系列模型',
          is_active: true,
          sort_order: 1,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          name: 'claude',
          display_name: 'Claude (Anthropic)',
          type: 'builtin',
          base_url: 'https://api.anthropic.com/v1',
          color: '#cc785c',
          icon: 'claude-icon',
          description: 'Anthropic Claude系列模型',
          is_active: true,
          sort_order: 2,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          name: 'gemini',
          display_name: 'Google Gemini',
          type: 'builtin',
          base_url: 'https://generativelanguage.googleapis.com/v1beta',
          color: '#4285f4',
          icon: 'gemini-icon',
          description: 'Google Gemini系列模型',
          is_active: true,
          sort_order: 3,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          name: 'baidu',
          display_name: '百度文心一言',
          type: 'builtin',
          base_url: 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop',
          color: '#2932e1',
          icon: 'baidu-icon',
          description: '百度文心一言系列模型',
          is_active: true,
          sort_order: 4,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          name: 'alibaba',
          display_name: '阿里通义千问',
          type: 'builtin',
          base_url: 'https://dashscope.aliyuncs.com/api/v1',
          color: '#ff6a00',
          icon: 'alibaba-icon',
          description: '阿里通义千问系列模型',
          is_active: true,
          sort_order: 5,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          name: 'tencent',
          display_name: '腾讯混元',
          type: 'builtin',
          base_url: 'https://hunyuan.tencentcloudapi.com',
          color: '#00a971',
          icon: 'tencent-icon',
          description: '腾讯混元系列模型',
          is_active: true,
          sort_order: 6,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          name: 'zhipu',
          display_name: '智谱GLM',
          type: 'builtin',
          base_url: 'https://open.bigmodel.cn/api/paas/v4',
          color: '#1890ff',
          icon: 'zhipu-icon',
          description: '智谱GLM系列模型',
          is_active: true,
          sort_order: 7,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          name: 'moonshot',
          display_name: '月之暗面Kimi',
          type: 'builtin',
          base_url: 'https://api.moonshot.cn/v1',
          color: '#8b5cf6',
          icon: 'moonshot-icon',
          description: '月之暗面Kimi系列模型',
          is_active: true,
          sort_order: 8,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          name: 'bytedance',
          display_name: '字节豆包',
          type: 'builtin',
          base_url: 'https://ark.cn-beijing.volces.com/api/v3',
          color: '#1677ff',
          icon: 'bytedance-icon',
          description: '字节豆包系列模型',
          is_active: true,
          sort_order: 9,
          created_at: new Date(),
          updated_at: new Date(),
        },
      ])

      // 创建默认AI模型
      await this.ai_models.bulkAdd([
        // OpenAI 模型
        {
          provider_id: 1, // openai
          name: 'gpt-4o',
          display_name: 'GPT-4o',
          type: 'builtin',
          max_tokens: 128000,
          description: 'OpenAI最新的多模态模型',
          is_active: true,
          sort_order: 1,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          provider_id: 1, // openai
          name: 'gpt-4o-mini',
          display_name: 'GPT-4o Mini',
          type: 'builtin',
          max_tokens: 128000,
          description: 'OpenAI高性价比模型',
          is_active: true,
          sort_order: 2,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          provider_id: 1, // openai
          name: 'gpt-4-turbo',
          display_name: 'GPT-4 Turbo',
          type: 'builtin',
          max_tokens: 128000,
          description: 'OpenAI GPT-4 Turbo模型',
          is_active: true,
          sort_order: 3,
          created_at: new Date(),
          updated_at: new Date(),
        },
        // Claude 模型
        {
          provider_id: 2, // claude
          name: 'claude-3-5-sonnet-20241022',
          display_name: 'Claude 3.5 Sonnet',
          type: 'builtin',
          max_tokens: 200000,
          description: 'Anthropic最强推理模型',
          is_active: true,
          sort_order: 1,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          provider_id: 2, // claude
          name: 'claude-3-5-haiku-20241022',
          display_name: 'Claude 3.5 Haiku',
          type: 'builtin',
          max_tokens: 200000,
          description: 'Anthropic高速模型',
          is_active: true,
          sort_order: 2,
          created_at: new Date(),
          updated_at: new Date(),
        },
        // Gemini 模型
        {
          provider_id: 3, // gemini
          name: 'gemini-1.5-pro',
          display_name: 'Gemini 1.5 Pro',
          type: 'builtin',
          max_tokens: 2000000,
          description: 'Google Gemini 1.5 Pro模型',
          is_active: true,
          sort_order: 1,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          provider_id: 3, // gemini
          name: 'gemini-1.5-flash',
          display_name: 'Gemini 1.5 Flash',
          type: 'builtin',
          max_tokens: 1000000,
          description: 'Google Gemini 1.5 Flash模型',
          is_active: true,
          sort_order: 2,
          created_at: new Date(),
          updated_at: new Date(),
        },
      ])
    }
  }

  // 清除所有数据并重新初始化
  async resetDatabase() {
    await this.transaction(
      'rw',
      [
        this.resources,
        this.categories,
        this.tags,
        this.resource_tags,
        this.images,
        this.image_urls,
        this.image_tags,
        this.image_tag_relations,
        this.ai_providers,
        this.ai_models,
        this.ai_configs,
      ],
      async () => {
        await this.resources.clear()
        await this.categories.clear()
        await this.tags.clear()
        await this.resource_tags.clear()
        await this.images.clear()
        await this.image_urls.clear()
        await this.image_tags.clear()
        await this.image_tag_relations.clear()
        await this.ai_providers.clear()
        await this.ai_models.clear()
        await this.ai_configs.clear()
      },
    )

    await this.initializeDefaultData()
  }
}

export const db = new KnowledgeDatabase()

// 初始化数据库
db.open()
  .then(() => {
    db.initializeDefaultData()
  })
  .catch((err) => {
    console.error('数据库初始化失败:', err)
  })
