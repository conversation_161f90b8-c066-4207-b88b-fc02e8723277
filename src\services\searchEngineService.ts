import type { SearchEngine, SearchSettings } from '@/types/search'

class SearchEngineService {
  private readonly STORAGE_KEY = 'search-engine-settings'

  // 默认搜索引擎
  private readonly defaultEngines: SearchEngine[] = [
    {
      id: 'google',
      name: 'Google',
      url: 'https://www.google.com/search?q={query}',
      icon: 'i-logos-google'
    },
    {
      id: 'bing',
      name: 'Bing',
      url: 'https://www.bing.com/search?q={query}',
      icon: 'i-logos-microsoft'
    },
    {
      id: 'baidu',
      name: '百度',
      url: 'https://www.baidu.com/s?wd={query}',
      icon: 'i-simple-icons-baidu'
    },
    {
      id: 'duckduckgo',
      name: 'DuckDuckGo',
      url: 'https://duckduckgo.com/?q={query}',
      icon: 'i-simple-icons-duckduckgo'
    },
    {
      id: 'github',
      name: 'GitHub',
      url: 'https://github.com/search?q={query}',
      icon: 'i-logos-github-icon'
    },
    {
      id: 'stackoverflow',
      name: 'Stack Overflow',
      url: 'https://stackoverflow.com/search?q={query}',
      icon: 'i-logos-stackoverflow-icon'
    }
  ]

  // 获取搜索引擎设置
  getSettings(): SearchSettings {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (stored) {
        const settings = JSON.parse(stored) as SearchSettings
        // 确保默认引擎存在
        if (!settings.engines.find(e => e.id === settings.defaultEngine)) {
          settings.defaultEngine = this.defaultEngines[0].id
        }
        return settings
      }
    } catch (error) {
      console.error('Failed to load search engine settings:', error)
    }

    return {
      defaultEngine: this.defaultEngines[0].id,
      engines: [...this.defaultEngines]
    }
  }

  // 保存搜索引擎设置
  saveSettings(settings: SearchSettings): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(settings))
    } catch (error) {
      console.error('Failed to save search engine settings:', error)
    }
  }

  // 获取默认搜索引擎
  getDefaultEngine(): SearchEngine {
    const settings = this.getSettings()
    return settings.engines.find(e => e.id === settings.defaultEngine) || this.defaultEngines[0]
  }

  // 设置默认搜索引擎
  setDefaultEngine(engineId: string): void {
    const settings = this.getSettings()
    if (settings.engines.find(e => e.id === engineId)) {
      settings.defaultEngine = engineId
      this.saveSettings(settings)
    }
  }

  // 添加自定义搜索引擎
  addCustomEngine(engine: Omit<SearchEngine, 'id'>): void {
    const settings = this.getSettings()
    const id = `custom-${Date.now()}`
    const newEngine: SearchEngine = {
      ...engine,
      id
    }
    
    settings.engines.push(newEngine)
    this.saveSettings(settings)
  }

  // 删除自定义搜索引擎
  removeCustomEngine(engineId: string): void {
    if (this.defaultEngines.find(e => e.id === engineId)) {
      // 不能删除默认引擎
      return
    }

    const settings = this.getSettings()
    settings.engines = settings.engines.filter(e => e.id !== engineId)
    
    // 如果删除的是默认引擎，重置为第一个引擎
    if (settings.defaultEngine === engineId) {
      settings.defaultEngine = settings.engines[0]?.id || this.defaultEngines[0].id
    }
    
    this.saveSettings(settings)
  }

  // 执行搜索
  search(query: string, engineId?: string): void {
    const settings = this.getSettings()
    const engine = engineId 
      ? settings.engines.find(e => e.id === engineId)
      : this.getDefaultEngine()
    
    if (!engine) return

    const searchUrl = engine.url.replace('{query}', encodeURIComponent(query))
    window.open(searchUrl, '_blank')
  }

  // 重置为默认设置
  resetToDefaults(): void {
    const defaultSettings: SearchSettings = {
      defaultEngine: this.defaultEngines[0].id,
      engines: [...this.defaultEngines]
    }
    this.saveSettings(defaultSettings)
  }
}

export const searchEngineService = new SearchEngineService()
