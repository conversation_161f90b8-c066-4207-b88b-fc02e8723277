import type { SearchResult, SearchType } from '@/types/search'
import { resourceService } from './resourceService'
import { categoryService } from './categoryService'
import { tagService } from './tagService'

class SearchService {
  // 执行搜索
  async search(query: string, type: SearchType): Promise<SearchResult[]> {
    const trimmedQuery = query.trim().toLowerCase()
    if (!trimmedQuery) return []

    switch (type) {
      case 'global':
        return this.globalSearch(trimmedQuery)
      case 'knowledge':
        return this.knowledgeSearch(trimmedQuery)
      case 'web':
        return [] // 网络搜索不返回结果，直接打开搜索引擎
      default:
        return []
    }
  }

  // 全局搜索
  private async globalSearch(query: string): Promise<SearchResult[]> {
    const results: SearchResult[] = []

    // 搜索功能
    const functionResults = this.searchFunctions(query)
    results.push(...functionResults)

    // 搜索分类
    const categories = await categoryService.searchCategories(query)
    const categoryResults = categories.map((category) => ({
      id: category.id!.toString(),
      type: 'category' as const,
      title: category.name,
      description: `${category.resource_count} 个资源`,
      category: category.parent_id ? '子分类' : '根分类',
    }))
    results.push(...categoryResults)

    // 搜索标签
    const tags = await tagService.searchTags(query)
    const tagResults = tags.map((tag) => ({
      id: tag.id!.toString(),
      type: 'tag' as const,
      title: tag.name,
      description: `${tag.resource_count} 个资源`,
      category: '标签',
    }))
    results.push(...tagResults)

    // 搜索资源
    const resources = await resourceService.searchResources({
      keyword: query,
      searchType: 'all',
    })
    const resourceResults = resources.slice(0, 5).map((resource) => ({
      id: resource.id!.toString(),
      type: 'resource' as const,
      title: resource.title,
      description: resource.description,
      category: resource.category_name || '未分类',
    }))
    results.push(...resourceResults)

    return results.slice(0, 10) // 限制结果数量
  }

  // 知识库搜索
  private async knowledgeSearch(query: string): Promise<SearchResult[]> {
    const resources = await resourceService.searchResources({
      keyword: query,
      searchType: 'all',
    })

    return resources.map((resource) => ({
      id: resource.id!.toString(),
      type: 'resource' as const,
      title: resource.title,
      description: resource.description,
      category: resource.category_name || '未分类',
    }))
  }

  // 搜索功能
  private searchFunctions(query: string): SearchResult[] {
    const functions = [
      {
        id: 'category-management',
        title: '分类管理',
        description: '管理知识库分类',
        keywords: ['分类', '管理', '文件夹', 'category', '目录'],
        action: () => {
          // 使用更直接的方式触发分类管理
          const event = new CustomEvent('search-function-trigger', {
            detail: { type: 'category-management' },
          })
          window.dispatchEvent(event)
        },
      },
      {
        id: 'tag-management',
        title: '标签管理',
        description: '管理知识库标签',
        keywords: ['标签', '管理', 'tag', '标记'],
        action: () => {
          const event = new CustomEvent('search-function-trigger', {
            detail: { type: 'tag-management' },
          })
          window.dispatchEvent(event)
        },
      },
      {
        id: 'search-engine-settings',
        title: '搜索引擎设置',
        description: '配置搜索引擎',
        keywords: ['搜索', '引擎', '设置', 'search', 'engine', '配置'],
        action: () => {
          const event = new CustomEvent('search-function-trigger', {
            detail: { type: 'search-engine-settings' },
          })
          window.dispatchEvent(event)
        },
      },
      {
        id: 'add-resource',
        title: '添加资源',
        description: '添加新的知识库资源',
        keywords: ['添加', '新建', '资源', 'add', 'new', '创建'],
        action: () => {
          const event = new CustomEvent('search-function-trigger', {
            detail: { type: 'add-resource' },
          })
          window.dispatchEvent(event)
        },
      },
      {
        id: 'import-data',
        title: '导入数据',
        description: '从文件导入数据',
        keywords: ['导入', '数据', 'import', '上传'],
        action: () => {
          const event = new CustomEvent('search-function-trigger', {
            detail: { type: 'import-data' },
          })
          window.dispatchEvent(event)
        },
      },
      {
        id: 'export-data',
        title: '导出数据',
        description: '导出知识库数据',
        keywords: ['导出', '数据', 'export', '下载', '备份'],
        action: () => {
          const event = new CustomEvent('search-function-trigger', {
            detail: { type: 'export-data' },
          })
          window.dispatchEvent(event)
        },
      },
      {
        id: 'theme-settings',
        title: '主题设置',
        description: '切换应用主题',
        keywords: ['主题', '设置', '深色', '浅色', 'theme', 'dark', 'light', '外观'],
        action: () => {
          const event = new CustomEvent('search-function-trigger', {
            detail: { type: 'theme-settings' },
          })
          window.dispatchEvent(event)
        },
      },
    ]

    return functions
      .filter(
        (func) =>
          func.title.toLowerCase().includes(query) ||
          func.description.toLowerCase().includes(query) ||
          func.keywords.some((keyword) => keyword.toLowerCase().includes(query)),
      )
      .map((func) => ({
        id: func.id,
        type: 'function' as const,
        title: func.title,
        description: func.description,
        category: '功能',
        action: func.action,
      }))
  }

  // 获取搜索建议
  async getSearchSuggestions(query: string, type: SearchType): Promise<string[]> {
    const trimmedQuery = query.trim().toLowerCase()
    if (!trimmedQuery) return []

    const suggestions: string[] = []

    if (type === 'global' || type === 'knowledge') {
      // 从分类名称获取建议
      const categories = await categoryService.getAllCategories()
      const categoryNames = categories
        .filter((cat) => cat.name.toLowerCase().includes(trimmedQuery))
        .map((cat) => cat.name)
        .slice(0, 3)
      suggestions.push(...categoryNames)

      // 从标签名称获取建议
      const tags = await tagService.getAllTags()
      const tagNames = tags
        .filter((tag) => tag.name.toLowerCase().includes(trimmedQuery))
        .map((tag) => tag.name)
        .slice(0, 3)
      suggestions.push(...tagNames)

      // 从资源标题获取建议
      const resources = await resourceService.searchResources({
        keyword: trimmedQuery,
        searchType: 'title',
      })
      const resourceTitles = resources.map((resource) => resource.title).slice(0, 3)
      suggestions.push(...resourceTitles)
    }

    return [...new Set(suggestions)].slice(0, 8) // 去重并限制数量
  }
}

export const searchService = new SearchService()
