import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router'

// 检测是否在file://协议下运行，如果是则使用hash模式
const isFileProtocol = typeof window !== 'undefined' && window.location.protocol === 'file:'

const router = createRouter({
  history: isFileProtocol ? createWebHashHistory() : createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/SimpleHomeView.vue'),
      meta: {
        title: '首页',
        transition: 'fade',
      },
    },
    {
      path: '/home-original',
      name: 'home-original',
      component: () => import('../views/HomeView.vue'),
      meta: {
        title: '原始首页',
        transition: 'fade',
      },
    },
    {
      path: '/knowledge',
      name: 'knowledge',
      component: () => import('../views/KnowledgeView.vue'),
      meta: {
        title: '知识库',
        transition: 'slide-left',
      },
    },
    {
      path: '/knowledge/create',
      name: 'knowledge-create',
      component: () => import('../views/ResourceCreateView.vue'),
      meta: {
        title: '添加资源',
        transition: 'slide-up',
      },
    },
    {
      path: '/knowledge/preview/:id',
      name: 'ResourcePreview',
      component: () => import('../views/ResourcePreviewView.vue'),
      meta: {
        title: '资源预览',
        transition: 'slide-left',
      },
    },
    {
      path: '/knowledge/:id',
      name: 'knowledge-detail',
      component: () => import('../views/ResourceDetailView.vue'),
      meta: {
        title: '资源详情',
        transition: 'slide-left',
      },
    },
    {
      path: '/settings',
      name: 'settings',
      component: () => import('../views/SettingsView.vue'),
      meta: {
        title: '系统设置',
        transition: 'slide-left',
      },
    },
    {
      path: '/ai-chat',
      name: 'ai-chat',
      component: () => import('../views/AiChatView.vue'),
      meta: {
        title: 'AI对话',
        transition: 'slide-left',
      },
    },
    {
      path: '/base-select-demo',
      name: 'base-select-demo',
      component: () => import('../views/BaseSelectDemo.vue'),
      meta: {
        title: 'BaseSelect 演示',
        transition: 'slide-left',
      },
    },
    {
      path: '/ant-design-demo',
      name: 'ant-design-demo',
      component: () => import('../views/AntDesignDemo.vue'),
      meta: {
        title: 'Ant Design 演示',
        transition: 'slide-left',
      },
    },
    {
      path: '/settings-antd-demo',
      name: 'settings-antd-demo',
      component: () => import('../views/SettingsAntdDemo.vue'),
      meta: {
        title: '设置界面 Ant Design 演示',
        transition: 'slide-left',
      },
    },
    {
      path: '/imagehost-store-test',
      name: 'imagehost-store-test',
      component: () => import('../components/settings/ImageHostStoreTest.vue'),
      meta: {
        title: '图床 Store 测试',
        transition: 'slide-left',
      },
    },
    {
      path: '/knowledge-settings-antd',
      name: 'knowledge-settings-antd',
      component: () => import('../components/settings/KnowledgeSettingsAntd.vue'),
      meta: {
        title: '知识库设置 Ant Design 版本',
        transition: 'slide-left',
      },
    },
    {
      path: '/ai-settings-antd',
      name: 'ai-settings-antd',
      component: () => import('../components/ai/AiSettingsModalAntd.vue'),
      meta: {
        title: 'AI设置 Ant Design 版本',
        transition: 'slide-left',
      },
    },
    {
      path: '/search-engine-settings-antd',
      name: 'search-engine-settings-antd',
      component: () => import('../components/settings/SearchEngineSettingsAntd.vue'),
      meta: {
        title: '搜索引擎设置 Ant Design 版本',
        transition: 'slide-left',
      },
    },
    {
      path: '/theme-settings-antd',
      name: 'theme-settings-antd',
      component: () => import('../components/settings/ThemeSettingsAntd.vue'),
      meta: {
        title: '主题设置 Ant Design 版本',
        transition: 'slide-left',
      },
    },
    {
      path: '/data-management-antd',
      name: 'data-management-antd',
      component: () => import('../components/settings/DataManagementAntd.vue'),
      meta: {
        title: '数据管理 Ant Design 版本',
        transition: 'slide-left',
      },
    },
    {
      path: '/settings-showcase',
      name: 'settings-showcase',
      component: () => import('../views/SettingsAntdShowcase.vue'),
      meta: {
        title: '设置界面组件展示',
        transition: 'slide-left',
      },
    },
    {
      path: '/image-gallery',
      name: 'image-gallery',
      component: () => import('../views/ImageGalleryView.vue'),
      meta: {
        title: '图床管理',
        transition: 'slide-left',
      },
    },
    {
      path: '/component-showcase',
      name: 'component-showcase',
      component: () => import('../views/ComponentShowcase.vue'),
      meta: {
        title: '组件样式展示',
        transition: 'slide-left',
      },
    },
    {
      path: '/test',
      name: 'test',
      component: () => import('../views/TestView.vue'),
      meta: {
        title: '组件测试',
        transition: 'fade',
      },
    },
    {
      path: '/simple-test',
      name: 'simple-test',
      component: () => import('../views/SimpleTestView.vue'),
      meta: {
        title: '简单测试',
        transition: 'fade',
      },
    },
  ],
})

export default router
