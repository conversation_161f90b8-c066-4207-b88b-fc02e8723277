<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="transform translate-x-full opacity-0"
      enter-to-class="transform translate-x-0 opacity-100"
      leave-active-class="transition-all duration-300 ease-in"
      leave-from-class="transform translate-x-0 opacity-100"
      leave-to-class="transform translate-x-full opacity-0"
    >
      <div
        v-if="visible"
        :class="[
          'fixed z-50 max-w-sm w-full',
          'bg-white dark:bg-gray-800 rounded-lg shadow-lg border-2',
          'cursor-pointer transition-all duration-200 hover:shadow-xl',
          positionClasses,
          typeClasses
        ]"
        @click="handleClick"
      >
        <div class="p-4 flex items-start space-x-3">
          <!-- 图标 -->
          <div :class="[
            'flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center',
            iconBgClasses
          ]">
            <div :class="[iconClasses, 'w-4 h-4']"></div>
          </div>
          
          <!-- 内容 -->
          <div class="flex-1 min-w-0">
            <p :class="[
              'text-sm font-medium',
              titleClasses
            ]">
              {{ title }}
            </p>
            <p v-if="message" :class="[
              'text-sm mt-1',
              messageClasses
            ]">
              {{ message }}
            </p>
          </div>
          
          <!-- 关闭按钮 -->
          <button
            @click.stop="close"
            :class="[
              'flex-shrink-0 p-1 rounded-full transition-colors',
              'hover:bg-gray-100 dark:hover:bg-gray-700',
              closeButtonClasses
            ]"
          >
            <div class="i-heroicons-x-mark w-4 h-4"></div>
          </button>
        </div>
        
        <!-- 进度条 -->
        <div
          v-if="showProgress && duration > 0"
          class="h-1 bg-gray-200 dark:bg-gray-700 rounded-b-lg overflow-hidden"
        >
          <div
            :class="[
              'h-full transition-all ease-linear',
              progressClasses
            ]"
            :style="{ width: `${progress}%` }"
          ></div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 通知类型
type NotificationType = 'success' | 'error' | 'warning' | 'info'

// 通知位置
type NotificationPosition = 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center'

interface Props {
  type?: NotificationType
  title: string
  message?: string
  duration?: number // 自动关闭时间（毫秒），0表示不自动关闭
  position?: NotificationPosition
  showProgress?: boolean
  clickToClose?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'success',
  duration: 3000,
  position: 'top-right',
  showProgress: true,
  clickToClose: true
})

const emit = defineEmits<{
  close: []
}>()

const visible = ref(false)
const progress = ref(100)
let timer: NodeJS.Timeout | null = null
let progressTimer: NodeJS.Timeout | null = null

// 位置样式
const positionClasses = computed(() => {
  const positions = {
    'top-right': 'top-20 right-4',
    'top-left': 'top-20 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-center': 'top-20 left-1/2 transform -translate-x-1/2'
  }
  return positions[props.position]
})

// 类型样式
const typeClasses = computed(() => {
  const types = {
    success: 'border-green-200 dark:border-green-800',
    error: 'border-red-200 dark:border-red-800',
    warning: 'border-yellow-200 dark:border-yellow-800',
    info: 'border-blue-200 dark:border-blue-800'
  }
  return types[props.type]
})

// 图标样式
const iconClasses = computed(() => {
  const icons = {
    success: 'i-heroicons-check text-white',
    error: 'i-heroicons-x-mark text-white',
    warning: 'i-heroicons-exclamation-triangle text-white',
    info: 'i-heroicons-information-circle text-white'
  }
  return icons[props.type]
})

// 图标背景样式
const iconBgClasses = computed(() => {
  const backgrounds = {
    success: 'bg-green-500',
    error: 'bg-red-500',
    warning: 'bg-yellow-500',
    info: 'bg-blue-500'
  }
  return backgrounds[props.type]
})

// 标题样式
const titleClasses = computed(() => {
  const colors = {
    success: 'text-green-800 dark:text-green-200',
    error: 'text-red-800 dark:text-red-200',
    warning: 'text-yellow-800 dark:text-yellow-200',
    info: 'text-blue-800 dark:text-blue-200'
  }
  return colors[props.type]
})

// 消息样式
const messageClasses = computed(() => {
  return 'text-gray-600 dark:text-gray-400'
})

// 关闭按钮样式
const closeButtonClasses = computed(() => {
  const colors = {
    success: 'text-green-600 dark:text-green-400',
    error: 'text-red-600 dark:text-red-400',
    warning: 'text-yellow-600 dark:text-yellow-400',
    info: 'text-blue-600 dark:text-blue-400'
  }
  return colors[props.type]
})

// 进度条样式
const progressClasses = computed(() => {
  const colors = {
    success: 'bg-green-500',
    error: 'bg-red-500',
    warning: 'bg-yellow-500',
    info: 'bg-blue-500'
  }
  return colors[props.type]
})

// 显示通知
const show = () => {
  visible.value = true
  
  if (props.duration > 0) {
    // 设置自动关闭定时器
    timer = setTimeout(() => {
      close()
    }, props.duration)
    
    // 设置进度条动画
    if (props.showProgress) {
      const interval = 50 // 更新间隔（毫秒）
      const step = (interval / props.duration) * 100
      
      progressTimer = setInterval(() => {
        progress.value -= step
        if (progress.value <= 0) {
          progress.value = 0
          if (progressTimer) {
            clearInterval(progressTimer)
            progressTimer = null
          }
        }
      }, interval)
    }
  }
}

// 关闭通知
const close = () => {
  visible.value = false
  if (timer) {
    clearTimeout(timer)
    timer = null
  }
  if (progressTimer) {
    clearInterval(progressTimer)
    progressTimer = null
  }
  emit('close')
}

// 点击处理
const handleClick = () => {
  if (props.clickToClose) {
    close()
  }
}

// 组件挂载时显示
onMounted(() => {
  show()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (timer) {
    clearTimeout(timer)
  }
  if (progressTimer) {
    clearInterval(progressTimer)
  }
})

// 暴露方法
defineExpose({
  show,
  close
})
</script>
