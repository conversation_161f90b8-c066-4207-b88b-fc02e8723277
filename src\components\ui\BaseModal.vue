<template>
  <Teleport to="body">
    <Transition enter-active-class="transition-opacity duration-300" enter-from-class="opacity-0"
      enter-to-class="opacity-100" leave-active-class="transition-opacity duration-300" leave-from-class="opacity-100"
      leave-to-class="opacity-0">
      <div v-if="modelValue" class="fixed inset-0 z-50 flex items-center justify-center p-4"
        @click="handleBackdropClick">
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-black/50 backdrop-blur-sm"></div>

        <!-- 模态框内容 -->
        <Transition enter-active-class="transition-all duration-300" enter-from-class="opacity-0 scale-95 translate-y-4"
          enter-to-class="opacity-100 scale-100 translate-y-0" leave-active-class="transition-all duration-300"
          leave-from-class="opacity-100 scale-100 translate-y-0" leave-to-class="opacity-0 scale-95 translate-y-4">
          <div v-if="modelValue" :class="modalClasses" @click.stop>
            <!-- 头部 -->
            <div v-if="title || $slots.header"
              class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div class="flex-1">
                <slot name="header">
                  <h3 class="text-lg font-semibold text-primary">{{ title }}</h3>
                </slot>
              </div>
              <button v-if="closable"
                class="ml-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                @click="handleClose">
                <div class="i-heroicons-x-mark w-6 h-6"></div>
              </button>
            </div>

            <!-- 内容 -->
            <div :class="bodyClasses">
              <slot></slot>
            </div>

            <!-- 底部 -->
            <div v-if="$slots.footer"
              class="flex items-center justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700">
              <slot name="footer"></slot>
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'

interface Props {
  modelValue: boolean
  title?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  closable?: boolean
  closeOnBackdrop?: boolean
  allowOverflow?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  closable: true,
  closeOnBackdrop: true,
  allowOverflow: false
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  close: []
}>()

const modalClasses = computed(() => {
  const baseClasses = [
    'relative bg-white dark:bg-gray-800 rounded-xl shadow-xl shadow-primary-200/50 dark:shadow-primary-900/30 max-h-[90vh]',
    'animate-scale-in'
  ]

  const sizeClasses = {
    sm: 'w-full max-w-md',
    md: 'w-full max-w-lg',
    lg: 'w-full max-w-2xl',
    xl: 'w-full max-w-4xl',
    full: 'w-full max-w-[95vw] h-[95vh]'
  }

  return [
    ...baseClasses,
    sizeClasses[props.size]
  ]
})

const bodyClasses = computed(() => {
  const baseClasses = []

  if (props.size === 'full') {
    baseClasses.push('flex-1 p-6')
    baseClasses.push(props.allowOverflow ? 'overflow-visible' : 'overflow-y-auto')
  } else {
    baseClasses.push('p-6 max-h-[70vh]')
    baseClasses.push(props.allowOverflow ? 'overflow-visible' : 'overflow-y-auto')
  }

  return baseClasses
})

const handleClose = () => {
  emit('update:modelValue', false)
  emit('close')
}

const handleBackdropClick = () => {
  if (props.closeOnBackdrop) {
    handleClose()
  }
}

// 监听ESC键关闭模态框
watch(() => props.modelValue, (isOpen) => {
  const handleEscape = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && props.closable) {
      handleClose()
    }
  }

  if (isOpen) {
    document.addEventListener('keydown', handleEscape)
    document.body.style.overflow = 'hidden'
  } else {
    document.removeEventListener('keydown', handleEscape)
    document.body.style.overflow = ''
  }

  return () => {
    document.removeEventListener('keydown', handleEscape)
    document.body.style.overflow = ''
  }
})
</script>
