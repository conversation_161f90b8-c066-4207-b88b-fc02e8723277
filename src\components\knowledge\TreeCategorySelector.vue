<template>
  <div>
    <BaseDropdown ref="dropdownRef" width="full">
      <template #trigger>
        <button type="button" :class="[
          'w-full flex items-center justify-between px-3 py-2 rounded-xl transition-all duration-200',
          'focus:outline-none text-left',
          error
            ? 'bg-red-50 border border-red-300 focus:bg-white focus:border-red-500 dark:bg-red-900/10 dark:border-red-600 dark:focus:bg-gray-800 dark:focus:border-red-400'
            : 'bg-gray-50 border-0 hover:bg-gray-100 focus:bg-white focus:border focus:border-primary-500 dark:bg-gray-700 dark:hover:bg-gray-650 dark:focus:bg-gray-800 dark:focus:border-primary-400'
        ]">
          <span :class="selectedCategoryPath ? 'text-primary' : 'text-gray-500 dark:text-gray-400'">
            {{ selectedCategoryPath || '选择分类' }}
          </span>
          <div class="i-heroicons-chevron-down w-4 h-4 text-gray-400"></div>
        </button>
      </template>

      <div
        class="w-full max-h-80 overflow-hidden bg-white dark:bg-gray-800 border border-primary-200 dark:border-primary-600 rounded-xl shadow-lg">
        <!-- 搜索框 -->
        <div class="p-3 border-b border-primary-200 dark:border-primary-600">
          <BaseInput v-model="searchQuery" placeholder="搜索分类..." prefix-icon="i-heroicons-magnifying-glass" size="sm"
            clearable @input="handleSearchInput" />
        </div>

        <!-- 树形分类列表 -->
        <div class="max-h-64 overflow-y-auto">
          <div class="py-1">
            <!-- 无分类选项 -->
            <DropdownItem text="无分类" :icon="!modelValue ? 'i-heroicons-check' : ''"
              @click.stop="selectCategory(null)" />

            <!-- 分类树 -->
            <CategoryTreeNode v-for="category in filteredCategories" :key="category.id" :category="category"
              :selected-id="modelValue" :search-query="searchQuery" :level="0" :all-categories="categories"
              @select="selectCategory" />
          </div>
        </div>

        <!-- 无匹配结果和创建新分类 -->
        <div v-if="filteredCategories.length === 0 && searchQuery"
          class="px-4 py-4 text-sm text-gray-500 dark:text-gray-400 text-center border-t border-primary-200 dark:border-primary-600">
          <p class="mb-3">未找到匹配的分类 "{{ searchQuery }}"</p>
          <BaseButton variant="primary" size="sm" @click="createCategoryWithSearch">
            <div class="i-heroicons-plus mr-2"></div>
            创建分类 "{{ searchQuery }}"
          </BaseButton>
        </div>

        <!-- 创建新分类按钮 -->
        <div v-else class="border-t border-primary-200 dark:border-primary-600 p-3">
          <BaseButton variant="ghost" size="sm" block @click="openCreateDialog">
            <div class="i-heroicons-plus mr-2"></div>
            创建新分类
          </BaseButton>
        </div>
      </div>
    </BaseDropdown>

    <!-- 错误提示 -->
    <p v-if="error" class="mt-1 text-sm text-red-600">
      {{ error }}
    </p>

    <!-- 创建分类对话框 -->
    <BaseModal v-model="showCreateDialog" title="创建新分类" size="md" :allow-overflow="true">
      <form @submit.prevent="createCategory" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            分类名称 <span class="text-red-500">*</span>
          </label>
          <BaseInput v-model="newCategoryName" placeholder="请输入分类名称" :error="createError" />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            父分类
          </label>
          <ParentCategorySelector v-model="newCategoryParent" :categories="categories" placeholder="选择父分类（可选）" />
        </div>
      </form>

      <template #footer>
        <BaseButton variant="secondary" @click="showCreateDialog = false">
          取消
        </BaseButton>
        <BaseButton type="submit" :loading="creating" @click="createCategory">
          创建
        </BaseButton>
      </template>
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import BaseDropdown from '@/components/ui/BaseDropdown.vue'
import BaseInput from '@/components/ui/BaseInput.vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import BaseModal from '@/components/ui/BaseModal.vue'
import DropdownItem from '@/components/ui/DropdownItem.vue'
import CategoryTreeNode from './CategoryTreeNode.vue'
import ParentCategorySelector from './ParentCategorySelector.vue'
import { categoryService } from '@/services/categoryService'
import type { Category } from '@/types'

interface Props {
  modelValue: number | null
  categories: Category[]
  error?: string
  readonly?: boolean
  placeholder?: string
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  placeholder: '选择分类'
})

const emit = defineEmits<{
  'update:modelValue': [value: number | null]
}>()

// 响应式数据
const dropdownRef = ref()
const searchQuery = ref('')
const showCreateDialog = ref(false)
const newCategoryName = ref('')
const newCategoryParent = ref<number | null>(null)
const creating = ref(false)
const createError = ref('')

// 计算属性
const selectedCategory = computed(() =>
  props.categories.find(cat => cat.id === props.modelValue)
)

const selectedCategoryPath = computed(() => {
  if (!selectedCategory.value) return ''
  return getCategoryPath(selectedCategory.value)
})

const filteredCategories = computed(() => {
  if (!searchQuery.value) {
    return getRootCategories()
  }

  return props.categories.filter(category =>
    category.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// 方法
const getRootCategories = () => {
  return props.categories.filter(cat => cat.parent_id === 0)
}

const getCategoryPath = (category: Category): string => {
  const path = [category.name]
  let current = category

  while (current.parent_id && current.parent_id !== 0) {
    const parent = props.categories.find(cat => cat.id === current.parent_id)
    if (parent) {
      path.unshift(parent.name)
      current = parent
    } else {
      break
    }
  }

  return path.join(' > ')
}

const selectCategory = (categoryId: number | null) => {
  emit('update:modelValue', categoryId)
  dropdownRef.value?.close()
}

const handleSearchInput = () => {
  // 搜索输入处理逻辑
}

const openCreateDialog = () => {
  showCreateDialog.value = true
  dropdownRef.value?.close()
}

const createCategoryWithSearch = () => {
  newCategoryName.value = searchQuery.value
  showCreateDialog.value = true
  dropdownRef.value?.close()
}

const createCategory = async () => {
  if (!newCategoryName.value.trim()) {
    createError.value = '请输入分类名称'
    return
  }

  try {
    creating.value = true
    createError.value = ''

    let categoryId: number
    let newCategory: Category

    if (props.readonly) {
      // 只读模式：创建临时分类
      categoryId = Date.now()
      newCategory = {
        id: categoryId,
        name: newCategoryName.value.trim(),
        parent_id: newCategoryParent.value || 0,
        resource_count: 0,
        sort_order: props.categories.length + 1,
        created_at: new Date(),
        isTemporary: true
      }
    } else {
      // 正常模式：保存到数据库
      categoryId = await categoryService.createCategory({
        name: newCategoryName.value.trim(),
        parent_id: newCategoryParent.value || 0,
        sort_order: props.categories.length + 1
      })

      const loadedCategory = await categoryService.getCategoryById(categoryId)
      if (!loadedCategory) {
        throw new Error('创建分类后无法加载分类信息')
      }
      newCategory = loadedCategory
    }

    // 添加到分类列表
    props.categories.push(newCategory)
    emit('update:modelValue', categoryId)

    // 重置表单
    newCategoryName.value = ''
    newCategoryParent.value = null
    showCreateDialog.value = false
    dropdownRef.value?.close()
  } catch (error) {
    console.error('创建分类失败:', error)
    createError.value = '创建失败，请重试'
  } finally {
    creating.value = false
  }
}
</script>
