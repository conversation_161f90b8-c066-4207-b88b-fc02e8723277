<template>
  <a-layout-header class="app-header-fixed">
    <div class="header-content">
      <!-- Logo区域 -->
      <div class="header-logo">
        <router-link to="/" class="logo-link">
          <div class="logo-icon">
            <BookOutlined />
          </div>
          <span class="logo-text">KnowlEdge</span>
        </router-link>
      </div>

      <!-- 主导航菜单 - 使用原生按钮而不是Ant Design Menu -->
      <div class="header-nav">
        <div class="nav-buttons">
          <button 
            v-for="item in menuItems" 
            :key="item.key"
            @click="handleDirectNavigation(item.key)"
            :class="['nav-button', { 'nav-button-active': route.path === item.key }]"
          >
            <component :is="item.icon" class="nav-icon" />
            {{ item.label }}
          </button>
        </div>
      </div>

      <!-- 搜索框 -->
      <div class="header-search">
        <a-input-search
          v-model:value="searchQuery"
          placeholder="搜索知识库..."
          size="middle"
          class="search-input"
          @search="handleSearch"
          @pressEnter="handleSearch"
        />
      </div>

      <!-- 右侧操作区 -->
      <div class="header-actions">
        <!-- AI对话 -->
        <a-button type="text" size="small" @click="handleAiChat" class="action-btn" title="AI对话">
          <template #icon>
            <MessageOutlined />
          </template>
        </a-button>

        <!-- AI设置 -->
        <a-button type="text" size="small" @click="handleAiSettings" class="action-btn" title="AI设置">
          <template #icon>
            <SettingOutlined />
          </template>
        </a-button>

        <!-- 主题切换 -->
        <a-dropdown placement="bottomRight" :trigger="['click']">
          <a-button type="text" size="small" class="action-btn" title="主题切换">
            <template #icon>
              <BulbOutlined />
            </template>
          </a-button>
          <template #overlay>
            <a-menu @click="handleThemeMenuClick">
              <a-menu-item key="light">
                <template #icon>
                  <BulbOutlined />
                </template>
                浅色模式
              </a-menu-item>
              <a-menu-item key="dark">
                <template #icon>
                  <EyeInvisibleOutlined />
                </template>
                深色模式
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>

        <!-- 设置菜单 -->
        <a-dropdown placement="bottomRight" :trigger="['click']">
          <a-button type="text" size="small" class="action-btn" title="更多设置">
            <template #icon>
              <MoreOutlined />
            </template>
          </a-button>
          <template #overlay>
            <a-menu @click="handleSettingsMenuClick">
              <a-menu-item key="about">
                <template #icon>
                  <InfoCircleOutlined />
                </template>
                关于
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </div>
  </a-layout-header>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  BookOutlined,
  HomeOutlined,
  DatabaseOutlined,
  PictureOutlined,
  BgColorsOutlined,
  MessageOutlined,
  SettingOutlined,
  BulbOutlined,
  EyeInvisibleOutlined,
  MoreOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const route = useRoute()

// 状态管理
const searchQuery = ref('')

// 导航菜单项
const menuItems = computed(() => [
  {
    key: '/',
    icon: HomeOutlined,
    label: '首页'
  },
  {
    key: '/knowledge',
    icon: DatabaseOutlined,
    label: '知识库'
  },
  {
    key: '/image-gallery',
    icon: PictureOutlined,
    label: '图床管理'
  },
  {
    key: '/component-showcase',
    icon: BgColorsOutlined,
    label: '样式展示'
  },
  {
    key: '/simple-test',
    icon: SettingOutlined,
    label: '简单测试'
  }
])

// 直接导航处理 - 使用与SimpleTestView相同的方式
const handleDirectNavigation = (path: string) => {
  console.log('直接导航到:', path)
  console.log('当前路由:', route.path)
  
  // 使用与SimpleTestView完全相同的路由跳转方式
  router.push(path).then(() => {
    console.log('路由跳转成功到:', path)
  }).catch(err => {
    console.error('路由跳转失败:', err)
  })
}

// 处理搜索
const handleSearch = (value: string) => {
  if (value.trim()) {
    console.log('搜索:', value)
    router.push(`/knowledge?search=${encodeURIComponent(value)}`)
  }
}

// 处理AI对话
const handleAiChat = () => {
  console.log('打开AI对话')
}

// 处理AI设置
const handleAiSettings = () => {
  console.log('打开AI设置')
}

// 处理主题菜单点击
const handleThemeMenuClick = ({ key }: { key: string }) => {
  console.log('切换主题到:', key)
}

// 处理设置菜单点击
const handleSettingsMenuClick = ({ key }: { key: string }) => {
  console.log('设置菜单点击:', key)
}
</script>

<style scoped>
/* Header样式 */
.app-header-fixed {
  position: sticky;
  top: 0;
  z-index: 1000;
  height: 48px;
  line-height: 48px;
  padding: 0;
  background: var(--ant-color-bg-container);
  border-bottom: 1px solid var(--ant-color-border);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.header-content {
  display: flex;
  align-items: center;
  height: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 20px;
  gap: 20px;
}

/* Logo区域 */
.header-logo {
  flex-shrink: 0;
  min-width: 140px;
}

.logo-link {
  display: flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
  transition: all 0.2s ease;
  height: 48px;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--ant-color-primary), var(--ant-color-primary-hover));
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.logo-text {
  font-size: 20px;
  font-weight: 700;
  color: var(--ant-color-primary);
  white-space: nowrap;
}

/* 导航按钮 */
.header-nav {
  flex-shrink: 0;
  height: 48px;
  display: flex;
  align-items: center;
}

.nav-buttons {
  display: flex;
  gap: 4px;
}

.nav-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  background: transparent;
  color: var(--ant-color-text);
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.nav-button:hover {
  background: var(--ant-color-fill-tertiary);
}

.nav-button-active {
  background: var(--ant-color-primary-bg);
  color: var(--ant-color-primary);
}

.nav-icon {
  font-size: 16px;
}

/* 搜索框 */
.header-search {
  flex: 1;
  max-width: 400px;
  margin: 0 20px;
}

.search-input {
  height: 32px !important;
  border-radius: 16px !important;
}

/* 右侧操作区 */
.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
