<template>
  <a-layout-header class="app-header-antd">
    <div class="header-content">
      <!-- Logo区域 -->
      <div class="header-logo">
        <router-link to="/" class="logo-link">
          <div class="logo-icon">
            <BookOutlined />
          </div>
          <span class="logo-text">KnowlEdge</span>
        </router-link>
      </div>

      <!-- 主导航菜单 -->
      <div class="header-nav">
        <a-menu v-model:selectedKeys="selectedKeys" mode="horizontal" :items="menuItems" class="main-menu"
          @click="handleMenuClick" />
      </div>

      <!-- 搜索框 -->
      <div class="header-search">
        <a-input-search v-model:value="searchQuery" placeholder="搜索知识库..." size="middle" class="search-input"
          @search="handleSearch" @pressEnter="handleSearch" />
      </div>

      <!-- 右侧操作区 -->
      <div class="header-actions">
        <!-- AI对话 -->
        <a-button type="text" size="small" @click="aiStore.openChatModal" class="action-btn" title="AI对话">
          <template #icon>
            <MessageOutlined />
          </template>
        </a-button>

        <!-- AI设置 -->
        <a-button type="text" size="small" @click="showAiSettings = true" class="action-btn" title="AI设置">
          <template #icon>
            <SettingOutlined />
          </template>
        </a-button>

        <!-- 主题切换 -->
        <a-dropdown placement="bottomRight" :trigger="['click']">
          <a-button type="text" size="small" class="action-btn">
            <template #icon>
              <component :is="themeIconComponent" />
            </template>
          </a-button>
          <template #overlay>
            <a-menu @click="handleThemeMenuClick">
              <a-menu-item key="light">
                <template #icon>
                  <BulbOutlined />
                </template>
                浅色模式
              </a-menu-item>
              <a-menu-item key="dark">
                <template #icon>
                  <EyeInvisibleOutlined />
                </template>
                深色模式
              </a-menu-item>
              <a-menu-item key="system">
                <template #icon>
                  <DesktopOutlined />
                </template>
                跟随系统
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>

        <!-- 设置菜单 -->
        <a-dropdown placement="bottomRight" :trigger="['click']">
          <a-button type="text" size="small" class="action-btn" title="更多设置">
            <template #icon>
              <MoreOutlined />
            </template>
          </a-button>
          <template #overlay>
            <a-menu @click="handleSettingsMenuClick">
              <a-menu-item-group title="快速操作">
                <a-menu-item key="category">
                  <template #icon>
                    <FolderOutlined />
                  </template>
                  分类管理
                </a-menu-item>
                <a-menu-item key="tag">
                  <template #icon>
                    <TagOutlined />
                  </template>
                  标签管理
                </a-menu-item>
                <a-menu-item key="search-engine">
                  <template #icon>
                    <SearchOutlined />
                  </template>
                  搜索引擎设置
                </a-menu-item>
              </a-menu-item-group>
              <a-menu-divider />
              <a-menu-item-group title="数据管理">
                <a-menu-item key="import">
                  <template #icon>
                    <ImportOutlined />
                  </template>
                  导入数据
                </a-menu-item>
                <a-menu-item key="export">
                  <template #icon>
                    <ExportOutlined />
                  </template>
                  导出数据
                </a-menu-item>
              </a-menu-item-group>
              <a-menu-divider />
              <a-menu-item key="about">
                <template #icon>
                  <InfoCircleOutlined />
                </template>
                关于
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </div>

    <!-- 模态框组件暂时注释掉 -->
    <!-- <CategoryManagementModal v-model="showCategoryManagement" /> -->
    <!-- <TagManagementModal v-model="showTagManagement" /> -->
    <!-- <SearchEngineSettings v-model="showSearchEngineSettings" /> -->
    <!-- <AiSettingsModal v-model="showAiSettings" /> -->
    <!-- <AiChatModal v-model="aiStore.isChatModalOpen" /> -->
  </a-layout-header>
</template>

<script setup lang="ts">
import { ref, computed, watch, h } from 'vue'
import { useRouter, useRoute } from 'vue-router'
// import { useTheme } from '@/composables/useTheme'
// import { useAiStore } from '@/stores/aiStore'
import {
  BookOutlined,
  HomeOutlined,
  DatabaseOutlined,
  PictureOutlined,
  BgColorsOutlined,
  MessageOutlined,
  SettingOutlined,
  BulbOutlined,
  EyeInvisibleOutlined,
  DesktopOutlined,
  MoreOutlined,
  FolderOutlined,
  TagOutlined,
  SearchOutlined,
  ImportOutlined,
  ExportOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue'
// 注释掉不存在的组件导入
// import SmartSearchBox from '@/components/search/SmartSearchBox.vue'
// import CategoryManagementModal from '@/components/management/CategoryManagementModal.vue'
// import TagManagementModal from '@/components/management/TagManagementModal.vue'
// import SearchEngineSettings from '@/components/settings/SearchEngineSettings.vue'
// import CloudStorageButton from './CloudStorageButton.vue'
// import AiSettingsModal from '@/components/ai/AiSettingsModalAntd.vue'
// import AiChatModal from '@/components/ai/AiChatModal.vue'

const router = useRouter()
const route = useRoute()
// const { themeIcon, setThemeMode } = useTheme()
// const aiStore = useAiStore()

// 状态管理
const showCategoryManagement = ref(false)
const showTagManagement = ref(false)
const showSearchEngineSettings = ref(false)
const showAiSettings = ref(false)
const searchQuery = ref('')

// 模拟主题相关功能
const themeIcon = ref('i-heroicons-sun')
const setThemeMode = (mode: string) => {
  console.log('切换主题:', mode)
}

// 模拟AI Store
const aiStore = {
  openChatModal: () => console.log('打开AI对话'),
  isChatModalOpen: ref(false)
}

// 当前选中的菜单项
const selectedKeys = ref<string[]>([])

// 监听路由变化更新选中状态
watch(
  () => route.path,
  (newPath) => {
    console.log('路由变化:', newPath)
    selectedKeys.value = [newPath]
  },
  { immediate: true }
)

// 主题图标组件
const themeIconComponent = computed(() => {
  switch (themeIcon.value) {
    case 'i-heroicons-sun':
      return BulbOutlined
    case 'i-heroicons-moon':
      return EyeInvisibleOutlined
    case 'i-heroicons-computer-desktop':
      return DesktopOutlined
    default:
      return BulbOutlined
  }
})

// 导航菜单项
const menuItems = computed(() => [
  {
    key: '/',
    icon: () => h(HomeOutlined),
    label: '首页'
  },
  {
    key: '/knowledge',
    icon: () => h(DatabaseOutlined),
    label: '知识库'
  },
  {
    key: '/image-gallery',
    icon: () => h(PictureOutlined),
    label: '图床管理'
  },
  {
    key: '/component-showcase',
    icon: () => h(BgColorsOutlined),
    label: '样式展示'
  },
  {
    key: '/test',
    icon: () => h(SettingOutlined),
    label: '测试页面'
  }
])

// 处理菜单点击
const handleMenuClick = ({ key }: { key: string }) => {
  console.log('导航到:', key)
  // 确保路由跳转正常工作
  router.push(key).catch(err => {
    console.error('路由跳转失败:', err)
  })
}

// 处理搜索
const handleSearch = (value: string) => {
  if (value.trim()) {
    console.log('搜索:', value)
    // 这里可以添加实际的搜索逻辑
    router.push(`/knowledge?search=${encodeURIComponent(value)}`)
  }
}

// 处理主题菜单点击
const handleThemeMenuClick = ({ key }: { key: string }) => {
  console.log('切换主题到:', key)
  setThemeMode(key as 'light' | 'dark' | 'system')

  // 更新主题图标
  switch (key) {
    case 'light':
      themeIcon.value = 'i-heroicons-sun'
      break
    case 'dark':
      themeIcon.value = 'i-heroicons-moon'
      break
    case 'system':
      themeIcon.value = 'i-heroicons-computer-desktop'
      break
  }
}

// 处理设置菜单点击
const handleSettingsMenuClick = ({ key }: { key: string }) => {
  console.log('设置菜单点击:', key)

  switch (key) {
    case 'category':
      console.log('打开分类管理')
      showCategoryManagement.value = true
      break
    case 'tag':
      console.log('打开标签管理')
      showTagManagement.value = true
      break
    case 'search-engine':
      console.log('打开搜索引擎设置')
      showSearchEngineSettings.value = true
      break
    case 'import':
      console.log('导入数据功能')
      // TODO: 实现导入功能
      break
    case 'export':
      console.log('导出数据功能')
      // TODO: 实现导出功能
      break
    case 'about':
      console.log('关于页面')
      // TODO: 实现关于页面
      break
  }
}
</script>

<style scoped>
/* Ant Design Header 样式重写 */
.app-header-antd {
  position: sticky;
  top: 0;
  z-index: 1000;
  height: 48px;
  line-height: 48px;
  padding: 0;
  background: var(--ant-color-bg-container);
  border-bottom: 1px solid var(--ant-color-border);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(8px);
}

.header-content {
  display: flex;
  align-items: center;
  height: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 20px;
  gap: 20px;
}

/* Logo区域 */
.header-logo {
  flex-shrink: 0;
  min-width: 140px;
}

.logo-link {
  display: flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
  transition: all 0.2s ease;
  height: 48px;
}

.logo-link:hover {
  transform: scale(1.02);
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--ant-color-primary), var(--ant-color-primary-hover));
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.logo-text {
  font-size: 20px;
  font-weight: 700;
  color: var(--ant-color-primary);
  white-space: nowrap;
  /* 备用渐变色方案 */
  background: linear-gradient(135deg, var(--ant-color-primary), var(--ant-color-primary-hover));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 确保在不支持渐变色文字的浏览器中显示正常颜色 */
@supports not (-webkit-background-clip: text) {
  .logo-text {
    color: var(--ant-color-primary) !important;
    background: none !important;
  }
}

/* 暗色主题下的Logo文字 */
.dark .logo-text {
  color: var(--ant-color-primary-active);
}

/* 导航菜单 */
.header-nav {
  flex-shrink: 0;
  height: 48px;
  display: flex;
  align-items: center;
}

.main-menu {
  border-bottom: none;
  background: transparent;
  line-height: 48px;
  height: 48px;
}

.main-menu :deep(.ant-menu-item) {
  height: 48px;
  line-height: 48px;
  margin: 0 2px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  padding: 0 12px;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.main-menu :deep(.ant-menu-item:hover) {
  background: var(--ant-color-fill-tertiary);
}

.main-menu :deep(.ant-menu-item-selected) {
  background: var(--ant-color-primary-bg);
  color: var(--ant-color-primary);
}

.main-menu :deep(.ant-menu-item-selected::after) {
  display: none;
}

/* 搜索框 */
.header-search {
  flex: 1;
  max-width: 480px;
  min-width: 200px;
  margin: 0 20px;
  height: 32px;
  display: flex;
  align-items: center;
}

.search-input {
  height: 32px !important;
  border-radius: 16px !important;
}

.header-search :deep(.ant-input) {
  height: 32px !important;
  border-radius: 16px !important;
  font-size: 14px;
  padding: 4px 12px;
  border: 1px solid var(--ant-color-border);
  transition: all 0.2s ease;
}

.header-search :deep(.ant-input:focus) {
  border-color: var(--ant-color-primary);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.header-search :deep(.ant-input-search) {
  height: 32px !important;
}

.header-search :deep(.ant-input-search-button) {
  height: 32px !important;
  border-radius: 0 16px 16px 0 !important;
  border-left: none;
  background: var(--ant-color-primary);
  border-color: var(--ant-color-primary);
}

.header-search :deep(.ant-input-search-button:hover) {
  background: var(--ant-color-primary-hover);
  border-color: var(--ant-color-primary-hover);
}

/* 右侧操作区 */
.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  height: 48px;
  min-width: 200px;
  justify-content: flex-end;
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.2s ease;
  border: none;
  box-shadow: none;
}

.action-btn:hover {
  background: var(--ant-color-fill-tertiary);
  transform: translateY(-1px);
}

.action-btn:active {
  transform: translateY(0);
}

/* 云存储按钮特殊样式 */
.header-actions :deep(.cloud-storage-btn) {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    padding: 0 16px;
    gap: 16px;
  }

  .header-search {
    max-width: 300px;
    margin: 0 16px;
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: 0 12px;
    gap: 12px;
  }

  .header-logo {
    min-width: 40px;
  }

  .logo-text {
    display: none;
  }

  .header-search {
    max-width: 200px;
    margin: 0 12px;
    min-width: 150px;
  }

  .header-actions {
    gap: 6px;
    min-width: 150px;
  }

  .action-btn {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 8px;
    gap: 8px;
  }

  .header-nav {
    display: none;
  }

  .header-search {
    max-width: 120px;
    margin: 0 8px;
    min-width: 100px;
  }

  .header-actions {
    gap: 4px;
    min-width: 120px;
  }

  .action-btn {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
}
</style>
