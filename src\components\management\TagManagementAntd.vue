<template>
  <a-space direction="vertical" size="middle" style="width: 100%">
    <!-- 标签管理 -->
    <a-card size="small" class="tag-management-card">
      <template #title>
        <div class="card-title-wrapper">
          <span class="card-title">标签管理</span>
          <a-tooltip title="查看操作说明" placement="bottom">
            <a-button type="text" size="small" class="help-icon-btn" @click="showHelpModal = true">
              <QuestionCircleOutlined />
            </a-button>
          </a-tooltip>
        </div>
      </template>

      <!-- 标签统计 -->
      <a-row :gutter="12" class="mb-3">
        <a-col :span="6">
          <div class="stat-card">
            <div class="stat-icon stat-icon-primary">
              <TagOutlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ tagStats.total }}</div>
              <div class="stat-label">标签总数</div>
            </div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-card">
            <div class="stat-icon stat-icon-success">
              <CheckCircleOutlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ tagStats.used }}</div>
              <div class="stat-label">已使用</div>
            </div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-card">
            <div class="stat-icon stat-icon-warning">
              <ExclamationCircleOutlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ tagStats.unused }}</div>
              <div class="stat-label">未使用</div>
            </div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-card">
            <div class="stat-icon stat-icon-info">
              <FireOutlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ tagStats.mostUsed?.resource_count || 0 }}</div>
              <div class="stat-label">最热标签</div>
            </div>
          </div>
        </a-col>
      </a-row>

      <!-- 操作工具栏 -->
      <div class="toolbar-section">
        <div class="toolbar-left">
          <a-button type="primary" @click="showAddForm = !showAddForm">
            <PlusOutlined />
            {{ showAddForm ? '收起表单' : '添加新标签' }}
          </a-button>
        </div>
        <div class="toolbar-right">
          <a-space>
            <a-select v-model:value="sortBy" @change="handleSort">
              <a-select-option value="name">按名称</a-select-option>
              <a-select-option value="count">按使用量</a-select-option>
              <a-select-option value="created">按创建时间</a-select-option>
            </a-select>
            <a-input-search v-model:value="searchText" placeholder="搜索标签" @search="handleSearch" />
            <a-button @click="handleRefresh" :loading="loading">
              <ReloadOutlined v-if="!loading" />
              刷新
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 添加标签表单 -->
      <div class="add-form-container">
        <a-collapse-transition>
          <div v-show="showAddForm" class="add-tag-form">
            <div class="form-content">
              <a-form :model="newTag" :rules="tagRules" layout="vertical" @finish="handleAddTag">
                <a-row :gutter="16">
                  <a-col :span="12">
                    <a-form-item label="标签名称" name="name">
                      <a-input v-model:value="newTag.name" placeholder="请输入标签名称" @input="handleNameInput" />
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="标签颜色" name="color">
                      <div class="color-selection">
                        <div class="color-palette">
                          <div v-for="color in colorPalette" :key="color"
                            :class="['color-option', { 'selected': newTag.color === color, 'used': isColorUsed(color) }]"
                            :style="{ backgroundColor: color }" @click="selectColor(color)"
                            :title="isColorUsed(color) ? '此颜色已被使用' : '选择此颜色'">
                            <CheckOutlined v-if="newTag.color === color" class="check-icon" />
                          </div>
                        </div>
                        <div class="color-input-group">
                          <input v-model="newTag.color" type="color" class="color-picker" @change="handleColorChange" />
                          <a-input v-model:value="newTag.color" placeholder="#1677ff" @change="handleColorChange" />
                        </div>
                      </div>
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-form-item>
                  <a-space>
                    <a-button type="primary" html-type="submit" :loading="adding">
                      <PlusOutlined v-if="!adding" />
                      {{ adding ? '添加中...' : '添加标签' }}
                    </a-button>
                    <a-button @click="resetForm">
                      重置
                    </a-button>
                    <a-button @click="showAddForm = false">
                      取消
                    </a-button>
                  </a-space>
                </a-form-item>
              </a-form>
            </div>
          </div>
        </a-collapse-transition>
      </div>

      <!-- 标签列表 -->
      <div class="space-y-4">
        <!-- 批量操作 -->
        <div v-if="selectedTags.length > 0" class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
          <a-space>
            <span class="text-sm text-blue-600 dark:text-blue-400">
              已选择 {{ selectedTags.length }} 个标签
            </span>
            <a-button size="small" @click="handleBatchDelete">
              <DeleteOutlined />
              批量删除
            </a-button>
            <a-button size="small" @click="handleBatchMerge">
              <MergeCellsOutlined />
              批量合并
            </a-button>
            <a-button size="small" @click="clearSelection">
              取消选择
            </a-button>
          </a-space>
        </div>

        <!-- 标签网格 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <a-card v-for="tag in filteredTags" :key="tag.id" size="small" :class="[
            'cursor-pointer transition-all',
            selectedTags.includes(tag.id) ? 'ring-2 ring-blue-500' : ''
          ]" @click="toggleTagSelection(tag.id)">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <a-checkbox :checked="selectedTags.includes(tag.id)" @click.stop="toggleTagSelection(tag.id)" />
                <a-tag :color="tag.color" class="mb-0">
                  {{ tag.name }}
                </a-tag>
                <span class="text-sm text-gray-500">
                  {{ tag.resource_count }} 项
                </span>
              </div>
              <a-dropdown>
                <a-button type="text" @click.stop>
                  <MoreOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="handleEditTag(tag)">
                      <EditOutlined />
                      编辑
                    </a-menu-item>
                    <a-menu-item @click="handleDuplicateTag(tag)">
                      <CopyOutlined />
                      复制
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item danger @click="handleDeleteTag(tag)">
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>



            <div class="mt-2 text-xs text-gray-500">
              创建于 {{ formatDate(tag.created_at) }}
            </div>
          </a-card>
        </div>

        <!-- 分页 -->
        <div class="flex justify-center mt-6">
          <a-pagination v-model:current="currentPage" v-model:page-size="pageSize" :total="totalTags" show-size-changer
            show-quick-jumper :show-total="(total, range) => `第 ${range[0]}-${range[1]} 项，共 ${total} 项`" />
        </div>
      </div>
    </a-card>

    <!-- 编辑标签模态框 -->
    <a-modal v-model:open="showEditModal" title="编辑标签" @cancel="resetEditForm" :footer="null">
      <a-form :model="editingTag" :rules="tagRules" layout="vertical" @finish="handleUpdateTag">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="标签名称" name="name">
              <a-input v-model:value="editingTag.name" placeholder="请输入标签名称" class="form-input" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="标签颜色" name="color">
              <div class="color-input-group">
                <input v-model="editingTag.color" type="color" class="color-picker" />
                <a-input v-model:value="editingTag.color" placeholder="#1677ff" class="form-input color-input" />
              </div>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item>
          <a-space>
            <a-button @click="resetEditForm" class="form-button form-button-default">
              取消
            </a-button>
            <a-button type="primary" html-type="submit" :loading="updating" class="form-button form-button-primary">
              {{ updating ? '更新中...' : '更新标签' }}
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 批量合并模态框 -->
    <a-modal v-model:open="showMergeModal" title="批量合并标签" @cancel="resetMergeForm" :footer="null" width="600px">
      <div class="merge-info-section">
        <div class="mb-4">
          <h4 class="text-base font-medium mb-2">待合并标签 ({{ selectedTags.length }} 个)</h4>
          <div class="merge-tags-list">
            <div v-for="tagId in selectedTags" :key="tagId" class="merge-tag-item">
              <a-tag :color="getTagById(tagId)?.color" class="merge-tag">
                {{ getTagById(tagId)?.name }}
              </a-tag>
              <span class="tag-resource-count">
                {{ getTagById(tagId)?.resource_count || 0 }} 个资源
              </span>
            </div>
          </div>
        </div>

        <div class="merge-summary">
          <a-alert type="info" show-icon :message="`合并后将保留目标标签，删除其他 ${selectedTags.length - 1} 个标签，并将所有资源关联转移到目标标签。`"
            class="mb-4" />
        </div>
      </div>

      <a-form :model="{ name: mergeTagName, color: mergeTagColor }" :rules="mergeTagRules"
        @finish="handleMergeFormSubmit" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="合并后的标签名称" name="name" required>
              <a-input v-model:value="mergeTagName" placeholder="请输入合并后的标签名称" class="form-input" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="合并后的标签颜色" name="color">
              <div class="color-input-group">
                <input v-model="mergeTagColor" type="color" class="color-picker" />
                <a-input v-model:value="mergeTagColor" placeholder="#1677ff" class="form-input color-input" />
              </div>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item>
          <a-space>
            <a-button @click="resetMergeForm" class="form-button form-button-default">
              取消
            </a-button>
            <a-button type="primary" html-type="submit" :loading="merging" class="form-button form-button-primary">
              {{ merging ? '合并中...' : '确认合并' }}
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 帮助说明模态框 -->
    <a-modal v-model:open="showHelpModal" title="标签管理操作说明" :footer="null" width="700px" class="help-modal">
      <div class="help-content">
        <!-- 功能概览 -->
        <div class="help-section help-section-overview">
          <div class="help-section-header">
            <div class="help-icon-wrapper overview">
              <TagOutlined />
            </div>
            <h3 class="help-section-title">功能概览</h3>
          </div>
          <div class="help-section-content">
            <p class="help-description">
              标签管理帮助您组织和管理知识库的标签体系，支持标签的创建、编辑、删除、合并等丰富的操作功能。
            </p>
          </div>
        </div>

        <!-- 添加标签 -->
        <div class="help-section help-section-add">
          <div class="help-section-header">
            <div class="help-icon-wrapper add">
              <PlusOutlined />
            </div>
            <h3 class="help-section-title">添加标签</h3>
          </div>
          <div class="help-section-content">
            <div class="help-steps">
              <div class="help-step">
                <span class="step-number">1</span>
                <span class="step-text">点击"添加新标签"按钮展开添加表单</span>
              </div>
              <div class="help-step">
                <span class="step-number">2</span>
                <span class="step-text">输入标签名称（必填，1-20个字符）</span>
              </div>
              <div class="help-step">
                <span class="step-number">3</span>
                <span class="step-text">选择标签颜色（可使用颜色选择器或输入色值）</span>
              </div>
              <div class="help-step">
                <span class="step-number">4</span>
                <span class="step-text">输入标签描述（可选）</span>
              </div>
              <div class="help-step">
                <span class="step-number">5</span>
                <span class="step-text">点击"添加标签"完成创建</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 编辑标签 -->
        <div class="help-section help-section-edit">
          <div class="help-section-header">
            <div class="help-icon-wrapper edit">
              <EditOutlined />
            </div>
            <h3 class="help-section-title">编辑标签</h3>
          </div>
          <div class="help-section-content">
            <div class="help-steps">
              <div class="help-step">
                <span class="step-number">1</span>
                <span class="step-text">点击标签卡片右上角的更多按钮</span>
              </div>
              <div class="help-step">
                <span class="step-number">2</span>
                <span class="step-text">选择"编辑"选项打开编辑模态框</span>
              </div>
              <div class="help-step">
                <span class="step-number">3</span>
                <span class="step-text">修改标签名称、颜色或描述</span>
              </div>
              <div class="help-step">
                <span class="step-number">4</span>
                <span class="step-text">点击"保存"确认修改</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 批量操作 -->
        <div class="help-section help-section-batch">
          <div class="help-section-header">
            <div class="help-icon-wrapper batch">
              <CheckCircleOutlined />
            </div>
            <h3 class="help-section-title">批量操作</h3>
          </div>
          <div class="help-section-content">
            <div class="help-steps">
              <div class="help-step">
                <span class="step-number">1</span>
                <span class="step-text">勾选需要操作的标签复选框</span>
              </div>
              <div class="help-step">
                <span class="step-number">2</span>
                <span class="step-text">使用批量操作工具栏进行批量删除</span>
              </div>
              <div class="help-step">
                <span class="step-number">3</span>
                <span class="step-text">支持全选和反选操作</span>
              </div>
              <div class="help-step">
                <span class="step-number">4</span>
                <span class="step-text">批量操作前会显示确认对话框</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 搜索和排序 -->
        <div class="help-section help-section-search">
          <div class="help-section-header">
            <div class="help-icon-wrapper search">
              <ReloadOutlined />
            </div>
            <h3 class="help-section-title">搜索和排序</h3>
          </div>
          <div class="help-section-content">
            <div class="help-steps">
              <div class="help-step">
                <span class="step-number">1</span>
                <span class="step-text">在搜索框中输入关键词搜索标签</span>
              </div>
              <div class="help-step">
                <span class="step-number">2</span>
                <span class="step-text">使用排序下拉框选择排序方式</span>
              </div>
              <div class="help-step">
                <span class="step-number">3</span>
                <span class="step-text">支持按名称、使用量、创建时间排序</span>
              </div>
              <div class="help-step">
                <span class="step-number">4</span>
                <span class="step-text">点击刷新按钮重新加载最新数据</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 注意事项 -->
        <div class="help-section help-section-warning">
          <div class="help-section-header">
            <div class="help-icon-wrapper warning">
              <ExclamationCircleOutlined />
            </div>
            <h3 class="help-section-title">注意事项</h3>
          </div>
          <div class="help-section-content">
            <div class="help-warnings">
              <div class="help-warning">
                <span class="warning-icon">⚠️</span>
                <span class="warning-text">标签名称必须唯一，不能重复</span>
              </div>
              <div class="help-warning">
                <span class="warning-icon">⚠️</span>
                <span class="warning-text">删除标签时，关联的资源标签关系也会被删除</span>
              </div>
              <div class="help-warning">
                <span class="warning-icon">⚠️</span>
                <span class="warning-text">合并标签操作不可恢复，请谨慎操作</span>
              </div>
              <div class="help-warning">
                <span class="warning-icon">💡</span>
                <span class="warning-text">建议定期清理未使用的标签，保持标签体系整洁</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-modal>

  </a-space>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, h } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { tagService } from '@/services/tagService'
import {
  PlusOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  CopyOutlined,
  MergeCellsOutlined,
  QuestionCircleOutlined,
  TagOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  FireOutlined,
  CheckOutlined
} from '@ant-design/icons-vue'

// 状态
const loading = ref(false)
const adding = ref(false)
const updating = ref(false)
const merging = ref(false)
const searchText = ref('')
const sortBy = ref('name')
const currentPage = ref(1)
const pageSize = ref(12)

const showEditModal = ref(false)
const showMergeModal = ref(false)
const showHelpModal = ref(false)
const showAddForm = ref(false)
const selectedTags = ref<string[]>([])

// 预定义颜色调色板 - 16种高对比度颜色
const colorPalette = [
  '#1890ff', '#52c41a', '#faad14', '#f5222d',
  '#722ed1', '#13c2c2', '#eb2f96', '#fa541c',
  '#2f54eb', '#a0d911', '#fadb14', '#ff4d4f',
  '#9254de', '#36cfc9', '#ff85c0', '#ffa940'
]

// 数据
const tags = ref<any[]>([])
const editingTag = reactive({
  id: '',
  name: '',
  color: '#1677ff'
})

const mergeTagName = ref('')
const mergeTagColor = ref('#1677ff')

// 新标签表单
const newTag = reactive({
  name: '',
  color: '#1677ff'
})

// 表单验证规则
const tagRules = {
  name: [
    { required: true, message: '请输入标签名称', trigger: 'blur' },
    { min: 1, max: 20, message: '标签名称长度应为1-20个字符', trigger: 'blur' }
  ],
  color: [
    { required: true, message: '请选择标签颜色', trigger: 'blur' }
  ]
}

// 合并表单验证规则
const mergeTagRules = {
  name: [
    { required: true, message: '请输入合并后的标签名称', trigger: 'blur' },
    { min: 1, max: 20, message: '标签名称长度应为1-20个字符', trigger: 'blur' }
  ],
  color: [
    { required: true, message: '请选择标签颜色', trigger: 'blur' }
  ]
}

// 计算属性
const tagStats = computed(() => {
  const total = tags.value.length
  const used = tags.value.filter(tag => (tag.resource_count || 0) > 0).length
  const unused = total - used
  const mostUsed = tags.value.reduce((max, tag) => {
    const maxCount = max?.resource_count || 0
    const tagCount = tag.resource_count || 0
    return tagCount > maxCount ? tag : max
  }, null)

  return {
    total,
    used,
    unused,
    mostUsed: mostUsed && (mostUsed.resource_count || 0) > 0 ? mostUsed : null
  }
})

const filteredTags = computed(() => {
  let filtered = tags.value

  if (searchText.value) {
    filtered = filtered.filter(tag =>
      tag.name.toLowerCase().includes(searchText.value.toLowerCase()) ||
      (tag.description && tag.description.toLowerCase().includes(searchText.value.toLowerCase()))
    )
  }

  // 排序
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'count':
        return b.resource_count - a.resource_count
      case 'created':
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      default:
        return a.name.localeCompare(b.name)
    }
  })

  return filtered
})

const totalTags = computed(() => filteredTags.value.length)

// 智能颜色分配相关方法
const isColorUsed = (color: string) => {
  return tags.value.some(tag => tag.color === color)
}

const getNextAvailableColor = () => {
  // 找到第一个未使用的颜色
  for (const color of colorPalette) {
    if (!isColorUsed(color)) {
      return color
    }
  }
  // 如果所有预定义颜色都被使用，返回随机颜色
  return `#${Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')}`
}

const selectColor = (color: string) => {
  newTag.color = color
}

const handleNameInput = () => {
  // 当用户输入名称时，如果当前颜色是默认颜色，自动分配一个可用颜色
  if (newTag.color === '#1677ff' && newTag.name.trim()) {
    newTag.color = getNextAvailableColor()
  }
}

const handleColorChange = () => {
  // 颜色变化时的处理逻辑（如果需要）
}

// 方法
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

const getTagById = (id: string) => {
  return tags.value.find(tag => tag.id === id)
}

const toggleTagSelection = (tagId: string) => {
  const index = selectedTags.value.indexOf(tagId)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else {
    selectedTags.value.push(tagId)
  }
}

const clearSelection = () => {
  selectedTags.value = []
}

// 加载标签数据
const loadTags = async () => {
  try {
    loading.value = true
    tags.value = await tagService.getAllTags()
  } catch (error: any) {
    message.error('加载标签数据失败!')
    console.error('Load tags error:', error)
  } finally {
    loading.value = false
  }
}

const handleAddTag = async () => {
  adding.value = true
  try {
    await tagService.createTag({
      name: newTag.name,
      color: newTag.color
    })

    message.success('标签添加成功!')
    resetForm()
    await loadTags() // 重新加载标签列表
  } catch (error: any) {
    message.error(error.message || '标签添加失败!')
  } finally {
    adding.value = false
  }
}

const resetForm = () => {
  newTag.name = ''
  newTag.color = getNextAvailableColor()
}

const handleEditTag = (tag: any) => {
  editingTag.id = tag.id
  editingTag.name = tag.name
  editingTag.color = tag.color
  showEditModal.value = true
}

const handleUpdateTag = async () => {
  updating.value = true
  try {
    await tagService.updateTag(Number(editingTag.id), {
      name: editingTag.name,
      color: editingTag.color
    })

    message.success('标签更新成功!')
    resetEditForm()
    await loadTags() // 重新加载标签列表
  } catch (error: any) {
    message.error(error.message || '标签更新失败!')
  } finally {
    updating.value = false
  }
}

const resetEditForm = () => {
  showEditModal.value = false
  editingTag.id = ''
  editingTag.name = ''
  editingTag.color = '#1677ff'
}

const handleDeleteTag = (tag: any) => {
  const resourceCount = tag.resource_count || 0
  const title = '确认删除标签'
  let content = `确定要删除标签"${tag.name}"吗？`

  if (resourceCount > 0) {
    content += `\n\n⚠️ 警告：此标签被 ${resourceCount} 个资源使用，删除后这些关联关系将被永久移除。`
  }

  Modal.confirm({
    title,
    content,
    icon: h(ExclamationCircleOutlined, { style: { color: '#faad14' } }),
    okText: '确认删除',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      try {
        await tagService.deleteTag(Number(tag.id))
        message.success('标签删除成功!')
        await loadTags() // 重新加载标签列表
      } catch (error: any) {
        message.error(error.message || '标签删除失败!')
      }
    }
  })
}

const handleDuplicateTag = async (tag: any) => {
  const duplicated = {
    ...tag,
    id: Date.now().toString(),
    name: `${tag.name} (副本)`,
    resource_count: 0,
    created_at: new Date().toISOString()
  }

  tags.value.push(duplicated)
  message.success('标签复制成功!')
}

const handleBatchDelete = () => {
  const selectedCount = selectedTags.value.length
  const selectedTagObjects = tags.value.filter(tag => selectedTags.value.includes(tag.id))
  const totalResourceCount = selectedTagObjects.reduce((sum, tag) => sum + (tag.resource_count || 0), 0)

  const title = '确认批量删除标签'
  let content = `确定要删除选中的 ${selectedCount} 个标签吗？`

  if (totalResourceCount > 0) {
    content += `\n\n⚠️ 警告：这些标签共被 ${totalResourceCount} 个资源使用，删除后所有关联关系将被永久移除。`
  }

  Modal.confirm({
    title,
    content,
    icon: h(ExclamationCircleOutlined, { style: { color: '#faad14' } }),
    okText: '确认删除',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 批量删除标签
        for (const tagId of selectedTags.value) {
          await tagService.deleteTag(Number(tagId))
        }
        message.success(`已删除 ${selectedCount} 个标签`)
        clearSelection()
        await loadTags() // 重新加载标签列表
      } catch (error: any) {
        message.error(error.message || '批量删除失败!')
      }
    }
  })
}

const handleBatchMerge = () => {
  if (selectedTags.value.length < 2) {
    message.warning('请至少选择2个标签进行合并')
    return
  }
  showMergeModal.value = true
}

// 表单提交处理
const handleMergeFormSubmit = () => {
  // 验证表单
  if (!mergeTagName.value.trim()) {
    message.error('请输入合并后的标签名称')
    return
  }

  // 显示确认对话框
  showMergeConfirmDialog()
}

// 显示合并确认对话框
const showMergeConfirmDialog = () => {
  const selectedCount = selectedTags.value.length
  const selectedTagObjects = tags.value.filter(tag => selectedTags.value.includes(tag.id))
  const totalResourceCount = selectedTagObjects.reduce((sum, tag) => sum + (tag.resource_count || 0), 0)

  const title = '确认合并标签'
  let content = `确定要将 ${selectedCount} 个标签合并为"${mergeTagName.value}"吗？`

  if (totalResourceCount > 0) {
    content += `\n\n📊 合并详情：\n• 将删除其他 ${selectedCount - 1} 个标签\n• 将 ${totalResourceCount} 个资源关联转移到目标标签\n• 此操作不可撤销`
  }

  Modal.confirm({
    title,
    content,
    icon: h(ExclamationCircleOutlined, { style: { color: '#1890ff' } }),
    okText: '确认合并',
    okType: 'primary',
    cancelText: '取消',
    onOk: async () => {
      await executeMerge()
    }
  })
}

// 执行合并操作
const executeMerge = async () => {
  merging.value = true
  try {
    // 创建合并后的标签
    const mergedTagId = await tagService.createTag({
      name: mergeTagName.value,
      color: mergeTagColor.value
    })

    // 将所有选中的标签合并到新创建的标签
    for (const tagId of selectedTags.value) {
      await tagService.mergeTags(Number(tagId), Number(mergedTagId))
    }

    message.success('标签合并成功!')
    resetMergeForm()
    clearSelection()
    await loadTags() // 重新加载标签列表
  } catch (error: any) {
    message.error(error.message || '标签合并失败!')
  } finally {
    merging.value = false
  }
}

const resetMergeForm = () => {
  showMergeModal.value = false
  mergeTagName.value = ''
  mergeTagColor.value = '#1677ff'
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

const handleSort = () => {
  // 排序逻辑已在计算属性中实现
}

const handleRefresh = async () => {
  await loadTags()
  message.success('数据刷新成功!')
}

onMounted(() => {
  loadTags()
})
</script>

<style scoped>
/* 标签管理卡片 */
.tag-management-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.dark .tag-management-card {
  background: #1f1f1f;
  border-color: #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 卡片标题 */
.card-title-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.dark .card-title {
  color: #f0f0f0;
}

.help-icon-btn {
  color: #8c8c8c;
  font-size: 14px;
  width: 22px;
  height: 22px;
  padding: 0;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  /* 防止图标被压缩 */
}

.help-icon-btn:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.dark .help-icon-btn {
  color: #a6a6a6;
}

.dark .help-icon-btn:hover {
  color: #40a9ff;
  background: rgba(64, 169, 255, 0.1);
}

/* 统计卡片紧凑样式 - 统一标准 */
.stat-card {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
  position: relative;
}

.stat-card:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.dark .stat-card {
  background: #1f1f1f;
  border-color: #303030;
}

.dark .stat-card:hover {
  border-color: #434343;
  box-shadow: 0 2px 6px rgba(255, 255, 255, 0.02);
}

.stat-icon {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-right: 10px;
  color: white;
}

.stat-icon-primary {
  background: #1890ff;
}

.stat-icon-success {
  background: #52c41a;
}

.stat-icon-info {
  background: #13c2c2;
}

.stat-icon-warning {
  background: #faad14;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  line-height: 1;
  margin-bottom: 2px;
}

.dark .stat-value {
  color: #fff;
}

.stat-label {
  font-size: 13px;
  color: #8c8c8c;
  font-weight: 400;
}

.dark .stat-label {
  color: #a6a6a6;
}

/* 工具栏样式 - 紧凑型统一标准 */
.toolbar-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 8px 0;
  gap: 12px;
}

.toolbar-left {
  flex: 0 0 auto;
}

.toolbar-right {
  flex: 0 0 auto;
}



/* 添加标签表单容器 - 紧凑型统一标准 */
.add-form-container {
  margin-bottom: 12px;
}

.add-tag-form {
  margin-top: 8px;
  padding: 16px;
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.dark .add-tag-form {
  background: #1f1f1f;
  border-color: #303030;
}

.form-content {
  margin: 0;
}

/* 表单输入框统一样式 */
.form-input {
  height: 36px;
  border-radius: 6px;
  font-size: 14px;
}

.form-input :deep(.ant-input) {
  height: 36px;
  border-radius: 6px;
  font-size: 14px;
}

.color-picker {
  width: 40px;
  height: 36px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  background: none;
}

.color-input {
  width: 120px;
}

/* 颜色选择器样式 */
.color-selection {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.color-palette {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 8px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.dark .color-palette {
  background: #262626;
  border-color: #303030;
}

.color-option {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative;
}

.color-option:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.color-option.selected {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.color-option.used {
  opacity: 0.6;
  position: relative;
}

.color-option.used::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2px;
  height: 20px;
  background: rgba(0, 0, 0, 0.5);
  transform: translate(-50%, -50%) rotate(45deg);
}

.check-icon {
  color: white;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.color-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 表单按钮样式 */
.form-button-primary {
  min-width: 100px;
}

.form-button-default {
  min-width: 80px;
}

/* 帮助模态框样式 */
.help-modal :deep(.ant-modal-header) {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.help-modal :deep(.ant-modal-body) {
  padding: 0;
  max-height: 70vh;
  overflow: hidden;
}

.help-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 24px;
  line-height: 1.6;
}

/* 自定义滚动条样式 */
.help-content::-webkit-scrollbar {
  width: 6px;
}

.help-content::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.help-content::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.help-content::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 帮助章节样式 - 紧凑型统一标准 */
.help-section {
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  overflow: hidden;
  transition: all 0.2s ease;
}

.help-section:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.help-section:last-child {
  margin-bottom: 0;
}

/* 章节头部 - 紧凑型统一标准 */
.help-section-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.help-icon-wrapper {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-size: 16px;
  color: white;
}

.help-icon-wrapper.overview {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.help-icon-wrapper.add {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.help-icon-wrapper.edit {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.help-icon-wrapper.batch {
  background: linear-gradient(135deg, #13c2c2, #36cfc9);
}

.help-icon-wrapper.search {
  background: linear-gradient(135deg, #722ed1, #9254de);
}

.help-icon-wrapper.warning {
  background: linear-gradient(135deg, #faad14, #ffc53d);
}

.help-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

/* 章节内容 - 紧凑型统一标准 */
.help-section-content {
  padding: 16px;
}

.help-description {
  color: #595959;
  margin: 0;
  line-height: 1.6;
}

/* 操作步骤样式 - 紧凑型统一标准 */
.help-steps {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.help-step {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 10px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
}

.step-number {
  width: 20px;
  height: 20px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
  margin-top: 1px;
}

.step-text {
  color: #262626;
  line-height: 1.5;
  font-size: 14px;
}

/* 警告信息样式 - 紧凑型统一标准 */
.help-warnings {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.help-warning {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 10px 12px;
  background: #fff7e6;
  border-radius: 6px;
  border-left: 3px solid #faad14;
}

.warning-icon {
  font-size: 16px;
  flex-shrink: 0;
  margin-top: 1px;
}

.warning-text {
  color: #262626;
  line-height: 1.5;
  font-size: 14px;
}

/* 不同主题色的步骤样式 */
.help-section-add .help-step {
  border-left-color: #52c41a;
}

.help-section-add .step-number {
  background: #52c41a;
}

.help-section-edit .help-step {
  border-left-color: #1890ff;
}

.help-section-edit .step-number {
  background: #1890ff;
}

.help-section-batch .help-step {
  border-left-color: #13c2c2;
}

.help-section-batch .step-number {
  background: #13c2c2;
}

.help-section-search .help-step {
  border-left-color: #722ed1;
}

.help-section-search .step-number {
  background: #722ed1;
}

/* 暗黑模式下的帮助模态框 */
.dark .help-modal :deep(.ant-modal-header) {
  border-bottom-color: #303030;
  background: #1f1f1f;
}

.dark .help-modal :deep(.ant-modal-content) {
  background: #1f1f1f;
}

.dark .help-content::-webkit-scrollbar-track {
  background: #262626;
}

.dark .help-content::-webkit-scrollbar-thumb {
  background: #434343;
}

.dark .help-content::-webkit-scrollbar-thumb:hover {
  background: #595959;
}

.dark .help-section {
  border-color: #303030;
}

.dark .help-section:hover {
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.04);
}

.dark .help-section-header {
  background: #262626;
  border-bottom-color: #303030;
}

.dark .help-section-title {
  color: #fff;
}

.dark .help-description {
  color: #a6a6a6;
}

.dark .help-step {
  background: #262626;
}

.dark .step-text {
  color: #a6a6a6;
}

.dark .help-warning {
  background: #2a2a2a;
}

.dark .warning-text {
  color: #a6a6a6;
}

/* 批量合并模态框样式 */
.merge-info-section {
  margin-bottom: 24px;
}

.merge-tags-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.dark .merge-tags-list {
  background: #262626;
  border-color: #303030;
}

.merge-tag-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.dark .merge-tag-item {
  background: #1f1f1f;
  border-color: #303030;
}

.merge-tag {
  margin: 0;
}

.tag-resource-count {
  font-size: 12px;
  color: #8c8c8c;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
}

.dark .tag-resource-count {
  color: #a6a6a6;
  background: #434343;
}

.merge-summary {
  margin-top: 16px;
}
</style>
