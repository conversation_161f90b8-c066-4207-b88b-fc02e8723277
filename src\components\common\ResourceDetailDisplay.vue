<template>
  <div class="resource-detail-main">
    <!-- 卡片式布局 -->
    <a-space direction="vertical" size="middle" style="width: 100%">

      <!-- 资源基本信息卡片 -->
      <a-card size="small" class="resource-info-card">
        <template #title>
          <div class="card-title-wrapper">
            <span class="card-title">
              <FileTextOutlined class="title-icon" />
              资源信息
            </span>
          </div>
        </template>
        <template #extra v-if="showActions">
          <!-- 操作按钮 -->
          <a-dropdown placement="bottomRight">
            <template #overlay>
              <a-menu>
                <a-menu-item key="edit" @click="$emit('edit')">
                  <EditOutlined />
                  编辑
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="delete" @click="$emit('delete')" class="danger-item">
                  <DeleteOutlined />
                  删除
                </a-menu-item>
              </a-menu>
            </template>
            <a-button type="text">
              <template #icon>
                <MoreOutlined />
              </template>
            </a-button>
          </a-dropdown>
        </template>

        <a-row :gutter="24">
          <!-- 左侧：封面图片 -->
          <a-col :span="8">
            <div class="resource-cover">
              <div v-if="resource.cover_image_url" class="cover-image"
                :style="{ backgroundImage: `url(${resource.cover_image_url})` }">
              </div>
              <div v-else class="cover-placeholder">
                <FileTextOutlined class="placeholder-icon" />
              </div>
            </div>
          </a-col>

          <!-- 右侧：基本信息 -->
          <a-col :span="16">
            <div class="resource-details">
              <!-- 标题 -->
              <h1 class="resource-title">{{ resource.title }}</h1>

              <!-- 元信息 -->
              <div class="resource-meta">
                <div class="meta-item">
                  <EyeOutlined />
                  <span>{{ resource.view_count || 0 }} 次浏览</span>
                </div>
                <div class="meta-item">
                  <ClockCircleOutlined />
                  <span>{{ formatDate(resource.created_at || new Date()) }}</span>
                </div>
                <div v-if="resource.updated_at && resource.updated_at !== resource.created_at" class="meta-item">
                  <SyncOutlined />
                  <span>更新于 {{ formatDate(resource.updated_at) }}</span>
                </div>
              </div>

              <!-- 分类和标签 -->
              <div class="resource-tags-section">
                <div class="tags-row">
                  <span class="tags-label">分类：</span>
                  <a-tag v-if="resource.category" color="blue" class="category-tag">
                    <FolderOutlined />
                    {{ resource.category.name }}
                  </a-tag>
                  <span v-else class="no-category">未分类</span>
                </div>

                <div v-if="resource.tags && resource.tags.length > 0" class="tags-row">
                  <span class="tags-label">标签：</span>
                  <div class="tags-list">
                    <div v-for="tag in resource.tags" :key="tag.id" class="detail-tag" :style="getDetailTagStyle(tag)">
                      <span class="tag-name">{{ tag.name }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 资源链接 -->
              <div class="resource-url">
                <LinkOutlined />
                <a :href="resource.url" target="_blank" class="url-link">
                  {{ formatUrl(resource.url) }}
                  <span class="url-actions">
                    <a-tooltip title="访问资源">
                      <ExportOutlined class="action-icon" @click.prevent="openResource" />
                    </a-tooltip>
                    <a-tooltip title="复制链接">
                      <CopyOutlined class="action-icon" @click.prevent="copyUrl" />
                    </a-tooltip>
                  </span>
                </a>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>

      <!-- 描述内容卡片 -->
      <a-card size="small" class="description-card">
        <template #title>
          <div class="card-title-wrapper">
            <span class="card-title">
              <FileTextOutlined class="title-icon" />
              描述内容
            </span>
          </div>
        </template>

        <div class="description-content">
          <div v-if="resource.description" class="markdown-content">
            <MarkdownPreview :content="resource.description" />
          </div>
          <div v-else class="description-empty">
            <FileTextOutlined class="empty-icon" />
            <p>暂无描述内容</p>
          </div>
        </div>
      </a-card>

      <!-- 相关资源推荐卡片（仅在详情页显示） -->
      <a-card v-if="showRelatedResources && relatedResources && relatedResources.length > 0" size="small"
        class="related-resources-card">
        <template #title>
          <div class="card-title-wrapper">
            <span class="card-title">
              <AppstoreOutlined class="title-icon" />
              相关资源
            </span>
          </div>
        </template>

        <div class="related-resources-grid">
          <div v-for="relatedResource in relatedResources" :key="relatedResource.id" class="related-resource-item">
            <a-card size="small" hoverable @click="$emit('goToResource', relatedResource.id)">
              <div class="related-resource-content">
                <h4 class="related-resource-title">{{ relatedResource.title }}</h4>
                <p class="related-resource-desc">{{ relatedResource.description || '暂无描述' }}</p>
                <div class="related-resource-meta">
                  <a-tag v-if="relatedResource.category" color="blue" size="small">
                    {{ relatedResource.category.name }}
                  </a-tag>
                  <span class="view-count">
                    <EyeOutlined />
                    {{ relatedResource.view_count }}
                  </span>
                </div>
              </div>
            </a-card>
          </div>
        </div>
      </a-card>

    </a-space>
  </div>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  FileTextOutlined,
  ExportOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  SyncOutlined,
  FolderOutlined,
  LinkOutlined,
  CopyOutlined,
  AppstoreOutlined
} from '@ant-design/icons-vue'
import MarkdownPreview from '@/components/common/MarkdownPreview.vue'

// Props
interface Props {
  resource: any
  showActions?: boolean
  showRelatedResources?: boolean
  relatedResources?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  showActions: false,
  showRelatedResources: false,
  relatedResources: () => []
})

// Emits
defineEmits<{
  edit: []
  delete: []
  goToResource: [id: number]
}>()

// 格式化日期
const formatDate = (date: string | Date) => {
  return dayjs(date).format('YYYY年MM月DD日')
}

// 格式化 URL 显示
const formatUrl = (url: string) => {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname
  } catch {
    return url
  }
}

// 获取详情页标签样式
const getDetailTagStyle = (tag: any) => {
  const tagColor = tag.color || '#1677ff'

  return {
    backgroundColor: 'transparent',
    borderColor: tagColor,
    color: tagColor
  }
}

// 打开资源链接
const openResource = () => {
  if (props.resource?.url) {
    window.open(props.resource.url, '_blank')
  }
}

// 复制链接到剪贴板
const copyUrl = async () => {
  if (!props.resource?.url) return

  try {
    await navigator.clipboard.writeText(props.resource.url)
    message.success('链接已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    // 降级方案：使用传统方法复制
    try {
      const textArea = document.createElement('textarea')
      textArea.value = props.resource.url
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      message.success('链接已复制到剪贴板')
    } catch (fallbackError) {
      console.error('降级复制也失败:', fallbackError)
      message.error('复制失败，请手动复制链接')
    }
  }
}
</script>

<style scoped>
/* 复用 ResourceDetailView 的样式 */
.resource-detail-main {
  max-width: 1200px;
  margin: 0 auto;
}

/* 卡片标题样式 */
.card-title-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--ant-color-text);
}

.title-icon {
  font-size: 16px;
  color: var(--ant-color-primary);
}

/* 卡片样式 */
.resource-info-card,
.description-card,
.related-resources-card {
  border: 1px solid var(--ant-color-border);
  border-radius: 8px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  transition: all 0.2s;
}

.resource-info-card:hover,
.description-card:hover,
.related-resources-card:hover {
  border-color: var(--ant-color-border-secondary);
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);
}

/* 资源封面 */
.resource-cover {
  width: 100%;
  height: 160px;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid var(--ant-color-border);
}

.cover-image {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--ant-color-bg-layout);
  color: var(--ant-color-text-tertiary);
}

.placeholder-icon {
  font-size: 48px;
  opacity: 0.3;
}

/* 资源详情 */
.resource-details {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.resource-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--ant-color-text);
  margin: 0;
  line-height: 1.3;
}

.resource-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--ant-color-text-secondary);
  font-size: 13px;
}

.meta-item .anticon {
  font-size: 14px;
}

/* 标签区域 */
.resource-tags-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tags-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.tags-label {
  font-weight: 500;
  color: var(--ant-color-text-secondary);
  white-space: nowrap;
  font-size: 13px;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.category-tag {
  margin: 0;
}

.no-category {
  color: var(--ant-color-text-tertiary);
  font-size: 13px;
}

/* 详情页紧凑型标签样式 */
.detail-tag {
  display: inline-flex;
  align-items: center;
  padding: 3px 8px;
  border: 1px solid;
  border-radius: 10px;
  font-size: 12px;
  user-select: none;
  white-space: nowrap;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.detail-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.detail-tag .tag-name {
  font-weight: 500;
  line-height: 1.2;
}

/* 资源链接 */
.resource-url {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: auto;
}

.url-link {
  color: var(--ant-color-primary);
  text-decoration: none;
  font-size: 13px;
  flex: 1;
  display: flex;
  align-items: center;
  gap: 4px;
}

.url-link:hover {
  text-decoration: underline;
}

/* 链接内的操作图标 */
.url-actions {
  display: inline-flex;
  align-items: center;
  gap: 3px;
  margin-left: 4px;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.url-link:hover .url-actions {
  opacity: 1;
}

.action-icon {
  font-size: 10px !important;
  color: var(--ant-color-text-tertiary);
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-icon:hover {
  color: var(--ant-color-primary);
  background: var(--ant-color-primary-bg);
  transform: scale(1.1);
}

/* 描述内容样式 */
.description-content {
  min-height: 200px;
}

.markdown-content {
  padding: 8px 0;
}

.description-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  color: var(--ant-color-text-tertiary);
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.3;
}

/* 相关资源样式 */
.related-resources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.related-resource-item {
  cursor: pointer;
}

.related-resource-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.related-resource-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--ant-color-text);
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.related-resource-desc {
  font-size: 13px;
  color: var(--ant-color-text-secondary);
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

.related-resource-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
}

.view-count {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--ant-color-text-tertiary);
  font-size: 12px;
}

/* 危险操作样式 */
.danger-item {
  color: var(--ant-color-error) !important;
}

.danger-item:hover {
  background: var(--ant-color-error-bg) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .resource-info-card .ant-row {
    flex-direction: column;
  }

  .resource-cover {
    height: 200px;
  }

  .resource-title {
    font-size: 18px;
  }

  .related-resources-grid {
    grid-template-columns: 1fr;
  }

  .resource-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .tags-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .card-title-wrapper {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .resource-title {
    font-size: 16px;
  }
}
</style>
