<template>
  <!-- 知识库页面 - 重构版本 -->
  <div class="knowledge-view-container">
    <!-- 紧凑型功能区 -->
    <a-card class="function-card" size="small">
      <div class="function-content">
        <!-- 第一行：筛选控件和操作按钮 -->
        <div class="function-row function-controls">
          <!-- 左侧筛选区 -->
          <div class="filter-controls">
            <!-- 分类筛选 -->
            <div class="filter-item">
              <span class="filter-label">分类：</span>
              <a-tree-select 
                v-model:value="selectedCategoryId" 
                :tree-data="categoryTreeData"
                placeholder="选择分类" 
                allow-clear 
                class="category-selector" 
                :popup-match-select-width="true"
                @change="handleCategoryChange" 
              />
            </div>

            <!-- 排序筛选 -->
            <div class="filter-item">
              <span class="filter-label">排序：</span>
              <a-select 
                v-model:value="currentSortKey" 
                @change="handleSortChange" 
                class="sort-selector"
                placeholder="排序方式"
              >
                <a-select-option 
                  v-for="option in sortOptions" 
                  :key="option.key" 
                  :value="option.key"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </div>
          </div>

          <!-- 右侧操作区 -->
          <div class="action-controls">
            <a-button 
              v-if="hasActiveFilters" 
              size="small" 
              @click="clearFilters"
              class="clear-filters-btn"
            >
              <template #icon>
                <ClearOutlined />
              </template>
              清除筛选
            </a-button>

            <a-button type="primary" @click="$router.push('/knowledge/create')" class="add-btn">
              <template #icon>
                <PlusOutlined />
              </template>
              添加资源
            </a-button>
          </div>
        </div>

        <!-- 第二行：标签筛选区 -->
        <div class="function-row tags-section">
          <div class="tags-container">
            <div class="tags-header">
              <span class="tags-title">热门标签：</span>
            </div>
            <div class="tags-content">
              <div class="tags-list">
                <div 
                  v-for="tag in displayedTags" 
                  :key="tag.id"
                  :class="['modern-tag', { 'modern-tag-selected': isTagSelected(tag.id || 0) }]"
                  :style="getTagStyle(tag, isTagSelected(tag.id || 0))" 
                  @click="toggleTag(tag)"
                >
                  <span class="tag-name">{{ tag.name }}</span>
                  <span class="tag-count">({{ tag.resource_count }})</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 资源列表区域 -->
    <div class="resources-section">
      <!-- 统计信息 -->
      <div class="resources-header">
        <div class="resources-stats">
          <span class="stats-text">
            共找到 <strong>{{ totalResources }}</strong> 个资源
            <span v-if="hasActiveFilters" class="filter-summary">
              （{{ filterSummary }}）
            </span>
          </span>
        </div>
      </div>

      <!-- 虚拟滚动资源网格 -->
      <VirtualResourceGrid
        :items="resources"
        :item-height="280"
        :columns-count="3"
        :gap="16"
        :container-height="800"
        :has-more="hasMore"
        :is-loading="loading"
        @load-more="loadMore"
      >
        <template #default="{ item }">
          <ResourceCard 
            :resource="item"
            :highlight="highlightResourceId === item.id?.toString()"
            @click="goToResource(item.id!)"
          />
        </template>

        <template #empty>
          <a-empty description="暂无资源">
            <template #image>
              <FileTextOutlined style="font-size: 48px; color: var(--ant-color-text-tertiary);" />
            </template>
            <a-button type="primary" @click="$router.push('/knowledge/create')">
              <template #icon>
                <PlusOutlined />
              </template>
              添加第一个资源
            </a-button>
          </a-empty>
        </template>
      </VirtualResourceGrid>
    </div>

    <!-- 回到顶部按钮 -->
    <a-back-top :visibility-height="300" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  PlusOutlined,
  ClearOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'
import ResourceCard from '@/components/knowledge/ResourceCard.vue'
import VirtualResourceGrid from '@/components/common/VirtualResourceGrid.vue'
import { knowledgeSettingsService, type KnowledgeSettings } from '@/services/knowledgeSettingsService'
import { useResourceList } from '@/composables/useResourceList'
import { useFilters } from '@/composables/useFilters'

const router = useRouter()

// 知识库设置
const knowledgeSettings = ref<KnowledgeSettings>(knowledgeSettingsService.getSettings())

// 使用新的 Composables
const {
  selectedCategoryId,
  selectedTags,
  currentSortKey,
  categories,
  categoryTree,
  popularTags,
  hasActiveFilters,
  filterSummary,
  sortOptions,
  setCategory,
  toggleTag,
  setSortKey,
  clearFilters,
  loadCategories,
  loadTags,
  syncFromUrl
} = useFilters({ syncWithUrl: true })

const {
  resources,
  total: totalResources,
  isLoading: loading,
  hasMore,
  loadMore,
  refresh
} = useResourceList({
  pageSize: computed(() => knowledgeSettings.value.loading.pageSize),
  enableInfiniteScroll: computed(() => knowledgeSettings.value.loading.type === 'infinite')
})

// 本地状态
const highlightResourceId = ref<string | null>(null)

// 分类树数据转换
const categoryTreeData = computed(() => {
  const convertToTreeData = (categories: any[]): any[] => {
    return categories.map(category => ({
      title: category.name,
      value: category.id,
      key: category.id,
      children: category.children && category.children.length > 0 
        ? convertToTreeData(category.children) 
        : undefined
    }))
  }
  
  return convertToTreeData(categoryTree.value)
})

// 显示的标签（限制数量）
const displayedTags = computed(() => {
  const initialLoad = knowledgeSettings.value.tags.initialLoad
  return popularTags.value.slice(0, initialLoad)
})

// 辅助方法
const isTagSelected = (tagId: number) => {
  return selectedTags.value.some(tag => tag.id === tagId)
}

const getTagStyle = (tag: any, isSelected: boolean) => {
  const tagColor = tag.color || '#1677ff'
  
  if (isSelected) {
    return {
      backgroundColor: tagColor,
      borderColor: tagColor,
      color: '#ffffff'
    }
  } else {
    return {
      backgroundColor: 'transparent',
      borderColor: tagColor,
      color: tagColor
    }
  }
}

const handleCategoryChange = (categoryId: number | null) => {
  const category = categories.value.find(cat => cat.id === categoryId) || null
  setCategory(categoryId, category)
}

const handleSortChange = (sortKey: string) => {
  setSortKey(sortKey)
}

const goToResource = (resourceId: number) => {
  router.push(`/knowledge/${resourceId}`)
}

// 初始化
onMounted(async () => {
  try {
    await Promise.all([
      loadCategories(),
      loadTags()
    ])

    // 处理URL参数
    await syncFromUrl()
  } catch (error) {
    console.error('初始化失败:', error)
  }
})
</script>

<style scoped>
/* 复用原有的样式，这里只列出关键样式 */
.knowledge-view-container {
  padding: 16px;
  background: var(--ant-color-bg-layout);
  min-height: 100vh;
}

.function-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.function-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.function-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 12px;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 13px;
  color: var(--ant-color-text-secondary);
  white-space: nowrap;
}

.category-selector,
.sort-selector {
  min-width: 160px;
}

.action-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tags-section {
  flex-direction: column;
  align-items: flex-start;
}

.tags-container {
  width: 100%;
}

.tags-header {
  margin-bottom: 8px;
}

.tags-title {
  font-size: 13px;
  color: var(--ant-color-text-secondary);
  font-weight: 500;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.modern-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: 1px solid;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 12px;
  user-select: none;
  white-space: nowrap;
}

.modern-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tag-name {
  font-weight: 500;
}

.tag-count {
  opacity: 0.7;
  font-size: 11px;
}

.resources-section {
  background: var(--ant-color-bg-container);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.resources-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--ant-color-border);
}

.resources-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stats-text {
  color: var(--ant-color-text-secondary);
  font-size: 14px;
}

.filter-summary {
  color: var(--ant-color-primary);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .function-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-controls {
    justify-content: space-between;
  }
  
  .action-controls {
    justify-content: center;
  }
}
</style>
