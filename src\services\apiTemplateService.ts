import type { GenericApiConfig, ApiParameter, ResponseMapping } from '@/types'

/**
 * API配置模板服务
 * 提供常用免费API的预设配置模板
 */
class ApiTemplateService {
  
  /**
   * 获取所有可用的API模板
   */
  getAvailableTemplates(): Array<{ id: string; name: string; description: string; category: string }> {
    return [
      {
        id: 'doubao-deep-think',
        name: '豆包AI深度思考',
        description: '豆包AI深度思考接口，支持文本对话和深度思考模式',
        category: '文本对话'
      },
      {
        id: 'ai-chat-multi-model',
        name: 'AI聚合聊天',
        description: '支持多模型的AI聊天接口，包含角色扮演功能',
        category: '文本对话'
      },
      {
        id: 'baidu-ai-painting',
        name: '百度AI绘画',
        description: '百度AI绘画接口，支持多种绘画风格',
        category: '图像生成'
      }
    ]
  }

  /**
   * 根据模板ID获取配置
   */
  getTemplateConfig(templateId: string): Omit<GenericApiConfig, 'id' | 'createdAt' | 'updatedAt'> | null {
    switch (templateId) {
      case 'doubao-deep-think':
        return this.getDoubaoDeepThinkTemplate()
      case 'ai-chat-multi-model':
        return this.getAiChatMultiModelTemplate()
      case 'baidu-ai-painting':
        return this.getBaiduAiPaintingTemplate()
      default:
        return null
    }
  }

  /**
   * 豆包AI深度思考模板
   */
  private getDoubaoDeepThinkTemplate(): Omit<GenericApiConfig, 'id' | 'createdAt' | 'updatedAt'> {
    const parameters: ApiParameter[] = [
      {
        id: 'param_text',
        name: 'text',
        label: '问题内容',
        type: 'string',
        location: 'query',
        required: true,
        defaultValue: '',
        description: '输入的问题或对话内容'
      },
      {
        id: 'param_use_deep_think',
        name: 'use_deep_think',
        label: '启用深度思考',
        type: 'boolean',
        location: 'query',
        required: false,
        defaultValue: false,
        description: '是否启用深度思考模式，true为启用，false或空为不启用'
      },
      {
        id: 'param_n',
        name: 'n',
        label: '结果数量',
        type: 'number',
        location: 'query',
        required: false,
        defaultValue: 4,
        description: '用于控制绘画、视频、表情及图片的结果数量'
      }
    ]

    const responseMapping: ResponseMapping[] = [
      {
        id: 'mapping_code',
        sourceField: 'code',
        targetField: 'status_code',
        dataType: 'number',
        description: '响应状态码'
      },
      {
        id: 'mapping_text',
        sourceField: 'text',
        targetField: 'content',
        dataType: 'string',
        description: 'AI回复的文本内容'
      },
      {
        id: 'mapping_think',
        sourceField: 'think',
        targetField: 'thinking_process',
        dataType: 'string',
        description: '深度思考过程'
      },
      {
        id: 'mapping_images',
        sourceField: 'img_urls',
        targetField: 'images',
        dataType: 'array',
        description: '生成的图片URL列表'
      }
    ]

    return {
      name: '豆包AI深度思考',
      description: '调用豆包接口进行深度思考并返回内容，集成AI绘画、搜索视频、搜索图片、连续对话功能',
      method: 'GET',
      endpoint: 'http://missqiu.cnunon.cn/api/doubaoDeepThink',
      parameters,
      responseMapping,
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000,
      retryCount: 3
    }
  }

  /**
   * AI聚合聊天多模型模板
   */
  private getAiChatMultiModelTemplate(): Omit<GenericApiConfig, 'id' | 'createdAt' | 'updatedAt'> {
    const parameters: ApiParameter[] = [
      {
        id: 'param_content',
        name: 'content',
        label: '对话内容',
        type: 'string',
        location: 'query',
        required: true,
        defaultValue: '',
        description: '输入的问题或对话内容'
      },
      {
        id: 'param_id',
        name: 'id',
        label: '对话ID',
        type: 'string',
        location: 'query',
        required: false,
        defaultValue: '',
        description: '对话ID，用于区分不同的对话会话'
      },
      {
        id: 'param_system',
        name: 'system',
        label: '角色设定',
        type: 'string',
        location: 'query',
        required: false,
        defaultValue: '',
        description: '要扮演的角色或系统提示词'
      },
      {
        id: 'param_model',
        name: 'model',
        label: 'AI模型',
        type: 'string',
        location: 'query',
        required: false,
        defaultValue: 'gpt-3.5-turbo-0301',
        description: 'AI模型名称，不填默认使用gpt-3.5-turbo-0301',
        options: [
          { label: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo-0301' },
          { label: 'GPT-4o Mini', value: 'gpt-4o-mini' },
          { label: 'DeepSeek R1', value: 'deepseek-r1-distill-llama-70b' },
          { label: 'Qwen QwQ 32B', value: 'qwen-qwq-32b' },
          { label: 'Llama 3.3 70B', value: 'llama-3.3-70b' },
          { label: 'Gemini 2.0 Flash', value: 'gemini-2.0-flash-lite' },
          { label: 'GLM-4 9B', value: 'THUDM/GLM-4-9B-0414' }
        ]
      }
    ]

    const responseMapping: ResponseMapping[] = [
      {
        id: 'mapping_response',
        sourceField: '',
        targetField: 'content',
        dataType: 'string',
        description: 'AI回复内容（直接返回文本）'
      }
    ]

    return {
      name: 'AI聚合聊天多模型',
      description: '集聚大模型AI调用，支持多模型切换和AI角色扮演，支持OpenAI格式请求',
      method: 'GET',
      endpoint: 'https://missqiu.cnunon.cn/api/aichat',
      parameters,
      responseMapping,
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000,
      retryCount: 3
    }
  }

  /**
   * 百度AI绘画模板
   */
  private getBaiduAiPaintingTemplate(): Omit<GenericApiConfig, 'id' | 'createdAt' | 'updatedAt'> {
    const parameters: ApiParameter[] = [
      {
        id: 'param_prompt',
        name: 'prompt',
        label: '绘画描述',
        type: 'string',
        location: 'query',
        required: true,
        defaultValue: '',
        description: '绘画的描述词，描述想要生成的图片内容'
      },
      {
        id: 'param_type',
        name: 'type',
        label: '绘画风格',
        type: 'string',
        location: 'query',
        required: false,
        defaultValue: '二次元',
        description: '绘画风格类型',
        options: [
          { label: '二次元', value: '二次元' },
          { label: '3D渲染', value: '3D渲染' },
          { label: '写实风格', value: '写实风格' },
          { label: '油画风格', value: '油画风格' },
          { label: '水彩风格', value: '水彩风格' },
          { label: '素描风格', value: '素描风格' }
        ]
      }
    ]

    const responseMapping: ResponseMapping[] = [
      {
        id: 'mapping_status',
        sourceField: 'status',
        targetField: 'status',
        dataType: 'string',
        description: '生成状态'
      },
      {
        id: 'mapping_progress',
        sourceField: 'progress',
        targetField: 'progress',
        dataType: 'number',
        description: '生成进度'
      },
      {
        id: 'mapping_image_url',
        sourceField: 'image_url',
        targetField: 'image_url',
        dataType: 'string',
        description: '生成的图片URL'
      },
      {
        id: 'mapping_prompt',
        sourceField: 'prompt',
        targetField: 'used_prompt',
        dataType: 'string',
        description: '实际使用的提示词'
      }
    ]

    return {
      name: '百度AI绘画',
      description: '调用百度网页提供的AI绘画模型进行AI绘画，支持多种绘画风格',
      method: 'GET',
      endpoint: 'https://missqiu.cnunon.cn/api/baiduaihuiHua',
      parameters,
      responseMapping,
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 60000, // 绘画可能需要更长时间
      retryCount: 2
    }
  }

  /**
   * 根据分类获取模板
   */
  getTemplatesByCategory(category: string): Array<{ id: string; name: string; description: string }> {
    return this.getAvailableTemplates()
      .filter(template => template.category === category)
      .map(({ id, name, description }) => ({ id, name, description }))
  }

  /**
   * 获取所有分类
   */
  getCategories(): string[] {
    const categories = new Set(this.getAvailableTemplates().map(template => template.category))
    return Array.from(categories)
  }
}

// 导出单例实例
export const apiTemplateService = new ApiTemplateService()
export default apiTemplateService
