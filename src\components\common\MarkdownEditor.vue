<template>
  <div class="markdown-editor-container">
    <MdEditor v-model="content" :language="language" :theme="theme" :preview-theme="previewTheme"
      :code-theme="codeTheme" :placeholder="placeholder" :toolbars="toolbars" :footers="footers" :scroll-auto="true"
      :auto-focus="false" :tab-width="2" :table-shape="[6, 4]" :no-mermaid="false" :no-katex="false"
      :no-highlight="false" :sanitize="sanitizeHtml" :preview="preview" :preview-only="previewOnly" @save="handleSave"
      @upload-img="handleUploadImg" @change="handleChange" @focus="handleFocus" @blur="handleBlur" class="md-editor" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { MdEditor } from 'md-editor-v3'
import 'md-editor-v3/lib/style.css'

interface Props {
  modelValue: string
  placeholder?: string
  height?: string
  language?: 'zh-CN' | 'en-US'
  theme?: 'light' | 'dark'
  previewTheme?: 'default' | 'github' | 'vuepress' | 'mk-cute' | 'smart-blue' | 'cyanosis'
  codeTheme?: 'atom' | 'a11y' | 'github' | 'gradient' | 'kimbie' | 'paraiso' | 'qtcreator' | 'stackoverflow'
  readonly?: boolean
  disabled?: boolean
  preview?: boolean
  previewOnly?: boolean
  toolbars?: string[]
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
  (e: 'save', value: string): void
  (e: 'focus'): void
  (e: 'blur'): void
  (e: 'upload-img', files: File[], callback: (urls: string[]) => void): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入内容...',
  height: '400px',
  language: 'zh-CN',
  theme: 'light',
  previewTheme: 'github',
  codeTheme: 'github',
  readonly: false,
  disabled: false,
  preview: true,
  previewOnly: false,
  toolbars: () => [
    'bold',
    'underline',
    'italic',
    '-',
    'title',
    'strikeThrough',
    'sub',
    'sup',
    'quote',
    'unorderedList',
    'orderedList',
    'task',
    '-',
    'codeRow',
    'code',
    'link',
    'image',
    'table',
    'mermaid',
    'katex',
    '-',
    'revoke',
    'next',
    'save',
    '=',
    'pageFullscreen',
    'fullscreen',
    'preview',
    'previewOnly',
    'htmlPreview',
    'catalog'
  ]
})

const emit = defineEmits<Emits>()

// 响应式数据
const content = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 使用传入的工具栏配置
const toolbars = computed(() => props.toolbars)

// 底部工具栏
const footers = ['markdownTotal', '=', 'scrollSwitch']

// HTML 清理配置
const sanitizeHtml = (html: string) => {
  // 这里可以添加自定义的 HTML 清理逻辑
  return html
}

// 事件处理
const handleSave = (value: string) => {
  emit('save', value)
}

const handleUploadImg = async (files: File[], callback: (urls: string[]) => void) => {
  emit('upload-img', files, callback)
}

const handleChange = (value: string) => {
  emit('change', value)
}

const handleFocus = () => {
  emit('focus')
}

const handleBlur = () => {
  emit('blur')
}

// 监听主题变化
const isDark = computed(() => {
  // 这里可以根据你的主题系统来判断
  return document.documentElement.classList.contains('dark')
})

const theme = computed(() => {
  return isDark.value ? 'dark' : 'light'
})
</script>

<style scoped>
.markdown-editor-container {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--ant-color-border);
}

.md-editor {
  --md-color: var(--ant-color-text);
  --md-hover-color: var(--ant-color-text-secondary);
  --md-bk-color: var(--ant-color-bg-container);
  --md-bk-color-outstand: var(--ant-color-bg-layout);
  --md-bk-hover-color: var(--ant-color-fill-tertiary);
  --md-border-color: var(--ant-color-border);
  --md-border-hover-color: var(--ant-color-primary);
  --md-border-active-color: var(--ant-color-primary);
  --md-modal-mask: rgba(0, 0, 0, 0.45);
  --md-scrollbar-bg-color: var(--ant-color-fill-quaternary);
  --md-scrollbar-thumb-color: var(--ant-color-fill-secondary);
  --md-scrollbar-thumb-hover-color: var(--ant-color-fill);
  --md-scrollbar-thumb-active-color: var(--ant-color-fill);
}

/* 自定义编辑器高度 */
.md-editor :deep(.md-editor-content) {
  height: v-bind(height);
}

/* 工具栏样式调整 */
.md-editor :deep(.md-editor-toolbar) {
  border-bottom: 1px solid var(--ant-color-border);
  background: var(--ant-color-bg-container);
  padding: 8px 12px;
}

.md-editor :deep(.md-editor-toolbar-item) {
  color: var(--ant-color-text-secondary);
  border-radius: 4px;
  transition: all 0.2s;
}

.md-editor :deep(.md-editor-toolbar-item:hover) {
  color: var(--ant-color-primary);
  background: var(--ant-color-primary-bg);
}

.md-editor :deep(.md-editor-toolbar-item.active) {
  color: var(--ant-color-primary);
  background: var(--ant-color-primary-bg);
}

/* 编辑区域样式 */
.md-editor :deep(.md-editor-input-wrapper) {
  background: var(--ant-color-bg-container);
}

.md-editor :deep(.md-editor-input) {
  color: var(--ant-color-text);
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.6;
}

/* 预览区域样式 */
.md-editor :deep(.md-editor-preview-wrapper) {
  background: var(--ant-color-bg-container);
  border-left: 1px solid var(--ant-color-border);
}

.md-editor :deep(.md-editor-preview) {
  color: var(--ant-color-text);
  font-size: 14px;
  line-height: 1.6;
  padding: 16px;
}

/* 预览内容样式 */
.md-editor :deep(.md-editor-preview h1),
.md-editor :deep(.md-editor-preview h2),
.md-editor :deep(.md-editor-preview h3),
.md-editor :deep(.md-editor-preview h4),
.md-editor :deep(.md-editor-preview h5),
.md-editor :deep(.md-editor-preview h6) {
  color: var(--ant-color-text);
  margin: 16px 0 8px 0;
  font-weight: 600;
}

.md-editor :deep(.md-editor-preview p) {
  margin: 8px 0;
  color: var(--ant-color-text);
}

.md-editor :deep(.md-editor-preview code) {
  background: var(--ant-color-bg-layout);
  color: var(--ant-color-error);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
}

.md-editor :deep(.md-editor-preview pre) {
  background: var(--ant-color-bg-layout);
  border: 1px solid var(--ant-color-border);
  border-radius: 6px;
  padding: 12px;
  overflow-x: auto;
  margin: 12px 0;
}

.md-editor :deep(.md-editor-preview pre code) {
  background: none;
  padding: 0;
}

.md-editor :deep(.md-editor-preview blockquote) {
  border-left: 4px solid var(--ant-color-primary);
  background: var(--ant-color-primary-bg);
  margin: 12px 0;
  padding: 8px 16px;
  color: var(--ant-color-text-secondary);
}

.md-editor :deep(.md-editor-preview table) {
  border-collapse: collapse;
  width: 100%;
  margin: 12px 0;
}

.md-editor :deep(.md-editor-preview th),
.md-editor :deep(.md-editor-preview td) {
  border: 1px solid var(--ant-color-border);
  padding: 8px 12px;
  text-align: left;
}

.md-editor :deep(.md-editor-preview th) {
  background: var(--ant-color-bg-layout);
  font-weight: 600;
}

/* 暗黑模式适配 */
.dark .md-editor {
  --md-color: rgba(255, 255, 255, 0.85);
  --md-hover-color: rgba(255, 255, 255, 0.65);
  --md-bk-color: #141414;
  --md-bk-color-outstand: #1f1f1f;
  --md-bk-hover-color: rgba(255, 255, 255, 0.04);
  --md-border-color: #424242;
  --md-border-hover-color: #1677ff;
  --md-border-active-color: #1677ff;
}
</style>
