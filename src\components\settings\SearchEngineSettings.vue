<template>
  <BaseModal v-model="isVisible" title="搜索引擎设置" size="md">
    <div class="space-y-6">
      <!-- 默认搜索引擎 -->
      <div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">默认搜索引擎</h3>
        <div class="grid grid-cols-2 gap-3">
          <button v-for="engine in settings.engines" :key="engine.id" :class="[
            'flex items-center p-3 rounded-lg border-2 transition-all',
            settings.defaultEngine === engine.id
              ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
              : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
          ]" @click="setDefaultEngine(engine.id)">
            <div :class="[engine.icon, 'w-6 h-6 mr-3']"></div>
            <span class="font-medium text-gray-900 dark:text-gray-100">{{ engine.name }}</span>
            <div v-if="settings.defaultEngine === engine.id" class="i-heroicons-check w-5 h-5 ml-auto text-primary-500">
            </div>
          </button>
        </div>
      </div>

      <!-- 自定义搜索引擎 -->
      <div>
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">自定义搜索引擎</h3>
          <BaseButton @click="showAddEngine = true">
            <div class="i-heroicons-plus w-4 h-4 mr-2"></div>
            添加
          </BaseButton>
        </div>

        <div class="space-y-3">
          <div v-for="engine in customEngines" :key="engine.id"
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div class="flex items-center">
              <div :class="[engine.icon, 'w-5 h-5 mr-3']"></div>
              <div>
                <div class="font-medium text-gray-900 dark:text-gray-100">{{ engine.name }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">{{ engine.url }}</div>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <BaseButton variant="ghost" size="sm" @click="editEngine(engine)">
                <div class="i-heroicons-pencil w-4 h-4"></div>
              </BaseButton>
              <BaseButton variant="ghost" size="sm" @click="removeEngine(engine.id)">
                <div class="i-heroicons-trash w-4 h-4 text-red-500"></div>
              </BaseButton>
            </div>
          </div>

          <div v-if="customEngines.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            <div class="i-heroicons-magnifying-glass w-12 h-12 mx-auto mb-4 opacity-50"></div>
            <p>暂无自定义搜索引擎</p>
            <p class="text-sm mt-1">点击"添加"按钮创建自定义搜索引擎</p>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-between">
        <BaseButton variant="outline" @click="resetToDefaults">
          重置默认
        </BaseButton>
        <BaseButton variant="outline" @click="isVisible = false">
          关闭
        </BaseButton>
      </div>
    </template>
  </BaseModal>

  <!-- 添加/编辑搜索引擎对话框 -->
  <BaseModal v-model="showAddEngine" :title="editingEngine ? '编辑搜索引擎' : '添加搜索引擎'" size="sm">
    <div class="space-y-4">
      <a-form-item label="名称" required>
        <a-input v-model:value="engineForm.name" placeholder="搜索引擎名称" />
      </a-form-item>

      <a-form-item label="搜索URL" required>
        <a-input v-model:value="engineForm.url" placeholder="https://example.com/search?q={query}" />
      </a-form-item>

      <div class="text-sm text-gray-500 dark:text-gray-400">
        <p>在URL中使用 <code class="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded">{query}</code> 作为搜索关键词的占位符</p>
        <p class="mt-1">例如：https://www.google.com/search?q={query}</p>
      </div>

      <a-form-item label="图标类名">
        <a-input v-model:value="engineForm.icon" placeholder="i-logos-google" />
      </a-form-item>
    </div>

    <template #footer>
      <div class="flex justify-end space-x-3">
        <BaseButton variant="outline" @click="cancelEdit">
          取消
        </BaseButton>
        <BaseButton @click="saveEngine" :disabled="!engineForm.name || !engineForm.url">
          {{ editingEngine ? '保存' : '添加' }}
        </BaseButton>
      </div>
    </template>
  </BaseModal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseModal from '@/components/ui/BaseModal.vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import { searchEngineService } from '@/services/searchEngineService'
import type { SearchEngine, SearchSettings } from '@/types/search'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 搜索引擎设置
const settings = ref<SearchSettings>(searchEngineService.getSettings())

// 自定义搜索引擎
const customEngines = computed(() => {
  const defaultIds = ['google', 'bing', 'baidu', 'duckduckgo', 'github', 'stackoverflow']
  return settings.value.engines.filter(engine => !defaultIds.includes(engine.id))
})

// 添加/编辑搜索引擎
const showAddEngine = ref(false)
const editingEngine = ref<SearchEngine | null>(null)
const engineForm = ref({
  name: '',
  url: '',
  icon: 'i-heroicons-magnifying-glass'
})

// 设置默认搜索引擎
const setDefaultEngine = (engineId: string) => {
  settings.value.defaultEngine = engineId
  searchEngineService.saveSettings(settings.value)
}

// 编辑搜索引擎
const editEngine = (engine: SearchEngine) => {
  editingEngine.value = engine
  engineForm.value = {
    name: engine.name,
    url: engine.url,
    icon: engine.icon
  }
  showAddEngine.value = true
}

// 保存搜索引擎
const saveEngine = () => {
  if (!engineForm.value.name || !engineForm.value.url) return

  if (editingEngine.value) {
    // 编辑现有引擎
    const index = settings.value.engines.findIndex(e => e.id === editingEngine.value!.id)
    if (index >= 0) {
      settings.value.engines[index] = {
        ...editingEngine.value,
        name: engineForm.value.name,
        url: engineForm.value.url,
        icon: engineForm.value.icon
      }
    }
  } else {
    // 添加新引擎
    const newEngine: SearchEngine = {
      id: `custom-${Date.now()}`,
      name: engineForm.value.name,
      url: engineForm.value.url,
      icon: engineForm.value.icon
    }
    settings.value.engines.push(newEngine)
  }

  searchEngineService.saveSettings(settings.value)
  cancelEdit()
}

// 取消编辑
const cancelEdit = () => {
  showAddEngine.value = false
  editingEngine.value = null
  engineForm.value = {
    name: '',
    url: '',
    icon: 'i-heroicons-magnifying-glass'
  }
}

// 删除搜索引擎
const removeEngine = (engineId: string) => {
  if (confirm('确定要删除这个搜索引擎吗？')) {
    settings.value.engines = settings.value.engines.filter(e => e.id !== engineId)

    // 如果删除的是默认引擎，重置为第一个引擎
    if (settings.value.defaultEngine === engineId) {
      settings.value.defaultEngine = settings.value.engines[0]?.id || 'google'
    }

    searchEngineService.saveSettings(settings.value)
  }
}

// 重置为默认设置
const resetToDefaults = () => {
  if (confirm('确定要重置为默认设置吗？这将删除所有自定义搜索引擎。')) {
    searchEngineService.resetToDefaults()
    settings.value = searchEngineService.getSettings()
  }
}

// 监听设置变化
watch(() => props.modelValue, (visible) => {
  if (visible) {
    settings.value = searchEngineService.getSettings()
  }
})
</script>
