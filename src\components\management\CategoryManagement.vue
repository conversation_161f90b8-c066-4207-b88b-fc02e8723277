<template>
  <div class="space-y-6">
    <!-- 添加新分类 -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">添加新分类</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <BaseInput v-model="newCategory.name" label="分类名称" placeholder="请输入分类名称" required />
        <ParentCategorySelector v-model="newCategory.parent_id" label="父分类" placeholder="选择父分类（可选）"
          :categories="categories" />
      </div>
      <div class="mt-4 flex justify-end">
        <BaseButton @click="handleAddCategory" :disabled="!newCategory.name.trim()">
          <div class="i-heroicons-plus mr-2"></div>
          添加分类
        </BaseButton>
      </div>
    </div>

    <!-- 分类列表 -->
    <div>
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">分类列表</h3>
        <BaseButton variant="outline" @click="handleRefresh">
          <div class="i-heroicons-arrow-path mr-2"></div>
          刷新
        </BaseButton>
      </div>
      <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg">
        <CategoryManagementTree :categories="categoryTree" @edit="handleEditCategory" @delete="handleDeleteCategory"
          @move="handleMoveCategory" @reorder="handleReorderCategories" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, provide } from 'vue'
import BaseInput from '@/components/ui/BaseInput.vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import ParentCategorySelector from '@/components/knowledge/ParentCategorySelector.vue'
import CategoryManagementTree from './CategoryManagementTree.vue'
import { categoryService } from '@/services/categoryService'
import type { Category, CategoryWithChildren } from '@/types'

// 状态
const loading = ref(false)
const categories = ref<Category[]>([])
const categoryTree = ref<CategoryWithChildren[]>([])

// 新分类表单
const newCategory = ref({
  name: '',
  parent_id: 0
})

// 提供所有分类数据给子组件
provide('allCategories', categories)

// 加载分类数据
const loadCategories = async () => {
  try {
    loading.value = true
    const [allCategories, tree] = await Promise.all([
      categoryService.getAllCategories(),
      categoryService.getCategoryTree()
    ])
    categories.value = allCategories
    categoryTree.value = tree
  } catch (error) {
    console.error('加载分类失败:', error)
  } finally {
    loading.value = false
  }
}

// 添加分类
const handleAddCategory = async () => {
  try {
    await categoryService.createCategory({
      name: newCategory.value.name,
      parent_id: newCategory.value.parent_id,
      sort_order: 999
    })

    // 重置表单
    newCategory.value = {
      name: '',
      parent_id: 0
    }

    // 刷新数据
    await loadCategories()
  } catch (error) {
    console.error('添加分类失败:', error)
    alert('添加分类失败，请重试')
  }
}

// 编辑分类
const handleEditCategory = async (category: Category) => {
  const newName = prompt('请输入新的分类名称:', category.name)
  if (newName && newName.trim() && newName !== category.name) {
    try {
      await categoryService.updateCategory(category.id!, { name: newName.trim() })
      await loadCategories()
    } catch (error) {
      console.error('编辑分类失败:', error)
      alert('编辑分类失败，请重试')
    }
  }
}

// 删除分类
const handleDeleteCategory = async (category: Category) => {
  if (confirm(`确定要删除分类"${category.name}"吗？删除后该分类下的所有资源将移动到未分类。`)) {
    try {
      await categoryService.deleteCategory(category.id!)
      await loadCategories()
    } catch (error) {
      console.error('删除分类失败:', error)
      alert('删除分类失败，请重试')
    }
  }
}

// 移动分类
const handleMoveCategory = async (categoryId: number, newParentId: number) => {
  try {
    await categoryService.updateCategory(categoryId, { parent_id: newParentId })
    await loadCategories()
  } catch (error) {
    console.error('移动分类失败:', error)
    alert('移动分类失败，请重试')
  }
}

// 重新排序分类
const handleReorderCategories = async (categories: CategoryWithChildren[]) => {
  try {
    await categoryService.reorderCategories(categories)
    await loadCategories()
  } catch (error) {
    console.error('排序分类失败:', error)
    alert('排序分类失败，请重试')
  }
}

// 刷新数据
const handleRefresh = () => {
  loadCategories()
}

// 初始化
onMounted(() => {
  loadCategories()
})
</script>
