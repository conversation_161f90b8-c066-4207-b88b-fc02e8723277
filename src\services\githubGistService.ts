import type { ExportData } from './dataManagementService'
import { compressJson, decompressJson } from '@/utils/compression'

// GitHub Gist API接口
export interface GistFile {
  filename: string
  content: string
}

export interface GistData {
  description: string
  public: boolean
  files: Record<string, GistFile>
}

export interface GistResponse {
  id: string
  html_url: string
  description: string
  public: boolean
  created_at: string
  updated_at: string
  files: Record<
    string,
    {
      filename: string
      content: string
      size: number
    }
  >
}

export interface GitHubConfig {
  token: string
  gistId?: string
  lastUploadTime?: string // 最后上传时间
  lastDownloadTime?: string // 最后下载时间
  autoUpload?: {
    enabled: boolean
    interval: number // 分钟
    nextUpload?: string
  }
}

export interface SyncResult {
  success: boolean
  message: string
  gistId?: string
  url?: string
  error?: string
}

class GitHubGistService {
  private readonly STORAGE_KEY = 'github-gist-config'
  private readonly API_BASE = 'https://api.github.com'
  private readonly GIST_FILENAME = 'knowledge-backup.json.gz'
  private readonly COMPRESSION_ENABLED = true

  private config: GitHubConfig | null = null

  constructor() {
    this.loadConfig()
  }

  // 压缩JSON数据
  private compressData(data: ExportData): string {
    if (!this.COMPRESSION_ENABLED) {
      return JSON.stringify(data)
    }

    try {
      const compressed = compressJson(data)
      const originalSize = JSON.stringify(data).length
      const compressedSize = compressed.length

      console.log('📦 数据压缩信息:')
      console.log('  - 原始大小:', this.formatFileSize(originalSize))
      console.log('  - 压缩后大小:', this.formatFileSize(compressedSize))
      console.log('  - 压缩率:', Math.round((1 - compressedSize / originalSize) * 100) + '%')

      return compressed
    } catch (error) {
      console.error('数据压缩失败，使用原始数据:', error)
      return JSON.stringify(data)
    }
  }

  // 解压缩JSON数据
  private decompressData(compressedData: string): ExportData {
    if (!this.COMPRESSION_ENABLED) {
      return JSON.parse(compressedData)
    }

    try {
      // 尝试解压缩
      const data = decompressJson<ExportData>(compressedData)
      const originalSize = JSON.stringify(data).length

      console.log('📦 数据解压缩成功:')
      console.log('  - 压缩数据大小:', this.formatFileSize(compressedData.length))
      console.log('  - 解压后大小:', this.formatFileSize(originalSize))

      return data
    } catch (error) {
      console.log('📦 解压缩失败，尝试解析原始JSON:', error)
      // 如果解压缩失败，尝试直接解析JSON（兼容旧格式）
      try {
        return JSON.parse(compressedData)
      } catch (parseError) {
        console.error('JSON解析也失败:', parseError)
        throw new Error('数据格式错误，无法解析')
      }
    }
  }

  // 格式化文件大小
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 加载配置
  private loadConfig(): void {
    try {
      const saved = localStorage.getItem(this.STORAGE_KEY)
      if (saved) {
        const parsed = JSON.parse(saved)
        // 简单的解密（实际项目中应使用更安全的加密方式）
        if (parsed.token) {
          parsed.token = atob(parsed.token)
        }
        this.config = parsed
      }
    } catch (error) {
      console.error('加载GitHub配置失败:', error)
      this.config = null
    }
  }

  // 保存配置
  private saveConfig(): void {
    try {
      if (this.config) {
        // 简单的加密存储token
        const configToSave = {
          ...this.config,
          token: btoa(this.config.token),
        }
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(configToSave))
      }
    } catch (error) {
      console.error('保存GitHub配置失败:', error)
      throw error
    }
  }

  // 设置GitHub Token
  setToken(token: string): void {
    this.config = {
      ...this.config,
      token: token.trim(),
    }
    this.saveConfig()
  }

  // 获取当前配置
  getConfig(): GitHubConfig | null {
    return this.config ? { ...this.config } : null
  }

  // 验证Token
  async validateToken(): Promise<{ valid: boolean; error?: string }> {
    if (!this.config?.token) {
      return { valid: false, error: '未设置GitHub Token' }
    }

    try {
      const response = await fetch(`${this.API_BASE}/user`, {
        headers: {
          Authorization: `token ${this.config.token}`,
          Accept: 'application/vnd.github.v3+json',
        },
      })

      if (response.ok) {
        return { valid: true }
      } else if (response.status === 401) {
        return { valid: false, error: 'Token无效或已过期' }
      } else {
        return { valid: false, error: `验证失败: ${response.statusText}` }
      }
    } catch (error) {
      return { valid: false, error: `网络错误: ${(error as Error).message}` }
    }
  }

  // 上传数据到Gist
  async uploadToGist(data: ExportData, description?: string): Promise<SyncResult> {
    if (!this.config?.token) {
      return { success: false, message: '未配置GitHub Token', error: '未配置GitHub Token' }
    }

    try {
      // 压缩数据
      const compressedContent = this.compressData(data)

      const gistData: GistData = {
        description: description || `Knowledge Base Backup - ${new Date().toLocaleString()}`,
        public: false,
        files: {
          [this.GIST_FILENAME]: {
            filename: this.GIST_FILENAME,
            content: compressedContent,
          },
        },
      }

      let url = `${this.API_BASE}/gists`
      let method = 'POST'

      // 如果已有Gist ID，则更新现有Gist
      if (this.config.gistId) {
        url = `${this.API_BASE}/gists/${this.config.gistId}`
        method = 'PATCH'
      }

      const response = await fetch(url, {
        method,
        headers: {
          Authorization: `token ${this.config.token}`,
          Accept: 'application/vnd.github.v3+json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(gistData),
      })

      if (response.ok) {
        const gistResponse: GistResponse = await response.json()

        // 保存Gist ID和上传时间（使用GitHub返回的实际更新时间）
        const actualUploadTime = gistResponse.updated_at // GitHub的实际更新时间
        this.config.gistId = gistResponse.id
        this.config.lastUploadTime = actualUploadTime
        this.saveConfig()

        console.log('📤 上传完成，记录时间:')
        console.log('  - 本地记录时间:', new Date().toISOString())
        console.log('  - GitHub实际更新时间:', actualUploadTime)
        console.log(
          '  - 本地显示:',
          new Date(actualUploadTime).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
        )
        console.log(
          '  - 延迟差异(ms):',
          new Date().getTime() - new Date(actualUploadTime).getTime(),
        )

        return {
          success: true,
          message: '数据已成功上传到GitHub Gist',
          gistId: gistResponse.id,
          url: gistResponse.html_url,
        }
      } else {
        const errorData = await response.json().catch(() => ({}))
        return {
          success: false,
          message: '上传失败',
          error: errorData.message || response.statusText,
        }
      }
    } catch (error) {
      return {
        success: false,
        message: '上传过程中发生错误',
        error: (error as Error).message,
      }
    }
  }

  // 从Gist下载数据
  async downloadFromGist(): Promise<{ success: boolean; data?: ExportData; error?: string }> {
    if (!this.config?.token) {
      return { success: false, error: '未配置GitHub Token' }
    }

    // 如果没有gistId，尝试查找现有的Gist
    if (!this.config.gistId) {
      const findResult = await this.findExistingGist()
      if (!findResult.success) {
        return { success: false, error: '未找到云端备份' }
      }
    }

    try {
      const response = await fetch(`${this.API_BASE}/gists/${this.config.gistId}`, {
        headers: {
          Authorization: `token ${this.config.token}`,
          Accept: 'application/vnd.github.v3+json',
        },
      })

      if (response.ok) {
        const gistResponse: GistResponse = await response.json()
        const file = gistResponse.files[this.GIST_FILENAME]

        if (!file) {
          return { success: false, error: '云端备份文件不存在' }
        }

        try {
          // 解压缩数据
          const data: ExportData = this.decompressData(file.content)

          // 记录下载时间（使用当前云端的更新时间，而不是本地下载时间）
          const cloudUpdatedTime = gistResponse.updated_at // 当前云端的实际更新时间
          this.config.lastDownloadTime = cloudUpdatedTime
          this.saveConfig()

          console.log('📥 下载完成，记录时间:')
          console.log('  - 本地下载时间:', new Date().toISOString())
          console.log('  - 云端更新时间:', cloudUpdatedTime)
          console.log(
            '  - 本地显示:',
            new Date(cloudUpdatedTime).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
          )
          console.log('  - 说明: 记录云端更新时间，用于后续比较')

          return { success: true, data }
        } catch (parseError) {
          return { success: false, error: '云端数据格式错误或解压缩失败' }
        }
      } else if (response.status === 404) {
        return { success: false, error: '云端备份不存在' }
      } else {
        return { success: false, error: `下载失败: ${response.statusText}` }
      }
    } catch (error) {
      return { success: false, error: `网络错误: ${(error as Error).message}` }
    }
  }

  // 获取Gist信息
  async getGistInfo(): Promise<{ success: boolean; info?: any; error?: string }> {
    if (!this.config?.token || !this.config.gistId) {
      return { success: false, error: '未配置完整的GitHub信息' }
    }

    try {
      const response = await fetch(`${this.API_BASE}/gists/${this.config.gistId}`, {
        headers: {
          Authorization: `token ${this.config.token}`,
          Accept: 'application/vnd.github.v3+json',
        },
      })

      if (response.ok) {
        const gistResponse: GistResponse = await response.json()
        return {
          success: true,
          info: {
            id: gistResponse.id,
            description: gistResponse.description,
            url: gistResponse.html_url,
            createdAt: gistResponse.created_at,
            updatedAt: gistResponse.updated_at,
            fileSize: gistResponse.files[this.GIST_FILENAME]?.size || 0,
          },
        }
      } else {
        return { success: false, error: `获取信息失败: ${response.statusText}` }
      }
    } catch (error) {
      return { success: false, error: `网络错误: ${(error as Error).message}` }
    }
  }

  // 删除配置
  clearConfig(): void {
    this.config = null
    localStorage.removeItem(this.STORAGE_KEY)
  }

  // 检查是否已配置
  isConfigured(): boolean {
    return !!this.config?.token
  }

  // 检查是否有云端备份
  hasCloudBackup(): boolean {
    return !!this.config?.gistId
  }

  // 获取最后上传时间
  getLastUploadTime(): string | null {
    return this.config?.lastUploadTime || null
  }

  // 获取最后下载时间
  getLastDownloadTime(): string | null {
    return this.config?.lastDownloadTime || null
  }

  // 获取最后同步时间（兼容性方法，返回最近的上传或下载时间）
  getLastSyncTime(): string | null {
    const uploadTime = this.config?.lastUploadTime
    const downloadTime = this.config?.lastDownloadTime

    if (!uploadTime && !downloadTime) return null
    if (!uploadTime) return downloadTime
    if (!downloadTime) return uploadTime

    // 返回较新的时间（时区安全比较）
    return this.compareUTCTimes(uploadTime, downloadTime) > 0 ? uploadTime : downloadTime
  }

  // 时区安全的时间比较工具
  private compareUTCTimes(time1: string, time2: string): number {
    // 确保两个时间都转换为UTC时间进行比较
    const utc1 = new Date(time1).getTime()
    const utc2 = new Date(time2).getTime()
    return utc1 - utc2
  }

  // 检查云端是否有更新（时区安全）
  hasCloudUpdate(cloudUpdatedAt: string): boolean {
    // 获取最后同步时间（上传或下载中较新的时间）
    const lastSyncTime = this.getLastSyncTime()

    if (!lastSyncTime) {
      console.log('🔍 GitHub服务检测: 从未同步过，返回false')
      return false
    }

    // 时区安全的时间比较
    const result = this.compareUTCTimes(cloudUpdatedAt, lastSyncTime) > 0

    const lastUploadTime = this.getLastUploadTime()
    const lastDownloadTime = this.getLastDownloadTime()

    console.log('🔍 GitHub服务时间比较:')
    console.log('  - 当前云端时间:', cloudUpdatedAt)
    console.log('  - 最后上传时间:', lastUploadTime || '从未上传')
    console.log('  - 最后下载时间:', lastDownloadTime || '从未下载')
    console.log('  - 最后同步时间:', lastSyncTime)
    console.log('  - 时间差(ms):', this.compareUTCTimes(cloudUpdatedAt, lastSyncTime))
    console.log('  - 有更新:', result)
    console.log('  - 比较逻辑: 当前云端时间 vs 最后同步时间')

    return result
  }

  // 设置自动上传配置
  setAutoUploadConfig(enabled: boolean, interval: number): void {
    if (!this.config) return

    this.config.autoUpload = {
      enabled,
      interval,
      nextUpload: enabled ? this.calculateNextUploadTime(interval) : undefined,
    }
    this.saveConfig()
  }

  // 获取自动上传配置
  getAutoUploadConfig(): { enabled: boolean; interval: number; nextUpload?: string } {
    return this.config?.autoUpload || { enabled: false, interval: 60 }
  }

  // 计算下次上传时间
  private calculateNextUploadTime(intervalMinutes: number): string {
    const nextTime = new Date()
    nextTime.setMinutes(nextTime.getMinutes() + intervalMinutes)
    return nextTime.toISOString()
  }

  // 更新下次上传时间
  updateNextUploadTime(): void {
    if (!this.config?.autoUpload?.enabled) return

    this.config.autoUpload.nextUpload = this.calculateNextUploadTime(
      this.config.autoUpload.interval,
    )
    this.saveConfig()
  }

  // 检查是否需要自动上传
  shouldAutoUpload(): boolean {
    const autoConfig = this.config?.autoUpload
    if (!autoConfig?.enabled || !autoConfig.nextUpload) return false

    return new Date() >= new Date(autoConfig.nextUpload)
  }

  // 获取Gist详细信息（包含文件大小等）
  async getGistDetails(): Promise<{ success: boolean; details?: any; error?: string }> {
    if (!this.config?.token) {
      return { success: false, error: '未配置GitHub Token' }
    }

    // 如果没有gistId，尝试查找现有的Gist
    if (!this.config.gistId) {
      const findResult = await this.findExistingGist()
      if (!findResult.success) {
        return { success: false, error: '未找到云端备份' }
      }
    }

    try {
      const response = await fetch(`${this.API_BASE}/gists/${this.config.gistId}`, {
        headers: {
          Authorization: `token ${this.config.token}`,
          Accept: 'application/vnd.github.v3+json',
        },
      })

      if (response.ok) {
        const gistResponse: GistResponse = await response.json()
        const file = gistResponse.files[this.GIST_FILENAME]

        return {
          success: true,
          details: {
            id: gistResponse.id,
            description: gistResponse.description,
            url: gistResponse.html_url,
            createdAt: gistResponse.created_at,
            updatedAt: gistResponse.updated_at,
            fileSize: file?.size || 0,
            hasBackup: !!file,
          },
        }
      } else if (response.status === 404) {
        return { success: false, error: '云端备份不存在' }
      } else {
        return { success: false, error: `获取详情失败: ${response.statusText}` }
      }
    } catch (error) {
      return { success: false, error: `网络错误: ${(error as Error).message}` }
    }
  }

  // 查找现有的Gist
  private async findExistingGist(): Promise<{ success: boolean; gistId?: string }> {
    if (!this.config?.token) {
      return { success: false }
    }

    try {
      const response = await fetch(`${this.API_BASE}/gists`, {
        headers: {
          Authorization: `token ${this.config.token}`,
          Accept: 'application/vnd.github.v3+json',
        },
      })

      if (response.ok) {
        const gists: GistResponse[] = await response.json()

        // 查找包含我们备份文件的Gist
        for (const gist of gists) {
          if (gist.files[this.GIST_FILENAME]) {
            // 找到了，保存gistId
            this.config.gistId = gist.id
            this.saveConfig()
            return { success: true, gistId: gist.id }
          }
        }

        return { success: false }
      } else {
        return { success: false }
      }
    } catch (error) {
      console.error('查找现有Gist失败:', error)
      return { success: false }
    }
  }
}

// 创建单例实例
export const githubGistService = new GitHubGistService()

// 导出类型
export type { GitHubConfig, SyncResult }
