<template>
  <div v-if="content" class="markdown-preview prose prose-sm dark:prose-invert max-w-none" v-html="renderedContent">
  </div>
  <div v-else class="text-gray-400 dark:text-gray-500 italic text-center py-8">
    暂无内容
  </div>
</template>

<style scoped>
/* 确保代码块在浅色模式下也有正确的样式 */
.markdown-preview :deep(pre.hljs) {
  background-color: #1f2937 !important;
  color: #f9fafb !important;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.dark .markdown-preview :deep(pre.hljs) {
  border-color: #374151;
}

/* 确保代码块内的语法高亮颜色正确 */
.markdown-preview :deep(pre.hljs code) {
  background: transparent !important;
  color: inherit !important;
}
</style>

<script setup lang="ts">
import { computed } from 'vue'
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import 'highlight.js/styles/github-dark.css'

// 导入markdown-it插件
import markdownItMark from 'markdown-it-mark'
import markdownItIns from 'markdown-it-ins'
import markdownItSub from 'markdown-it-sub'
import markdownItSup from 'markdown-it-sup'
import markdownItFootnote from 'markdown-it-footnote'
import markdownItDeflist from 'markdown-it-deflist'
import markdownItAbbr from 'markdown-it-abbr'
import markdownItContainer from 'markdown-it-container'

interface Props {
  content: string
}

const props = defineProps<Props>()

// 配置 Markdown-it
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  breaks: true, // 支持换行
  highlight: (str: string, lang: string) => {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return `<pre class="hljs language-${lang} !bg-gray-900 !text-gray-100 rounded-lg p-4 overflow-x-auto border border-gray-200 dark:border-gray-700 shadow-sm"><code>${hljs.highlight(str, { language: lang, ignoreIllegals: true }).value}</code></pre>`
      } catch (__) { }
    }
    return `<pre class="hljs !bg-gray-900 !text-gray-100 rounded-lg p-4 overflow-x-auto border border-gray-200 dark:border-gray-700 shadow-sm"><code>${md.utils.escapeHtml(str)}</code></pre>`
  }
})

// 使用插件
md.use(markdownItMark) // ==高亮文本==
md.use(markdownItIns) // ++插入文本++
md.use(markdownItSub) // H~2~O
md.use(markdownItSup) // x^2^
md.use(markdownItFootnote) // 脚注支持
md.use(markdownItDeflist) // 定义列表
md.use(markdownItAbbr) // 缩写

// 配置容器插件（警告框）
md.use(markdownItContainer, 'info', {
  validate: (params: string) => params.trim().match(/^info\s+(.*)$/),
  render: (tokens: any[], idx: number) => {
    const m = tokens[idx].info.trim().match(/^info\s+(.*)$/)
    if (tokens[idx].nesting === 1) {
      return `<div class="alert alert-info"><div class="alert-title">${md.utils.escapeHtml(m[1])}</div>\n`
    } else {
      return '</div>\n'
    }
  }
})

md.use(markdownItContainer, 'warning', {
  validate: (params: string) => params.trim().match(/^warning\s+(.*)$/),
  render: (tokens: any[], idx: number) => {
    const m = tokens[idx].info.trim().match(/^warning\s+(.*)$/)
    if (tokens[idx].nesting === 1) {
      return `<div class="alert alert-warning"><div class="alert-title">${md.utils.escapeHtml(m[1])}</div>\n`
    } else {
      return '</div>\n'
    }
  }
})

md.use(markdownItContainer, 'error', {
  validate: (params: string) => params.trim().match(/^error\s+(.*)$/),
  render: (tokens: any[], idx: number) => {
    const m = tokens[idx].info.trim().match(/^error\s+(.*)$/)
    if (tokens[idx].nesting === 1) {
      return `<div class="alert alert-error"><div class="alert-title">${md.utils.escapeHtml(m[1])}</div>\n`
    } else {
      return '</div>\n'
    }
  }
})

md.use(markdownItContainer, 'success', {
  validate: (params: string) => params.trim().match(/^success\s+(.*)$/),
  render: (tokens: any[], idx: number) => {
    const m = tokens[idx].info.trim().match(/^success\s+(.*)$/)
    if (tokens[idx].nesting === 1) {
      return `<div class="alert alert-success"><div class="alert-title">${md.utils.escapeHtml(m[1])}</div>\n`
    } else {
      return '</div>\n'
    }
  }
})

// 自定义渲染规则
// 支持删除线
md.renderer.rules.s_open = () => '<del>'
md.renderer.rules.s_close = () => '</del>'

// 支持任务列表
md.renderer.rules.list_item_open = (tokens, idx) => {
  const token = tokens[idx]
  const nextToken = tokens[idx + 1]

  if (nextToken && nextToken.content && nextToken.content.match(/^\[[ x]\]/)) {
    const checked = nextToken.content.match(/^\[x\]/) ? 'checked' : ''
    nextToken.content = nextToken.content.replace(/^\[[ x]\]\s*/, '')
    return `<li class="task-list-item"><input type="checkbox" ${checked} disabled class="task-list-checkbox"> `
  }

  return '<li>'
}

// 支持表格
md.renderer.rules.table_open = () => '<div class="table-wrapper"><table>'
md.renderer.rules.table_close = () => '</table></div>'

// 支持脚注
md.renderer.rules.footnote_ref = (tokens, idx) => {
  const id = tokens[idx].meta.id
  return `<sup class="footnote-ref"><a href="#fn${id}" id="fnref${id}">${id}</a></sup>`
}

md.renderer.rules.footnote_block_open = () => '<div class="footnotes">'
md.renderer.rules.footnote_block_close = () => '</div>'

// 渲染内容
const renderedContent = computed(() => {
  if (!props.content) return ''

  try {
    return md.render(props.content)
  } catch (error) {
    console.error('Markdown 渲染失败:', error)
    return `<p class="text-red-500">Markdown 渲染失败</p>`
  }
})
</script>

<style scoped>
.markdown-preview {
  @apply text-gray-900 dark:text-gray-100;
}

/* 自定义 prose 样式 */
.markdown-preview :deep(h1) {
  @apply text-2xl font-bold text-primary mb-4 mt-6 first:mt-0;
}

.markdown-preview :deep(h2) {
  @apply text-xl font-bold text-primary mb-3 mt-5;
}

.markdown-preview :deep(h3) {
  @apply text-lg font-semibold text-primary mb-2 mt-4;
}

.markdown-preview :deep(h4) {
  @apply text-base font-semibold text-primary mb-2 mt-3;
}

.markdown-preview :deep(h5) {
  @apply text-sm font-semibold text-primary mb-1 mt-2;
}

.markdown-preview :deep(h6) {
  @apply text-sm font-medium text-primary mb-1 mt-2;
}

.markdown-preview :deep(p) {
  @apply mb-4 leading-relaxed;
}

.markdown-preview :deep(a) {
  @apply text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 underline;
}

.markdown-preview :deep(strong) {
  @apply font-semibold text-gray-900 dark:text-gray-100;
}

.markdown-preview :deep(em) {
  @apply italic;
}

.markdown-preview :deep(code) {
  @apply px-1.5 py-0.5 bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 rounded text-sm font-mono;
}

.markdown-preview :deep(pre) {
  @apply mb-4 overflow-x-auto rounded-lg;
}

.markdown-preview :deep(pre code) {
  @apply p-4 block bg-gray-50 dark:bg-gray-900 text-sm font-mono leading-relaxed;
}

.markdown-preview :deep(blockquote) {
  @apply border-l-4 border-primary-300 dark:border-primary-700 pl-4 py-2 mb-4 bg-primary-50 dark:bg-primary-900/10 italic;
}

.markdown-preview :deep(ul) {
  @apply mb-4 pl-6 space-y-1;
}

.markdown-preview :deep(ol) {
  @apply mb-4 pl-6 space-y-1;
}

.markdown-preview :deep(li) {
  @apply leading-relaxed;
}

.markdown-preview :deep(ul li) {
  @apply list-disc;
}

.markdown-preview :deep(ol li) {
  @apply list-decimal;
}

.markdown-preview :deep(table) {
  @apply w-full mb-4 border-collapse;
}

.markdown-preview :deep(th) {
  @apply border border-gray-300 dark:border-gray-600 px-3 py-2 bg-gray-100 dark:bg-gray-800 font-semibold text-left;
}

.markdown-preview :deep(td) {
  @apply border border-gray-300 dark:border-gray-600 px-3 py-2;
}

.markdown-preview :deep(hr) {
  @apply my-6 border-gray-300 dark:border-gray-600;
}

.markdown-preview :deep(img) {
  @apply max-w-full h-auto rounded-lg shadow-md;
}

/* 代码高亮样式调整 */
.markdown-preview :deep(.hljs) {
  @apply bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200 rounded-lg;
  font-family: 'Fira Code', 'Monaco', 'Consolas', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* 语言标签 */
.markdown-preview :deep(pre.hljs) {
  position: relative;
}

.markdown-preview :deep(pre.hljs.language-javascript::before) {
  content: 'JavaScript';
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
  background: rgba(255, 255, 255, 0.8);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.markdown-preview :deep(pre.hljs.language-typescript::before) {
  content: 'TypeScript';
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
  background: rgba(255, 255, 255, 0.8);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.markdown-preview :deep(pre.hljs.language-vue::before) {
  content: 'Vue';
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
  background: rgba(255, 255, 255, 0.8);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.markdown-preview :deep(pre.hljs.language-css::before) {
  content: 'CSS';
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
  background: rgba(255, 255, 255, 0.8);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.markdown-preview :deep(pre.hljs.language-html::before) {
  content: 'HTML';
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
  background: rgba(255, 255, 255, 0.8);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.markdown-preview :deep(pre.hljs.language-python::before) {
  content: 'Python';
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
  background: rgba(255, 255, 255, 0.8);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.markdown-preview :deep(pre.hljs.language-java::before) {
  content: 'Java';
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
  background: rgba(255, 255, 255, 0.8);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

/* 删除线样式 */
.markdown-preview :deep(del) {
  @apply line-through text-gray-500 dark:text-gray-400;
}

/* 任务列表样式 */
.markdown-preview :deep(.task-list-item) {
  @apply list-none relative;
}

.markdown-preview :deep(.task-list-checkbox) {
  @apply mr-2 accent-primary-500;
}

/* 表格包装器 */
.markdown-preview :deep(.table-wrapper) {
  @apply overflow-x-auto mb-4;
}

/* 脚注样式 */
.markdown-preview :deep(.footnote-ref) {
  @apply text-primary-600 dark:text-primary-400 no-underline;
}

.markdown-preview :deep(.footnote-ref a) {
  @apply text-xs px-1 py-0.5 bg-primary-100 dark:bg-primary-900 rounded no-underline;
}

.markdown-preview :deep(.footnotes) {
  @apply mt-8 pt-4 border-t border-gray-300 dark:border-gray-600 text-sm;
}

/* 键盘按键样式 */
.markdown-preview :deep(kbd) {
  @apply px-2 py-1 bg-gray-200 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-xs font-mono shadow-sm;
}

/* 标记高亮样式 */
.markdown-preview :deep(mark) {
  @apply bg-yellow-200 dark:bg-yellow-800 px-1 rounded;
}

/* 数学公式样式 */
.markdown-preview :deep(.math) {
  @apply overflow-x-auto;
}

/* 警告框样式 */
.markdown-preview :deep(.alert) {
  @apply p-4 mb-4 rounded-lg border-l-4;
}

.markdown-preview :deep(.alert-info) {
  @apply bg-blue-50 dark:bg-blue-900/20 border-blue-400 text-blue-800 dark:text-blue-200;
}

.markdown-preview :deep(.alert-warning) {
  @apply bg-yellow-50 dark:bg-yellow-900/20 border-yellow-400 text-yellow-800 dark:text-yellow-200;
}

.markdown-preview :deep(.alert-error) {
  @apply bg-red-50 dark:bg-red-900/20 border-red-400 text-red-800 dark:text-red-200;
}

.markdown-preview :deep(.alert-success) {
  @apply bg-green-50 dark:bg-green-900/20 border-green-400 text-green-800 dark:text-green-200;
}

/* 高亮文本样式 (==text==) */
.markdown-preview :deep(mark) {
  @apply bg-yellow-200 dark:bg-yellow-800 px-1 rounded text-yellow-900 dark:text-yellow-100;
}

/* 插入文本样式 (++text++) */
.markdown-preview :deep(ins) {
  @apply bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 no-underline px-1 rounded;
}

/* 下标样式 (H~2~O) */
.markdown-preview :deep(sub) {
  @apply text-xs align-sub;
}

/* 上标样式 (x^2^) */
.markdown-preview :deep(sup) {
  @apply text-xs align-super;
}

/* 缩写样式 */
.markdown-preview :deep(abbr) {
  @apply border-b border-dotted border-gray-400 cursor-help;
}

/* 定义列表样式 */
.markdown-preview :deep(dl) {
  @apply mb-4;
}

.markdown-preview :deep(dt) {
  @apply font-semibold text-primary mb-1;
}

.markdown-preview :deep(dd) {
  @apply ml-4 mb-2 text-gray-700 dark:text-gray-300;
}

/* 脚注样式增强 */
.markdown-preview :deep(.footnotes) {
  @apply mt-8 pt-4 border-t border-gray-300 dark:border-gray-600 text-sm;
}

.markdown-preview :deep(.footnotes ol) {
  @apply pl-0;
}

.markdown-preview :deep(.footnotes li) {
  @apply mb-2;
}

/* 警告框标题样式 */
.markdown-preview :deep(.alert-title) {
  @apply font-semibold mb-2 flex items-center;
}

.markdown-preview :deep(.alert-info .alert-title::before) {
  content: 'ℹ️';
  @apply mr-2;
}

.markdown-preview :deep(.alert-warning .alert-title::before) {
  content: '⚠️';
  @apply mr-2;
}

.markdown-preview :deep(.alert-error .alert-title::before) {
  content: '❌';
  @apply mr-2;
}

.markdown-preview :deep(.alert-success .alert-title::before) {
  content: '✅';
  @apply mr-2;
}

/* 表格增强样式 */
.markdown-preview :deep(table) {
  @apply w-full mb-4 border-collapse bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm;
}

.markdown-preview :deep(thead) {
  @apply bg-primary-50 dark:bg-primary-900/20;
}

.markdown-preview :deep(th) {
  @apply border border-primary-200 dark:border-primary-600 px-4 py-3 font-semibold text-left text-primary-700 dark:text-primary-300;
}

.markdown-preview :deep(td) {
  @apply border border-primary-200 dark:border-primary-600 px-4 py-3;
}

.markdown-preview :deep(tbody tr:nth-child(even)) {
  @apply bg-primary-25 dark:bg-primary-950/10;
}

.markdown-preview :deep(tbody tr:hover) {
  @apply bg-primary-50 dark:bg-primary-900/20 transition-colors;
}

/* 代码块复制按钮 */
.markdown-preview :deep(pre) {
  @apply relative group;
}

.markdown-preview :deep(pre::after) {
  content: '📋';
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  opacity: 0;
  cursor: pointer;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  transition: opacity 0.2s;
}

.markdown-preview :deep(pre:hover::after) {
  opacity: 1;
}
</style>
