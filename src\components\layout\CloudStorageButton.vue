<template>
  <div class="relative">
    <!-- 云存储按钮 -->
    <button @click="toggleDropdown" :class="[
      'relative p-2 rounded-lg transition-colors',
      'text-gray-600 dark:text-gray-300',
      'hover:bg-gray-100 dark:hover:bg-gray-700',
      'focus:outline-none focus:ring-2 focus:ring-primary-500'
    ]" :title="isConnected ? '云存储已连接' : '云存储未连接'">
      <div :class="[
        'w-5 h-5',
        isConnected ? 'i-heroicons-cloud' : 'i-heroicons-cloud-slash'
      ]"></div>

      <!-- 更新提示红点 -->
      <div v-if="hasCloudUpdate" class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
    </button>

    <!-- 下拉菜单 -->
    <Transition name="dropdown">
      <div v-if="showDropdown"
        class="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
        <div class="p-4">
          <!-- 连接状态 -->
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">云存储</h3>
            <div :class="[
              'px-2 py-1 rounded text-xs font-medium flex items-center',
              isConnected
                ? 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
            ]">
              <div :class="[
                'w-2 h-2 rounded-full mr-1',
                isConnected ? 'bg-green-500' : 'bg-gray-400'
              ]"></div>
              {{ isConnected ? '已连接' : '未连接' }}
            </div>
          </div>

          <!-- 未连接状态 -->
          <div v-if="!isConnected" class="space-y-3">
            <p class="text-sm text-gray-600 dark:text-gray-400">
              连接GitHub账户以启用云端数据同步功能
            </p>
            <button @click="goToSettings" class="w-full btn-primary text-sm py-2">
              <div class="i-heroicons-cog-6-tooth w-4 h-4 mr-2"></div>
              前往设置
            </button>
          </div>

          <!-- 已连接状态 -->
          <div v-else class="space-y-3">
            <!-- 云端信息 -->
            <div v-if="cloudInfo" class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
              <div>最后同步: {{ formatDate(cloudInfo.lastSync) }}</div>
              <div v-if="cloudInfo.fileSize">文件大小: {{ formatFileSize(cloudInfo.fileSize) }}</div>
            </div>

            <!-- 更新提示 -->
            <div v-if="hasCloudUpdate"
              class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-700 rounded-lg p-3">
              <div class="flex items-center text-orange-700 dark:text-orange-300">
                <div class="i-heroicons-exclamation-triangle w-4 h-4 mr-2"></div>
                <span class="text-sm font-medium">检测到云端更新</span>
              </div>
              <p class="text-xs text-orange-600 dark:text-orange-400 mt-1">
                云端有新的备份数据可以下载
              </p>
            </div>

            <!-- 操作按钮 -->
            <div class="grid grid-cols-2 gap-2">
              <button @click="uploadToCloud" :disabled="uploading" class="btn-primary text-xs py-2">
                <div v-if="uploading" class="i-heroicons-arrow-path w-3 h-3 mr-1 animate-spin"></div>
                <div v-else class="i-heroicons-cloud-arrow-up w-3 h-3 mr-1"></div>
                {{ uploading ? '上传中' : '上传' }}
              </button>

              <button @click="downloadFromCloud" :disabled="downloading" class="btn-secondary text-xs py-2">
                <div v-if="downloading" class="i-heroicons-arrow-path w-3 h-3 mr-1 animate-spin"></div>
                <div v-else class="i-heroicons-cloud-arrow-down w-3 h-3 mr-1"></div>
                {{ downloading ? '下载中' : '下载' }}
              </button>
            </div>

            <!-- 快速导出导入 -->
            <div class="border-t border-gray-200 dark:border-gray-700 pt-3 mt-3">
              <div class="text-xs text-gray-500 dark:text-gray-400 mb-2">本地操作</div>
              <div class="grid grid-cols-2 gap-2">
                <button @click="exportData" :disabled="exporting" class="btn-secondary text-xs py-2">
                  <div v-if="exporting" class="i-heroicons-arrow-path w-3 h-3 mr-1 animate-spin"></div>
                  <div v-else class="i-heroicons-arrow-down-tray w-3 h-3 mr-1"></div>
                  {{ exporting ? '导出中' : '导出' }}
                </button>

                <button @click="triggerImport" class="btn-secondary text-xs py-2">
                  <div class="i-heroicons-arrow-up-tray w-3 h-3 mr-1"></div>
                  导入
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>

    <!-- 隐藏的文件输入 -->
    <input ref="fileInput" type="file" accept=".json" @change="handleFileImport" class="hidden" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { githubGistService } from '@/services/githubGistService'
import { dataManagementService } from '@/services/dataManagementService'
import { notificationService } from '@/services/notificationService'

const router = useRouter()

// 响应式数据
const showDropdown = ref(false)
const isConnected = ref(false)
const hasCloudUpdate = ref(false)
const hasCloudBackup = ref(false)
const cloudInfo = ref<any>(null)
const uploading = ref(false)
const downloading = ref(false)
const exporting = ref(false)
const fileInput = ref<HTMLInputElement>()

// 切换下拉菜单
const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}

// 关闭下拉菜单
const closeDropdown = () => {
  showDropdown.value = false
}

// 前往设置页面
const goToSettings = () => {
  router.push('/settings')
  closeDropdown()
}

// 上传到云端
const uploadToCloud = async () => {
  try {
    uploading.value = true
    const data = await dataManagementService.exportAllData()
    const result = await githubGistService.uploadToGist(data)

    if (result.success) {
      notificationService.cloudUploadSuccess(result.url)
      await updateCloudInfo()
      hasCloudUpdate.value = false // 上传后清除更新标记
    } else {
      notificationService.cloudUploadError(result.error || '上传失败')
    }
  } catch (error) {
    notificationService.cloudUploadError((error as Error).message)
  } finally {
    uploading.value = false
    closeDropdown()
  }
}

// 从云端下载
const downloadFromCloud = async () => {
  try {
    downloading.value = true
    const result = await githubGistService.downloadFromGist()

    if (result.success && result.data) {
      const importResult = await dataManagementService.importData(result.data, {
        mode: 'overwrite',
        importKnowledge: true,
        importCategories: true,
        importTags: true,
        importSettings: true,
        importAiConfigs: true,
        importAiSessions: true
      })

      if (importResult.success) {
        notificationService.cloudDownloadSuccess()
        hasCloudUpdate.value = false // 下载后清除更新标记
      } else {
        notificationService.cloudDownloadError(importResult.message)
      }
    } else {
      notificationService.cloudDownloadError(result.error || '下载失败')
    }
  } catch (error) {
    notificationService.cloudDownloadError((error as Error).message)
  } finally {
    downloading.value = false
    closeDropdown()
  }
}

// 导出数据
const exportData = async () => {
  try {
    exporting.value = true
    const data = await dataManagementService.exportAllData()
    dataManagementService.downloadExportFile(data)

    const timestamp = new Date().toISOString().split('T')[0]
    const filename = `knowledge-backup-${timestamp}.json`
    notificationService.dataExportSuccess(filename)
  } catch (error) {
    notificationService.dataExportError((error as Error).message)
  } finally {
    exporting.value = false
    closeDropdown()
  }
}

// 触发导入
const triggerImport = () => {
  fileInput.value?.click()
  closeDropdown()
}

// 处理文件导入
const handleFileImport = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  try {
    const text = await file.text()
    const data = JSON.parse(text)

    const result = await dataManagementService.importData(data, {
      mode: 'merge',
      importKnowledge: true,
      importCategories: true,
      importTags: true,
      importSettings: true
    })

    if (result.success) {
      notificationService.dataImportSuccess(result.details)
    } else {
      notificationService.dataImportError(result.message)
    }
  } catch (error) {
    notificationService.dataImportError('文件格式错误或数据无效')
  } finally {
    // 清空文件输入
    if (target) {
      target.value = ''
    }
  }
}

// 更新云端信息
const updateCloudInfo = async () => {
  isConnected.value = githubGistService.isConfigured()

  if (isConnected.value) {
    try {
      const result = await githubGistService.getGistDetails()
      if (result.success && result.details) {
        hasCloudBackup.value = result.details.hasBackup
        cloudInfo.value = {
          lastSync: githubGistService.getLastSyncTime(),
          fileSize: result.details.fileSize
        }
        console.log('云端信息更新:', {
          hasBackup: result.details.hasBackup,
          fileSize: result.details.fileSize
        })
      } else {
        hasCloudBackup.value = false
        cloudInfo.value = null
        console.log('未找到云端备份:', result.error)
      }
    } catch (error) {
      console.error('获取云端信息失败:', error)
      hasCloudBackup.value = false
      cloudInfo.value = null
    }
  } else {
    hasCloudBackup.value = false
    cloudInfo.value = null
  }
}

// 格式化日期
const formatDate = (dateString: string | null): string => {
  if (!dateString) return '从未同步'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 监听云端更新事件
const handleCloudUpdate = () => {
  hasCloudUpdate.value = true

  // 显示通知
  notificationService.cloudUpdateDetected(() => {
    downloadFromCloud()
  })
}

// 点击外部关闭下拉菜单
const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.relative')) {
    closeDropdown()
  }
}

onMounted(() => {
  updateCloudInfo()

  // 监听云端更新事件
  window.addEventListener('cloud-update-detected', handleCloudUpdate)

  // 监听点击外部事件
  document.addEventListener('click', handleClickOutside)

  // 定期更新状态
  const interval = setInterval(updateCloudInfo, 30000) // 30秒更新一次

  onUnmounted(() => {
    window.removeEventListener('cloud-update-detected', handleCloudUpdate)
    document.removeEventListener('click', handleClickOutside)
    clearInterval(interval)
  })
})
</script>

<style scoped>
/* 下拉菜单动画 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s ease;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}
</style>
