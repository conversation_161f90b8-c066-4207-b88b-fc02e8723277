<template>
  <!-- 资源卡片 - Ant Design Vue重设计 -->
  <a-card class="resource-card" :hoverable="true" @click="$emit('click')">
    <!-- 封面图片 -->
    <div class="card-cover">
      <div v-if="resource.cover_image_url" class="cover-image"
        :style="{ backgroundImage: `url(${resource.cover_image_url})` }"></div>
      <div v-else class="cover-placeholder">
        <FileTextOutlined class="placeholder-icon" />
      </div>

      <!-- 分类标签 -->
      <div v-if="resource.category" class="category-tag">
        <a-tag :color="getCategoryColor(resource.category.name)" size="small">
          {{ resource.category.name }}
        </a-tag>
      </div>


    </div>

    <!-- 内容区域 -->
    <div class="card-content">
      <!-- 标题 -->
      <h3 class="card-title">
        {{ resource.title }}
      </h3>

      <!-- 描述 -->
      <p class="card-description">
        {{ getPlainTextDescription(resource.description) }}
      </p>
    </div>

    <!-- 底部固定区域：标签、元信息和操作按钮 -->
    <div class="card-bottom">
      <!-- 左侧：标签和元信息 -->
      <div class="bottom-left">
        <!-- 标签行 - 紧凑型样式 -->
        <div v-if="resource.tags && resource.tags.length > 0" class="card-tags">
          <div v-for="tag in resource.tags.slice(0, 3)" :key="tag.id" class="compact-tag" :style="getTagStyle(tag)">
            <span class="tag-name">{{ tag.name }}</span>
          </div>
          <div v-if="resource.tags.length > 3" class="compact-tag more-tags">
            <span class="tag-name">+{{ resource.tags.length - 3 }}</span>
          </div>
        </div>

        <!-- 元信息行 -->
        <div class="meta-info">
          <span class="meta-item">
            <EyeOutlined class="meta-icon" />
            {{ resource.view_count || 0 }}
          </span>
          <span class="meta-item">
            <ClockCircleOutlined class="meta-icon" />
            {{ formatRelativeTime(resource.updated_at) }}
          </span>
        </div>
      </div>

      <!-- 右侧：操作按钮组 -->
      <div class="bottom-right">
        <div class="action-buttons">
          <a-button type="text" size="small" class="action-button view-button" @click.stop="$emit('click')">
            <template #icon>
              <ArrowRightOutlined />
            </template>
          </a-button>
          <a-button type="text" size="small" class="action-button edit-button" @click.stop="$emit('edit')">
            <template #icon>
              <EditOutlined />
            </template>
          </a-button>
          <a-button type="text" size="small" class="action-button delete-button" @click.stop="$emit('delete')">
            <template #icon>
              <DeleteOutlined />
            </template>
          </a-button>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime.js'
import 'dayjs/locale/zh-cn.js'
import {
  FileTextOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  ArrowRightOutlined
} from '@ant-design/icons-vue'
import type { ResourceWithDetails } from '@/types'

// 配置dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

interface Props {
  resource: ResourceWithDetails
}

const props = defineProps<Props>()

const emit = defineEmits<{
  click: []
  edit: []
  delete: []
}>()

// 获取分类颜色
const getCategoryColor = (categoryName: string) => {
  // 根据分类名称返回对应的颜色
  const colorMap: Record<string, string> = {
    '前端开发': 'blue',
    '后端开发': 'green',
    '数据库': 'orange',
    '工具': 'purple',
    '文档': 'cyan',
    '教程': 'red'
  }
  return colorMap[categoryName] || 'default'
}

// 获取标签样式 - 与知识库界面保持一致
const getTagStyle = (tag: any) => {
  const tagColor = tag.color || '#1677ff'

  // 卡片中的标签始终显示为未选中状态（边框和文字为标签颜色）
  return {
    backgroundColor: 'transparent',
    borderColor: tagColor,
    color: tagColor
  }
}



// 获取纯文本描述（去除Markdown格式）
const getPlainTextDescription = (markdown: string) => {
  if (!markdown) return ''

  // 简单的Markdown转纯文本
  return markdown
    .replace(/#{1,6}\s+/g, '') // 移除标题标记
    .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体标记
    .replace(/\*(.*?)\*/g, '$1') // 移除斜体标记
    .replace(/`(.*?)`/g, '$1') // 移除代码标记
    .replace(/\[(.*?)\]\(.*?\)/g, '$1') // 移除链接，保留文本
    .replace(/!\[.*?\]\(.*?\)/g, '') // 移除图片
    .replace(/\n+/g, ' ') // 将换行替换为空格
    .trim()
}

// 格式化相对时间
const formatRelativeTime = (date: Date) => {
  return dayjs(date).fromNow()
}
</script>

<style scoped>
/* 资源卡片 - Ant Design Vue重设计样式 */
.resource-card {
  height: 100%;
  border-radius: var(--resource-card-radius, 12px);
  border: 1px solid var(--resource-card-border, var(--ant-color-border, #d9d9d9));
  box-shadow: var(--resource-card-shadow, 0 2px 8px rgba(0, 0, 0, 0.06));
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
}

/* 覆盖 Ant Design Card 的默认内边距 - 实现精确的边距控制 */
.resource-card :deep(.ant-card-body) {
  padding: 0 !important;
}

.resource-card:hover {
  border-color: var(--resource-card-hover-border, var(--ant-color-primary-border, #91d5ff));
  box-shadow: var(--resource-card-hover-shadow, 0 4px 16px rgba(0, 0, 0, 0.12));
  transform: translateY(-2px);
}

/* 封面区域 - 完全贴边设计，上左右0边距 */
.card-cover {
  position: relative;
  height: var(--resource-card-cover-height, 240px);
  overflow: hidden;
  border-radius: var(--resource-card-border-radius, 8px) var(--resource-card-border-radius, 8px) 0 0;
  margin: 0;
  margin-bottom: var(--resource-card-cover-bottom-margin, 10px);
}

.cover-image {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.resource-card:hover .cover-image {
  transform: scale(1.05);
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  background: var(--resource-card-placeholder-bg, linear-gradient(135deg, #f0f2f5, #e6f7ff));
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-icon {
  font-size: var(--resource-card-placeholder-icon-size, 48px);
  color: var(--resource-card-placeholder-icon-color, var(--ant-color-text-quaternary, #00000025));
}

/* 分类标签 - 紧凑型设计 */
.category-tag {
  position: absolute;
  top: var(--resource-card-tag-top, 8px);
  left: var(--resource-card-tag-left, 8px);
  z-index: 2;
}

.category-tag .ant-tag {
  font-size: var(--resource-card-category-tag-size, 11px);
  font-weight: var(--resource-card-category-tag-weight, 600);
  padding: var(--resource-card-category-tag-padding, 3px 8px);
  line-height: 1.2;
  border-radius: var(--resource-card-category-tag-radius, 4px);
  box-shadow: var(--resource-card-category-tag-shadow, 0 1px 3px rgba(0, 0, 0, 0.12));
  border: none;
}

/* 底部固定区域 - 与内容区域保持一致的左右边距，增加下边距，去掉阴影 */
.card-bottom {
  position: absolute !important;
  bottom: 0 !important;
  left: 0;
  right: 0;
  padding: var(--resource-card-bottom-padding, 8px) var(--resource-card-content-horizontal-padding, 12px);
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  background: transparent;
  min-height: 50px;
  z-index: 10;
  /* 确保底部区域不会被内容推动 */
  flex-shrink: 0;
}

.bottom-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
  justify-content: flex-end;
}

.bottom-right {
  opacity: 0;
  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.resource-card:hover .bottom-right {
  opacity: 1;
}

.action-buttons {
  display: flex;
  gap: 4px;
  align-items: center;
}

/* 简化的操作按钮样式 - 无边框无背景的灰色图标 */
.action-button {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  width: var(--resource-card-action-size, 20px);
  height: var(--resource-card-action-size, 20px);
  min-width: auto;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--resource-card-action-color, rgba(0, 0, 0, 0.45));
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-button:hover {
  background: transparent !important;
  transform: scale(1.1);
}

.action-button .anticon {
  font-size: var(--resource-card-action-icon-size, 12px);
}

/* 简化的按钮悬浮效果 - 保持灰色图标风格 */
.view-button:hover {
  color: var(--ant-color-success, #52c41a);
}

.edit-button:hover {
  color: var(--ant-color-primary, #1890ff);
}

.delete-button:hover {
  color: var(--ant-color-error, #ff4d4f);
}

/* 新的元信息样式 */
.meta-info {
  display: flex;
  gap: var(--resource-card-meta-gap, 6px);
}

.meta-info .meta-item {
  display: flex;
  align-items: center;
  gap: var(--resource-card-meta-icon-gap, 3px);
  font-size: var(--resource-card-meta-size, 9px);
  color: var(--resource-card-meta-color, var(--ant-color-text-tertiary, #00000040));
  line-height: 1;
  /* 移除所有装饰样式，保持纯文本+图标显示 */
  background: none;
  backdrop-filter: none;
  padding: 0;
  border-radius: 0;
  border: none;
}

.meta-info .meta-icon {
  font-size: var(--resource-card-meta-icon-size, 8px);
}

/* 内容区域 - 增加左右边距 */
.card-content {
  padding: 0 var(--resource-card-content-horizontal-padding, 12px);
  padding-bottom: var(--resource-card-content-bottom-padding, 60px);
  display: flex;
  flex-direction: column;
  gap: var(--resource-card-content-gap, 6px);
  flex: 1;
  position: relative;
  /* 为底部标签和元信息两行预留足够空间 */
  box-sizing: border-box;
  overflow: hidden;
}

/* 标题 - 主题信息突出显示 */
.card-title {
  font-size: var(--resource-card-title-size, 15px);
  font-weight: var(--resource-card-title-weight, 700);
  color: var(--resource-card-title-color, var(--ant-color-text, #000000e6));
  line-height: 1.2;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.resource-card:hover .card-title {
  color: var(--resource-card-title-hover-color, var(--ant-color-primary, #1890ff));
}

/* 描述 - 紧凑型设计 */
.card-description {
  font-size: var(--resource-card-desc-size, 12px);
  color: var(--resource-card-desc-color, var(--ant-color-text-secondary, #00000073));
  line-height: 1.3;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

/* 标签区域 - 紧凑型设计 */
.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin: 0;
  padding: 0;
  align-items: center;
}

/* 紧凑型标签样式 - 与知识库界面保持一致 */
.compact-tag {
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  border: 1px solid;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 10px;
  user-select: none;
  white-space: nowrap;
}

.compact-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.compact-tag .tag-name {
  font-weight: 500;
  line-height: 1.2;
}

/* 更多标签按钮样式 */
.compact-tag.more-tags {
  border-style: dashed !important;
  border-color: var(--ant-color-border-secondary) !important;
  background: var(--ant-color-bg-layout) !important;
  color: var(--ant-color-text-secondary) !important;
}

.compact-tag.more-tags:hover {
  border-color: var(--ant-color-primary) !important;
  background: var(--ant-color-primary-bg) !important;
  color: var(--ant-color-primary) !important;
}

/* 元信息 - 紧凑型设计 */
.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--resource-card-meta-size, 10px);
  color: var(--resource-card-meta-color, var(--ant-color-text-tertiary, #00000040));
  margin-top: auto;
  padding-top: var(--resource-card-meta-padding, 4px);
}

.meta-left {
  display: flex;
  gap: var(--resource-card-meta-gap, 8px);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--resource-card-meta-item-gap, 2px);
}

.meta-icon {
  font-size: var(--resource-card-meta-icon-size, 10px);
}

.meta-right {
  opacity: 0.4;
  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.resource-card:hover .meta-right {
  opacity: 0.8;
}

.link-icon {
  font-size: var(--resource-card-link-icon-size, 12px);
}

/* 危险菜单项 */
.danger-item {
  color: var(--ant-color-error, #ff4d4f) !important;
}

.danger-item:hover {
  background: var(--ant-color-error-bg, #fff2f0) !important;
}

/* 暗色主题适配 - 完整的暗色主题支持 */
.dark {
  /* 资源卡片暗色主题变量 */
  --resource-card-border: #424242;
  --resource-card-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
  --resource-card-hover-border: #1677ff;
  --resource-card-hover-shadow: 0 4px 16px rgba(0, 0, 0, 0.6);
  --resource-card-placeholder-bg: linear-gradient(135deg, #303030, #262626);
  --resource-card-placeholder-icon-color: rgba(255, 255, 255, 0.25);
  --resource-card-action-bg: rgba(38, 38, 38, 0.95);
  --resource-card-action-border: rgba(255, 255, 255, 0.08);
  --resource-card-action-hover-bg: rgba(38, 38, 38, 1);
  --resource-card-action-hover-border: rgba(255, 255, 255, 0.15);
  --resource-card-title-color: rgba(255, 255, 255, 0.85);
  --resource-card-title-hover-color: #40a9ff;
  --resource-card-desc-color: rgba(255, 255, 255, 0.65);
  --resource-card-more-tags-bg: #1a1a1a;
  --resource-card-more-tags-color: rgba(255, 255, 255, 0.65);
  --resource-card-more-tags-border: #424242;
  --resource-card-meta-color: rgba(255, 255, 255, 0.75);

  /* 精确布局设计变量 - 更窄更高的卡片 */
  --resource-card-cover-height: 240px;
  --resource-card-cover-bottom-margin: 10px;
  --resource-card-content-horizontal-padding: 12px;
  --resource-card-content-gap: 6px;
  --resource-card-title-size: 15px;
  --resource-card-title-weight: 700;
  --resource-card-desc-size: 12px;
  --resource-card-meta-size: 9px;
  --resource-card-meta-gap: 6px;
  --resource-card-meta-icon-gap: 3px;
  --resource-card-tag-size: 11px;
  --resource-card-tags-gap: 3px;
  --resource-card-tag-padding: 1px 4px;
  --resource-card-category-tag-weight: 600;
  --resource-card-action-size: 24px;
}

.dark .resource-card {
  background: #262626 !important;
  border-color: #424242 !important;
}

/* 暗色模式下的Ant Design Card组件强制样式 */
.dark .resource-card .ant-card {
  background: #262626 !important;
  border-color: #424242 !important;
}

.dark .resource-card .ant-card-body {
  background: #262626 !important;
}

/* 暗黑模式下的 Ant Design Card 悬浮效果 */
.dark .resource-card.ant-card-hoverable:hover {
  border-color: #1677ff !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.6) !important;
}

/* 暗色模式下的卡片内容区域 */
.dark .card-content {
  background: #262626 !important;
}

.dark .card-title {
  color: rgba(255, 255, 255, 0.85) !important;
}

.dark .card-description {
  color: rgba(255, 255, 255, 0.65) !important;
}

/* 暗黑模式下的悬浮效果 */
.dark .resource-card:hover {
  border-color: #1677ff !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.6) !important;
}

.dark .resource-card:hover .card-title {
  color: #40a9ff !important;
}

.dark .card-meta {
  color: rgba(255, 255, 255, 0.75) !important;
}

.dark .card-meta .meta-item {
  color: rgba(255, 255, 255, 0.75) !important;
}

/* 暗色模式下的底部元信息样式 */
.dark .meta-info .meta-item {
  color: rgba(255, 255, 255, 0.75) !important;
}

/* 暗色模式下的标签样式 */
.dark .resource-card .ant-tag.more-tags {
  background: rgba(255, 255, 255, 0.08) !important;
  border-color: rgba(255, 255, 255, 0.15) !important;
  color: rgba(255, 255, 255, 0.85) !important;
}

.dark .resource-card .ant-tag.category-tag {
  background: rgba(22, 119, 255, 0.15) !important;
  border-color: rgba(22, 119, 255, 0.3) !important;
  color: #40a9ff !important;
}

/* 暗黑模式下的紧凑型标签样式 */
.dark .compact-tag {
  /* 在暗黑模式下增强标签的可见度 */
  filter: brightness(1.2) saturate(1.1);
}

.dark .compact-tag:hover {
  filter: brightness(1.3) saturate(1.2);
}
</style>
