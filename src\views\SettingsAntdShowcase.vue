<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-6xl mx-auto px-4">
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8 text-center">
          设置界面 Ant Design 组件展示
        </h1>
        
        <a-space direction="vertical" size="large" style="width: 100%">
          <!-- 组件展示卡片 -->
          <a-row :gutter="[24, 24]">
            <!-- 知识库设置 -->
            <a-col :span="8">
              <a-card
                hoverable
                class="h-full cursor-pointer"
                @click="openKnowledgeSettings"
              >
                <template #cover>
                  <div class="h-32 bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center">
                    <DatabaseOutlined class="text-4xl text-white" />
                  </div>
                </template>
                <a-card-meta
                  title="知识库设置"
                  description="配置知识库浏览、排序、加载等相关设置"
                />
                <div class="mt-4">
                  <a-tag color="blue">表单组件</a-tag>
                  <a-tag color="green">栅格布局</a-tag>
                  <a-tag color="orange">卡片设计</a-tag>
                </div>
              </a-card>
            </a-col>

            <!-- AI设置 -->
            <a-col :span="8">
              <a-card
                hoverable
                class="h-full cursor-pointer"
                @click="openAiSettings"
              >
                <template #cover>
                  <div class="h-32 bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center">
                    <RobotOutlined class="text-4xl text-white" />
                  </div>
                </template>
                <a-card-meta
                  title="AI设置"
                  description="配置AI服务提供商、模型参数等设置"
                />
                <div class="mt-4">
                  <a-tag color="purple">模态框</a-tag>
                  <a-tag color="blue">表单验证</a-tag>
                  <a-tag color="green">高级组件</a-tag>
                </div>
              </a-card>
            </a-col>

            <!-- 搜索引擎设置 -->
            <a-col :span="8">
              <a-card
                hoverable
                class="h-full cursor-pointer"
                @click="openSearchEngineSettings"
              >
                <template #cover>
                  <div class="h-32 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center">
                    <SearchOutlined class="text-4xl text-white" />
                  </div>
                </template>
                <a-card-meta
                  title="搜索引擎设置"
                  description="管理默认搜索引擎和自定义搜索引擎"
                />
                <div class="mt-4">
                  <a-tag color="green">嵌套模态框</a-tag>
                  <a-tag color="blue">动态列表</a-tag>
                  <a-tag color="orange">交互设计</a-tag>
                </div>
              </a-card>
            </a-col>

            <!-- 主题设置 -->
            <a-col :span="8">
              <a-card
                hoverable
                class="h-full cursor-pointer"
                @click="openThemeSettings"
              >
                <template #cover>
                  <div class="h-32 bg-gradient-to-br from-orange-400 to-orange-600 flex items-center justify-center">
                    <BgColorsOutlined class="text-4xl text-white" />
                  </div>
                </template>
                <a-card-meta
                  title="主题设置"
                  description="配置应用主题模式和自定义主题色"
                />
                <div class="mt-4">
                  <a-tag color="orange">颜色选择器</a-tag>
                  <a-tag color="blue">预览效果</a-tag>
                  <a-tag color="green">开关组件</a-tag>
                </div>
              </a-card>
            </a-col>

            <!-- 数据管理 -->
            <a-col :span="8">
              <a-card
                hoverable
                class="h-full cursor-pointer"
                @click="openDataManagement"
              >
                <template #cover>
                  <div class="h-32 bg-gradient-to-br from-red-400 to-red-600 flex items-center justify-center">
                    <CloudSyncOutlined class="text-4xl text-white" />
                  </div>
                </template>
                <a-card-meta
                  title="数据管理"
                  description="数据导入导出、云端同步、存储统计"
                />
                <div class="mt-4">
                  <a-tag color="red">文件上传</a-tag>
                  <a-tag color="blue">统计图表</a-tag>
                  <a-tag color="green">状态管理</a-tag>
                </div>
              </a-card>
            </a-col>

            <!-- 综合演示 -->
            <a-col :span="8">
              <a-card
                hoverable
                class="h-full cursor-pointer"
                @click="openComprehensiveDemo"
              >
                <template #cover>
                  <div class="h-32 bg-gradient-to-br from-indigo-400 to-indigo-600 flex items-center justify-center">
                    <AppstoreOutlined class="text-4xl text-white" />
                  </div>
                </template>
                <a-card-meta
                  title="综合演示"
                  description="完整的设置界面演示，包含所有组件类型"
                />
                <div class="mt-4">
                  <a-tag color="indigo">完整演示</a-tag>
                  <a-tag color="blue">所有组件</a-tag>
                  <a-tag color="green">最佳实践</a-tag>
                </div>
              </a-card>
            </a-col>
          </a-row>

          <!-- 技术特性展示 -->
          <a-card title="技术特性" size="small">
            <a-row :gutter="[16, 16]">
              <a-col :span="6">
                <a-statistic
                  title="组件替换"
                  value="100"
                  suffix="%"
                  :value-style="{ color: '#3f8600' }"
                />
                <div class="text-sm text-gray-500 mt-2">
                  Base组件 → Ant Design组件
                </div>
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="响应式布局"
                  value="12"
                  suffix="列栅格"
                  :value-style="{ color: '#1677ff' }"
                />
                <div class="text-sm text-gray-500 mt-2">
                  a-row + a-col 栅格系统
                </div>
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="主题兼容"
                  value="2"
                  suffix="种模式"
                  :value-style="{ color: '#722ed1' }"
                />
                <div class="text-sm text-gray-500 mt-2">
                  浅色/暗色模式完美支持
                </div>
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="表单验证"
                  value="5"
                  suffix="种规则"
                  :value-style="{ color: '#fa8c16' }"
                />
                <div class="text-sm text-gray-500 mt-2">
                  完整的验证和错误提示
                </div>
              </a-col>
            </a-row>
          </a-card>

          <!-- 组件映射表 -->
          <a-card title="组件映射关系" size="small">
            <a-table
              :columns="mappingColumns"
              :data-source="mappingData"
              :pagination="false"
              size="small"
            />
          </a-card>
        </a-space>
      </div>
    </div>

    <!-- 各种设置组件的引用 -->
    <KnowledgeSettingsAntd v-model="showKnowledgeSettings" />
    <AiSettingsModalAntd v-model="showAiSettings" />
    <SearchEngineSettingsAntd v-model="showSearchEngineSettings" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  DatabaseOutlined,
  RobotOutlined,
  SearchOutlined,
  BgColorsOutlined,
  CloudSyncOutlined,
  AppstoreOutlined
} from '@ant-design/icons-vue'
import KnowledgeSettingsAntd from '@/components/settings/KnowledgeSettingsAntd.vue'
import AiSettingsModalAntd from '@/components/ai/AiSettingsModalAntd.vue'
import SearchEngineSettingsAntd from '@/components/settings/SearchEngineSettingsAntd.vue'

// 状态
const showKnowledgeSettings = ref(false)
const showAiSettings = ref(false)
const showSearchEngineSettings = ref(false)

// 表格数据
const mappingColumns = [
  { title: '原组件', dataIndex: 'original', key: 'original' },
  { title: 'Ant Design组件', dataIndex: 'antd', key: 'antd' },
  { title: '功能增强', dataIndex: 'enhancement', key: 'enhancement' }
]

const mappingData = [
  {
    key: '1',
    original: 'BaseInput',
    antd: 'a-input + a-form-item',
    enhancement: '内置验证、更好的样式'
  },
  {
    key: '2',
    original: 'BaseSelect',
    antd: 'a-select + a-form-item',
    enhancement: '搜索、过滤、多选支持'
  },
  {
    key: '3',
    original: 'BaseButton',
    antd: 'a-button',
    enhancement: '加载状态、图标支持'
  },
  {
    key: '4',
    original: 'BaseToggle',
    antd: 'a-switch / a-checkbox',
    enhancement: '更好的交互反馈'
  },
  {
    key: '5',
    original: 'BaseModal',
    antd: 'a-modal',
    enhancement: '拖拽、全屏、嵌套支持'
  }
]

// 方法
const openKnowledgeSettings = () => {
  showKnowledgeSettings.value = true
}

const openAiSettings = () => {
  showAiSettings.value = true
}

const openSearchEngineSettings = () => {
  showSearchEngineSettings.value = true
}

const openThemeSettings = () => {
  window.open('/theme-settings-antd', '_blank')
}

const openDataManagement = () => {
  window.open('/data-management-antd', '_blank')
}

const openComprehensiveDemo = () => {
  window.open('/settings-antd-demo', '_blank')
}
</script>
