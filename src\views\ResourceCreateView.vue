<template>
  <div class="resource-create-container">
    <div class="resource-create-content">
      <!-- 顶部操作栏 -->
      <div class="top-actions">
        <a-button type="text" size="small" @click="$router.back()" class="back-btn">
          <template #icon>
            <ArrowLeftOutlined />
          </template>
          返回
        </a-button>
      </div>

      <!-- 主要内容区域 - 卡片式布局 -->
      <a-form @submit.prevent="handleSubmit" layout="vertical" class="resource-form">
        <a-space direction="vertical" size="middle" style="width: 100%">

          <!-- 基本信息卡片 -->
          <a-card size="small" class="info-card">
            <template #title>
              <div class="card-title-wrapper">
                <span class="card-title">
                  <LinkOutlined class="title-icon" />
                  基本信息
                </span>
              </div>
            </template>

            <a-row :gutter="16">
              <a-col :span="24">
                <!-- 资源链接 -->
                <a-form-item label="资源链接" required :validate-status="errors.url ? 'error' : ''" :help="errors.url">
                  <a-input-group compact>
                    <a-input v-model:value="form.url" placeholder="https://example.com"
                      style="width: calc(100% - 100px)" :status="errors.url ? 'error' : ''">
                      <template #prefix>
                        <LinkOutlined />
                      </template>
                    </a-input>
                    <a-button type="default" :loading="parsing" :disabled="!form.url || !isValidUrl(form.url)"
                      @click="parseLink" style="width: 100px">
                      <template #icon>
                        <ThunderboltOutlined />
                      </template>
                      解析
                    </a-button>
                  </a-input-group>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="16">
              <a-col :span="16">
                <!-- 标题 -->
                <a-form-item label="标题" required :validate-status="errors.title ? 'error' : ''" :help="errors.title">
                  <a-input v-model:value="form.title" placeholder="输入资源标题" :status="errors.title ? 'error' : ''"
                    allow-clear>
                    <template #prefix>
                      <FileTextOutlined />
                    </template>
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <!-- 封面图片URL -->
                <a-form-item label="封面图片" :validate-status="errors.cover_image_url ? 'error' : ''"
                  :help="errors.cover_image_url">
                  <a-input v-model:value="form.cover_image_url" placeholder="图片链接"
                    :status="errors.cover_image_url ? 'error' : ''" allow-clear>
                    <template #prefix>
                      <PictureOutlined />
                    </template>
                  </a-input>
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>

          <!-- 分类和标签卡片 -->
          <a-card size="small" class="category-tag-card">
            <template #title>
              <div class="card-title-wrapper">
                <span class="card-title">
                  <FolderOutlined class="title-icon" />
                  分类和标签
                </span>
              </div>
            </template>

            <!-- 分类选择 -->
            <a-form-item label="分类" required :validate-status="errors.category_id ? 'error' : ''"
              :help="errors.category_id">
              <a-tree-select v-model:value="form.category_id" :tree-data="categoryTreeData" placeholder="选择分类"
                :disabled="isEditing" allow-clear show-search tree-default-expand-all
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :field-names="{ children: 'children', label: 'title', value: 'value', key: 'key' }"
                :filter-tree-node="filterTreeNode" class="category-selector" @search="handleCategorySearch"
                @select="handleCategorySelect">
                <template #title="{ title, resource_count, isAddOption }">
                  <div v-if="isAddOption" class="add-category-option" @click.stop="openCreateCategoryDialog()">
                    <PlusOutlined style="margin-right: 6px; font-size: 12px;" />
                    <span>{{ title }}</span>
                  </div>
                  <span v-else>{{ title }} <span class="resource-count">({{ resource_count || 0 }})</span></span>
                </template>
                <template #notFoundContent>
                  <div class="category-not-found-content">
                    <div class="not-found-text">未找到匹配的分类</div>
                    <a-button type="link" size="small" @click="openCreateCategoryDialog(categorySearchQuery)">
                      <template #icon>
                        <PlusOutlined />
                      </template>
                      创建分类 "{{ categorySearchQuery }}"
                    </a-button>
                  </div>
                </template>
              </a-tree-select>
            </a-form-item>

            <!-- 创建分类对话框 -->
            <CreateCategoryModal v-model:open="showCreateCategoryDialog" :categories="categories"
              :initial-name="categorySearchQuery" @success="handleCategoryCreated"
              @cancel="handleCreateCategoryCancel" />

            <!-- 标签选择 -->
            <a-form-item label="标签" :validate-status="errors.tag_ids ? 'error' : ''" :help="errors.tag_ids">
              <!-- 标签输入框（包含已选择的标签） -->
              <div class="tag-input-container">
                <!-- 已选择的标签显示在输入框内 -->
                <div v-if="selectedTagsDisplay.length > 0" class="selected-tags-inline">
                  <div v-for="tag in selectedTagsDisplay" :key="tag.id" class="compact-tag-inline"
                    :style="getTagStyle(tag)">
                    <span class="tag-name">{{ tag.name }}</span>
                    <CloseOutlined class="tag-remove-icon" @click="tag.id && removeTag(tag.id)" />
                  </div>
                </div>

                <!-- 标签搜索输入框 -->
                <a-input v-model:value="tagSearchQuery" placeholder="搜索或创建标签..." :disabled="isEditing"
                  @input="handleTagSearch" @press-enter="handleTagEnter" class="tag-search-input"
                  :class="{ 'has-tags': selectedTagsDisplay.length > 0 }">
                  <template #prefix>
                    <TagOutlined />
                  </template>
                </a-input>
              </div>

              <!-- 标签推荐区域 - 搜索时显示搜索结果，否则显示热门标签 -->
              <div class="tags-recommendation-section">
                <!-- 搜索结果 -->
                <div v-if="tagSearchQuery">
                  <div class="tags-section-label">
                    搜索结果：
                    <span v-if="filteredTags.length > 0" class="result-count">({{ filteredTags.length }}个)</span>
                  </div>
                  <div class="tags-container">
                    <!-- 创建新标签选项 -->
                    <div v-if="tagSearchQuery && !hasExactTagMatch" class="create-tag-button"
                      @click="createAndAddTag(tagSearchQuery)">
                      <PlusOutlined class="create-tag-icon" />
                      <span>创建 "{{ tagSearchQuery }}"</span>
                    </div>

                    <!-- 搜索到的标签 -->
                    <div v-for="tag in filteredTags" :key="tag.id"
                      :class="['compact-tag-option', { 'tag-selected': isTagSelected(tag) }]"
                      :style="getOptionTagStyle(tag, isTagSelected(tag))" @click="toggleTag(tag)">
                      <span class="tag-name">{{ tag.name }}</span>
                      <span class="tag-count">{{ tag.resource_count }}</span>
                      <CheckOutlined v-if="isTagSelected(tag)" class="tag-selected-icon" />
                    </div>

                    <!-- 无搜索结果 -->
                    <div v-if="filteredTags.length === 0 && hasExactTagMatch" class="no-results">
                      未找到匹配的标签
                    </div>
                  </div>
                </div>

                <!-- 热门标签推荐 -->
                <div v-else-if="popularTags.length > 0">
                  <div class="tags-section-label">热门标签：</div>
                  <div class="tags-container">
                    <div v-for="tag in popularTags.slice(0, 12)" :key="tag.id"
                      :class="['compact-tag-option', { 'tag-selected': isTagSelected(tag) }]"
                      :style="getOptionTagStyle(tag, isTagSelected(tag))" @click="toggleTag(tag)">
                      <span class="tag-name">{{ tag.name }}</span>
                      <CheckOutlined v-if="isTagSelected(tag)" class="tag-selected-icon" />
                    </div>
                  </div>
                </div>
              </div>
            </a-form-item>
          </a-card>

          <!-- 描述编辑卡片 -->
          <a-card size="small" class="description-card" style="margin-top: 16px;">
            <template #title>
              <div class="card-title-wrapper">
                <span class="card-title">
                  <FileTextOutlined class="title-icon" />
                  描述内容
                </span>
              </div>
            </template>

            <!-- Markdown 编辑器 - 全宽显示 -->
            <MarkdownEditor v-model="form.description" placeholder="请输入资源描述，支持 Markdown 语法..." height="400px"
              :preview="false" :readonly="isEditing" :disabled="isEditing" @change="handleDescriptionChange"
              @upload-img="handleImageUpload" @save="handleMarkdownSave" />
          </a-card>

          <!-- 操作按钮 -->
          <a-form-item style="margin-top: 24px;">
            <a-space>
              <a-button @click="$router.back()">
                取消
              </a-button>
              <a-button type="default" @click="handlePreview" :disabled="!canPreview">
                <template #icon>
                  <EyeOutlined />
                </template>
                预览效果
              </a-button>
              <a-button type="primary" html-type="submit" :loading="submitting">
                {{ isEditing ? '更新' : '创建' }}
              </a-button>
            </a-space>
          </a-form-item>

        </a-space>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  FileTextOutlined,
  LinkOutlined,
  ThunderboltOutlined,
  PictureOutlined,
  FolderOutlined,
  TagOutlined,
  EyeOutlined,
  PlusOutlined,
  CheckOutlined,
  CloseOutlined
} from '@ant-design/icons-vue'
import CreateCategoryModal from '@/components/common/CreateCategoryModal.vue'
import MarkdownEditor from '@/components/common/MarkdownEditor.vue'
import { resourceService } from '@/services/resourceService'
import { categoryService } from '@/services/categoryService'
import { tagService } from '@/services/tagService'
import { linkParserService } from '@/services/linkParser'
import { temporaryDataService } from '@/services/temporaryDataService'
import type { ResourceForm, Category, Tag } from '@/types'

const route = useRoute()
const router = useRouter()

// 状态
const loading = ref(true)
const submitting = ref(false)
const parsing = ref(false)
const categories = ref<Category[]>([])
const allTags = ref<Tag[]>([])

// 创建分类相关状态
const showCreateCategoryDialog = ref(false)
const categorySearchQuery = ref('')

// 标签相关状态
const tagSearchQuery = ref('')
const popularTags = ref<Tag[]>([])



// 表单数据
const form = ref<ResourceForm>({
  url: '',
  title: '',
  description: '',
  cover_image_url: '',
  category_id: null,
  tag_ids: []
})

// 表单验证错误
const errors = ref<Record<string, string>>({})

// 计算属性
const isEditing = computed(() => !!route.query.id)
const selectedCategory = computed(() =>
  categories.value.find(cat => cat.id === form.value.category_id)
)
const selectedTags = computed(() =>
  allTags.value.filter(tag => form.value.tag_ids.includes(tag.id!))
)

// 转换分类数据为树形结构 - 适配 Ant Design TreeSelect
const categoryTreeData = computed(() => {
  const buildTreeData = (parentId: number = 0): any[] => {
    return categories.value
      .filter(cat => cat.parent_id === parentId)
      .map(category => ({
        key: category.id!,
        value: category.id!,
        title: category.name,
        resource_count: category.resource_count,
        children: buildTreeData(category.id!)
      }))
      .filter(node => node.children.length > 0 || node.value) // 过滤掉空节点
  }

  const treeData = buildTreeData()

  // 添加"添加分类"选项
  treeData.push({
    key: 'add-category',
    value: 'add-category',
    title: '添加分类',
    resource_count: 0,
    children: [],
    selectable: false,
    disabled: true,
    isAddOption: true
  })

  return treeData
})

// 树节点搜索过滤
const filterTreeNode = (inputValue: string, treeNode: any) => {
  return treeNode.title.toLowerCase().includes(inputValue.toLowerCase())
}

// 分类搜索处理
const handleCategorySearch = (value: string) => {
  categorySearchQuery.value = value
}

// 处理分类选择
const handleCategorySelect = (value: any) => {
  if (value === 'add-category') {
    // 点击了"添加分类"选项
    form.value.category_id = null // 清除选择
    openCreateCategoryDialog()
  }
}

// 打开创建分类对话框
const openCreateCategoryDialog = (searchValue?: string) => {
  categorySearchQuery.value = searchValue || ''
  showCreateCategoryDialog.value = true
}

// 处理分类创建成功
const handleCategoryCreated = async (newCategory: Category) => {
  // 重新加载分类数据
  await loadCategories()

  // 自动选择新创建的分类
  form.value.category_id = newCategory.id!

  // 清空搜索查询
  categorySearchQuery.value = ''
}

// 处理创建分类取消
const handleCreateCategoryCancel = () => {
  categorySearchQuery.value = ''
}

// 标签相关方法
const handleTagSearch = () => {
  // 搜索时不需要特殊处理，直接通过计算属性更新显示
}

const handleTagEnter = () => {
  if (tagSearchQuery.value && !hasExactTagMatch.value) {
    createAndAddTag(tagSearchQuery.value)
  } else if (filteredTags.value.length > 0) {
    toggleTag(filteredTags.value[0])
  }
}

const toggleTag = (tag: Tag) => {
  const tagId = tag.id!
  const currentTags = [...form.value.tag_ids]

  if (currentTags.includes(tagId)) {
    const index = currentTags.indexOf(tagId)
    currentTags.splice(index, 1)
  } else {
    currentTags.push(tagId)
  }

  form.value.tag_ids = currentTags
  // 选择标签后清空搜索，回到热门标签显示
  tagSearchQuery.value = ''
}

const removeTag = (tagId: number) => {
  form.value.tag_ids = form.value.tag_ids.filter(id => id !== tagId)
}

const isTagSelected = (tag: Tag) => {
  return form.value.tag_ids.includes(tag.id!)
}

const createAndAddTag = async (tagName: string) => {
  try {
    // 生成随机颜色
    const colors = [
      '#EF4444', '#F97316', '#F59E0B', '#EAB308', '#84CC16',
      '#22C55E', '#10B981', '#14B8A6', '#06B6D4', '#0EA5E9',
      '#3B82F6', '#6366F1', '#8B5CF6', '#A855F7', '#D946EF',
      '#EC4899', '#F43F5E'
    ]
    const randomColor = colors[Math.floor(Math.random() * colors.length)]

    // 创建临时标签（编辑模式下不保存到数据库）
    const tagId = Date.now() // 使用时间戳作为临时ID
    const newTag: Tag = {
      id: tagId,
      name: tagName.trim(),
      color: randomColor,
      resource_count: 0,
      created_at: new Date(),
      isTemporary: true // 标记为临时标签
    }

    // 添加到所有标签列表
    allTags.value.push(newTag)

    // 添加到选中列表
    form.value.tag_ids.push(tagId)

    tagSearchQuery.value = ''
  } catch (error) {
    console.error('创建标签失败:', error)
  }
}

// 标签相关计算属性
const selectedTagsDisplay = computed(() => {
  return allTags.value.filter(tag => form.value.tag_ids.includes(tag.id!))
})

const filteredTags = computed(() => {
  if (!tagSearchQuery.value) return []

  const query = tagSearchQuery.value.toLowerCase()
  return allTags.value
    .filter(tag =>
      tag.name.toLowerCase().includes(query) &&
      !form.value.tag_ids.includes(tag.id!)
    )
    .slice(0, 10)
})

const hasExactTagMatch = computed(() => {
  return allTags.value.some(tag =>
    tag.name.toLowerCase() === tagSearchQuery.value.toLowerCase()
  )
})

// 获取标签样式 - 与其他界面保持一致
const getTagStyle = (tag: Tag) => {
  const tagColor = tag.color || '#1677ff'

  // 输入框内的标签显示为选中状态（背景为标签颜色）
  return {
    backgroundColor: tagColor,
    borderColor: tagColor,
    color: '#ffffff'
  }
}

// 获取选项标签样式 - 推荐区域的标签
const getOptionTagStyle = (tag: Tag, isSelected: boolean) => {
  const tagColor = tag.color || '#1677ff'

  if (isSelected) {
    // 选中状态：背景色为标签颜色，文字为白色
    return {
      backgroundColor: tagColor,
      borderColor: tagColor,
      color: '#ffffff'
    }
  } else {
    // 未选中状态：边框和文字为标签颜色，背景透明
    return {
      backgroundColor: 'transparent',
      borderColor: tagColor,
      color: tagColor
    }
  }
}



// 验证URL格式
const isValidUrl = (url: string): boolean => {
  try {
    new URL(url)
    return url.startsWith('http://') || url.startsWith('https://')
  } catch {
    return false
  }
}





// 初始化
onMounted(async () => {
  try {
    await Promise.all([
      loadCategories(),
      loadTags()
    ])

    // 先检查是否有预览数据需要恢复（无论是否为编辑模式）
    const previewData = sessionStorage.getItem('resource-preview-data')
    if (previewData) {
      console.log('发现预览数据，尝试恢复...')
      const restored = restorePreviewData()
      if (restored) {
        console.log('预览数据恢复成功')
        return // 恢复成功，不需要加载原始数据
      } else {
        console.log('预览数据恢复失败')
      }
    }

    // 如果没有预览数据或恢复失败，且是编辑模式，则加载原始资源数据
    if (isEditing.value) {
      console.log('加载原始资源数据')
      await loadResource()
    }

  } catch (error) {
    console.error('初始化失败:', error)
  } finally {
    loading.value = false
  }
})



// 加载分类
const loadCategories = async () => {
  categories.value = await categoryService.getAllCategories()
}

// 加载标签
const loadTags = async () => {
  allTags.value = await tagService.getAllTags()
  // 加载热门标签（按资源数量排序）
  popularTags.value = [...allTags.value]
    .sort((a, b) => (b.resource_count || 0) - (a.resource_count || 0))
    .slice(0, 12)
}

// 解析链接
const parseLink = async () => {
  if (!form.value.url || !isValidUrl(form.value.url)) {
    return
  }

  try {
    parsing.value = true
    const result = await linkParserService.parseLink(form.value.url)

    if (result.success && result.data) {
      const metadata = result.data

      // 自动填充标题（如果用户没有输入）
      if (!form.value.title && metadata.title) {
        form.value.title = metadata.title
      }

      // 自动填充描述（如果用户没有输入）
      if (!form.value.description && metadata.description) {
        form.value.description = metadata.description
      }

      // 自动填充封面图片（如果用户没有输入）
      if (!form.value.cover_image_url && metadata.image) {
        form.value.cover_image_url = metadata.image
      }

      // 处理关键词作为标签（不自动保存，只是建议）
      if (metadata.keywords && metadata.keywords.length > 0) {
        const suggestedTags = metadata.keywords.slice(0, 5) // 最多建议5个标签
        console.log('建议的标签:', suggestedTags)
      }

      console.log('链接解析成功:', metadata)

    } else {
      console.error('链接解析失败:', result.error)
    }

  } catch (error) {
    console.error('解析链接时出错:', error)
  } finally {
    parsing.value = false
  }
}

// 恢复预览数据
const restorePreviewData = () => {
  const previewData = sessionStorage.getItem('resource-preview-data')
  console.log('检查预览数据:', previewData ? '存在' : '不存在')

  if (!previewData) return false

  try {
    const parsedData = JSON.parse(previewData)
    console.log('解析的预览数据:', parsedData)
    console.log('当前路由ID:', route.query.id)
    console.log('预览数据中的原始ID:', parsedData.originalResourceId)
    console.log('是否为预览数据:', parsedData.isPreview)
    console.log('ID是否匹配:', String(parsedData.originalResourceId) === String(route.query.id))

    if (parsedData.isPreview) {
      // 如果当前路由没有ID但预览数据有原始ID，说明是从预览页面返回的
      const shouldRestore = String(parsedData.originalResourceId) === String(route.query.id) ||
        (!route.query.id && parsedData.originalResourceId)

      console.log('应该恢复数据:', shouldRestore)

      if (shouldRestore) {
        // 恢复表单数据
        form.value = {
          url: parsedData.url || '',
          title: parsedData.title || '',
          description: parsedData.description || '',
          cover_image_url: parsedData.cover_image_url || '',
          category_id: parsedData.category_id || null,
          tag_ids: parsedData.tag_ids || []
        }

        console.log('恢复后的表单数据:', form.value)

        // 使用 nextTick 确保响应式更新
        nextTick(() => {
          console.log('nextTick 后的表单数据:', form.value)
        })

        // 清除预览数据
        sessionStorage.removeItem('resource-preview-data')
        console.log('已恢复预览数据到表单')
        return true
      } else {
        console.log('预览数据不匹配，不进行恢复')
      }
    }
  } catch (error) {
    console.error('恢复预览数据失败:', error)
    sessionStorage.removeItem('resource-preview-data')
  }

  return false
}

// 加载资源（编辑模式）
const loadResource = async () => {
  const resourceId = Number(route.query.id)
  if (!resourceId) return

  try {
    const resource = await resourceService.getResourceById(resourceId)
    if (resource) {
      form.value = {
        url: resource.url,
        title: resource.title,
        description: resource.description,
        cover_image_url: resource.cover_image_url || '',
        category_id: resource.category_id,
        tag_ids: resource.tags?.map((tag: any) => tag.id!) || []
      }
    }
  } catch (error) {
    console.error('加载资源失败:', error)
    alert('加载资源失败')
    router.back()
  }
}

// 表单验证
const validateForm = () => {
  errors.value = {}

  if (!form.value.url) {
    errors.value.url = '请输入资源链接'
  } else if (!/^https?:\/\/.+/.test(form.value.url)) {
    errors.value.url = '请输入有效的URL'
  }

  if (!form.value.title) {
    errors.value.title = '请输入标题'
  }

  if (!form.value.description) {
    errors.value.description = '请输入描述'
  }

  if (!form.value.category_id) {
    errors.value.category_id = '请选择分类'
  }

  return Object.keys(errors.value).length === 0
}

// 提交表单
const handleSubmit = async () => {
  if (!validateForm()) return

  try {
    submitting.value = true

    // 处理临时数据（在编辑模式下可能创建了临时标签和分类）
    let processedForm = form.value
    if (isEditing.value && temporaryDataService.hasTemporaryData(allTags.value, categories.value)) {
      try {
        processedForm = await temporaryDataService.processTemporaryData(
          form.value,
          allTags.value,
          categories.value
        )
      } catch (error) {
        console.error('保存临时数据失败:', error)
        alert('保存新创建的标签或分类失败，请重试')
        return
      }
    }

    if (isEditing.value) {
      const resourceId = Number(route.query.id)
      await resourceService.updateResource(resourceId, processedForm)
    } else {
      await resourceService.createResource(processedForm)
    }

    // 清理临时数据标记
    temporaryDataService.cleanupTemporaryFlags(allTags.value, categories.value)

    router.push('/knowledge')
  } catch (error) {
    console.error('保存失败:', error)
    alert('保存失败，请重试')
  } finally {
    submitting.value = false
  }
}

// Markdown 编辑器相关方法
const handleDescriptionChange = () => {
  // 描述内容变化时的处理
  if (errors.value.description) {
    errors.value.description = ''
  }
}

const handleImageUpload = async (files: File[], callback: (urls: string[]) => void) => {
  try {
    // 这里实现图片上传逻辑
    // 示例：上传到服务器并返回 URL
    const urls: string[] = []

    for (const file of files) {
      // 创建临时 URL 用于预览（实际项目中应该上传到服务器）
      const url = URL.createObjectURL(file)
      urls.push(url)

      // TODO: 实际的图片上传逻辑
      // const uploadedUrl = await uploadService.uploadImage(file)
      // urls.push(uploadedUrl)
    }

    callback(urls)
  } catch (error) {
    console.error('图片上传失败:', error)
    callback([])
  }
}

const handleMarkdownSave = (value: string) => {
  // Markdown 编辑器保存时的处理（Ctrl+S）
  console.log('Markdown 内容保存:', value)
}

// 预览相关逻辑
const canPreview = computed(() => {
  return form.value.title && form.value.url && form.value.category_id
})

const handlePreview = () => {
  if (!canPreview.value) {
    message.warning('请先填写标题、链接和分类信息')
    return
  }

  // 将当前表单数据保存到临时存储
  const previewData = {
    ...form.value,
    selectedTags: selectedTags.value,
    selectedCategory: selectedCategory.value,
    isPreview: true,
    isEditing: isEditing.value, // 保存编辑状态
    originalResourceId: String(route.query.id), // 确保保存为字符串
    previewId: Date.now().toString() // 生成临时ID
  }

  console.log('保存预览数据:', previewData)

  // 保存到 sessionStorage 用于预览和返回时恢复
  sessionStorage.setItem('resource-preview-data', JSON.stringify(previewData))

  // 跳转到预览页面
  router.push({
    name: 'ResourcePreview',
    params: { id: previewData.previewId }
  })
}


</script>

<style scoped>
/* 资源创建页面 - 参考分类管理器的卡片式设计 */
.resource-create-container {
  padding: 24px;
  background: var(--ant-color-bg-layout);
  min-height: calc(100vh - 48px);
}

.resource-create-content {
  max-width: 1200px;
  margin: 0 auto;
}

/* 顶部操作栏 */
.top-actions {
  margin-bottom: 16px;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--ant-color-text-secondary);
  transition: all 0.2s;
}

.back-btn:hover {
  color: var(--ant-color-primary);
}

/* 卡片标题样式 - 参考分类管理器 */
.card-title-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--ant-color-text);
}

.title-icon {
  font-size: 16px;
  color: var(--ant-color-primary);
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--ant-color-text);
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.page-description {
  font-size: 14px;
  color: var(--ant-color-text-secondary);
  margin: 0;
  line-height: 1.5;
}

/* 卡片样式 - 参考分类管理器 */
.info-card,
.category-tag-card,
.description-card {
  border: 1px solid var(--ant-color-border);
  border-radius: 8px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  transition: all 0.2s;
}

.info-card:hover,
.category-tag-card:hover,
.description-card:hover {
  border-color: var(--ant-color-border-secondary);
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);
}

/* 表单样式优化 */
.resource-form {
  width: 100%;
}

.resource-form .ant-form-item {
  margin-bottom: 16px;
}

.resource-form .ant-form-item-label>label {
  font-weight: 500;
  color: var(--ant-color-text);
}

/* 预览区域样式 */
.preview-wrapper {
  height: 400px;
  border: 1px solid var(--ant-color-border);
  border-radius: 6px;
  overflow: hidden;
  background: var(--ant-color-bg-container);
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--ant-color-bg-layout);
  border-bottom: 1px solid var(--ant-color-border);
  font-size: 13px;
  font-weight: 500;
  color: var(--ant-color-text-secondary);
}

.preview-icon {
  font-size: 14px;
  color: var(--ant-color-primary);
}

.preview-content-wrapper {
  height: calc(100% - 41px);
  overflow-y: auto;
  padding: 16px;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--ant-color-text-tertiary);
  text-align: center;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.3;
}



/* 分类选择器样式 - 模拟原来的 TreeCategorySelector */
.category-selector {
  width: 100% !important;
  border-radius: 6px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.category-selector :deep(.ant-select-selector) {
  border-radius: 6px !important;
}

.category-selector:hover :deep(.ant-select-selector) {
  border-color: #4096ff !important;
}

.category-selector:focus-within :deep(.ant-select-selector) {
  border-color: #1677ff !important;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1) !important;
}

/* 分类选择器下拉菜单样式 */
.category-selector :deep(.ant-select-dropdown) {
  border-radius: 8px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

/* 资源数量显示样式 */
.resource-count {
  color: var(--ant-color-text-tertiary);
  font-size: 12px;
  margin-left: 4px;
}

/* 添加分类选项样式 - 按钮风格 */
.add-category-option {
  color: var(--ant-color-primary) !important;
  font-weight: 500;
  cursor: pointer;
  padding: 6px 12px;
  border: 1px dashed var(--ant-color-primary);
  border-radius: 6px;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  background: transparent;
  font-size: 13px;
  margin: 2px 0;
  width: fit-content;
}

.add-category-option:hover {
  background: var(--ant-color-primary-bg) !important;
  color: var(--ant-color-primary) !important;
  border-color: var(--ant-color-primary) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(22, 119, 255, 0.1);
}

/* 暗黑模式下的添加分类选项 */
.dark .add-category-option {
  border-color: var(--ant-color-primary);
  color: var(--ant-color-primary) !important;
}

.dark .add-category-option:hover {
  background: rgba(22, 119, 255, 0.1) !important;
  border-color: var(--ant-color-primary) !important;
  box-shadow: 0 2px 4px rgba(22, 119, 255, 0.2);
}

/* 标签输入容器样式 */
.tag-input-container {
  position: relative;
  border: 1px solid var(--ant-color-border);
  border-radius: 6px;
  background: var(--ant-color-bg-container);
  transition: all 0.2s;
}

.tag-input-container:hover {
  border-color: var(--ant-color-border-secondary);
}

.tag-input-container:focus-within {
  border-color: var(--ant-color-primary);
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

/* 输入框内的已选标签 */
.selected-tags-inline {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  padding: 6px 8px 2px 8px;
  align-items: center;
}

.compact-tag-inline {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border: 1px solid;
  border-radius: 8px;
  font-size: 11px;
  user-select: none;
  white-space: nowrap;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.compact-tag-inline .tag-name {
  font-weight: 500;
  line-height: 1.2;
}

.tag-remove-icon {
  font-size: 8px;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.tag-remove-icon:hover {
  opacity: 1;
}

/* 标签搜索输入框 */
.tag-search-input {
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  background: transparent !important;
}

.tag-search-input.has-tags {
  padding-top: 2px !important;
}

/* 标签推荐区域样式 */
.tags-recommendation-section {
  margin-top: 12px;
}

.tags-section-label {
  font-size: 13px;
  color: var(--ant-color-text-secondary);
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.result-count {
  color: var(--ant-color-text-tertiary);
  font-size: 12px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

/* 创建标签按钮样式 */
.create-tag-button {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: 1px dashed var(--ant-color-primary);
  border-radius: 4px;
  background: transparent;
  color: var(--ant-color-primary);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  margin-right: 6px;
}

.create-tag-button:hover {
  background: var(--ant-color-primary-bg);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(22, 119, 255, 0.1);
}

.create-tag-icon {
  font-size: 10px;
}

/* 推荐标签选项样式 */
.compact-tag-option {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: 1px solid;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 12px;
  user-select: none;
  white-space: nowrap;
  margin: 0;
}

.compact-tag-option:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.compact-tag-option .tag-name {
  font-weight: 500;
  line-height: 1.2;
}

.tag-count {
  font-size: 10px;
  color: var(--ant-color-text-tertiary);
  margin-left: 2px;
}

.tag-selected-icon {
  color: var(--ant-color-primary);
  font-size: 10px;
  margin-left: 2px;
}

/* 无搜索结果样式 */
.no-results {
  padding: 12px 16px;
  text-align: center;
  color: var(--ant-color-text-tertiary);
  font-size: 13px;
  background: var(--ant-color-bg-layout);
  border-radius: 4px;
  border: 1px dashed var(--ant-color-border);
}

/* 搜索无结果时的创建分类内容 */
.category-not-found-content {
  padding: 16px;
  text-align: center;
}

.not-found-text {
  color: var(--ant-color-text-secondary);
  margin-bottom: 8px;
  font-size: 14px;
}



/* 预览区域 */
.preview-section {
  min-width: 0;
  width: 100%;
}

.preview-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--ant-color-border);
  background: var(--ant-color-bg-container);
  position: sticky;
  top: 24px;
  max-height: calc(100vh - 48px);
  overflow-y: auto;
}

/* 预览卡片样式 - 模拟知识库资源卡片 */
.resource-preview-card {
  border: 1px solid var(--ant-color-border);
  border-radius: 8px;
  overflow: hidden;
  background: var(--ant-color-bg-container);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.resource-preview-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border-color: var(--ant-color-primary);
}

.preview-cover {
  position: relative;
  height: 180px;
  background: var(--ant-color-bg-layout);
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  transition: transform 0.3s ease;
}

.resource-preview-card:hover .cover-image {
  transform: scale(1.05);
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg,
      var(--ant-color-primary-bg) 0%,
      var(--ant-color-primary-bg-hover) 100%);
}

.placeholder-icon {
  font-size: 48px;
  color: var(--ant-color-primary);
  opacity: 0.6;
}

.category-tag {
  position: absolute;
  top: 12px;
  left: 12px;
  z-index: 1;
}

.preview-content {
  padding: 20px;
}

.preview-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--ant-color-text);
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.preview-description {
  font-size: 14px;
  color: var(--ant-color-text-secondary);
  line-height: 1.6;
  margin-bottom: 16px;
}

.preview-description h1,
.preview-description h2,
.preview-description h3 {
  margin: 16px 0 8px 0;
  color: var(--ant-color-text);
}

.preview-description h1 {
  font-size: 18px;
}

.preview-description h2 {
  font-size: 16px;
}

.preview-description h3 {
  font-size: 14px;
}

.preview-description strong {
  font-weight: 600;
  color: var(--ant-color-text);
}

.preview-description code {
  background: var(--ant-color-bg-layout);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.preview-description pre {
  background: var(--ant-color-bg-layout);
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 8px 0;
}

.preview-description pre code {
  background: none;
  padding: 0;
}

.preview-description a {
  color: var(--ant-color-primary);
  text-decoration: none;
}

.preview-description a:hover {
  text-decoration: underline;
}



.preview-placeholder {
  font-size: 14px;
  color: var(--ant-color-text-tertiary);
  font-style: italic;
  margin-bottom: 16px;
  padding: 20px 0;
  text-align: center;
}

.preview-tags {
  margin-bottom: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.preview-tags .ant-tag {
  margin: 0;
  font-size: 12px;
}

.preview-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  color: var(--ant-color-text-tertiary);
  border-top: 1px solid var(--ant-color-border-secondary);
  padding-top: 12px;
}

.meta-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-right {
  opacity: 0.6;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .main-content {
    gap: 24px;
  }
}

@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .preview-card {
    position: static;
    max-height: none;
  }

  .preview-section {
    order: -1;
    /* 在移动端将预览区域移到表单上方 */
  }
}

@media (max-width: 768px) {
  .resource-create-container {
    padding: 16px;
  }

  .main-content {
    gap: 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .preview-cover {
    height: 160px;
  }

  .preview-content {
    padding: 16px;
  }

  .resource-form .ant-form-item {
    margin-bottom: 16px;
  }
}

@media (max-width: 480px) {
  .resource-create-container {
    padding: 12px;
  }

  .page-title {
    font-size: 20px;
  }

  .preview-cover {
    height: 140px;
  }
}

/* 暗黑模式适配 */
.dark .resource-create-container {
  background: var(--ant-color-bg-layout);
}

.dark .form-card,
.dark .preview-card {
  background: var(--ant-color-bg-container);
  border-color: var(--ant-color-border);
}

.dark .resource-preview-card {
  background: var(--ant-color-bg-container);
  border-color: var(--ant-color-border);
}

.dark .cover-placeholder {
  background: linear-gradient(135deg,
      rgba(22, 119, 255, 0.1) 0%,
      rgba(22, 119, 255, 0.05) 100%);
}

/* 暗黑模式下的分类选择器 */
.dark .category-selector :deep(.ant-select-selector) {
  background-color: #141414 !important;
  border-color: #424242 !important;
  color: rgba(255, 255, 255, 0.85) !important;
}

.dark .category-selector:hover :deep(.ant-select-selector) {
  background-color: #141414 !important;
  border-color: #4096ff !important;
}

.dark .category-selector:focus-within :deep(.ant-select-selector) {
  border-color: #1677ff !important;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.2) !important;
  background-color: #141414 !important;
}

.dark .category-selector :deep(.ant-select-dropdown) {
  background: #1f1f1f !important;
  border-color: #424242 !important;
}

.dark .resource-count {
  color: rgba(255, 255, 255, 0.45) !important;
}
</style>