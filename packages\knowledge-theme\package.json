{"name": "@knowledge/theme", "version": "1.0.0", "description": "Knowledge System Design System and Theme", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "src"], "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "build:tokens": "style-dictionary build", "build:css": "sass src/styles:dist/css", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"color2k": "^2.0.2", "polished": "^4.2.2"}, "devDependencies": {"@rollup/plugin-typescript": "^11.1.5", "rollup": "^4.6.1", "sass": "^1.69.5", "style-dictionary": "^3.9.0", "typescript": "^5.2.2"}, "publishConfig": {"access": "public"}}