<template>
  <div class="space-y-8">
    <!-- 说明信息 -->
    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
      <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
        <div class="i-heroicons-information-circle inline-block w-4 h-4 mr-1"></div>
        主题配置
      </h3>
      <p class="text-sm text-blue-700 dark:text-blue-300">
        配置应用的外观主题，包括默认主题模式和自定义主题色。设置将在下次启动时生效。
      </p>
    </div>

    <!-- 默认主题模式 -->
    <div class="space-y-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          默认主题模式
        </label>
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-3">
          <button v-for="mode in themeOptions" :key="mode.value" :class="[
            'flex items-center justify-center p-4 rounded-xl border-2 transition-all duration-200',
            settings.defaultMode === mode.value
              ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
              : 'border-gray-200 dark:border-gray-700 hover:border-primary-300 dark:hover:border-primary-600 bg-white dark:bg-gray-800'
          ]" @click="updateDefaultMode(mode.value)">
            <div class="text-center">
              <div :class="[mode.icon, 'w-6 h-6 mx-auto mb-2']"></div>
              <div class="font-medium text-sm">{{ mode.label }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ mode.description }}</div>
            </div>
          </button>
        </div>
      </div>
    </div>

    <!-- 自定义主题色 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          自定义主题色
        </label>
        <label class="flex items-center">
          <input type="checkbox" v-model="settings.useCustomColor" @change="handleCustomColorToggle" class="sr-only" />
          <div :class="[
            'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
            settings.useCustomColor ? 'bg-primary-500' : 'bg-gray-200 dark:bg-gray-700'
          ]">
            <span :class="[
              'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
              settings.useCustomColor ? 'translate-x-6' : 'translate-x-1'
            ]"></span>
          </div>
          <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">启用</span>
        </label>
      </div>

      <div v-if="settings.useCustomColor" class="space-y-4">
        <!-- 预设颜色 -->
        <div>
          <label class="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-3">
            预设颜色
          </label>
          <div class="grid grid-cols-4 sm:grid-cols-8 gap-3">
            <button v-for="color in presetColors" :key="color.value" :class="[
              'w-12 h-12 rounded-lg border-2 transition-all duration-200 relative',
              settings.customPrimaryColor === color.value
                ? 'border-gray-400 dark:border-gray-500 scale-110'
                : 'border-gray-200 dark:border-gray-700 hover:scale-105'
            ]" :style="{ backgroundColor: color.value }" :title="`${color.name} - ${color.description}`"
              @click="selectPresetColor(color.value)">
              <div v-if="settings.customPrimaryColor === color.value"
                class="absolute inset-0 flex items-center justify-center">
                <div class="i-heroicons-check w-5 h-5 text-white drop-shadow-sm"></div>
              </div>
            </button>
          </div>
        </div>

        <!-- 自定义颜色选择器 -->
        <div>
          <label class="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-3">
            自定义颜色
          </label>
          <div class="flex items-center space-x-3">
            <input type="color" v-model="settings.customPrimaryColor" @change="handleColorChange"
              class="w-12 h-12 rounded-lg border border-gray-300 dark:border-gray-600 cursor-pointer" />
            <div class="flex-1">
              <input type="text" v-model="settings.customPrimaryColor" @input="handleColorInput" placeholder="#10b981"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent" />
            </div>
          </div>
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
            输入十六进制颜色代码，例如：#10b981
          </p>
        </div>

        <!-- 颜色预览 -->
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <label class="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-3">
            颜色预览
          </label>
          <div class="flex items-center space-x-4">
            <div class="flex space-x-1">
              <div v-for="shade in colorPreview" :key="shade.name" :style="{ backgroundColor: shade.color }"
                :title="shade.name" class="w-8 h-8 rounded border border-gray-200 dark:border-gray-600">
              </div>
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              主题色阶预览
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
      <button @click="resetToDefaults"
        class="px-4 py-2 text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors">
        恢复默认
      </button>

      <div class="flex space-x-3">
        <button @click="previewChanges" :disabled="saving"
          class="px-4 py-2 text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors disabled:opacity-50">
          预览效果
        </button>
        <button @click="saveSettings" :disabled="saving" class="btn-primary text-sm">
          <div v-if="saving" class="i-heroicons-arrow-path w-4 h-4 mr-2 animate-spin"></div>
          <div v-else class="i-heroicons-check w-4 h-4 mr-2"></div>
          {{ saving ? '保存中...' : '保存设置' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { themeSettingsService, presetColors, type ThemeSettings } from '@/services/themeSettingsService'
import type { ThemeMode } from '@/types'

// 响应式数据
const settings = ref<ThemeSettings>(themeSettingsService.getSettings())
const saving = ref(false)

// 主题选项
const themeOptions = [
  {
    value: 'light' as ThemeMode,
    label: '浅色模式',
    description: '始终使用浅色主题',
    icon: 'i-heroicons-sun'
  },
  {
    value: 'dark' as ThemeMode,
    label: '深色模式',
    description: '始终使用深色主题',
    icon: 'i-heroicons-moon'
  },
  {
    value: 'system' as ThemeMode,
    label: '跟随系统',
    description: '根据系统设置自动切换',
    icon: 'i-heroicons-computer-desktop'
  }
]

// 颜色预览
const colorPreview = computed(() => {
  if (!settings.value.useCustomColor || !settings.value.customPrimaryColor) {
    return []
  }

  const hex = settings.value.customPrimaryColor.replace('#', '')
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)

  return [
    { name: '100', color: `rgb(${Math.round(r + (255 - r) * 0.8)}, ${Math.round(g + (255 - g) * 0.8)}, ${Math.round(b + (255 - b) * 0.8)})` },
    { name: '300', color: `rgb(${Math.round(r + (255 - r) * 0.4)}, ${Math.round(g + (255 - g) * 0.4)}, ${Math.round(b + (255 - b) * 0.4)})` },
    { name: '500', color: `rgb(${r}, ${g}, ${b})` },
    { name: '700', color: `rgb(${Math.round(r * 0.6)}, ${Math.round(g * 0.6)}, ${Math.round(b * 0.6)})` },
    { name: '900', color: `rgb(${Math.round(r * 0.2)}, ${Math.round(g * 0.2)}, ${Math.round(b * 0.2)})` }
  ]
})

// 更新默认主题模式
const updateDefaultMode = (mode: ThemeMode) => {
  settings.value.defaultMode = mode
}

// 处理自定义颜色开关
const handleCustomColorToggle = () => {
  if (settings.value.useCustomColor) {
    // 立即应用颜色
    themeSettingsService.updateSettings({ useCustomColor: true })
  } else {
    // 移除自定义颜色
    themeSettingsService.updateSettings({ useCustomColor: false })
  }
}

// 选择预设颜色
const selectPresetColor = (color: string) => {
  settings.value.customPrimaryColor = color
  if (settings.value.useCustomColor) {
    // 立即预览
    previewChanges()
  }
}

// 处理颜色变化
const handleColorChange = () => {
  if (settings.value.useCustomColor) {
    previewChanges()
  }
}

// 处理颜色输入
const handleColorInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  const color = target.value

  if (themeSettingsService.isValidColor(color)) {
    settings.value.customPrimaryColor = color
    if (settings.value.useCustomColor) {
      previewChanges()
    }
  }
}

// 预览更改
const previewChanges = () => {
  // 直接应用当前设置以预览效果
  const root = document.documentElement

  if (settings.value.useCustomColor && settings.value.customPrimaryColor) {
    // 将十六进制颜色转换为RGB
    const hex = settings.value.customPrimaryColor.replace('#', '')
    const r = parseInt(hex.substr(0, 2), 16)
    const g = parseInt(hex.substr(2, 2), 16)
    const b = parseInt(hex.substr(4, 2), 16)

    // 生成主题色阶
    const colorScale = generateColorScale(r, g, b)

    // 应用到CSS变量 - 多种格式确保兼容性
    Object.entries(colorScale).forEach(([key, value]) => {
      root.style.setProperty(`--color-primary-${key}`, value)
      root.style.setProperty(`--un-preset-color-primary-${key}`, value)
      root.style.setProperty(`--uno-color-primary-${key}`, value)
      root.style.setProperty(`--primary-${key}`, value)
    })

    // 强制更新样式
    document.body.style.display = 'none'
    document.body.offsetHeight // 触发重排
    document.body.style.display = ''

    console.log('预览自定义主题色:', settings.value.customPrimaryColor, colorScale)

    // 调试：检查CSS变量是否正确设置
    setTimeout(() => {
      const computedStyle = getComputedStyle(document.documentElement)
      console.log('当前CSS变量值:')
      console.log('--primary-500:', computedStyle.getPropertyValue('--primary-500'))
      console.log('--color-primary-500:', computedStyle.getPropertyValue('--color-primary-500'))
      console.log('--un-preset-color-primary-500:', computedStyle.getPropertyValue('--un-preset-color-primary-500'))
    }, 100)
  } else {
    // 移除自定义颜色变量，恢复默认
    const keys = ['25', '50', '100', '200', '300', '400', '500', '600', '700', '800', '900']
    keys.forEach(key => {
      root.style.removeProperty(`--color-primary-${key}`)
      root.style.removeProperty(`--un-preset-color-primary-${key}`)
      root.style.removeProperty(`--uno-color-primary-${key}`)
      root.style.removeProperty(`--primary-${key}`)
    })

    // 强制更新样式
    document.body.style.display = 'none'
    document.body.offsetHeight // 触发重排
    document.body.style.display = ''
  }
}

// 生成颜色色阶的辅助函数
const generateColorScale = (r: number, g: number, b: number): Record<string, string> => {
  const scale: Record<string, string> = {}

  // 基础色 (500)
  scale['500'] = `rgb(${r}, ${g}, ${b})`

  // 生成浅色系 (25-400)
  const lightSteps = [0.95, 0.9, 0.8, 0.6, 0.4] // 25, 50, 100, 200, 300
  const lightKeys = ['25', '50', '100', '200', '300']

  lightSteps.forEach((step, index) => {
    const lightR = Math.round(r + (255 - r) * step)
    const lightG = Math.round(g + (255 - g) * step)
    const lightB = Math.round(b + (255 - b) * step)
    scale[lightKeys[index]] = `rgb(${lightR}, ${lightG}, ${lightB})`
  })

  // 生成深色系 (600-900)
  const darkSteps = [0.8, 0.6, 0.4, 0.2] // 600, 700, 800, 900
  const darkKeys = ['600', '700', '800', '900']

  darkSteps.forEach((step, index) => {
    const darkR = Math.round(r * step)
    const darkG = Math.round(g * step)
    const darkB = Math.round(b * step)
    scale[darkKeys[index]] = `rgb(${darkR}, ${darkG}, ${darkB})`
  })

  // 400 介于300和500之间
  scale['400'] = `rgb(${Math.round(r * 0.7 + (255 - r) * 0.2)}, ${Math.round(g * 0.7 + (255 - g) * 0.2)}, ${Math.round(b * 0.7 + (255 - b) * 0.2)})`

  return scale
}

// 保存设置
const saveSettings = async () => {
  try {
    saving.value = true
    themeSettingsService.saveSettings(settings.value)

    // 显示成功提示
    // TODO: 添加通知系统
    console.log('主题设置保存成功')
  } catch (error) {
    console.error('保存主题设置失败:', error)
    // TODO: 显示错误提示
  } finally {
    saving.value = false
  }
}

// 恢复默认设置
const resetToDefaults = () => {
  themeSettingsService.resetToDefaults()
  settings.value = themeSettingsService.getSettings()
}

// 监听设置变化
let unsubscribe: (() => void) | null = null

onMounted(() => {
  // 初始化时应用当前设置
  themeSettingsService.applyCustomColor()

  // 监听设置变化
  unsubscribe = themeSettingsService.addListener((newSettings: ThemeSettings) => {
    settings.value = newSettings
  })
})

onUnmounted(() => {
  if (unsubscribe) {
    unsubscribe()
  }
})
</script>
