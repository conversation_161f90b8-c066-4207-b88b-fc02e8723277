# 知识库排序选择器增强

## 功能改进
将原本分离的排序字段和排序方向合并到一个下拉菜单中，通过图标和箭头符号直观显示排序方式。

## 实现方案

### 1. HTML 结构简化
**修改前**：分离的排序字段和方向选择器
```vue
<div class="sort-controls">
  <a-select v-model:value="currentSort" @change="handleSortChange" class="sort-selector">
    <a-select-option v-for="option in sortOptions" :key="option.value" :value="option.value">
      {{ option.label }}
    </a-select-option>
  </a-select>
  <a-select v-model:value="sortOrder" @change="handleSortOrderChange" class="sort-order-selector">
    <a-select-option value="asc">正序</a-select-option>
    <a-select-option value="desc">倒序</a-select-option>
  </a-select>
</div>
```

**修改后**：统一的排序选择器
```vue
<a-select v-model:value="currentSortKey" @change="handleSortChange" class="sort-selector">
  <a-select-option v-for="option in sortOptions" :key="option.key" :value="option.key">
    <div class="sort-option">
      <component :is="option.icon" class="sort-icon" />
      <span>{{ option.label }}</span>
    </div>
  </a-select-option>
</a-select>
```

### 2. 排序选项数据结构
```javascript
const sortOptions = [
  { key: 'created_at_desc', field: 'created_at', order: 'desc', label: '创建时间 ↓', icon: ClockCircleOutlined },
  { key: 'created_at_asc', field: 'created_at', order: 'asc', label: '创建时间 ↑', icon: ClockCircleOutlined },
  { key: 'updated_at_desc', field: 'updated_at', order: 'desc', label: '更新时间 ↓', icon: ClockCircleOutlined },
  { key: 'updated_at_asc', field: 'updated_at', order: 'asc', label: '更新时间 ↑', icon: ClockCircleOutlined },
  { key: 'view_count_desc', field: 'view_count', order: 'desc', label: '浏览次数 ↓', icon: EyeOutlined },
  { key: 'view_count_asc', field: 'view_count', order: 'asc', label: '浏览次数 ↑', icon: EyeOutlined },
  { key: 'title_asc', field: 'title', order: 'asc', label: '标题 A-Z', icon: FontSizeOutlined },
  { key: 'title_desc', field: 'title', order: 'desc', label: '标题 Z-A', icon: FontSizeOutlined }
]
```

### 3. 状态管理
```javascript
// 新增统一的排序键状态
const currentSortKey = ref(`${sortField}_${defaultSortOrder}`)

// 排序处理函数
const handleSortChange = (sortKey: string) => {
  const option = sortOptions.find(opt => opt.key === sortKey)
  if (option) {
    currentSortKey.value = sortKey
    currentSort.value = option.field as SortOption
    sortOrder.value = option.order as SortOrder
    loadResources(true)
  }
}
```

### 4. 图标导入
```javascript
import {
  PlusOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
  FileTextOutlined,
  ClearOutlined,
  UpOutlined,
  ClockCircleOutlined,  // 时间相关排序
  EyeOutlined,          // 浏览次数排序
  FontSizeOutlined      // 标题排序
} from '@ant-design/icons-vue'
```

### 5. 样式设计
```css
/* 排序选项样式 */
.sort-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.sort-icon {
  font-size: 14px;
  color: var(--ant-color-text-secondary, #666);
  flex-shrink: 0;
}

/* 暗黑模式适配 */
.dark .sort-icon {
  color: rgba(255, 255, 255, 0.65) !important;
}
```

## 用户体验改进

### 1. 直观的视觉反馈
- **图标识别**：每种排序类型都有对应的图标
- **方向指示**：使用 ↑↓ 箭头清晰表示排序方向
- **语义化标签**：如 "A-Z"、"Z-A" 更直观

### 2. 操作简化
- **一步选择**：用户只需点击一次即可设置排序字段和方向
- **减少界面复杂度**：从两个选择器简化为一个
- **保持功能完整性**：所有排序组合都可选择

### 3. 视觉一致性
- **图标风格统一**：使用 Ant Design 图标库
- **布局对齐**：图标和文字垂直居中对齐
- **暗黑模式适配**：图标颜色在暗黑模式下正确显示

## 排序选项说明

| 选项 | 图标 | 说明 |
|------|------|------|
| 创建时间 ↓ | ClockCircleOutlined | 按创建时间倒序（最新在前） |
| 创建时间 ↑ | ClockCircleOutlined | 按创建时间正序（最旧在前） |
| 更新时间 ↓ | ClockCircleOutlined | 按更新时间倒序（最近更新在前） |
| 更新时间 ↑ | ClockCircleOutlined | 按更新时间正序（最早更新在前） |
| 浏览次数 ↓ | EyeOutlined | 按浏览次数倒序（最多浏览在前） |
| 浏览次数 ↑ | EyeOutlined | 按浏览次数正序（最少浏览在前） |
| 标题 A-Z | FontSizeOutlined | 按标题字母顺序正序 |
| 标题 Z-A | FontSizeOutlined | 按标题字母顺序倒序 |

## 技术细节

### 修改文件
- `src/views/KnowledgeView.vue`

### 核心改进
1. **数据结构优化**：将排序字段和方向合并为单一键值
2. **组件简化**：减少UI组件数量，提升性能
3. **状态同步**：确保新旧状态变量保持同步
4. **设置兼容**：保持与现有设置系统的兼容性

### 兼容性保证
- ✅ **向后兼容**：现有的排序设置仍然有效
- ✅ **状态同步**：内部仍然维护分离的字段和方向状态
- ✅ **API 兼容**：后端接口调用方式不变

## 总结
通过将排序字段和方向合并到一个选择器中，并添加直观的图标和方向指示，显著提升了用户体验。新的设计更加简洁直观，减少了用户的操作步骤，同时保持了所有排序功能的完整性。
