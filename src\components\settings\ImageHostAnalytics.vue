<template>
  <div class="analytics-dashboard">
    <!-- 页面标题 -->
    <div class="dashboard-header">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">图床统计分析</h2>
          <p class="text-gray-600 dark:text-gray-400 mt-2">
            全面了解图床使用情况和性能数据
          </p>
        </div>
        <div class="flex items-center space-x-3">
          <button @click="refreshData" :disabled="loading" class="btn btn-outline">
            <div :class="['w-4 h-4 mr-2', loading ? 'i-heroicons-arrow-path animate-spin' : 'i-heroicons-arrow-path']">
            </div>
            刷新数据
          </button>
          <select v-model="timeRange" class="time-range-select">
            <option value="7d">最近7天</option>
            <option value="30d">最近30天</option>
            <option value="90d">最近90天</option>
            <option value="all">全部时间</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 概览卡片 -->
    <div class="overview-cards">
      <div class="stat-card primary">
        <div class="stat-icon">
          <div class="i-heroicons-photo w-6 h-6"></div>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ formatNumber(stats.totalImages) }}</div>
          <div class="stat-label">总图片数</div>
          <div class="stat-change positive">
            <div class="i-heroicons-arrow-trending-up w-4 h-4"></div>
            +{{ stats.recentImages }} 本周
          </div>
        </div>
      </div>

      <div class="stat-card success">
        <div class="stat-icon">
          <div class="i-heroicons-server w-6 h-6"></div>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.activeHosts }}</div>
          <div class="stat-label">活跃图床</div>
          <div class="stat-change">
            {{ stats.totalHosts }} 个已配置
          </div>
        </div>
      </div>

      <div class="stat-card warning">
        <div class="stat-icon">
          <div class="i-heroicons-circle-stack w-6 h-6"></div>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ formatFileSize(stats.totalSize) }}</div>
          <div class="stat-label">总存储量</div>
          <div class="stat-change">
            平均 {{ formatFileSize(stats.avgFileSize) }}/张
          </div>
        </div>
      </div>

      <div class="stat-card info">
        <div class="stat-icon">
          <div class="i-heroicons-chart-bar w-6 h-6"></div>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.successRate }}%</div>
          <div class="stat-label">上传成功率</div>
          <div class="stat-change" :class="stats.successRate >= 95 ? 'positive' : 'negative'">
            <div
              :class="['w-4 h-4', stats.successRate >= 95 ? 'i-heroicons-arrow-trending-up' : 'i-heroicons-arrow-trending-down']">
            </div>
            {{ stats.successRate >= 95 ? '优秀' : '需改进' }}
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-grid">
      <!-- 上传趋势图 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>上传趋势</h3>
          <div class="chart-legend">
            <span class="legend-item">
              <div class="legend-color primary"></div>
              上传数量
            </span>
            <span class="legend-item">
              <div class="legend-color success"></div>
              成功率
            </span>
          </div>
        </div>
        <div class="chart-container">
          <canvas ref="uploadTrendChart" class="chart-canvas"></canvas>
        </div>
      </div>

      <!-- 图床使用分布 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>图床使用分布</h3>
        </div>
        <div class="chart-container">
          <canvas ref="hostDistributionChart" class="chart-canvas"></canvas>
        </div>
      </div>

      <!-- 文件大小分布 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>文件大小分布</h3>
        </div>
        <div class="chart-container">
          <canvas ref="fileSizeChart" class="chart-canvas"></canvas>
        </div>
      </div>

      <!-- 标签使用排行 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>热门标签</h3>
        </div>
        <div class="tag-ranking">
          <div v-for="(tag, index) in topTags" :key="tag.name" class="tag-rank-item">
            <div class="rank-number">{{ index + 1 }}</div>
            <div class="tag-info">
              <div class="tag-name">{{ tag.name }}</div>
              <div class="tag-count">{{ tag.count }} 张图片</div>
            </div>
            <div class="tag-bar">
              <div class="tag-bar-fill" :style="{ width: `${(tag.count / topTags[0]?.count) * 100}%` }"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 图床性能对比 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>图床性能对比</h3>
        </div>
        <div class="performance-grid">
          <div v-for="host in hostPerformance" :key="host.name" class="performance-item">
            <div class="host-info">
              <div class="host-name">{{ host.name }}</div>
              <div class="host-status" :class="host.status">{{ getStatusText(host.status) }}</div>
            </div>
            <div class="performance-metrics">
              <div class="metric">
                <span class="metric-label">成功率</span>
                <div class="metric-bar">
                  <div class="metric-fill success" :style="{ width: `${host.successRate}%` }"></div>
                </div>
                <span class="metric-value">{{ host.successRate }}%</span>
              </div>
              <div class="metric">
                <span class="metric-label">平均速度</span>
                <div class="metric-bar">
                  <div class="metric-fill info" :style="{ width: `${(host.avgSpeed / 10) * 100}%` }"></div>
                </div>
                <span class="metric-value">{{ host.avgSpeed }}s</span>
              </div>
              <div class="metric">
                <span class="metric-label">图片数量</span>
                <div class="metric-bar">
                  <div class="metric-fill primary"
                    :style="{ width: `${(host.imageCount / Math.max(...hostPerformance.map(h => h.imageCount))) * 100}%` }">
                  </div>
                </div>
                <span class="metric-value">{{ host.imageCount }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 存储空间使用 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>存储空间使用</h3>
        </div>
        <div class="storage-overview">
          <div class="storage-chart">
            <canvas ref="storageChart" class="chart-canvas"></canvas>
          </div>
          <div class="storage-details">
            <div v-for="category in storageCategories" :key="category.name" class="storage-item">
              <div class="storage-color" :style="{ backgroundColor: category.color }"></div>
              <div class="storage-info">
                <div class="storage-name">{{ category.name }}</div>
                <div class="storage-size">{{ formatFileSize(category.size) }}</div>
                <div class="storage-percentage">{{ category.percentage }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细数据表格 -->
    <div class="data-table-card">
      <div class="table-header">
        <h3>详细数据</h3>
        <button @click="exportData" class="btn btn-outline btn-sm">
          <div class="i-heroicons-arrow-down-tray w-4 h-4 mr-2"></div>
          导出数据
        </button>
      </div>
      <div class="table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th>图床名称</th>
              <th>状态</th>
              <th>图片数量</th>
              <th>成功率</th>
              <th>平均大小</th>
              <th>最后上传</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="host in detailedHostData" :key="host.id">
              <td>
                <div class="host-cell">
                  <div class="host-avatar" :style="{ backgroundColor: host.color }">
                    {{ host.name.charAt(0).toUpperCase() }}
                  </div>
                  <div class="host-name">{{ host.name }}</div>
                </div>
              </td>
              <td>
                <span class="status-badge" :class="host.status">
                  {{ getStatusText(host.status) }}
                </span>
              </td>
              <td>{{ formatNumber(host.imageCount) }}</td>
              <td>
                <div class="success-rate">
                  <span>{{ host.successRate }}%</span>
                  <div class="rate-bar">
                    <div class="rate-fill" :style="{ width: `${host.successRate}%` }"></div>
                  </div>
                </div>
              </td>
              <td>{{ formatFileSize(host.avgSize) }}</td>
              <td>{{ formatDate(host.lastUpload) }}</td>
              <td>
                <button @click="viewHostDetails(host)" class="btn-icon" title="查看详情">
                  <div class="i-heroicons-eye w-4 h-4"></div>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import { imageDataService } from '@/services/imageDataService'

// 响应式数据
const loading = ref(false)
const timeRange = ref('30d')

// 统计数据
const stats = ref({
  totalImages: 0,
  recentImages: 0,
  activeHosts: 0,
  totalHosts: 0,
  totalSize: 0,
  avgFileSize: 0,
  successRate: 0
})

const topTags = ref<Array<{ name: string; count: number }>>([])
const hostPerformance = ref<Array<{
  name: string
  status: 'active' | 'inactive' | 'error'
  successRate: number
  avgSpeed: number
  imageCount: number
}>>([])

const storageCategories = ref<Array<{
  name: string
  size: number
  percentage: number
  color: string
}>>([])

const detailedHostData = ref<Array<{
  id: string
  name: string
  color: string
  status: 'active' | 'inactive' | 'error'
  imageCount: number
  successRate: number
  avgSize: number
  lastUpload: Date
}>>([])

// 图表引用
const uploadTrendChart = ref<HTMLCanvasElement>()
const hostDistributionChart = ref<HTMLCanvasElement>()
const fileSizeChart = ref<HTMLCanvasElement>()
const storageChart = ref<HTMLCanvasElement>()

// 工具函数
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    active: '正常',
    inactive: '未激活',
    error: '异常'
  }
  return statusMap[status] || '未知'
}

// 数据加载
const loadAnalyticsData = async () => {
  try {
    loading.value = true

    // 加载基础统计数据
    const basicStats = await imageDataService.getStatistics()
    const tagStats = await imageDataService.getImageTagStats()

    // 模拟更详细的统计数据（实际项目中应该从API获取）
    stats.value = {
      totalImages: basicStats.imageCount,
      recentImages: Math.floor(basicStats.imageCount * 0.1), // 假设10%是最近的
      activeHosts: Math.floor(basicStats.hostCount * 0.8), // 假设80%是活跃的
      totalHosts: basicStats.hostCount,
      totalSize: basicStats.totalSize,
      avgFileSize: basicStats.imageCount > 0 ? basicStats.totalSize / basicStats.imageCount : 0,
      successRate: 95 // 模拟成功率
    }

    // 加载热门标签
    const allTags = await imageDataService.getAllTags()
    topTags.value = allTags
      .sort((a, b) => b.imageCount - a.imageCount)
      .slice(0, 10)
      .map(tag => ({
        name: tag.name,
        count: tag.imageCount
      }))

    // 模拟图床性能数据
    hostPerformance.value = [
      { name: 'SM.MS', status: 'active', successRate: 98, avgSpeed: 2.3, imageCount: 150 },
      { name: '聚合图床', status: 'active', successRate: 95, avgSpeed: 1.8, imageCount: 120 },
      { name: 'ImgBB', status: 'active', successRate: 92, avgSpeed: 3.1, imageCount: 80 },
      { name: 'Imgur', status: 'inactive', successRate: 88, avgSpeed: 4.2, imageCount: 45 },
      { name: '自定义图床', status: 'error', successRate: 75, avgSpeed: 5.5, imageCount: 20 }
    ]

    // 模拟存储分类数据
    const totalSize = stats.value.totalSize
    storageCategories.value = [
      { name: '图片文件', size: totalSize * 0.7, percentage: 70, color: '#3B82F6' },
      { name: '缩略图', size: totalSize * 0.2, percentage: 20, color: '#10B981' },
      { name: '备份文件', size: totalSize * 0.08, percentage: 8, color: '#F59E0B' },
      { name: '其他', size: totalSize * 0.02, percentage: 2, color: '#EF4444' }
    ]

    // 模拟详细主机数据
    detailedHostData.value = [
      {
        id: '1',
        name: 'SM.MS',
        color: '#3B82F6',
        status: 'active',
        imageCount: 150,
        successRate: 98,
        avgSize: 2.5 * 1024 * 1024, // 2.5MB
        lastUpload: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2小时前
      },
      {
        id: '2',
        name: '聚合图床',
        color: '#10B981',
        status: 'active',
        imageCount: 120,
        successRate: 95,
        avgSize: 1.8 * 1024 * 1024,
        lastUpload: new Date(Date.now() - 5 * 60 * 60 * 1000) // 5小时前
      },
      {
        id: '3',
        name: 'ImgBB',
        color: '#F59E0B',
        status: 'active',
        imageCount: 80,
        successRate: 92,
        avgSize: 3.2 * 1024 * 1024,
        lastUpload: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000) // 1天前
      }
    ]

    // 渲染图表
    await nextTick()
    renderCharts()

  } catch (error) {
    console.error('加载分析数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 图表渲染
const renderCharts = () => {
  renderUploadTrendChart()
  renderHostDistributionChart()
  renderFileSizeChart()
  renderStorageChart()
}

const renderUploadTrendChart = () => {
  const canvas = uploadTrendChart.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // 简单的折线图实现
  canvas.width = canvas.offsetWidth * 2
  canvas.height = canvas.offsetHeight * 2
  ctx.scale(2, 2)

  const width = canvas.offsetWidth
  const height = canvas.offsetHeight
  const padding = 40

  // 模拟数据
  const data = [
    { day: '周一', uploads: 25, success: 95 },
    { day: '周二', uploads: 32, success: 97 },
    { day: '周三', uploads: 28, success: 94 },
    { day: '周四', uploads: 45, success: 96 },
    { day: '周五', uploads: 38, success: 98 },
    { day: '周六', uploads: 52, success: 93 },
    { day: '周日', uploads: 41, success: 95 }
  ]

  // 清空画布
  ctx.clearRect(0, 0, width, height)

  // 绘制网格
  ctx.strokeStyle = '#E5E7EB'
  ctx.lineWidth = 1
  for (let i = 1; i < 5; i++) {
    const y = padding + (height - 2 * padding) * i / 4
    ctx.beginPath()
    ctx.moveTo(padding, y)
    ctx.lineTo(width - padding, y)
    ctx.stroke()
  }

  // 绘制上传数量折线
  ctx.strokeStyle = '#3B82F6'
  ctx.lineWidth = 3
  ctx.beginPath()

  const maxUploads = Math.max(...data.map(d => d.uploads))
  data.forEach((point, index) => {
    const x = padding + (width - 2 * padding) * index / (data.length - 1)
    const y = height - padding - (height - 2 * padding) * point.uploads / maxUploads

    if (index === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  })
  ctx.stroke()

  // 绘制数据点
  ctx.fillStyle = '#3B82F6'
  data.forEach((point, index) => {
    const x = padding + (width - 2 * padding) * index / (data.length - 1)
    const y = height - padding - (height - 2 * padding) * point.uploads / maxUploads

    ctx.beginPath()
    ctx.arc(x, y, 4, 0, 2 * Math.PI)
    ctx.fill()
  })
}

const renderHostDistributionChart = () => {
  const canvas = hostDistributionChart.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  if (!ctx) return

  canvas.width = canvas.offsetWidth * 2
  canvas.height = canvas.offsetHeight * 2
  ctx.scale(2, 2)

  const width = canvas.offsetWidth
  const height = canvas.offsetHeight
  const centerX = width / 2
  const centerY = height / 2
  const radius = Math.min(width, height) / 2 - 20

  // 饼图数据
  const data = hostPerformance.value.map(host => ({
    name: host.name,
    value: host.imageCount,
    color: getHostColor(host.name)
  }))

  const total = data.reduce((sum, item) => sum + item.value, 0)
  let currentAngle = -Math.PI / 2

  // 绘制饼图
  data.forEach(item => {
    const sliceAngle = (item.value / total) * 2 * Math.PI

    ctx.beginPath()
    ctx.moveTo(centerX, centerY)
    ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle)
    ctx.closePath()
    ctx.fillStyle = item.color
    ctx.fill()

    currentAngle += sliceAngle
  })
}

const renderFileSizeChart = () => {
  const canvas = fileSizeChart.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  if (!ctx) return

  canvas.width = canvas.offsetWidth * 2
  canvas.height = canvas.offsetHeight * 2
  ctx.scale(2, 2)

  const width = canvas.offsetWidth
  const height = canvas.offsetHeight
  const padding = 40

  // 文件大小分布数据
  const sizeRanges = [
    { range: '< 1MB', count: 45, color: '#10B981' },
    { range: '1-5MB', count: 120, color: '#3B82F6' },
    { range: '5-10MB', count: 80, color: '#F59E0B' },
    { range: '> 10MB', count: 25, color: '#EF4444' }
  ]

  const maxCount = Math.max(...sizeRanges.map(r => r.count))
  const barWidth = (width - 2 * padding) / sizeRanges.length - 10

  // 绘制柱状图
  sizeRanges.forEach((range, index) => {
    const x = padding + index * (barWidth + 10)
    const barHeight = (height - 2 * padding) * range.count / maxCount
    const y = height - padding - barHeight

    ctx.fillStyle = range.color
    ctx.fillRect(x, y, barWidth, barHeight)

    // 绘制数值
    ctx.fillStyle = '#374151'
    ctx.font = '12px sans-serif'
    ctx.textAlign = 'center'
    ctx.fillText(range.count.toString(), x + barWidth / 2, y - 5)
  })
}

const renderStorageChart = () => {
  const canvas = storageChart.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  if (!ctx) return

  canvas.width = canvas.offsetWidth * 2
  canvas.height = canvas.offsetHeight * 2
  ctx.scale(2, 2)

  const width = canvas.offsetWidth
  const height = canvas.offsetHeight
  const centerX = width / 2
  const centerY = height / 2
  const radius = Math.min(width, height) / 2 - 30

  let currentAngle = 0

  // 绘制环形图
  storageCategories.value.forEach(category => {
    const sliceAngle = (category.percentage / 100) * 2 * Math.PI

    ctx.beginPath()
    ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle)
    ctx.arc(centerX, centerY, radius * 0.6, currentAngle + sliceAngle, currentAngle, true)
    ctx.closePath()
    ctx.fillStyle = category.color
    ctx.fill()

    currentAngle += sliceAngle
  })
}

const getHostColor = (hostName: string): string => {
  const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6']
  const index = hostName.length % colors.length
  return colors[index]
}

// 事件处理
const refreshData = () => {
  loadAnalyticsData()
}

const exportData = () => {
  // 导出数据逻辑
  const data = {
    stats: stats.value,
    hostPerformance: hostPerformance.value,
    topTags: topTags.value,
    detailedHostData: detailedHostData.value
  }

  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `image-host-analytics-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

const viewHostDetails = (host: any) => {
  // 查看主机详情逻辑
  console.log('查看主机详情:', host)
}

// 监听时间范围变化
watch(timeRange, () => {
  loadAnalyticsData()
})

// 初始化
onMounted(() => {
  loadAnalyticsData()
})
</script>

<style scoped>
.analytics-dashboard {
  padding: 0;
  max-width: 100%;
}

/* 页面标题 */
.dashboard-header {
  margin-bottom: 32px;
}

.time-range-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 14px;
}

/* 概览卡片 */
.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.stat-card.primary .stat-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.success .stat-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card.warning .stat-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card.info .stat-icon {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #d97706;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6b7280;
}

.stat-change.positive {
  color: #059669;
}

.stat-change.negative {
  color: #dc2626;
}

/* 图表网格 */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.chart-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.chart-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.chart-legend {
  display: flex;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #6b7280;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.primary {
  background: #3b82f6;
}

.legend-color.success {
  background: #10b981;
}

.chart-container {
  height: 300px;
  position: relative;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}

/* 标签排行 */
.tag-ranking {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tag-rank-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f3f4f6;
}

.tag-rank-item:last-child {
  border-bottom: none;
}

.rank-number {
  width: 24px;
  height: 24px;
  background: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  flex-shrink: 0;
}

.tag-info {
  flex: 1;
}

.tag-name {
  font-weight: 500;
  color: #1f2937;
  font-size: 14px;
}

.tag-count {
  font-size: 12px;
  color: #6b7280;
}

.tag-bar {
  width: 80px;
  height: 6px;
  background: #f3f4f6;
  border-radius: 3px;
  overflow: hidden;
}

.tag-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* 性能网格 */
.performance-grid {
  display: grid;
  gap: 16px;
}

.performance-item {
  padding: 16px;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.host-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.host-name {
  font-weight: 500;
  color: #1f2937;
}

.host-status {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.host-status.active {
  background: #d1fae5;
  color: #065f46;
}

.host-status.inactive {
  background: #fef3c7;
  color: #92400e;
}

.host-status.error {
  background: #fee2e2;
  color: #991b1b;
}

.performance-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric {
  display: flex;
  align-items: center;
  gap: 8px;
}

.metric-label {
  font-size: 12px;
  color: #6b7280;
  width: 60px;
  flex-shrink: 0;
}

.metric-bar {
  flex: 1;
  height: 6px;
  background: #f3f4f6;
  border-radius: 3px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.metric-fill.success {
  background: #10b981;
}

.metric-fill.info {
  background: #3b82f6;
}

.metric-fill.primary {
  background: #8b5cf6;
}

.metric-value {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  width: 40px;
  text-align: right;
  flex-shrink: 0;
}

/* 存储概览 */
.storage-overview {
  display: flex;
  gap: 24px;
  align-items: center;
}

.storage-chart {
  width: 200px;
  height: 200px;
  flex-shrink: 0;
}

.storage-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.storage-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.storage-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  flex-shrink: 0;
}

.storage-info {
  flex: 1;
}

.storage-name {
  font-weight: 500;
  color: #1f2937;
  font-size: 14px;
}

.storage-size {
  font-size: 12px;
  color: #6b7280;
}

.storage-percentage {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  margin-left: auto;
}

/* 数据表格 */
.data-table-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.table-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.table-container {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.data-table th {
  font-weight: 600;
  color: #374151;
  background: #f9fafb;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.data-table td {
  color: #6b7280;
  font-size: 14px;
}

.host-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.host-avatar {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.active {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.inactive {
  background: #fef3c7;
  color: #92400e;
}

.status-badge.error {
  background: #fee2e2;
  color: #991b1b;
}

.success-rate {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rate-bar {
  width: 60px;
  height: 4px;
  background: #f3f4f6;
  border-radius: 2px;
  overflow: hidden;
}

.rate-fill {
  height: 100%;
  background: #10b981;
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  border: 1px solid transparent;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn-outline {
  color: #6b7280;
  background: white;
  border-color: #d1d5db;
}

.btn-outline:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn-outline:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 13px;
}

.btn-icon {
  padding: 8px;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background: #f3f4f6;
  color: #374151;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .storage-overview {
    flex-direction: column;
  }

  .storage-chart {
    width: 150px;
    height: 150px;
  }

  .dashboard-header .flex {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
}
</style>
