// 知识库设置接口
export interface KnowledgeSettings {
  tags: {
    initialLoad: number
    loadMore: number
  }
  sorting: {
    default: string
    tags: string
  }
  layout: {
    cardsPerRow: number
    cardSpacing: string
  }
  loading: {
    type: 'pagination' | 'infinite'
    pageSize: number
    preloadDistance: number
  }
  performance: {
    virtualScroll: boolean
    lazyImages: boolean
    cacheStrategy: string
    cacheTime: number
  }
}

// 默认设置
export const defaultKnowledgeSettings: KnowledgeSettings = {
  tags: {
    initialLoad: 10,
    loadMore: 5
  },
  sorting: {
    default: 'created_desc',
    tags: 'name_asc'
  },
  layout: {
    cardsPerRow: 3,
    cardSpacing: 'normal'
  },
  loading: {
    type: 'infinite',
    pageSize: 12,
    preloadDistance: 300
  },
  performance: {
    virtualScroll: false,
    lazyImages: true,
    cacheStrategy: 'memory',
    cacheTime: 30
  }
}

// 知识库设置服务
class KnowledgeSettingsService {
  private readonly STORAGE_KEY = 'knowledge-settings'
  private settings: KnowledgeSettings
  private listeners: Array<(settings: KnowledgeSettings) => void> = []

  constructor() {
    this.settings = this.loadSettings()
  }

  // 加载设置
  private loadSettings(): KnowledgeSettings {
    try {
      const saved = localStorage.getItem(this.STORAGE_KEY)
      if (saved) {
        const parsed = JSON.parse(saved)
        return { ...defaultKnowledgeSettings, ...parsed }
      }
    } catch (error) {
      console.error('加载知识库设置失败:', error)
    }
    return { ...defaultKnowledgeSettings }
  }

  // 获取当前设置
  getSettings(): KnowledgeSettings {
    return { ...this.settings }
  }

  // 保存设置
  saveSettings(newSettings: KnowledgeSettings): void {
    try {
      this.settings = { ...newSettings }
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.settings))
      
      // 通知所有监听器
      this.listeners.forEach(listener => listener(this.settings))
      
      // 触发全局事件
      window.dispatchEvent(new CustomEvent('knowledge-settings-updated', {
        detail: this.settings
      }))
    } catch (error) {
      console.error('保存知识库设置失败:', error)
      throw error
    }
  }

  // 更新部分设置
  updateSettings(updates: Partial<KnowledgeSettings>): void {
    const newSettings = { ...this.settings, ...updates }
    this.saveSettings(newSettings)
  }

  // 重置为默认设置
  resetToDefaults(): void {
    this.saveSettings({ ...defaultKnowledgeSettings })
  }

  // 添加设置变更监听器
  addListener(listener: (settings: KnowledgeSettings) => void): void {
    this.listeners.push(listener)
  }

  // 移除设置变更监听器
  removeListener(listener: (settings: KnowledgeSettings) => void): void {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  // 获取特定设置值
  getTagsSettings() {
    return this.settings.tags
  }

  getSortingSettings() {
    return this.settings.sorting
  }

  getLayoutSettings() {
    return this.settings.layout
  }

  getLoadingSettings() {
    return this.settings.loading
  }

  getPerformanceSettings() {
    return this.settings.performance
  }

  // 获取CSS变量（用于动态样式）
  getCSSVariables(): Record<string, string> {
    const { layout } = this.settings
    
    const spacingMap = {
      compact: '4px',
      normal: '8px',
      comfortable: '16px',
      spacious: '24px'
    }

    return {
      '--cards-per-row': layout.cardsPerRow.toString(),
      '--card-spacing': spacingMap[layout.cardSpacing as keyof typeof spacingMap] || '8px'
    }
  }

  // 应用CSS变量到文档
  applyCSSVariables(): void {
    const variables = this.getCSSVariables()
    const root = document.documentElement
    
    Object.entries(variables).forEach(([key, value]) => {
      root.style.setProperty(key, value)
    })
  }
}

// 创建单例实例
export const knowledgeSettingsService = new KnowledgeSettingsService()

// 导出类型
export type { KnowledgeSettings }
