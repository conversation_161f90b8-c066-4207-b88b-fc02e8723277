<template>
  <div class="space-y-6">
    <!-- 默认搜索引擎 -->
    <div>
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">默认搜索引擎</h3>
      <div class="grid grid-cols-2 gap-3">
        <button v-for="engine in settings.engines" :key="engine.id" :class="[
          'flex items-center p-3 rounded-lg border-2 transition-all',
          settings.defaultEngine === engine.id
            ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
            : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
        ]" @click="setDefaultEngine(engine.id)">
          <div :class="[engine.icon, 'w-6 h-6 mr-3']"></div>
          <span class="font-medium text-gray-900 dark:text-gray-100">{{ engine.name }}</span>
          <div v-if="settings.defaultEngine === engine.id" class="i-heroicons-check w-5 h-5 ml-auto text-primary-500">
          </div>
        </button>
      </div>
    </div>

    <!-- 自定义搜索引擎 -->
    <div>
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">自定义搜索引擎</h3>
        <BaseButton @click="showAddEngine = true">
          <div class="i-heroicons-plus w-4 h-4 mr-2"></div>
          添加
        </BaseButton>
      </div>

      <div class="space-y-3">
        <div v-for="engine in customEngines" :key="engine.id"
          class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div class="flex items-center">
            <div :class="[engine.icon, 'w-5 h-5 mr-3']"></div>
            <div>
              <div class="font-medium text-gray-900 dark:text-gray-100">{{ engine.name }}</div>
              <div class="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">{{ engine.url }}</div>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <BaseButton variant="ghost" size="sm" @click="editEngine(engine)">
              <div class="i-heroicons-pencil w-4 h-4"></div>
            </BaseButton>
            <BaseButton variant="ghost" size="sm" @click="deleteEngine(engine)">
              <div class="i-heroicons-trash w-4 h-4"></div>
            </BaseButton>
          </div>
        </div>

        <div v-if="customEngines.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
          暂无自定义搜索引擎
        </div>
      </div>
    </div>

    <!-- 添加/编辑搜索引擎对话框 -->
    <BaseModal v-model="showAddEngine" :title="editingEngine ? '编辑搜索引擎' : '添加搜索引擎'">
      <div class="space-y-4">
        <a-form-item label="搜索引擎名称" required>
          <a-input v-model:value="engineForm.name" placeholder="例如：Google" />
        </a-form-item>

        <a-form-item label="搜索URL" required>
          <a-input v-model:value="engineForm.url" placeholder="例如：https://www.google.com/search?q={query}" />
          <template #extra>
            <div class="text-sm text-gray-500">使用 {query} 作为搜索关键词的占位符</div>
          </template>
        </a-form-item>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">图标</label>
          <div class="grid grid-cols-6 gap-2">
            <button v-for="icon in availableIcons" :key="icon" :class="[
              'p-2 rounded border-2 transition-all',
              engineForm.icon === icon
                ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
            ]" @click="engineForm.icon = icon">
              <div :class="[icon, 'w-5 h-5 mx-auto']"></div>
            </button>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-3">
          <BaseButton variant="outline" @click="cancelEdit">取消</BaseButton>
          <BaseButton @click="saveEngine">{{ editingEngine ? '保存' : '添加' }}</BaseButton>
        </div>
      </template>
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import BaseModal from '@/components/ui/BaseModal.vue'
import { searchEngineService } from '@/services/searchEngineService'
import type { SearchEngine, SearchSettings } from '@/types/search'

// 搜索引擎设置
const settings = ref<SearchSettings>(searchEngineService.getSettings())

// 自定义搜索引擎
const customEngines = computed(() => {
  const defaultIds = ['google', 'bing', 'baidu', 'duckduckgo', 'github', 'stackoverflow']
  return settings.value.engines.filter(engine => !defaultIds.includes(engine.id))
})

// 添加/编辑搜索引擎
const showAddEngine = ref(false)
const editingEngine = ref<SearchEngine | null>(null)
const engineForm = ref({
  name: '',
  url: '',
  icon: 'i-heroicons-magnifying-glass'
})

// 可用图标
const availableIcons = [
  'i-heroicons-magnifying-glass',
  'i-heroicons-globe-alt',
  'i-heroicons-computer-desktop',
  'i-heroicons-device-phone-mobile',
  'i-heroicons-academic-cap',
  'i-heroicons-beaker'
]

// 设置默认搜索引擎
const setDefaultEngine = (engineId: string) => {
  settings.value.defaultEngine = engineId
  searchEngineService.saveSettings(settings.value)
}

// 编辑搜索引擎
const editEngine = (engine: SearchEngine) => {
  editingEngine.value = engine
  engineForm.value = {
    name: engine.name,
    url: engine.url,
    icon: engine.icon
  }
  showAddEngine.value = true
}

// 删除搜索引擎
const deleteEngine = (engine: SearchEngine) => {
  if (confirm(`确定要删除搜索引擎"${engine.name}"吗？`)) {
    const index = settings.value.engines.findIndex(e => e.id === engine.id)
    if (index >= 0) {
      settings.value.engines.splice(index, 1)
      // 如果删除的是默认搜索引擎，重置为第一个
      if (settings.value.defaultEngine === engine.id) {
        settings.value.defaultEngine = settings.value.engines[0]?.id || 'google'
      }
      searchEngineService.saveSettings(settings.value)
    }
  }
}

// 取消编辑
const cancelEdit = () => {
  showAddEngine.value = false
  editingEngine.value = null
  engineForm.value = {
    name: '',
    url: '',
    icon: 'i-heroicons-magnifying-glass'
  }
}

// 保存搜索引擎
const saveEngine = () => {
  if (!engineForm.value.name || !engineForm.value.url) return

  if (editingEngine.value) {
    // 编辑现有引擎
    const index = settings.value.engines.findIndex(e => e.id === editingEngine.value!.id)
    if (index >= 0) {
      settings.value.engines[index] = {
        ...editingEngine.value,
        name: engineForm.value.name,
        url: engineForm.value.url,
        icon: engineForm.value.icon
      }
    }
  } else {
    // 添加新引擎
    const newEngine: SearchEngine = {
      id: `custom-${Date.now()}`,
      name: engineForm.value.name,
      url: engineForm.value.url,
      icon: engineForm.value.icon
    }
    settings.value.engines.push(newEngine)
  }

  searchEngineService.saveSettings(settings.value)
  cancelEdit()
}

// 初始化
onMounted(() => {
  settings.value = searchEngineService.getSettings()
})
</script>
