<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 translate-y-2"
      enter-to-class="opacity-100 translate-y-0"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 translate-y-0"
      leave-to-class="opacity-0 translate-y-2"
    >
      <div
        v-if="visible"
        class="fixed top-4 right-4 z-40 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 min-w-64"
      >
        <!-- 头部 -->
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center space-x-2">
            <div class="w-4 h-4 border-2 border-primary-600 border-t-transparent rounded-full animate-spin"></div>
            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
              {{ title }}
            </span>
          </div>
          <button
            @click="$emit('close')"
            class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded transition-colors"
          >
            <div class="i-heroicons-x-mark w-4 h-4"></div>
          </button>
        </div>

        <!-- 进度条 -->
        <div class="mb-2">
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              class="bg-primary-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${progress}%` }"
            ></div>
          </div>
        </div>

        <!-- 进度信息 -->
        <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>{{ current }}/{{ total }}</span>
          <span v-if="speed">{{ speed }}</span>
        </div>

        <!-- 状态信息 -->
        <div v-if="status" class="mt-2 text-xs text-gray-600 dark:text-gray-300">
          {{ status }}
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface Props {
  title: string
  current: number
  total: number
  speed?: string
  status?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
}>()

const visible = ref(false)

const progress = computed(() => {
  if (props.total === 0) return 0
  return Math.round((props.current / props.total) * 100)
})

onMounted(() => {
  visible.value = true
})
</script>
