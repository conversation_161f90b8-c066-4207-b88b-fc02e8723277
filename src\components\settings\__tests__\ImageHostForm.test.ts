import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import ImageHostForm from '../ImageHostForm.vue'

// Mock Ant Design Vue components
const mockAntdComponents = {
  'a-form': {
    template: '<form @submit="$emit(\'finish\')"><slot /></form>',
    emits: ['finish'],
  },
  'a-form-item': {
    template: '<div class="ant-form-item"><slot /></div>',
    props: ['label', 'name'],
  },
  'a-input': {
    template:
      '<input class="ant-input" :value="value" @input="$emit(\'update:value\', $event.target.value)" />',
    props: ['value'],
    emits: ['update:value'],
  },
  'a-input-password': {
    template:
      '<input type="password" class="ant-input-password" :value="value" @input="$emit(\'update:value\', $event.target.value)" />',
    props: ['value'],
    emits: ['update:value'],
  },
  'a-input-number': {
    template:
      '<input type="number" class="ant-input-number" :value="value" @input="$emit(\'update:value\', Number($event.target.value))" />',
    props: ['value', 'min', 'max'],
    emits: ['update:value'],
  },
  'a-select': {
    template:
      '<select class="ant-select" :value="value" @change="$emit(\'update:value\', $event.target.value)"><slot /></select>',
    props: ['value'],
    emits: ['update:value'],
  },
  'a-select-option': {
    template: '<option :value="value"><slot /></option>',
    props: ['value'],
  },
  'a-switch': {
    template:
      '<input type="checkbox" class="ant-switch" :checked="checked" @change="$emit(\'update:checked\', $event.target.checked)" />',
    props: ['checked'],
    emits: ['update:checked'],
  },
  'a-row': {
    template: '<div class="ant-row"><slot /></div>',
    props: ['gutter'],
  },
  'a-col': {
    template: '<div class="ant-col"><slot /></div>',
    props: ['span'],
  },
  'a-space': {
    template: '<div class="ant-space"><slot /></div>',
  },
  'a-button': {
    template:
      '<button class="ant-btn" :type="htmlType" :loading="loading" @click="$emit(\'click\')"><slot /></button>',
    props: ['type', 'htmlType', 'loading'],
    emits: ['click'],
  },
}

describe('ImageHostForm', () => {
  let wrapper: any

  const defaultProps = {
    config: null,
    saving: false,
  }

  beforeEach(() => {
    wrapper = mount(ImageHostForm, {
      props: defaultProps,
      global: {
        components: mockAntdComponents,
      },
    })
  })

  it('应该正确渲染表单结构', () => {
    expect(wrapper.find('.form-content').exists()).toBe(true)
    expect(wrapper.find('.form-section').exists()).toBe(true)
    expect(wrapper.find('.section-title').exists()).toBe(true)
  })

  it('应该显示所有必要的表单字段', () => {
    // 基础信息字段
    expect(wrapper.find('input[placeholder="如：我的PICUI图床"]').exists()).toBe(true)
    expect(wrapper.find('input[placeholder="如：picui"]').exists()).toBe(true)

    // API配置字段
    expect(wrapper.find('input[placeholder="https://example.com/api/upload"]').exists()).toBe(true)
    expect(wrapper.find('input[placeholder="file"]').exists()).toBe(true)

    // 认证配置字段
    expect(wrapper.find('input[placeholder="输入API Key或Token"]').exists()).toBe(true)
  })

  it('应该正确处理表单提交', async () => {
    const submitSpy = vi.fn()
    wrapper.vm.$emit = submitSpy

    // 填写必要字段
    await wrapper.find('input[placeholder="如：我的PICUI图床"]').setValue('测试图床')
    await wrapper.find('input[placeholder="如：picui"]').setValue('test')
    await wrapper
      .find('input[placeholder="https://example.com/api/upload"]')
      .setValue('https://test.com/upload')
    await wrapper.find('input[placeholder="file"]').setValue('file')
    await wrapper.find('input[placeholder="data.url"]').setValue('url')

    // 提交表单
    await wrapper.find('form').trigger('submit')
    await nextTick()

    expect(submitSpy).toHaveBeenCalledWith('submit', expect.any(Object))
  })

  it('应该正确处理取消操作', async () => {
    const cancelSpy = vi.fn()
    wrapper.vm.$emit = cancelSpy

    await wrapper.find('button').trigger('click')

    expect(cancelSpy).toHaveBeenCalledWith('cancel')
  })

  it('应该在编辑模式下正确初始化表单', async () => {
    const testConfig = {
      id: '1',
      name: '测试图床',
      provider: 'test',
      enabled: true,
      priority: 1,
      apiUrl: 'https://test.com/upload',
      method: 'POST',
      authType: 'none',
      fileField: 'file',
      responseType: 'json',
      urlField: 'url',
      maxFileSize: 10,
      allowedFormats: ['jpg', 'png'],
    }

    const editWrapper = mount(ImageHostForm, {
      props: {
        config: testConfig,
        saving: false,
      },
      global: {
        components: mockAntdComponents,
      },
    })

    await nextTick()

    expect(editWrapper.vm.form.name).toBe('测试图床')
    expect(editWrapper.vm.form.provider).toBe('test')
    expect(editWrapper.vm.form.apiUrl).toBe('https://test.com/upload')
  })

  it('应该正确处理格式文本转换', async () => {
    // 测试格式文本的getter
    wrapper.vm.form.allowedFormats = ['jpg', 'png', 'gif']
    expect(wrapper.vm.allowedFormatsText).toBe('jpg,png,gif')

    // 测试格式文本的setter
    wrapper.vm.allowedFormatsText = 'webp,svg,bmp'
    expect(wrapper.vm.form.allowedFormats).toEqual(['webp', 'svg', 'bmp'])
  })

  it('应该正确验证表单', () => {
    // 测试空表单验证
    expect(wrapper.vm.validateForm()).toBe(false)
    expect(Object.keys(wrapper.vm.errors).length).toBeGreaterThan(0)

    // 填写必要字段后验证
    wrapper.vm.form.name = '测试图床'
    wrapper.vm.form.provider = 'test'
    wrapper.vm.form.apiUrl = 'https://test.com/upload'
    wrapper.vm.form.fileField = 'file'
    wrapper.vm.form.urlField = 'url'
    wrapper.vm.form.priority = 5

    expect(wrapper.vm.validateForm()).toBe(true)
    expect(Object.keys(wrapper.vm.errors).length).toBe(0)
  })

  it('应该正确处理开关状态显示', async () => {
    // 测试启用状态
    wrapper.vm.form.enabled = true
    await nextTick()
    expect(wrapper.find('.switch-label').text()).toBe('启用')

    // 测试禁用状态
    wrapper.vm.form.enabled = false
    await nextTick()
    expect(wrapper.find('.switch-label').text()).toBe('禁用')
  })
})
