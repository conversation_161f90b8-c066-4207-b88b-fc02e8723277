import { tagService } from './tagService'
import { categoryService } from './categoryService'
import type { Tag, Category, ResourceForm } from '@/types'

/**
 * 临时数据服务
 * 处理编辑模式下创建的临时标签和分类的保存
 */
class TemporaryDataService {
  /**
   * 保存临时标签到数据库
   * @param tags 标签列表
   * @returns 标签ID映射表 (临时ID -> 真实ID)
   */
  async saveTemporaryTags(tags: Tag[]): Promise<Map<number, number>> {
    const idMapping = new Map<number, number>()
    
    for (const tag of tags) {
      if (tag.isTemporary && tag.id) {
        try {
          // 保存临时标签到数据库
          const realTagId = await tagService.createTag({
            name: tag.name,
            color: tag.color
          })
          
          // 记录ID映射
          idMapping.set(tag.id, realTagId)
          
          // 更新标签对象
          tag.id = realTagId
          tag.isTemporary = false
          
        } catch (error) {
          console.error(`保存临时标签失败: ${tag.name}`, error)
          throw new Error(`保存标签"${tag.name}"失败`)
        }
      }
    }
    
    return idMapping
  }
  
  /**
   * 保存临时分类到数据库
   * @param categories 分类列表
   * @returns 分类ID映射表 (临时ID -> 真实ID)
   */
  async saveTemporaryCategories(categories: Category[]): Promise<Map<number, number>> {
    const idMapping = new Map<number, number>()
    
    // 按层级排序，先保存父分类
    const sortedCategories = this.sortCategoriesByLevel(categories)
    
    for (const category of sortedCategories) {
      if (category.isTemporary && category.id) {
        try {
          // 如果父分类也是临时的，需要先映射父分类ID
          let parentId = category.parent_id
          if (parentId && idMapping.has(parentId)) {
            parentId = idMapping.get(parentId)!
          }
          
          // 保存临时分类到数据库
          const realCategoryId = await categoryService.createCategory({
            name: category.name,
            parent_id: parentId,
            sort_order: category.sort_order
          })
          
          // 记录ID映射
          idMapping.set(category.id, realCategoryId)
          
          // 更新分类对象
          category.id = realCategoryId
          category.parent_id = parentId
          category.isTemporary = false
          
        } catch (error) {
          console.error(`保存临时分类失败: ${category.name}`, error)
          throw new Error(`保存分类"${category.name}"失败`)
        }
      }
    }
    
    return idMapping
  }
  
  /**
   * 处理资源表单中的临时数据
   * @param form 资源表单数据
   * @param allTags 所有标签列表
   * @param allCategories 所有分类列表
   * @returns 处理后的表单数据
   */
  async processTemporaryData(
    form: ResourceForm,
    allTags: Tag[],
    allCategories: Category[]
  ): Promise<ResourceForm> {
    const processedForm = { ...form }
    
    // 保存临时标签
    const tagIdMapping = await this.saveTemporaryTags(allTags)
    
    // 更新表单中的标签ID
    if (processedForm.tag_ids.length > 0) {
      processedForm.tag_ids = processedForm.tag_ids.map(tagId => {
        return tagIdMapping.get(tagId) || tagId
      })
    }
    
    // 保存临时分类
    const categoryIdMapping = await this.saveTemporaryCategories(allCategories)
    
    // 更新表单中的分类ID
    if (processedForm.category_id && categoryIdMapping.has(processedForm.category_id)) {
      processedForm.category_id = categoryIdMapping.get(processedForm.category_id)!
    }
    
    return processedForm
  }
  
  /**
   * 按层级排序分类，确保父分类先于子分类保存
   * @param categories 分类列表
   * @returns 排序后的分类列表
   */
  private sortCategoriesByLevel(categories: Category[]): Category[] {
    const sorted: Category[] = []
    const remaining = [...categories]
    
    // 多轮排序，每轮处理一个层级
    while (remaining.length > 0) {
      const currentRoundCount = remaining.length
      
      for (let i = remaining.length - 1; i >= 0; i--) {
        const category = remaining[i]
        
        // 如果是根分类或父分类已经处理过，则可以处理这个分类
        if (category.parent_id === 0 || 
            sorted.some(c => c.id === category.parent_id) ||
            !categories.some(c => c.id === category.parent_id)) {
          sorted.push(category)
          remaining.splice(i, 1)
        }
      }
      
      // 如果一轮下来没有处理任何分类，说明存在循环依赖，跳出循环
      if (remaining.length === currentRoundCount) {
        console.warn('检测到分类循环依赖，剩余分类:', remaining)
        sorted.push(...remaining)
        break
      }
    }
    
    return sorted
  }
  
  /**
   * 清理临时数据标记
   * @param tags 标签列表
   * @param categories 分类列表
   */
  cleanupTemporaryFlags(tags: Tag[], categories: Category[]): void {
    // 清理标签的临时标记
    tags.forEach(tag => {
      if (tag.isTemporary) {
        delete tag.isTemporary
      }
    })
    
    // 清理分类的临时标记
    categories.forEach(category => {
      if (category.isTemporary) {
        delete category.isTemporary
      }
    })
  }
  
  /**
   * 检查是否有临时数据需要保存
   * @param tags 标签列表
   * @param categories 分类列表
   * @returns 是否有临时数据
   */
  hasTemporaryData(tags: Tag[], categories: Category[]): boolean {
    return tags.some(tag => tag.isTemporary) || 
           categories.some(category => category.isTemporary)
  }
}

export const temporaryDataService = new TemporaryDataService()
