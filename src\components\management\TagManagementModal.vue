<template>
  <BaseModal v-model="isVisible" title="标签管理" size="lg">
    <div class="space-y-6">
      <!-- 添加新标签 -->
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">添加新标签</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="md:col-span-2">
            <BaseInput
              v-model="newTag.name"
              label="标签名称"
              placeholder="请输入标签名称"
              required
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              标签颜色
            </label>
            <div class="flex items-center space-x-2">
              <input
                v-model="newTag.color"
                type="color"
                class="w-10 h-10 rounded border border-gray-300 dark:border-gray-600 cursor-pointer"
              />
              <BaseInput
                v-model="newTag.color"
                placeholder="#000000"
                class="flex-1"
              />
            </div>
          </div>
        </div>
        <div class="mt-4 flex justify-end">
          <BaseButton @click="handleAddTag" :disabled="!newTag.name.trim()">
            <div class="i-heroicons-plus mr-2"></div>
            添加标签
          </BaseButton>
        </div>
      </div>

      <!-- 标签统计 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ tagStats.total }}</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">总标签数</div>
        </div>
        <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div class="text-2xl font-bold text-green-600">{{ tagStats.used }}</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">已使用</div>
        </div>
        <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div class="text-2xl font-bold text-gray-400">{{ tagStats.unused }}</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">未使用</div>
        </div>
        <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div class="text-2xl font-bold text-blue-600">{{ tagStats.mostPopular?.resource_count || 0 }}</div>
          <div class="text-sm text-gray-500 dark:text-gray-400">最热标签</div>
        </div>
      </div>

      <!-- 标签列表 -->
      <div>
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">标签列表</h3>
          <div class="flex items-center space-x-2">
            <BaseInput
              v-model="searchQuery"
              placeholder="搜索标签..."
              prefix-icon="i-heroicons-magnifying-glass"
              clearable
              class="w-64"
            />
            <BaseButton variant="outline" @click="handleCleanupUnused">
              <div class="i-heroicons-trash mr-2"></div>
              清理未使用
            </BaseButton>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
          <div class="max-h-96 overflow-y-auto">
            <TagManagementList
              :tags="filteredTags"
              @edit="handleEditTag"
              @delete="handleDeleteTag"
              @merge="handleMergeTag"
            />
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-between">
        <BaseButton variant="outline" @click="handleRefresh">
          <div class="i-heroicons-arrow-path mr-2"></div>
          刷新
        </BaseButton>
        <BaseButton variant="outline" @click="isVisible = false">
          关闭
        </BaseButton>
      </div>
    </template>
  </BaseModal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseModal from '@/components/ui/BaseModal.vue'
import BaseInput from '@/components/ui/BaseInput.vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import TagManagementList from './TagManagementList.vue'
import { tagService } from '@/services/tagService'
import type { Tag } from '@/types'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 数据状态
const tags = ref<Tag[]>([])
const tagStats = ref({
  total: 0,
  used: 0,
  unused: 0,
  mostPopular: null as Tag | null
})
const loading = ref(false)
const searchQuery = ref('')

// 新标签表单
const newTag = ref({
  name: '',
  color: '#3B82F6'
})

// 过滤后的标签
const filteredTags = computed(() => {
  if (!searchQuery.value.trim()) {
    return tags.value
  }
  const query = searchQuery.value.toLowerCase()
  return tags.value.filter(tag => 
    tag.name.toLowerCase().includes(query)
  )
})

// 加载标签数据
const loadTags = async () => {
  try {
    loading.value = true
    const [allTags, stats] = await Promise.all([
      tagService.getAllTags(),
      tagService.getTagStats()
    ])
    tags.value = allTags
    tagStats.value = stats
  } catch (error) {
    console.error('加载标签失败:', error)
  } finally {
    loading.value = false
  }
}

// 添加标签
const handleAddTag = async () => {
  try {
    await tagService.createTag({
      name: newTag.value.name,
      color: newTag.value.color
    })
    
    // 重置表单
    newTag.value = {
      name: '',
      color: '#3B82F6'
    }
    
    // 刷新数据
    await loadTags()
  } catch (error) {
    console.error('添加标签失败:', error)
    alert('添加标签失败：' + (error as Error).message)
  }
}

// 编辑标签
const handleEditTag = async (tag: Tag) => {
  const newName = prompt('请输入新的标签名称:', tag.name)
  if (newName && newName.trim() && newName !== tag.name) {
    try {
      await tagService.updateTag(tag.id!, { name: newName.trim() })
      await loadTags()
    } catch (error) {
      console.error('编辑标签失败:', error)
      alert('编辑标签失败：' + (error as Error).message)
    }
  }
}

// 删除标签
const handleDeleteTag = async (tag: Tag) => {
  if (confirm(`确定要删除标签"${tag.name}"吗？\n注意：删除后相关的标签关联也会被移除。`)) {
    try {
      await tagService.deleteTag(tag.id!)
      await loadTags()
    } catch (error) {
      console.error('删除标签失败:', error)
      alert('删除标签失败，请重试')
    }
  }
}

// 合并标签
const handleMergeTag = async (sourceTag: Tag) => {
  const targetTagName = prompt(`将标签"${sourceTag.name}"合并到哪个标签？\n请输入目标标签名称:`)
  if (targetTagName && targetTagName.trim()) {
    try {
      const targetTag = tags.value.find(tag => tag.name === targetTagName.trim())
      if (!targetTag) {
        alert('目标标签不存在')
        return
      }
      if (targetTag.id === sourceTag.id) {
        alert('不能合并到自己')
        return
      }
      
      await tagService.mergeTags(sourceTag.id!, targetTag.id!)
      await loadTags()
    } catch (error) {
      console.error('合并标签失败:', error)
      alert('合并标签失败，请重试')
    }
  }
}

// 清理未使用的标签
const handleCleanupUnused = async () => {
  if (confirm('确定要清理所有未使用的标签吗？此操作不可撤销。')) {
    try {
      const count = await tagService.cleanupUnusedTags()
      alert(`已清理 ${count} 个未使用的标签`)
      await loadTags()
    } catch (error) {
      console.error('清理标签失败:', error)
      alert('清理标签失败，请重试')
    }
  }
}

// 刷新数据
const handleRefresh = () => {
  loadTags()
}

// 监听模态框显示状态
watch(isVisible, (visible) => {
  if (visible) {
    loadTags()
  }
})
</script>
