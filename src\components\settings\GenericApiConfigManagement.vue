<template>
  <a-space direction="vertical" size="large" style="width: 100%">
    <!-- 页面标题和操作按钮 -->
    <a-card class="generic-api-config-card">
      <div class="card-title-wrapper">
        <h2 class="card-title">通用API配置管理</h2>
        <a-space>
          <a-dropdown>
            <template #overlay>
              <a-menu @click="handleTemplateSelect">
                <a-menu-item key="custom">
                  <PlusOutlined />
                  自定义配置
                </a-menu-item>
                <a-menu-divider />
                <a-sub-menu key="text-chat" title="文本对话">
                  <a-menu-item key="doubao-deep-think">豆包AI深度思考</a-menu-item>
                  <a-menu-item key="ai-chat-multi-model">AI聚合聊天</a-menu-item>
                </a-sub-menu>
                <a-sub-menu key="image-generation" title="图像生成">
                  <a-menu-item key="baidu-ai-painting">百度AI绘画</a-menu-item>
                </a-sub-menu>
              </a-menu>
            </template>
            <a-button type="primary">
              <PlusOutlined />
              添加配置
              <DownOutlined />
            </a-button>
          </a-dropdown>
          <a-button @click="handleRefresh">
            <ReloadOutlined />
            刷新
          </a-button>
        </a-space>
      </div>

      <p style="color: #8c8c8c; margin-top: 8px; margin-bottom: 0;">
        配置各种非标准免费API接口，支持自定义请求参数和响应解析规则
      </p>
    </a-card>

    <!-- 配置列表 -->
    <a-card class="config-list-card">
      <a-list :data-source="configs" :loading="loading">
        <template #renderItem="{ item }">
          <a-list-item>
            <template #actions>
              <a-button type="text" @click="handleEditConfig(item)">
                <EditOutlined />
                编辑
              </a-button>
              <a-button type="text" @click="handleTestConfig(item)">
                <ApiOutlined />
                测试
              </a-button>
              <a-button type="text" danger @click="handleDeleteConfig(item)">
                <DeleteOutlined />
                删除
              </a-button>
            </template>

            <a-list-item-meta>
              <template #title>
                <div style="display: flex; align-items: center; gap: 8px;">
                  <span>{{ item.name }}</span>
                  <a-tag :color="getMethodColor(item.method)">{{ item.method }}</a-tag>
                </div>
              </template>
              <template #description>
                <div>
                  <div>{{ item.description || '暂无描述' }}</div>
                  <div style="color: #8c8c8c; font-size: 12px; margin-top: 4px;">
                    端点：{{ item.endpoint || '未配置' }}
                  </div>
                  <div style="color: #8c8c8c; font-size: 12px;">
                    参数：{{ item.parameters.length }} 个 |
                    响应映射：{{ item.responseMapping.length }} 个
                  </div>
                </div>
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>

        <template #empty>
          <a-empty description="暂无通用API配置">
            <a-button type="primary" @click="handleAddConfig">
              立即添加
            </a-button>
          </a-empty>
        </template>
      </a-list>
    </a-card>

    <!-- 配置编辑模态框 -->
    <a-modal v-model:open="showEditModal" :title="editingConfig.id ? '编辑通用API配置' : '添加通用API配置'" width="800px"
      :footer="null">
      <a-form layout="vertical" @submit.prevent="handleSaveConfig">
        <!-- 基本信息 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="配置名称" required>
              <a-input v-model:value="editingConfig.name" placeholder="请输入配置名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="HTTP方法" required>
              <a-select v-model:value="editingConfig.method" placeholder="选择HTTP方法">
                <a-select-option value="GET">GET</a-select-option>
                <a-select-option value="POST">POST</a-select-option>
                <a-select-option value="PUT">PUT</a-select-option>
                <a-select-option value="DELETE">DELETE</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="API端点" required>
          <a-input v-model:value="editingConfig.endpoint" placeholder="请输入完整的API端点URL" />
        </a-form-item>

        <a-form-item label="配置描述">
          <a-textarea v-model:value="editingConfig.description" placeholder="请输入配置描述（可选）" :rows="2" />
        </a-form-item>

        <!-- 参数配置 -->
        <a-form-item label="请求参数配置">
          <div style="border: 1px solid #d9d9d9; border-radius: 6px; padding: 16px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
              <span style="font-weight: 500;">参数列表</span>
              <a-button type="dashed" size="small" @click="handleAddParameter">
                <PlusOutlined />
                添加参数
              </a-button>
            </div>

            <div v-if="editingConfig.parameters.length === 0"
              style="text-align: center; color: #8c8c8c; padding: 20px;">
              暂无参数，点击上方按钮添加
            </div>

            <div v-for="(param, index) in editingConfig.parameters" :key="param.id"
              style="border: 1px solid #f0f0f0; border-radius: 4px; padding: 12px; margin-bottom: 8px;">
              <a-row :gutter="8">
                <a-col :span="5">
                  <a-input v-model:value="param.name" placeholder="参数名" size="small" />
                </a-col>
                <a-col :span="4">
                  <a-select v-model:value="param.type" placeholder="类型" size="small">
                    <a-select-option value="string">字符串</a-select-option>
                    <a-select-option value="number">数字</a-select-option>
                    <a-select-option value="boolean">布尔值</a-select-option>
                    <a-select-option value="object">对象</a-select-option>
                    <a-select-option value="array">数组</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="4">
                  <a-select v-model:value="param.location" placeholder="位置" size="small">
                    <a-select-option value="query">URL参数</a-select-option>
                    <a-select-option value="body">请求体</a-select-option>
                    <a-select-option value="header">请求头</a-select-option>
                    <a-select-option value="path">路径参数</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="5">
                  <a-input v-model:value="param.defaultValue" placeholder="默认值" size="small" />
                </a-col>
                <a-col :span="2">
                  <a-checkbox v-model:checked="param.required" size="small">必填</a-checkbox>
                </a-col>
                <a-col :span="4">
                  <a-space size="small">
                    <a-button type="text" size="small" danger @click="handleRemoveParameter(index)">
                      <DeleteOutlined />
                    </a-button>
                  </a-space>
                </a-col>
              </a-row>
              <a-row style="margin-top: 8px;">
                <a-col :span="24">
                  <a-input v-model:value="param.description" placeholder="参数描述（可选）" size="small" />
                </a-col>
              </a-row>
            </div>
          </div>
        </a-form-item>

        <!-- 响应映射配置 -->
        <a-form-item label="响应字段映射">
          <div style="border: 1px solid #d9d9d9; border-radius: 6px; padding: 16px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
              <span style="font-weight: 500;">字段映射列表</span>
              <a-button type="dashed" size="small" @click="handleAddResponseMapping">
                <PlusOutlined />
                添加映射
              </a-button>
            </div>

            <div v-if="editingConfig.responseMapping.length === 0"
              style="text-align: center; color: #8c8c8c; padding: 20px;">
              暂无字段映射，点击上方按钮添加
            </div>

            <div v-for="(mapping, index) in editingConfig.responseMapping" :key="mapping.id"
              style="border: 1px solid #f0f0f0; border-radius: 4px; padding: 12px; margin-bottom: 8px;">
              <a-row :gutter="8">
                <a-col :span="8">
                  <a-input v-model:value="mapping.sourceField" placeholder="源字段路径（如：data.content）" size="small" />
                </a-col>
                <a-col :span="6">
                  <a-input v-model:value="mapping.targetField" placeholder="目标字段名" size="small" />
                </a-col>
                <a-col :span="4">
                  <a-select v-model:value="mapping.dataType" placeholder="数据类型" size="small">
                    <a-select-option value="string">字符串</a-select-option>
                    <a-select-option value="number">数字</a-select-option>
                    <a-select-option value="boolean">布尔值</a-select-option>
                    <a-select-option value="array">数组</a-select-option>
                    <a-select-option value="object">对象</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="4">
                  <a-button type="text" size="small" danger @click="handleRemoveResponseMapping(index)">
                    <DeleteOutlined />
                    删除
                  </a-button>
                </a-col>
              </a-row>
              <a-row style="margin-top: 8px;">
                <a-col :span="20">
                  <a-input v-model:value="mapping.description" placeholder="字段描述（可选）" size="small" />
                </a-col>
              </a-row>
            </div>
          </div>
        </a-form-item>

        <!-- 高级配置 -->
        <a-form-item label="高级配置">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="超时时间（毫秒）">
                <a-input-number v-model:value="editingConfig.timeout" :min="1000" :max="300000" placeholder="30000"
                  style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="重试次数">
                <a-input-number v-model:value="editingConfig.retryCount" :min="0" :max="10" placeholder="3"
                  style="width: 100%" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form-item>

        <!-- 操作按钮 -->
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="saving">
              保存配置
            </a-button>
            <a-button @click="handleCancelEdit">
              取消
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>
  </a-space>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined,
  ApiOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import { aiConfigService } from '@/services/aiConfigService'
import { apiTemplateService } from '@/services/apiTemplateService'
import type { GenericApiConfig, ApiParameter, ResponseMapping } from '@/types'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const showEditModal = ref(false)
const configs = ref<GenericApiConfig[]>([])

// 编辑配置数据
const editingConfig = reactive<Partial<GenericApiConfig> & {
  parameters: ApiParameter[]
  responseMapping: ResponseMapping[]
}>({
  name: '',
  description: '',
  method: 'POST',
  endpoint: '',
  parameters: [],
  responseMapping: [],
  headers: {},
  timeout: 30000,
  retryCount: 3
})

// 加载配置列表
const loadConfigs = async () => {
  loading.value = true
  try {
    configs.value = aiConfigService.getGenericApiConfigs()
  } catch (error: any) {
    message.error('加载配置失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 获取HTTP方法颜色
const getMethodColor = (method: string) => {
  const colors: Record<string, string> = {
    GET: 'blue',
    POST: 'green',
    PUT: 'orange',
    DELETE: 'red'
  }
  return colors[method] || 'default'
}

// 处理模板选择
const handleTemplateSelect = ({ key }: { key: string }) => {
  if (key === 'custom') {
    handleAddConfig()
  } else {
    handleAddConfigFromTemplate(key)
  }
}

// 添加配置
const handleAddConfig = () => {
  Object.assign(editingConfig, aiConfigService.createDefaultGenericApiConfig())
  editingConfig.parameters = []
  editingConfig.responseMapping = []
  showEditModal.value = true
}

// 从模板添加配置
const handleAddConfigFromTemplate = (templateId: string) => {
  const template = apiTemplateService.getTemplateConfig(templateId)
  if (template) {
    Object.assign(editingConfig, template)
    editingConfig.parameters = [...template.parameters]
    editingConfig.responseMapping = [...template.responseMapping]
    showEditModal.value = true
    message.success(`已加载"${template.name}"模板配置`)
  } else {
    message.error('模板加载失败')
  }
}

// 编辑配置
const handleEditConfig = (config: GenericApiConfig) => {
  Object.assign(editingConfig, {
    ...config,
    parameters: [...config.parameters],
    responseMapping: [...config.responseMapping]
  })
  showEditModal.value = true
}

// 保存配置
const handleSaveConfig = async () => {
  try {
    if (!editingConfig.name?.trim()) {
      message.error('请输入配置名称')
      return
    }

    if (!editingConfig.endpoint?.trim()) {
      message.error('请输入API端点')
      return
    }

    saving.value = true

    const configData = {
      name: editingConfig.name.trim(),
      description: editingConfig.description?.trim() || '',
      method: editingConfig.method!,
      endpoint: editingConfig.endpoint.trim(),
      parameters: editingConfig.parameters,
      responseMapping: editingConfig.responseMapping,
      headers: editingConfig.headers || {},
      timeout: editingConfig.timeout || 30000,
      retryCount: editingConfig.retryCount || 3
    }

    if (editingConfig.id) {
      // 更新配置
      const success = aiConfigService.updateGenericApiConfig(editingConfig.id, configData)
      if (success) {
        message.success('配置更新成功!')
      } else {
        message.error('配置更新失败!')
        return
      }
    } else {
      // 新增配置
      aiConfigService.saveGenericApiConfig(configData)
      message.success('配置添加成功!')
    }

    showEditModal.value = false
    await loadConfigs()

  } catch (error: any) {
    message.error(error.message || '保存配置失败!')
  } finally {
    saving.value = false
  }
}

// 取消编辑
const handleCancelEdit = () => {
  showEditModal.value = false
}

// 删除配置
const handleDeleteConfig = (config: GenericApiConfig) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除配置"${config.name}"吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        const success = aiConfigService.deleteGenericApiConfig(config.id)
        if (success) {
          message.success('配置删除成功!')
          await loadConfigs()
        } else {
          message.error('配置删除失败!')
        }
      } catch (error: any) {
        message.error(error.message || '删除配置失败!')
      }
    }
  })
}

// 测试配置
const handleTestConfig = (config: GenericApiConfig) => {
  message.info('API测试功能开发中...')
}

// 刷新配置列表
const handleRefresh = () => {
  loadConfigs()
}

// 添加参数
const handleAddParameter = () => {
  const newParam: ApiParameter = {
    id: `param_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    name: '',
    label: '',
    type: 'string',
    location: 'query',
    required: false,
    defaultValue: '',
    description: ''
  }
  editingConfig.parameters.push(newParam)
}

// 删除参数
const handleRemoveParameter = (index: number) => {
  editingConfig.parameters.splice(index, 1)
}

// 添加响应映射
const handleAddResponseMapping = () => {
  const newMapping: ResponseMapping = {
    id: `mapping_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    sourceField: '',
    targetField: '',
    dataType: 'string',
    description: ''
  }
  editingConfig.responseMapping.push(newMapping)
}

// 删除响应映射
const handleRemoveResponseMapping = (index: number) => {
  editingConfig.responseMapping.splice(index, 1)
}

// 初始化
onMounted(() => {
  loadConfigs()
})
</script>

<style scoped>
.generic-api-config-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.config-list-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-title-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.dark .card-title {
  color: #f0f0f0;
}
</style>
