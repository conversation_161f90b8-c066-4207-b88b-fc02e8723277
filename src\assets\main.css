@import './base.css';

#app {
  width: 100%;
  margin: 0;
  padding: 0;
  font-weight: normal;
  background: var(--color-page-background);
  min-height: 100vh;
  transition: background-color 0.3s ease;
}

/* 确保暗色模式下#app元素也应用正确的背景色 */
:root.dark #app {
  background: var(--color-page-background, #000000) !important;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024px) {
  body {
    margin: 0;
    padding: 0;
  }

  #app {
    width: 100%;
    padding: 0;
  }
}
