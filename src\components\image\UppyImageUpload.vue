<template>
  <div class="uppy-upload-container">
    <!-- Uppy Dashboard 容器 -->
    <div ref="uppyContainer" class="uppy-dashboard-container"></div>
    
    <!-- 自定义配置面板 -->
    <div class="upload-config-panel" v-if="showConfig">
      <div class="config-header">
        <h3>上传配置</h3>
        <button @click="showConfig = false" class="close-btn">×</button>
      </div>
      
      <div class="config-content">
        <!-- 图床选择 -->
        <div class="config-section">
          <label>选择图床</label>
          <div class="host-selection">
            <div 
              v-for="host in availableHosts" 
              :key="host.id"
              class="host-option"
              :class="{ active: selectedHosts.includes(host.id) }"
              @click="toggleHost(host.id)"
            >
              <span class="host-name">{{ host.name }}</span>
              <span class="host-status" :class="host.enabled ? 'online' : 'offline'">
                {{ host.enabled ? '在线' : '离线' }}
              </span>
            </div>
          </div>
        </div>
        
        <!-- 批量设置 -->
        <div class="config-section">
          <label>名称前缀</label>
          <input v-model="batchSettings.namePrefix" placeholder="可选" />
          
          <label>标签</label>
          <input v-model="batchSettings.tags" placeholder="用逗号分隔" />
          
          <label>描述</label>
          <textarea v-model="batchSettings.description" placeholder="可选"></textarea>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import Uppy from '@uppy/core'
import Dashboard from '@uppy/dashboard'
import XHRUpload from '@uppy/xhr-upload'
import ImageEditor from '@uppy/image-editor'
import Webcam from '@uppy/webcam'
import ScreenCapture from '@uppy/screen-capture'
import { imageHostService } from '@/services/imageHostService'
import { imageDataService } from '@/services/imageDataService'
import type { ImageHostConfig } from '@/types/imageHost'

// 引入 Uppy 样式
import '@uppy/core/dist/style.min.css'
import '@uppy/dashboard/dist/style.min.css'
import '@uppy/image-editor/dist/style.min.css'
import '@uppy/webcam/dist/style.min.css'
import '@uppy/screen-capture/dist/style.min.css'

interface Props {
  maxFiles?: number
  allowedTypes?: string[]
  maxFileSize?: number
  autoUpload?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  maxFiles: 10,
  allowedTypes: () => ['image/*'],
  maxFileSize: 10 * 1024 * 1024, // 10MB
  autoUpload: true
})

const emit = defineEmits<{
  uploaded: [results: any[]]
  error: [error: string]
  progress: [progress: number]
}>()

// 响应式数据
const uppyContainer = ref<HTMLElement>()
const showConfig = ref(false)
const availableHosts = ref<ImageHostConfig[]>([])
const selectedHosts = ref<string[]>([])
const batchSettings = ref({
  namePrefix: '',
  tags: '',
  description: ''
})

let uppy: Uppy | null = null

// 初始化 Uppy
const initUppy = async () => {
  if (!uppyContainer.value) return

  // 加载图床配置
  availableHosts.value = await imageHostService.getEnabledConfigs()
  
  // 创建 Uppy 实例
  uppy = new Uppy({
    debug: true,
    autoProceed: props.autoUpload,
    restrictions: {
      maxFileSize: props.maxFileSize,
      maxNumberOfFiles: props.maxFiles,
      allowedFileTypes: props.allowedTypes
    },
    locale: {
      strings: {
        // 中文本地化
        dropPasteFiles: '拖拽文件到这里或 %{browseFiles}',
        browseFiles: '选择文件',
        uploadComplete: '上传完成',
        uploadFailed: '上传失败',
        retry: '重试',
        cancel: '取消',
        remove: '移除',
        edit: '编辑',
        done: '完成'
      }
    }
  })

  // 添加 Dashboard 插件
  uppy.use(Dashboard, {
    target: uppyContainer.value,
    inline: true,
    width: '100%',
    height: 400,
    showProgressDetails: true,
    showRemoveButtonAfterComplete: true,
    showSelectedFiles: true,
    proudlyDisplayPoweredByUppy: false,
    theme: 'auto', // 自动主题
    note: '支持图片格式，单个文件最大 10MB',
    metaFields: [
      { id: 'name', name: '文件名', placeholder: '输入文件名' },
      { id: 'caption', name: '描述', placeholder: '输入描述信息' }
    ]
  })

  // 添加图片编辑器
  uppy.use(ImageEditor, {
    target: Dashboard,
    quality: 0.8
  })

  // 添加摄像头支持
  uppy.use(Webcam, {
    target: Dashboard,
    countdown: 3,
    modes: ['picture'],
    mirror: true,
    facingMode: 'user'
  })

  // 添加屏幕截图支持
  uppy.use(ScreenCapture, {
    target: Dashboard
  })

  // 自定义上传插件 - 这里是关键！
  uppy.use(XHRUpload, {
    endpoint: 'dummy', // 占位符，实际不会使用
    formData: true,
    fieldName: 'file',
    // 自定义上传函数
    getUploadParameters: (file) => {
      return {
        method: 'POST',
        url: 'dummy', // 占位符
        fields: {},
        headers: {}
      }
    }
  })

  // 重写上传逻辑 - 使用你的自定义图床
  uppy.addPreProcessor(async (fileIDs) => {
    for (const fileID of fileIDs) {
      const file = uppy!.getFile(fileID)
      if (file) {
        // 设置文件为上传中状态
        uppy!.setFileState(fileID, {
          progress: { uploadStarted: Date.now() }
        })
      }
    }
  })

  // 监听上传事件
  uppy.on('upload', async (data) => {
    // 阻止默认上传行为
    data.fileIDs.forEach(fileID => {
      uppy!.removeFile(fileID)
    })
    
    // 使用自定义上传逻辑
    await handleCustomUpload(data.fileIDs.map(id => uppy!.getFile(id)!))
  })

  // 监听文件添加
  uppy.on('file-added', (file) => {
    console.log('文件已添加:', file.name)
  })

  // 监听上传进度
  uppy.on('upload-progress', (file, progress) => {
    const percentage = Math.round((progress.bytesUploaded / progress.bytesTotal) * 100)
    emit('progress', percentage)
  })

  // 监听上传完成
  uppy.on('complete', (result) => {
    console.log('上传完成:', result)
    emit('uploaded', result.successful)
  })

  // 监听错误
  uppy.on('upload-error', (file, error) => {
    console.error('上传错误:', error)
    emit('error', error.message)
  })
}

// 自定义上传处理函数
const handleCustomUpload = async (files: any[]) => {
  const results = []
  
  for (const file of files) {
    try {
      // 更新文件状态为上传中
      uppy!.setFileState(file.id, {
        progress: { uploadStarted: Date.now(), uploadComplete: false, percentage: 0 }
      })
      
      // 使用你现有的上传逻辑
      const result = await uploadToCustomHosts(file.data, {
        name: file.meta.name || file.name,
        description: file.meta.caption || batchSettings.value.description,
        tags: batchSettings.value.tags.split(',').map(t => t.trim()).filter(Boolean)
      })
      
      // 更新文件状态为完成
      uppy!.setFileState(file.id, {
        progress: { uploadComplete: true, percentage: 100 },
        uploadURL: result.url
      })
      
      results.push(result)
    } catch (error) {
      // 更新文件状态为错误
      uppy!.setFileState(file.id, {
        error: error instanceof Error ? error.message : '上传失败'
      })
      
      console.error('上传失败:', error)
    }
  }
  
  return results
}

// 使用你现有的图床上传逻辑
const uploadToCustomHosts = async (file: File, metadata: any) => {
  // 获取选中的图床或使用默认策略
  const hostsToUse = selectedHosts.value.length > 0 
    ? availableHosts.value.filter(h => selectedHosts.value.includes(h.id))
    : availableHosts.value.filter(h => h.enabled)
  
  if (hostsToUse.length === 0) {
    throw new Error('没有可用的图床')
  }
  
  // 随机选择图床
  const randomHost = hostsToUse[Math.floor(Math.random() * hostsToUse.length)]
  
  // 使用你现有的上传方法
  return await uploadToSingleHost(file, randomHost, (progress) => {
    // 更新 Uppy 进度
    const fileInUppy = Array.from(uppy!.getFiles()).find(f => f.data === file)
    if (fileInUppy) {
      uppy!.setFileState(fileInUppy.id, {
        progress: { 
          uploadStarted: Date.now(),
          uploadComplete: false,
          percentage: progress.percentage,
          bytesUploaded: progress.loaded,
          bytesTotal: progress.total
        }
      })
    }
  })
}

// 你现有的上传到单个图床的方法（复制过来）
const uploadToSingleHost = async (file: File, hostConfig: ImageHostConfig, onProgress?: any) => {
  // 这里使用你现有的 uploadToSingleHost 实现
  // 为了简化示例，这里只是一个占位符
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()
    const formData = new FormData()
    
    formData.append(hostConfig.fileField || 'file', file)
    
    if (onProgress) {
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          onProgress({
            loaded: event.loaded,
            total: event.total,
            percentage: Math.round((event.loaded / event.total) * 100)
          })
        }
      })
    }
    
    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText)
          resolve({
            url: response.data?.links?.url || response.url,
            hostName: hostConfig.name
          })
        } catch (error) {
          reject(new Error('解析响应失败'))
        }
      } else {
        reject(new Error(`HTTP ${xhr.status}`))
      }
    })
    
    xhr.addEventListener('error', () => {
      reject(new Error('网络错误'))
    })
    
    xhr.open('POST', hostConfig.apiUrl)
    
    // 设置认证头
    if (hostConfig.authType === 'header' && hostConfig.authKey) {
      xhr.setRequestHeader(hostConfig.authHeader!, hostConfig.authKey)
    }
    
    xhr.send(formData)
  })
}

// 切换图床选择
const toggleHost = (hostId: string) => {
  const index = selectedHosts.value.indexOf(hostId)
  if (index > -1) {
    selectedHosts.value.splice(index, 1)
  } else {
    selectedHosts.value.push(hostId)
  }
}

// 生命周期
onMounted(() => {
  initUppy()
})

onUnmounted(() => {
  if (uppy) {
    uppy.destroy()
  }
})

// 暴露方法
defineExpose({
  showConfigPanel: () => { showConfig.value = true },
  hideConfigPanel: () => { showConfig.value = false },
  addFiles: (files: File[]) => {
    files.forEach(file => uppy?.addFile({
      name: file.name,
      type: file.type,
      data: file,
      source: 'Local'
    }))
  },
  startUpload: () => uppy?.upload(),
  reset: () => uppy?.reset()
})
</script>

<style scoped>
.uppy-upload-container {
  position: relative;
  width: 100%;
}

.uppy-dashboard-container {
  border-radius: 8px;
  overflow: hidden;
}

/* 自定义 Uppy 主题 */
:deep(.uppy-Dashboard) {
  --uppy-c-primary: #3b82f6;
  --uppy-c-primary-dark: #2563eb;
  --uppy-c-secondary: #64748b;
  --uppy-c-bg: #ffffff;
  --uppy-c-border: #e2e8f0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 暗黑模式适配 */
.dark :deep(.uppy-Dashboard) {
  --uppy-c-bg: #1f2937;
  --uppy-c-border: #374151;
  --uppy-c-text: #f9fafb;
}

.upload-config-panel {
  position: absolute;
  top: 0;
  right: -320px;
  width: 300px;
  height: 100%;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.config-content {
  padding: 16px;
  height: calc(100% - 60px);
  overflow-y: auto;
}

.config-section {
  margin-bottom: 24px;
}

.config-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
}

.host-selection {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.host-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.host-option:hover {
  border-color: #3b82f6;
}

.host-option.active {
  background: #eff6ff;
  border-color: #3b82f6;
}

.host-status.online {
  color: #10b981;
}

.host-status.offline {
  color: #ef4444;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
}

.close-btn:hover {
  color: #374151;
}

input, textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
}

input:focus, textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

textarea {
  resize: vertical;
  min-height: 60px;
}
</style>
