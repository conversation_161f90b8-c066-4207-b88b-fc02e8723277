import type {
  ImageHostConfig,
  ImageHostConfigForm,
  ImageHostPreset,
  ImageHostTestResult,
} from '@/types/imageHost'
import { imageHostTestService, type DetailedTestResult } from './imageHostTestService'

// 预设图床配置模板
export const IMAGE_HOST_PRESETS: Record<string, ImageHostPreset> = {
  picui: {
    name: 'PICUI图床',
    description: '支持游客上传，可选Token认证',
    provider: 'picui',
    config: {
      name: 'PICUI图床',
      provider: 'picui',
      apiUrl: 'https://picui.cn/api/v1/upload',
      method: 'POST',
      authType: 'header',
      authHeader: 'Authorization',
      authPrefix: 'Bearer ',
      fileField: 'file',
      responseType: 'json',
      successField: 'status',
      successValue: true,
      urlField: 'data.links.url',
      errorField: 'message',
      maxFileSize: 10,
      allowedFormats: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
      enabled: true,
      priority: 1,
    },
  },

  jingdong: {
    name: '京东图床',
    description: '无需认证，5MB限制，无压缩',
    provider: 'jingdong',
    config: {
      name: '京东图床',
      provider: 'jingdong',
      apiUrl: 'https://api.xinyew.cn/api/jdtc',
      method: 'POST',
      authType: 'none',
      fileField: 'file',
      responseType: 'json',
      successField: 'errno',
      successValue: 0,
      urlField: 'data.url',
      errorField: 'message',
      maxFileSize: 5,
      allowedFormats: ['jpg', 'jpeg', 'png', 'gif'],
      enabled: true,
      priority: 2,
    },
  },

  yiyun: {
    name: '一云图床',
    description: '可选Token认证，支持多种格式',
    provider: 'yiyun',
    config: {
      name: '一云图床',
      provider: 'yiyun',
      apiUrl: 'https://imgbed.yiyunt.cn/api/upload',
      method: 'POST',
      authType: 'query',
      fileField: 'fileupload',
      responseType: 'json',
      successField: 'success',
      successValue: true,
      urlField: 'url',
      errorField: 'error',
      maxFileSize: 10,
      allowedFormats: ['jpg', 'jpeg', 'png', 'gif'],
      enabled: true,
      priority: 3,
    },
  },

  picgo: {
    name: 'PicGo图床',
    description: '功能丰富，需要API Key',
    provider: 'picgo',
    config: {
      name: 'PicGo图床',
      provider: 'picgo',
      apiUrl: 'https://www.picgo.net/api/1/upload',
      method: 'POST',
      authType: 'header',
      authHeader: 'X-API-Key',
      fileField: 'source',
      responseType: 'json',
      successField: 'status_code',
      successValue: 200,
      urlField: 'image.url',
      errorField: 'error.message',
      maxFileSize: 50,
      allowedFormats: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'],
      enabled: true,
      priority: 4,
    },
  },

  // tc360: {
  //   name: '360图床',
  //   description: '免费图床，无需认证，支持多种格式',
  //   provider: 'tc360',
  //   config: {
  //     name: '360图床',
  //     provider: 'tc360',
  //     apiUrl: 'https://api.xinyew.cn/api/360tc',
  //     method: 'POST',
  //     authType: 'none',
  //     fileField: 'file',
  //     responseType: 'json',
  //     successField: 'errno',
  //     successValue: 0,
  //     urlField: 'data.url',
  //     errorField: 'error',
  //     maxFileSize: 5,
  //     allowedFormats: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  //     enabled: true,
  //     priority: 5,
  //   },
  // },
}

class ImageHostService {
  private readonly STORAGE_KEY = 'image_host_configs'

  // 获取所有配置
  async getAllConfigs(): Promise<ImageHostConfig[]> {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (!stored) return []

      const configs = JSON.parse(stored) as ImageHostConfig[]
      return configs.map((config) => ({
        ...config,
        createdAt: new Date(config.createdAt),
        updatedAt: new Date(config.updatedAt),
      }))
    } catch (error) {
      console.error('获取图床配置失败:', error)
      return []
    }
  }

  // 保存配置
  async saveConfig(form: ImageHostConfigForm, id?: string): Promise<ImageHostConfig> {
    try {
      const configs = await this.getAllConfigs()
      const now = new Date()

      if (id) {
        // 更新现有配置
        const index = configs.findIndex((c) => c.id === id)
        if (index === -1) {
          throw new Error('配置不存在')
        }

        const updatedConfig: ImageHostConfig = {
          ...configs[index],
          ...form,
          updatedAt: now,
        }

        configs[index] = updatedConfig
        await this.saveConfigs(configs)
        return updatedConfig
      } else {
        // 创建新配置
        const newConfig: ImageHostConfig = {
          id: `host_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
          ...form,
          createdAt: now,
          updatedAt: now,
        }

        configs.push(newConfig)
        await this.saveConfigs(configs)
        return newConfig
      }
    } catch (error) {
      console.error('保存图床配置失败:', error)
      throw error
    }
  }

  // 删除配置
  async deleteConfig(id: string): Promise<void> {
    try {
      const configs = await this.getAllConfigs()
      const filteredConfigs = configs.filter((c) => c.id !== id)
      await this.saveConfigs(filteredConfigs)
    } catch (error) {
      console.error('删除图床配置失败:', error)
      throw error
    }
  }

  // 测试配置连接 - 使用增强版测试服务
  async testConfig(config: ImageHostConfig): Promise<ImageHostTestResult> {
    try {
      console.log(`使用增强版测试服务测试图床: ${config.name}`)
      const detailedResult = await imageHostTestService.testImageHost(config)

      // 将详细结果转换为简化的结果格式（保持向后兼容）
      const result: ImageHostTestResult = {
        success: detailedResult.success,
        message: detailedResult.message,
        responseTime: detailedResult.duration,
        timestamp: detailedResult.timestamp,
      }

      if (detailedResult.error) {
        result.error = detailedResult.error
      }

      // 添加详细信息到结果中（可选）
      if (detailedResult.details) {
        ;(result as any).details = detailedResult.details
        ;(result as any).stage = detailedResult.stage
      }

      return result
    } catch (error) {
      console.error('增强版测试服务失败，回退到基础测试:', error)

      // 如果增强版测试失败，回退到简单的连接测试
      return this.basicConnectivityTest(config)
    }
  }

  // 基础连接测试（作为回退方案）
  private async basicConnectivityTest(config: ImageHostConfig): Promise<ImageHostTestResult> {
    const startTime = Date.now()

    try {
      const response = await fetch(config.apiUrl, {
        method: 'HEAD',
        signal: AbortSignal.timeout(10000),
      })

      return {
        success: response.ok,
        message: response.ok ? '基础连接测试成功' : `连接失败: ${response.status}`,
        responseTime: Date.now() - startTime,
        timestamp: Date.now(),
      }
    } catch (error) {
      return {
        success: false,
        message: `连接测试失败: ${(error as Error).message}`,
        responseTime: Date.now() - startTime,
        timestamp: Date.now(),
        error: (error as Error).message,
      }
    }
  }

  // 获取启用的配置（按优先级排序）
  async getEnabledConfigs(): Promise<ImageHostConfig[]> {
    const configs = await this.getAllConfigs()
    return configs.filter((config) => config.enabled).sort((a, b) => a.priority - b.priority)
  }

  // 构建请求
  private buildRequest(file: File, config: ImageHostConfig) {
    const formData = new FormData()
    formData.append(config.fileField, file)

    // 添加额外参数
    if (config.params) {
      Object.entries(config.params).forEach(([key, value]) => {
        formData.append(key, String(value))
      })
    }

    // 构建URL
    let url = config.apiUrl
    if (config.authType === 'query' && config.authKey) {
      url += `/${config.authKey}`
    }

    // 构建请求头
    const headers: Record<string, string> = {
      ...config.headers,
    }

    // 添加认证头
    if (config.authType === 'header' && config.authKey) {
      const authValue = config.authPrefix ? `${config.authPrefix}${config.authKey}` : config.authKey
      headers[config.authHeader!] = authValue
    }

    return {
      url,
      options: {
        method: config.method,
        headers,
        body: formData,
      },
    }
  }

  // 获取嵌套对象值
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }

  // 保存配置到localStorage
  private async saveConfigs(configs: ImageHostConfig[]): Promise<void> {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(configs))
    } catch (error) {
      console.error('保存图床配置到localStorage失败:', error)
      throw new Error('保存配置失败')
    }
  }
}

export const imageHostService = new ImageHostService()
