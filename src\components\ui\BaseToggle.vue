<template>
  <div class="space-y-1">
    <div class="flex items-center justify-between">
      <div class="flex-1">
        <label v-if="label" class="text-sm font-medium text-gray-700 dark:text-gray-300">
          {{ label }}
          <span v-if="required" class="text-red-500">*</span>
        </label>
        <p v-if="hint" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
          {{ hint }}
        </p>
      </div>

      <label class="relative inline-flex items-center cursor-pointer">
        <input :checked="modelValue" :disabled="disabled" type="checkbox" class="sr-only peer" @change="handleChange" />
        <div :class="[
          'relative w-11 h-6 rounded-full transition-colors duration-200',
          'peer-focus:outline-none peer-focus:ring-4',
          disabled
            ? 'bg-gray-200 dark:bg-gray-600 cursor-not-allowed'
            : modelValue
              ? 'bg-primary-600 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800'
              : 'bg-gray-200 dark:bg-gray-700 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800',
          error ? 'ring-2 ring-red-300 dark:ring-red-600' : ''
        ]">
          <!-- 滑块 -->
          <div :class="[
            'absolute top-0.5 left-0.5 bg-white border border-gray-300 rounded-full h-5 w-5 transition-transform duration-200 shadow-sm',
            modelValue ? 'translate-x-5 border-white' : 'translate-x-0'
          ]"></div>
        </div>
      </label>
    </div>

    <p v-if="error" class="text-xs text-red-600 dark:text-red-400">
      {{ error }}
    </p>
  </div>
</template>

<script setup lang="ts">
// Props
interface Props {
  modelValue?: boolean
  label?: string
  hint?: string
  error?: string
  disabled?: boolean
  required?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'change': [value: boolean]
}>()

// 事件处理
const handleChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.checked

  emit('update:modelValue', value)
  emit('change', value)
}
</script>
