<template>
    <!-- 图床配置表单 - 支持模态框和内联模式 -->
    <div v-if="inline" class="inline-config-form">
        <div class="form-header">
            <h4 class="form-title">{{ isEditing ? '编辑图床配置' : '添加图床配置' }}</h4>
        </div>
        <ImageHostForm :config="config" :saving="saving" @submit="handleSubmit" @cancel="$emit('close')" />
    </div>

    <a-modal v-else :open="true" :title="isEditing ? '编辑图床配置' : '添加图床配置'" width="800px" :footer="null"
        @cancel="$emit('close')" destroy-on-close>
        <ImageHostForm :config="config" :saving="saving" @submit="handleSubmit" @cancel="$emit('close')" />
    </a-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { imageHostService } from '@/services/imageHostService'
import type { ImageHostConfig, ImageHostConfigForm } from '@/types/imageHost'
import ImageHostForm from './ImageHostForm.vue'

// 定义组件名称
defineOptions({
    name: 'ImageHostConfigModal'
})

// Props
interface Props {
    config?: ImageHostConfig | null
    inline?: boolean // 是否为内联模式
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
    save: [config: ImageHostConfig]
    close: []
}>()

// 响应式数据
const saving = ref(false)

const isEditing = computed(() => !!props.config)

// 处理表单提交
const handleSubmit = async (formData: ImageHostConfigForm) => {
    saving.value = true

    try {
        const savedConfig = await imageHostService.saveConfig(
            formData,
            props.config?.id
        )

        emit('save', savedConfig)
    } catch (error) {
        console.error('保存配置失败:', error)
        alert('保存配置失败')
    } finally {
        saving.value = false
    }
}
</script>

<style scoped>
/* 内联表单样式 - 统一标准（参考AI配置管理、分类管理） */
.inline-config-form {
    background: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 16px;
    margin-top: 8px;
    transition: all 0.3s ease;
}

.dark .inline-config-form {
    background: #1f1f1f;
    border-color: #303030;
}

.form-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--ant-color-border);
}

.form-title {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: var(--ant-color-text);
}

.dark .form-header {
    border-bottom-color: #303030;
}

.dark .form-title {
    color: #f0f0f0;
}
</style>
