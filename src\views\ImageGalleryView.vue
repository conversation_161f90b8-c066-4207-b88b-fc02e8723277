<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- 悬浮导航条 -->
    <div class="fixed top-20 left-1/2 transform -translate-x-1/2 z-30">
      <nav
        class="flex space-x-1 bg-white dark:bg-gray-800 rounded-full shadow-lg border border-gray-200 dark:border-gray-700 p-1">
        <button v-for="tab in tabs" :key="tab.id" @click="activeTab = tab.id" :class="[
          'px-4 py-2 rounded-full text-sm font-medium transition-all duration-200',
          activeTab === tab.id
            ? 'bg-primary-600 text-white shadow-md'
            : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
        ]">
          <div :class="[tab.icon, 'w-4 h-4 mr-2 inline-block']"></div>
          {{ tab.name }}
        </button>
      </nav>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content-wrapper pt-24 bg-gray-50 dark:bg-gray-900">
      <div class="content-container pb-8">
        <!-- 图床配置提示 -->
        <div class="mb-6">
          <a-alert message="图床配置已迁移到设置页面" description="为了更好的管理体验，图床配置功能已迁移到设置页面。您可以在设置中配置多个图床服务，实现图片自动备份和故障转移。"
            type="info" show-icon closable>
            <template #action>
              <a-button size="small" type="primary" @click="goToSettings">
                前往设置
              </a-button>
            </template>
          </a-alert>
        </div>

        <!-- 标签页内容 -->
        <div class="tab-content">
          <!-- 图片库 -->
          <div v-show="activeTab === 'gallery'">
            <ImageGallery ref="imageGalleryRef" @view-gallery="activeTab = 'gallery'" />
          </div>

          <!-- 上传管理 -->
          <div v-show="activeTab === 'upload'">
            <UploadManagement />
          </div>
        </div>
      </div>
    </div>

    <!-- 悬浮添加按钮 -->
    <button
      class="fixed bottom-8 right-8 w-16 h-16 bg-primary-500 hover:bg-primary-600 text-white rounded-2xl shadow-lg shadow-primary-200/50 hover:shadow-xl hover:shadow-primary-300/50 transition-all duration-200 transform hover:scale-105 active:scale-95 z-30 border-0 outline-none"
      @click="showUploader = true" title="上传图片">
      <div class="i-heroicons-plus w-7 h-7 mx-auto"></div>
    </button>

    <!-- 上传器模态框 -->
    <ImageUploadModal v-if="showUploader" @close="handleUploaderClose" @uploaded="handleUploaded" />

    <!-- 小型进度指示器 -->
    <ProgressIndicator v-if="showProgressIndicator" :title="progressInfo.title" :current="progressInfo.current"
      :total="progressInfo.total" :speed="progressInfo.speed" :status="progressInfo.status"
      @close="showProgressIndicator = false" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
// import { uploadSettingsService } from '@/services/uploadSettingsService' // 已关闭自动链接检测
import type { ImageRecord } from '@/services/imageDataService'
import ImageGallery from '@/components/image/ImageGallery.vue'
import ImageUploadModal from '@/components/image/ImageUploadModal.vue'
import ProgressIndicator from '@/components/ui/ProgressIndicator.vue'
import UploadManagement from '@/components/settings/UploadManagement.vue'

const router = useRouter()

// 响应式数据
const activeTab = ref('gallery')
const showUploader = ref(false)
const showProgressIndicator = ref(false)
const imageGalleryRef = ref()

// 进度信息
const progressInfo = ref({
  title: '上传中...',
  current: 0,
  total: 0,
  speed: '',
  status: ''
})

// 标签页配置 - 移除图床配置和统计分析，这些功能已迁移到设置页面
const tabs = [
  { id: 'gallery', name: '图片库', icon: 'i-heroicons-photo' },
  { id: 'upload', name: '上传管理', icon: 'i-heroicons-arrow-up-tray' }
]

// 方法
const handleUploadSuccess = (results: ImageRecord[]) => {
  showUploader.value = false
  activeTab.value = 'gallery'
}

// 跳转到设置页面的图床管理
const goToSettings = () => {
  router.push('/settings?tab=image-host')
}

// 处理上传器关闭
const handleUploaderClose = () => {
  showUploader.value = false
}

// 处理上传完成
const handleUploaded = async (results: any[]) => {
  // 🔄 不再自动关闭上传窗口，让用户可以继续上传
  // showUploader.value = false
  showProgressIndicator.value = false

  // 更新图片列表
  console.log('上传完成:', results)

  // 刷新图库数据（但不切换标签页）
  if (imageGalleryRef.value && typeof imageGalleryRef.value.refreshImages === 'function') {
    try {
      await imageGalleryRef.value.refreshImages()
      console.log('图库已刷新')
    } catch (error) {
      console.error('刷新图库失败:', error)
    }
  }
}

// 组件挂载时加载数据
onMounted(async () => {
  // 🚫 已关闭自动链接检测功能，避免CORS问题和不必要的后台请求
  // const settings = await uploadSettingsService.getSettings()
  // if (settings.linkCheckEnabled) {
  //   uploadSettingsService.startLinkCheck(settings)
  // }

  // 确保停止任何正在运行的链接检测
  try {
    // 如果需要完全确保停止，可以取消注释下面的代码
    // const { uploadSettingsService } = await import('@/services/uploadSettingsService')
    // uploadSettingsService.stopLinkCheck()
  } catch (error) {
    // 忽略导入错误
  }

  console.log('图库已加载，自动链接检测已关闭')
})
</script>

<style scoped>
.stat-card {
  @apply bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4;
  @apply hover:shadow-md transition-shadow duration-200;
}

.stat-icon {
  @apply w-10 h-10 rounded-lg flex items-center justify-center mb-3;
}

.stat-content {
  @apply space-y-1;
}

.stat-value {
  @apply text-lg font-bold text-gray-900 dark:text-gray-100;
}

.stat-label {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

.btn {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-primary {
  @apply text-white bg-primary-600 hover:bg-primary-700 focus:ring-primary-500;
}

.btn-secondary {
  @apply text-gray-700 bg-gray-100 hover:bg-gray-200 focus:ring-gray-500;
  @apply dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600;
}

.tab-content {
  @apply min-h-[600px];
}

.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4;
}

.modal-container {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700;
}

.modal-title {
  @apply text-lg font-semibold text-gray-900 dark:text-gray-100;
}

.modal-close {
  @apply p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors;
}

.modal-body {
  @apply p-6 overflow-y-auto max-h-[calc(90vh-140px)];
}
</style>
