import type { ThemeMode } from '@/types'

// 主题设置接口
export interface ThemeSettings {
  // 默认主题模式
  defaultMode: ThemeMode
  // 自定义主题色
  customPrimaryColor: string
  // 是否启用自定义主题色
  useCustomColor: boolean
  // 自动切换时间（如果需要的话）
  autoSwitchEnabled: boolean
  lightModeTime: string // 格式: "06:00"
  darkModeTime: string // 格式: "18:00"
}

// 默认主题设置
const defaultThemeSettings: ThemeSettings = {
  defaultMode: 'system',
  customPrimaryColor: '#10b981', // 默认绿色
  useCustomColor: false,
  autoSwitchEnabled: false,
  lightModeTime: '06:00',
  darkModeTime: '18:00',
}

// 预设主题色
export const presetColors = [
  { name: '翠绿色', value: '#10b981', description: '默认主题色' },
  { name: '蓝色', value: '#3b82f6', description: '经典蓝色' },
  { name: '紫色', value: '#8b5cf6', description: '优雅紫色' },
  { name: '粉色', value: '#ec4899', description: '活力粉色' },
  { name: '橙色', value: '#f59e0b', description: '温暖橙色' },
  { name: '红色', value: '#ef4444', description: '热情红色' },
  { name: '青色', value: '#06b6d4', description: '清新青色' },
  { name: '靛蓝色', value: '#6366f1', description: '深邃靛蓝' },
]

class ThemeSettingsService {
  private readonly STORAGE_KEY = 'knowledge-theme-settings'
  private settings: ThemeSettings
  private listeners: Array<(settings: ThemeSettings) => void> = []

  constructor() {
    this.settings = this.loadSettings()
  }

  // 加载设置
  private loadSettings(): ThemeSettings {
    try {
      const saved = localStorage.getItem(this.STORAGE_KEY)
      if (saved) {
        const parsed = JSON.parse(saved)
        return { ...defaultThemeSettings, ...parsed }
      }
    } catch (error) {
      console.error('加载主题设置失败:', error)
    }
    return { ...defaultThemeSettings }
  }

  // 获取当前设置
  getSettings(): ThemeSettings {
    return { ...this.settings }
  }

  // 保存设置
  saveSettings(newSettings: ThemeSettings): void {
    try {
      this.settings = { ...newSettings }
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.settings))

      // 应用主题色
      this.applyCustomColor()

      // 通知所有监听器
      this.listeners.forEach((listener) => listener(this.settings))

      // 触发全局事件
      window.dispatchEvent(
        new CustomEvent('theme-settings-updated', {
          detail: this.settings,
        }),
      )
    } catch (error) {
      console.error('保存主题设置失败:', error)
      throw error
    }
  }

  // 更新部分设置
  updateSettings(updates: Partial<ThemeSettings>): void {
    const newSettings = { ...this.settings, ...updates }
    this.saveSettings(newSettings)
  }

  // 重置为默认设置
  resetToDefaults(): void {
    this.saveSettings({ ...defaultThemeSettings })
  }

  // 应用自定义主题色
  applyCustomColor(): void {
    const root = document.documentElement

    if (this.settings.useCustomColor && this.settings.customPrimaryColor) {
      // 将十六进制颜色转换为RGB
      const hex = this.settings.customPrimaryColor.replace('#', '')
      const r = parseInt(hex.substr(0, 2), 16)
      const g = parseInt(hex.substr(2, 2), 16)
      const b = parseInt(hex.substr(4, 2), 16)

      // 生成主题色阶
      const colorScale = this.generateColorScale(r, g, b)

      // 应用到CSS变量 - 多种格式确保兼容性
      Object.entries(colorScale).forEach(([key, value]) => {
        // 设置标准CSS变量格式
        root.style.setProperty(`--color-primary-${key}`, value)
        // 设置UnoCSS变量格式
        root.style.setProperty(`--un-preset-color-primary-${key}`, value)
        // 设置另一种可能的UnoCSS格式
        root.style.setProperty(`--uno-color-primary-${key}`, value)
        // 直接设置到:root选择器
        root.style.setProperty(`--primary-${key}`, value)
      })

      // 强制更新样式
      document.body.style.display = 'none'
      document.body.offsetHeight // 触发重排
      document.body.style.display = ''

      console.log('应用自定义主题色:', this.settings.customPrimaryColor, colorScale)
    } else {
      // 移除自定义颜色变量，恢复默认
      const keys = ['25', '50', '100', '200', '300', '400', '500', '600', '700', '800', '900']
      keys.forEach((key) => {
        root.style.removeProperty(`--color-primary-${key}`)
        root.style.removeProperty(`--un-preset-color-primary-${key}`)
        root.style.removeProperty(`--uno-color-primary-${key}`)
        root.style.removeProperty(`--primary-${key}`)
      })

      // 强制更新样式
      document.body.style.display = 'none'
      document.body.offsetHeight // 触发重排
      document.body.style.display = ''

      console.log('恢复默认主题色')
    }
  }

  // 生成颜色色阶 - 优化对比度和可访问性
  private generateColorScale(r: number, g: number, b: number): Record<string, string> {
    const scale: Record<string, string> = {}

    // 基础色 (500) - 确保足够的对比度
    scale['500'] = `rgb(${r}, ${g}, ${b})`

    // 生成浅色系 (25-400) - 优化渐变曲线
    const lightConfig = [
      { key: '25', mix: 0.97, minLightness: 0.95 },
      { key: '50', mix: 0.92, minLightness: 0.9 },
      { key: '100', mix: 0.85, minLightness: 0.8 },
      { key: '200', mix: 0.7, minLightness: 0.65 },
      { key: '300', mix: 0.5, minLightness: 0.45 },
      { key: '400', mix: 0.25, minLightness: 0.25 },
    ]

    lightConfig.forEach(({ key, mix, minLightness }) => {
      // 使用更科学的颜色混合算法
      const lightR = Math.round(r + (255 - r) * mix)
      const lightG = Math.round(g + (255 - g) * mix)
      const lightB = Math.round(b + (255 - b) * mix)

      // 确保最小亮度
      const lightness = (lightR * 0.299 + lightG * 0.587 + lightB * 0.114) / 255
      if (lightness < minLightness) {
        const adjustment = minLightness - lightness
        const adjustedR = Math.min(255, Math.round(lightR + adjustment * 255))
        const adjustedG = Math.min(255, Math.round(lightG + adjustment * 255))
        const adjustedB = Math.min(255, Math.round(lightB + adjustment * 255))
        scale[key] = `rgb(${adjustedR}, ${adjustedG}, ${adjustedB})`
      } else {
        scale[key] = `rgb(${lightR}, ${lightG}, ${lightB})`
      }
    })

    // 生成深色系 (600-900) - 确保足够的对比度
    const darkConfig = [
      { key: '600', factor: 0.85, maxLightness: 0.45 },
      { key: '700', factor: 0.7, maxLightness: 0.35 },
      { key: '800', factor: 0.55, maxLightness: 0.25 },
      { key: '900', factor: 0.4, maxLightness: 0.15 },
    ]

    darkConfig.forEach(({ key, factor, maxLightness }) => {
      let darkR = Math.round(r * factor)
      let darkG = Math.round(g * factor)
      let darkB = Math.round(b * factor)

      // 确保最大亮度限制
      const lightness = (darkR * 0.299 + darkG * 0.587 + darkB * 0.114) / 255
      if (lightness > maxLightness) {
        const adjustment = lightness - maxLightness
        darkR = Math.max(0, Math.round(darkR - adjustment * 255))
        darkG = Math.max(0, Math.round(darkG - adjustment * 255))
        darkB = Math.max(0, Math.round(darkB - adjustment * 255))
      }

      scale[key] = `rgb(${darkR}, ${darkG}, ${darkB})`
    })

    return scale
  }

  // 计算颜色对比度
  private calculateContrast(color1: string, color2: string): number {
    const getLuminance = (r: number, g: number, b: number): number => {
      const [rs, gs, bs] = [r, g, b].map((c) => {
        c = c / 255
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
      })
      return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs
    }

    // 简化的颜色解析（假设输入是rgb格式）
    const parseRgb = (rgb: string): [number, number, number] => {
      const match = rgb.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/)
      return match ? [parseInt(match[1]), parseInt(match[2]), parseInt(match[3])] : [0, 0, 0]
    }

    const [r1, g1, b1] = parseRgb(color1)
    const [r2, g2, b2] = parseRgb(color2)

    const lum1 = getLuminance(r1, g1, b1)
    const lum2 = getLuminance(r2, g2, b2)

    const brightest = Math.max(lum1, lum2)
    const darkest = Math.min(lum1, lum2)

    return (brightest + 0.05) / (darkest + 0.05)
  }

  // 添加设置变化监听器
  addListener(listener: (settings: ThemeSettings) => void): () => void {
    this.listeners.push(listener)

    // 返回取消监听的函数
    return () => {
      const index = this.listeners.indexOf(listener)
      if (index > -1) {
        this.listeners.splice(index, 1)
      }
    }
  }

  // 获取预设颜色
  getPresetColors() {
    return presetColors
  }

  // 验证颜色格式
  isValidColor(color: string): boolean {
    return /^#[0-9A-F]{6}$/i.test(color)
  }
}

// 创建单例实例
export const themeSettingsService = new ThemeSettingsService()

// 导出类型
export type { ThemeSettings }
