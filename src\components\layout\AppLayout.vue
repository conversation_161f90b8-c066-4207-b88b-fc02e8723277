<template>
  <!-- 应用根容器 -->
  <div class="app-root">
    <!-- 顶部导航栏 - 独立占满页面宽度 -->
    <!-- Ant Design风格的整合版Header -->
    <div class="app-header-antd">
      <div class="header-content">
        <!-- Logo区域 -->
        <div class="header-logo">
          <div class="logo-link" @click="navigateTo('/')">
            <div class="logo-icon">
              📚
            </div>
            <span class="logo-text">KnowlEdge</span>
          </div>
        </div>

        <!-- 主导航菜单 -->
        <div class="header-nav">
          <div class="nav-menu">
            <button v-for="item in menuItems" :key="item.key" @click="navigateTo(item.key)"
              :class="['nav-menu-item', { 'nav-menu-item-selected': route.path === item.key }]">
              <component :is="getIconComponent(item.icon)" class="nav-item-icon" />
              <span class="nav-item-text">{{ item.label }}</span>
            </button>
          </div>
        </div>

        <!-- 搜索框 -->
        <div class="header-search">
          <input v-model="searchQuery" placeholder="搜索知识库..." class="search-input" @keyup.enter="handleSearch" />
        </div>

        <!-- 右侧操作区 -->
        <div class="header-actions">
          <!-- AI对话按钮 -->
          <button class="action-btn action-btn-ai" title="AI对话" @click="handleAiChat">
            <MessageOutlined class="action-icon" />
            <span class="action-tooltip">AI对话</span>
          </button>

          <!-- 主题切换按钮 -->
          <button class="action-btn action-btn-theme" :title="isDark ? '切换到明亮模式' : '切换到暗色模式'"
            @click="handleThemeToggle">
            <component :is="themeIcon" class="action-icon" />
            <span class="action-tooltip">{{ isDark ? '明亮模式' : '暗色模式' }}</span>
          </button>

          <!-- 设置按钮 - 悬浮显示菜单，点击跳转设置页面 -->
          <div class="settings-container" @mouseenter="showSettingsHover = true"
            @mouseleave="showSettingsHover = false">
            <button class="action-btn action-btn-settings" title="设置" @click="handleSettingsClick">
              <MoreOutlined class="action-icon" />
              <span class="action-tooltip">设置</span>
            </button>

            <!-- 悬浮菜单 -->
            <div v-if="showSettingsHover" class="hover-menu" @mouseenter="showSettingsHover = true"
              @mouseleave="showSettingsHover = false">
              <div class="hover-menu-section">
                <div class="hover-menu-title">快速操作</div>
                <button class="hover-menu-item" @click="handleQuickAction('category')">
                  <FolderOutlined class="hover-menu-icon" />
                  <span>分类管理</span>
                </button>
                <button class="hover-menu-item" @click="handleQuickAction('tag')">
                  <TagOutlined class="hover-menu-icon" />
                  <span>标签管理</span>
                </button>
                <button class="hover-menu-item" @click="handleQuickAction('search-engine')">
                  <SearchOutlined class="hover-menu-icon" />
                  <span>搜索引擎设置</span>
                </button>
              </div>

              <div class="hover-menu-divider"></div>

              <div class="hover-menu-section">
                <div class="hover-menu-title">数据管理</div>
                <button class="hover-menu-item" @click="handleQuickAction('import')">
                  <ImportOutlined class="hover-menu-icon" />
                  <span>导入数据</span>
                </button>
                <button class="hover-menu-item" @click="handleQuickAction('export')">
                  <ExportOutlined class="hover-menu-icon" />
                  <span>导出数据</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主应用容器 - 实现1440px最大宽度和居中布局 -->
    <div class="app-container">
      <!-- 主要内容区域 -->
      <main class="app-main">
        <router-view />
      </main>
    </div>

    <!-- 全局加载遮罩 -->
    <div v-if="globalLoading" class="global-loading">
      <a-spin size="large" tip="加载中..." />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTheme } from '@/composables/useTheme'
import {
  DatabaseOutlined,
  PictureOutlined,
  BgColorsOutlined,
  ExperimentOutlined,
  MessageOutlined,
  MoreOutlined,
  BulbOutlined,
  EyeInvisibleOutlined,
  FolderOutlined,
  TagOutlined,
  SearchOutlined,
  ImportOutlined,
  ExportOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue'

const route = useRoute()
const router = useRouter()

// 全局加载状态
const globalLoading = ref(false)

// Header相关状态
const searchQuery = ref('')
const showSettingsMenu = ref(false)
const showSettingsHover = ref(false)

// 使用统一的主题系统
const { isDark, toggleDarkMode } = useTheme()

// 主题图标计算属性
const themeIcon = computed(() => {
  return isDark.value ? BulbOutlined : EyeInvisibleOutlined
})

// 图标组件映射
const iconComponents = {
  DatabaseOutlined,
  PictureOutlined,
  BgColorsOutlined,
  ExperimentOutlined,
  MessageOutlined,
  MoreOutlined,
  BulbOutlined,
  FolderOutlined,
  TagOutlined,
  SearchOutlined,
  ImportOutlined,
  ExportOutlined,
  InfoCircleOutlined
}

// 获取图标组件的方法
const getIconComponent = (iconName: string) => {
  return iconComponents[iconName as keyof typeof iconComponents] || DatabaseOutlined
}

// 导航菜单项 - 移除首页，使用Ant Design Icons
const menuItems = computed(() => [
  {
    key: '/knowledge',
    icon: 'DatabaseOutlined',
    label: '知识库'
  },
  {
    key: '/image-gallery',
    icon: 'PictureOutlined',
    label: '图床管理'
  },
  {
    key: '/component-showcase',
    icon: 'BgColorsOutlined',
    label: '样式展示'
  },
  {
    key: '/simple-test',
    icon: 'ExperimentOutlined',
    label: '简单测试'
  }
])

// 核心导航方法 - 使用已验证可行的方式
const navigateTo = (path: string) => {
  console.log('AppLayout: 导航到', path)
  console.log('AppLayout: 当前路由', route.path)

  if (route.path === path) {
    console.log('AppLayout: 已在目标路由')
    return
  }

  // 使用与SimpleTestView相同的路由跳转方式
  router.push(path).then(() => {
    console.log('AppLayout: 路由跳转成功到', path)
  }).catch(err => {
    console.error('AppLayout: 路由跳转失败', err)
  })
}

// 处理搜索
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    console.log('搜索:', searchQuery.value)
    navigateTo(`/knowledge?search=${encodeURIComponent(searchQuery.value)}`)
  }
}

// 处理AI对话 - 跳转到AI对话页面
const handleAiChat = () => {
  console.log('跳转到AI对话页面')
  // 这里可以跳转到AI对话相关页面
  navigateTo('/ai-chat')
}

// 处理主题切换 - 使用统一的主题系统
const handleThemeToggle = () => {
  toggleDarkMode()
  console.log('切换主题到:', isDark.value ? 'dark' : 'light')
}

// 处理设置按钮点击 - 跳转到设置页面
const handleSettingsClick = () => {
  console.log('跳转到设置页面')
  navigateTo('/settings')
  showSettingsHover.value = false
}

// 处理快速操作菜单项
const handleQuickAction = (action: string) => {
  console.log('快速操作:', action)

  switch (action) {
    case 'category':
      console.log('打开分类管理')
      // 可以打开分类管理模态框或跳转页面
      break
    case 'tag':
      console.log('打开标签管理')
      // 可以打开标签管理模态框或跳转页面
      break
    case 'search-engine':
      console.log('打开搜索引擎设置')
      // 可以打开搜索引擎设置模态框或跳转页面
      break
    case 'import':
      console.log('导入数据功能')
      // 可以打开导入数据对话框
      break
    case 'export':
      console.log('导出数据功能')
      // 可以打开导出数据对话框
      break
  }

  // 关闭悬浮菜单
  showSettingsHover.value = false
}

// 切换设置菜单显示
const toggleSettingsMenu = () => {
  showSettingsMenu.value = !showSettingsMenu.value
}

// 处理设置菜单项点击
const handleSettingsAction = (action: string) => {
  console.log('设置菜单点击:', action)

  switch (action) {
    case 'category':
      console.log('打开分类管理')
      break
    case 'tag':
      console.log('打开标签管理')
      break
    case 'search-engine':
      console.log('打开搜索引擎设置')
      break
    case 'import':
      console.log('导入数据功能')
      break
    case 'export':
      console.log('导出数据功能')
      break
    case 'about':
      console.log('关于页面')
      break
  }

  // 关闭下拉菜单
  showSettingsMenu.value = false
}

// 点击外部关闭下拉菜单
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  if (!target.closest('.settings-dropdown')) {
    showSettingsMenu.value = false
  }
}

// 生命周期钩子
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 监听路由变化进行调试
watch(
  () => route.path,
  (newPath, oldPath) => {
    console.log('AppLayout: 路由从', oldPath, '变化到', newPath)
  },
  { immediate: true }
)

// 暴露给外部使用的方法
defineExpose({
  showGlobalLoading: () => { globalLoading.value = true },
  hideGlobalLoading: () => { globalLoading.value = false }
})
</script>

<style scoped>
/* 应用根容器样式 */
.app-root {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: var(--ant-color-bg-layout, #f5f5f5);
  width: 100%;
}

/* 暗色模式下的根容器背景 */
.dark .app-root {
  background: var(--color-page-background, #141414) !important;
}

/* 主应用容器样式 - 限制内容宽度 */
.app-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  max-width: 1440px;
  margin: 0 auto;
  width: 100%;
  background: inherit;
  /* 继承父容器的背景色 */
}

/* 主内容区域样式 */
.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 0;
  /* 确保flex子项能正确收缩 */
  /* 确保主内容区域不产生额外滚动条 */
  overflow: visible;
}

/* 页面过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s ease;
}

.slide-left-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.slide-left-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s ease;
}

.slide-right-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.slide-right-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 全局加载样式 */
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.dark .global-loading {
  background: rgba(0, 0, 0, 0.8);
}

/* Ant Design风格的Header样式 */
.app-header-antd {
  position: sticky;
  top: 0;
  z-index: 1000;
  height: 48px;
  background: var(--ant-color-bg-container, #ffffff);
  border-bottom: 1px solid var(--ant-color-border, #d9d9d9);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(8px);
}

.header-content {
  display: flex;
  align-items: center;
  height: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 20px;
  gap: 20px;
}

/* Logo区域 */
.header-logo {
  flex-shrink: 0;
  min-width: 140px;
}

.logo-link {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  height: 48px;
}

.logo-link:hover {
  transform: scale(1.02);
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.logo-text {
  font-size: 20px;
  font-weight: 700;
  color: #1890ff;
  white-space: nowrap;
  /* 渐变色文字效果 */
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 确保在不支持渐变色文字的浏览器中显示正常颜色 */
@supports not (-webkit-background-clip: text) {
  .logo-text {
    color: #1890ff !important;
    background: none !important;
  }
}

/* 导航菜单 - 原子化设计 */
.header-nav {
  flex-shrink: 0;
  height: 48px;
  display: flex;
  align-items: center;
}

.nav-menu {
  display: flex;
  gap: 4px;
  height: 48px;
  align-items: center;
}

.nav-menu-item {
  /* 原子化基础样式 */
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  height: 40px;
  padding: 0 16px;
  border: none;
  background: transparent;
  color: var(--nav-text-color, var(--ant-color-text, #000000d9));
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;

  /* 现代化过渡效果 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);

  /* 微妙的边框效果 */
  border: 1px solid transparent;

  /* 文字渲染优化 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 悬停状态 - 原子化交互 */
.nav-menu-item:hover {
  background: var(--nav-hover-bg, var(--ant-color-fill-tertiary, #f5f5f5));
  border-color: var(--nav-hover-border, var(--ant-color-border-secondary, #f0f0f0));
  transform: translateY(-1px);
  box-shadow: 0 2px 8px var(--nav-hover-shadow, rgba(0, 0, 0, 0.06));
}

/* 选中状态 - 原子化激活样式 */
.nav-menu-item-selected {
  background: var(--nav-active-bg, var(--ant-color-primary-bg, #e6f7ff));
  color: var(--nav-active-text, var(--ant-color-primary, #1890ff));
  border-color: var(--nav-active-border, var(--ant-color-primary-border, #91d5ff));
  font-weight: 600;

  /* 选中状态的微妙阴影 */
  box-shadow: 0 1px 4px var(--nav-active-shadow, rgba(24, 144, 255, 0.15));
}

/* 选中状态悬停 */
.nav-menu-item-selected:hover {
  background: var(--nav-active-hover-bg, var(--ant-color-primary-bg-hover, #bae7ff));
  transform: translateY(-1px);
  box-shadow: 0 3px 12px var(--nav-active-hover-shadow, rgba(24, 144, 255, 0.25));
}

/* 按下状态 */
.nav-menu-item:active {
  transform: translateY(0);
  transition-duration: 0.1s;
}

/* 图标样式 - 原子化图标设计 */
.nav-item-icon {
  font-size: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--nav-icon-color, var(--ant-color-text-secondary, #00000073));
}

.nav-menu-item:hover .nav-item-icon {
  color: var(--nav-icon-hover-color, var(--ant-color-text, #000000d9));
  transform: scale(1.1);
}

.nav-menu-item-selected .nav-item-icon {
  color: var(--nav-active-text, var(--ant-color-primary, #1890ff));
  transform: scale(1.05);
}

/* 文字样式 - 原子化文字设计 */
.nav-item-text {
  font-size: 14px;
  font-weight: inherit;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 搜索框 */
.header-search {
  flex: 1;
  max-width: 480px;
  min-width: 200px;
  margin: 0 20px;
  height: 32px;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  height: 32px;
  padding: 4px 12px;
  border: 1px solid var(--ant-color-border, #d9d9d9);
  border-radius: 16px;
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
}

.search-input:focus {
  border-color: var(--ant-color-primary, #1890ff);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 右侧操作区 - 原子化设计 */
.header-actions {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-shrink: 0;
  height: 48px;
  min-width: 200px;
  justify-content: flex-end;
}

/* 操作按钮基础样式 - 原子化组件 */
.action-btn {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  border: 1px solid transparent;
  background: var(--action-btn-bg, transparent);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  /* 现代化过渡效果 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0) scale(1);

  /* 文字渲染优化 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 操作按钮图标样式 */
.action-icon {
  font-size: 18px;
  color: var(--action-icon-color, var(--ant-color-text-secondary, #00000073));
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 工具提示样式 */
.action-tooltip {
  position: absolute;
  bottom: -32px;
  left: 50%;
  transform: translateX(-50%) scale(0.8);
  background: var(--ant-color-bg-elevated, #ffffff);
  color: var(--ant-color-text, #000000d9);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--ant-color-border, #d9d9d9);
  opacity: 0;
  pointer-events: none;
  z-index: 1002;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 基础悬停状态 */
.action-btn:hover {
  background: var(--action-btn-hover-bg, var(--ant-color-fill-tertiary, #f5f5f5));
  border-color: var(--action-btn-hover-border, var(--ant-color-border-secondary, #f0f0f0));
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px var(--action-btn-hover-shadow, rgba(0, 0, 0, 0.1));
}

.action-btn:hover .action-icon {
  color: var(--action-icon-hover-color, var(--ant-color-text, #000000d9));
  transform: scale(1.1);
}

.action-btn:hover .action-tooltip {
  opacity: 1;
  transform: translateX(-50%) scale(1);
}

/* 按下状态 */
.action-btn:active {
  transform: translateY(-1px) scale(1.02);
  transition-duration: 0.1s;
}

/* AI对话按钮特殊样式 */
.action-btn-ai {
  --action-btn-bg: var(--ant-color-primary-bg, #e6f7ff);
  --action-btn-hover-bg: var(--ant-color-primary-bg-hover, #bae7ff);
  --action-icon-color: var(--ant-color-primary, #1890ff);
  --action-icon-hover-color: var(--ant-color-primary-hover, #40a9ff);
}

/* 主题切换按钮特殊样式 */
.action-btn-theme {
  --action-btn-bg: var(--ant-color-warning-bg, #fff7e6);
  --action-btn-hover-bg: var(--ant-color-warning-bg-hover, #ffe7ba);
  --action-icon-color: var(--ant-color-warning, #fa8c16);
  --action-icon-hover-color: var(--ant-color-warning-hover, #ffa940);
}

/* 设置按钮特殊样式 */
.action-btn-settings {
  --action-btn-bg: var(--ant-color-fill-quaternary, #fafafa);
  --action-btn-hover-bg: var(--ant-color-fill-tertiary, #f5f5f5);
  --action-icon-color: var(--ant-color-text-tertiary, #00000040);
  --action-icon-hover-color: var(--ant-color-text-secondary, #00000073);
}

/* 设置容器和悬浮菜单 - 原子化设计 */
.settings-container {
  position: relative;
}

/* 悬浮菜单样式 - 现代化设计 */
.hover-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  min-width: 220px;
  background: var(--hover-menu-bg, var(--ant-color-bg-elevated, #ffffff));
  border: 1px solid var(--hover-menu-border, var(--ant-color-border, #d9d9d9));
  border-radius: 12px;
  box-shadow: 0 8px 24px var(--hover-menu-shadow, rgba(0, 0, 0, 0.12));
  z-index: 1001;
  padding: 8px;
  backdrop-filter: blur(12px);

  /* 现代化动画效果 */
  animation: hoverMenuFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: top right;
}

@keyframes hoverMenuFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-4px);
  }

  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 悬浮菜单分组 */
.hover-menu-section {
  padding: 4px 0;
}

.hover-menu-title {
  font-size: 11px;
  color: var(--hover-menu-title-color, var(--ant-color-text-tertiary, #00000040));
  font-weight: 600;
  padding: 8px 12px 6px;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  margin-bottom: 2px;
}

/* 悬浮菜单项 */
.hover-menu-item {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  padding: 10px 12px;
  border: none;
  background: transparent;
  color: var(--hover-menu-item-color, var(--ant-color-text, #000000d9));
  font-size: 14px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;
  font-weight: 500;
}

.hover-menu-item:hover {
  background: var(--hover-menu-item-hover-bg, var(--ant-color-primary-bg, #e6f7ff));
  color: var(--hover-menu-item-hover-color, var(--ant-color-primary, #1890ff));
  transform: translateX(2px);
}

.hover-menu-item:active {
  transform: translateX(1px);
  transition-duration: 0.1s;
}

/* 悬浮菜单图标 */
.hover-menu-icon {
  font-size: 16px;
  color: var(--hover-menu-icon-color, var(--ant-color-text-secondary, #00000073));
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
}

.hover-menu-item:hover .hover-menu-icon {
  color: var(--hover-menu-icon-hover-color, var(--ant-color-primary, #1890ff));
  transform: scale(1.1);
}

/* 悬浮菜单分割线 */
.hover-menu-divider {
  height: 1px;
  background: var(--hover-menu-divider-color, var(--ant-color-border-secondary, #f0f0f0));
  margin: 8px 4px;
  border-radius: 1px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    padding: 0 16px;
    gap: 16px;
  }

  .header-search {
    max-width: 300px;
    margin: 0 16px;
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: 0 12px;
    gap: 12px;
  }

  .header-logo {
    min-width: 40px;
  }

  .logo-text {
    display: none;
  }

  .header-search {
    max-width: 200px;
    margin: 0 12px;
    min-width: 150px;
  }

  .header-actions {
    gap: 6px;
    min-width: 150px;
  }

  .action-btn {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .nav-item-text {
    display: none;
  }

  .nav-menu-item {
    padding: 0 8px;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 8px;
    gap: 8px;
  }

  .nav-menu {
    gap: 1px;
  }

  .nav-menu-item {
    padding: 0 6px;
  }

  .header-search {
    max-width: 120px;
    margin: 0 8px;
    min-width: 100px;
  }

  .header-actions {
    gap: 4px;
    min-width: 120px;
  }

  .action-btn {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
}

/* 暗色主题适配 - 完全对标 Ant Design 官网标准 */
.dark {
  /* 导航按钮暗色主题变量 - 基于 Ant Design 标准 */
  --nav-text-color: rgba(255, 255, 255, 0.85);
  --nav-hover-bg: #1f1f1f;
  --nav-hover-border: #424242;
  --nav-hover-shadow: rgba(255, 255, 255, 0.08);
  --nav-active-bg: #111b26;
  --nav-active-text: #1677ff;
  --nav-active-border: #1677ff;
  --nav-active-shadow: rgba(22, 119, 255, 0.15);
  --nav-active-hover-bg: #0f1419;
  --nav-active-hover-shadow: rgba(22, 119, 255, 0.25);
  --nav-icon-color: rgba(255, 255, 255, 0.45);
  --nav-icon-hover-color: rgba(255, 255, 255, 0.85);

  /* 操作按钮暗色主题变量 */
  --action-btn-bg: transparent;
  --action-btn-hover-bg: #1f1f1f;
  --action-btn-hover-border: #424242;
  --action-btn-hover-shadow: rgba(255, 255, 255, 0.08);
  --action-icon-color: rgba(255, 255, 255, 0.45);
  --action-icon-hover-color: rgba(255, 255, 255, 0.85);

  /* 悬浮菜单暗色主题变量 */
  --hover-menu-bg: #141414;
  --hover-menu-border: #424242;
  --hover-menu-shadow: rgba(0, 0, 0, 0.45);
  --hover-menu-title-color: rgba(255, 255, 255, 0.25);
  --hover-menu-item-color: rgba(255, 255, 255, 0.85);
  --hover-menu-item-hover-bg: #111b26;
  --hover-menu-item-hover-color: #1677ff;
  --hover-menu-icon-color: rgba(255, 255, 255, 0.45);
  --hover-menu-icon-hover-color: #1677ff;
  --hover-menu-divider-color: #424242;
}

.dark .app-header-antd {
  background: #141414;
  border-bottom-color: #424242;
}

.dark .logo-text {
  color: var(--ant-color-primary-active, #40a9ff);
}

.dark .search-input {
  background: var(--ant-color-bg-container-dark, #141414);
  border-color: var(--ant-color-border-dark, #303030);
  color: var(--ant-color-text-dark, #ffffffd9);
}

.dark .search-input:focus {
  border-color: var(--ant-color-primary-active, #40a9ff);
  box-shadow: 0 0 0 2px rgba(64, 169, 255, 0.2);
}

/* 暗色主题下的特殊按钮样式 */
.dark .action-btn-ai {
  --action-btn-bg: #111b26;
  --action-btn-hover-bg: #0f1419;
  --action-icon-color: #40a9ff;
  --action-icon-hover-color: #69c0ff;
}

.dark .action-btn-theme {
  --action-btn-bg: #2b1d0f;
  --action-btn-hover-bg: #3b2a1a;
  --action-icon-color: #ffa940;
  --action-icon-hover-color: #ffc069;
}

.dark .action-btn-settings {
  --action-btn-bg: #1a1a1a;
  --action-btn-hover-bg: #262626;
  --action-icon-color: #ffffff40;
  --action-icon-hover-color: #ffffff73;
}

.dark .action-tooltip {
  background: var(--ant-color-bg-elevated-dark, #262626);
  color: var(--ant-color-text-dark, #ffffffd9);
  border-color: var(--ant-color-border-dark, #434343);
}

/* 暗色主题下拉菜单 */
.dark .dropdown-menu {
  background: var(--ant-color-bg-container-dark, #141414);
  border-color: var(--ant-color-border-dark, #303030);
}

.dark .dropdown-title {
  color: var(--ant-color-text-secondary-dark, #ffffff73);
}

.dark .dropdown-item {
  color: var(--ant-color-text-dark, #ffffffd9);
}

.dark .dropdown-item:hover {
  background: var(--ant-color-fill-tertiary-dark, #262626);
}

.dark .dropdown-icon {
  color: var(--ant-color-text-secondary-dark, #ffffff73);
}

.dark .dropdown-divider {
  background: var(--ant-color-border-dark, #303030);
}
</style>
