<template>
  <div class="image-analytics">
    <!-- 概览统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon bg-blue-100 dark:bg-blue-900/30">
          <div class="i-heroicons-photo w-6 h-6 text-blue-600 dark:text-blue-400"></div>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.total }}</div>
          <div class="stat-label">总图片数</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon bg-green-100 dark:bg-green-900/30">
          <div class="i-heroicons-check-circle w-6 h-6 text-green-600 dark:text-green-400"></div>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.active }}</div>
          <div class="stat-label">正常图片</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon bg-yellow-100 dark:bg-yellow-900/30">
          <div class="i-heroicons-clock w-6 h-6 text-yellow-600 dark:text-yellow-400"></div>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.expiring }}</div>
          <div class="stat-label">即将过期</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon bg-red-100 dark:bg-red-900/30">
          <div class="i-heroicons-x-circle w-6 h-6 text-red-600 dark:text-red-400"></div>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.failed }}</div>
          <div class="stat-label">失效图片</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon bg-purple-100 dark:bg-purple-900/30">
          <div class="i-heroicons-server w-6 h-6 text-purple-600 dark:text-purple-400"></div>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatFileSize(statistics.totalSize) }}</div>
          <div class="stat-label">总存储量</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon bg-indigo-100 dark:bg-indigo-900/30">
          <div class="i-heroicons-cloud w-6 h-6 text-indigo-600 dark:text-indigo-400"></div>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ hostStats.length }}</div>
          <div class="stat-label">配置图床</div>
        </div>
      </div>
    </div>

    <!-- 图床使用情况 -->
    <div class="analytics-section">
      <h3 class="section-title">图床使用情况</h3>
      <div class="host-stats-grid">
        <div 
          v-for="host in hostStats" 
          :key="host.hostName"
          class="host-stat-card"
        >
          <div class="host-header">
            <h4 class="host-name">{{ host.hostName }}</h4>
            <span 
              :class="[
                'host-status',
                host.isEnabled ? 'status-enabled' : 'status-disabled'
              ]"
            >
              {{ host.isEnabled ? '启用' : '禁用' }}
            </span>
          </div>
          
          <div class="host-metrics">
            <div class="metric-item">
              <span class="metric-label">图片数量</span>
              <span class="metric-value">{{ host.imageCount }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">成功率</span>
              <span class="metric-value">{{ host.successRate }}%</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">存储量</span>
              <span class="metric-value">{{ formatFileSize(host.totalSize) }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">优先级</span>
              <span class="metric-value">{{ host.priority }}</span>
            </div>
          </div>
          
          <div class="host-progress">
            <div class="progress-bar">
              <div 
                class="progress-fill"
                :style="{ width: host.successRate + '%' }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="analytics-section">
      <h3 class="section-title">最近活动</h3>
      <div class="activity-list">
        <div 
          v-for="activity in recentActivities" 
          :key="activity.id"
          class="activity-item"
        >
          <div class="activity-icon">
            <div 
              :class="[
                'w-4 h-4',
                getActivityIcon(activity.type)
              ]"
            ></div>
          </div>
          <div class="activity-content">
            <div class="activity-title">{{ activity.title }}</div>
            <div class="activity-time">{{ formatDate(activity.time) }}</div>
          </div>
          <div 
            :class="[
              'activity-status',
              getActivityStatusClass(activity.status)
            ]"
          >
            {{ getActivityStatusText(activity.status) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 存储趋势 -->
    <div class="analytics-section">
      <h3 class="section-title">存储趋势</h3>
      <div class="trend-chart">
        <div class="chart-placeholder">
          <div class="i-heroicons-chart-bar w-12 h-12 text-gray-400 mb-4"></div>
          <p class="text-gray-500 dark:text-gray-400">
            图表功能开发中...
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { imageUploadService } from '@/services/imageUploadService'
import { imageHostService } from '@/services/imageHostService'
import { imageLifecycleService } from '@/services/imageLifecycleService'

// 响应式数据
const statistics = ref({
  total: 0,
  active: 0,
  expired: 0,
  failed: 0,
  expiring: 0,
  totalSize: 0
})

const hostStats = ref<Array<{
  hostName: string
  hostProvider: string
  isEnabled: boolean
  imageCount: number
  successRate: number
  totalSize: number
  priority: number
}>>([])

const recentActivities = ref<Array<{
  id: string
  type: 'upload' | 'check' | 'expire' | 'fail'
  title: string
  time: Date
  status: 'success' | 'warning' | 'error'
}>>([])

// 方法
const loadStatistics = async () => {
  try {
    statistics.value = await imageLifecycleService.getStatistics()
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

const loadHostStats = async () => {
  try {
    const configs = await imageHostService.getAllConfigs()
    const images = await imageUploadService.getAllImageRecords()
    
    hostStats.value = configs.map(config => {
      const hostImages = images.filter(image => 
        image.backups.some(backup => backup.hostProvider === config.provider)
      )
      
      const hostBackups = images.flatMap(image => 
        image.backups.filter(backup => backup.hostProvider === config.provider)
      )
      
      const successfulBackups = hostBackups.filter(backup => backup.status === 'active')
      const successRate = hostBackups.length > 0 
        ? Math.round((successfulBackups.length / hostBackups.length) * 100)
        : 0
      
      const totalSize = hostImages.reduce((sum, image) => sum + image.size, 0)
      
      return {
        hostName: config.name,
        hostProvider: config.provider,
        isEnabled: config.enabled,
        imageCount: hostImages.length,
        successRate,
        totalSize,
        priority: config.priority
      }
    })
  } catch (error) {
    console.error('加载图床统计失败:', error)
  }
}

const loadRecentActivities = async () => {
  try {
    const images = await imageUploadService.getAllImageRecords()
    
    // 模拟最近活动数据
    const activities = images
      .slice(0, 10)
      .map(image => ({
        id: image.id,
        type: 'upload' as const,
        title: `上传图片: ${image.originalName}`,
        time: image.uploadTime,
        status: image.status === 'active' ? 'success' as const : 'error' as const
      }))
    
    recentActivities.value = activities.sort((a, b) => b.time.getTime() - a.time.getTime())
  } catch (error) {
    console.error('加载最近活动失败:', error)
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const getActivityIcon = (type: string): string => {
  const iconMap = {
    upload: 'i-heroicons-arrow-up-tray text-blue-500',
    check: 'i-heroicons-magnifying-glass text-green-500',
    expire: 'i-heroicons-clock text-yellow-500',
    fail: 'i-heroicons-x-circle text-red-500'
  }
  return iconMap[type as keyof typeof iconMap] || 'i-heroicons-information-circle text-gray-500'
}

const getActivityStatusClass = (status: string): string => {
  const classMap = {
    success: 'text-green-600 dark:text-green-400',
    warning: 'text-yellow-600 dark:text-yellow-400',
    error: 'text-red-600 dark:text-red-400'
  }
  return classMap[status as keyof typeof classMap] || 'text-gray-600 dark:text-gray-400'
}

const getActivityStatusText = (status: string): string => {
  const textMap = {
    success: '成功',
    warning: '警告',
    error: '失败'
  }
  return textMap[status as keyof typeof textMap] || '未知'
}

// 组件挂载时加载数据
onMounted(() => {
  loadStatistics()
  loadHostStats()
  loadRecentActivities()
})
</script>

<style scoped>
.image-analytics {
  @apply space-y-8;
}

.stats-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6;
}

.stat-card {
  @apply bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6;
  @apply hover:shadow-md transition-shadow duration-200;
}

.stat-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center mb-4;
}

.stat-content {
  @apply space-y-1;
}

.stat-value {
  @apply text-2xl font-bold text-gray-900 dark:text-gray-100;
}

.stat-label {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.analytics-section {
  @apply bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6;
}

.section-title {
  @apply text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6;
}

.host-stats-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.host-stat-card {
  @apply bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 space-y-4;
}

.host-header {
  @apply flex items-center justify-between;
}

.host-name {
  @apply font-medium text-gray-900 dark:text-gray-100;
}

.host-status {
  @apply px-2 py-1 text-xs font-medium rounded;
}

.status-enabled {
  @apply bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300;
}

.status-disabled {
  @apply bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300;
}

.host-metrics {
  @apply grid grid-cols-2 gap-3;
}

.metric-item {
  @apply space-y-1;
}

.metric-label {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

.metric-value {
  @apply text-sm font-medium text-gray-900 dark:text-gray-100;
}

.host-progress {
  @apply space-y-2;
}

.progress-bar {
  @apply w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 overflow-hidden;
}

.progress-fill {
  @apply h-full bg-primary-600 transition-all duration-300;
}

.activity-list {
  @apply space-y-4;
}

.activity-item {
  @apply flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg;
}

.activity-icon {
  @apply flex-shrink-0 w-8 h-8 bg-white dark:bg-gray-800 rounded-lg flex items-center justify-center;
}

.activity-content {
  @apply flex-1 min-w-0;
}

.activity-title {
  @apply font-medium text-gray-900 dark:text-gray-100 truncate;
}

.activity-time {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.activity-status {
  @apply text-sm font-medium;
}

.trend-chart {
  @apply h-64 bg-gray-50 dark:bg-gray-700/50 rounded-lg flex items-center justify-center;
}

.chart-placeholder {
  @apply text-center;
}
</style>
