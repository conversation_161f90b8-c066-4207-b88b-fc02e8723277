<template>
  <div class="flex items-center justify-between px-4 py-3 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 sm:px-6">
    <!-- 移动端简化版 -->
    <div class="flex justify-between flex-1 sm:hidden">
      <button
        @click="goToPrevious"
        :disabled="currentPage <= 1"
        class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        上一页
      </button>
      <button
        @click="goToNext"
        :disabled="currentPage >= totalPages"
        class="relative ml-3 inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        下一页
      </button>
    </div>

    <!-- 桌面端完整版 -->
    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
      <!-- 信息显示 -->
      <div>
        <p class="text-sm text-gray-700 dark:text-gray-300">
          显示第
          <span class="font-medium">{{ startItem }}</span>
          到
          <span class="font-medium">{{ endItem }}</span>
          项，共
          <span class="font-medium">{{ total }}</span>
          项
        </p>
      </div>

      <!-- 页码导航 -->
      <div class="flex items-center space-x-2">
        <!-- 上一页 -->
        <button
          @click="goToPrevious"
          :disabled="currentPage <= 1"
          class="relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-l-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <div class="i-heroicons-chevron-left w-5 h-5"></div>
        </button>

        <!-- 页码数字 -->
        <div class="flex items-center space-x-1">
          <!-- 第一页 -->
          <button
            v-if="showFirstPage"
            @click="goToPage(1)"
            :class="[
              'relative inline-flex items-center px-4 py-2 text-sm font-medium border',
              currentPage === 1
                ? 'z-10 bg-primary-50 dark:bg-primary-900/20 border-primary-500 text-primary-600 dark:text-primary-400'
                : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
            ]"
          >
            1
          </button>

          <!-- 左侧省略号 -->
          <span v-if="showLeftEllipsis" class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600">
            ...
          </span>

          <!-- 中间页码 -->
          <button
            v-for="page in middlePages"
            :key="page"
            @click="goToPage(page)"
            :class="[
              'relative inline-flex items-center px-4 py-2 text-sm font-medium border',
              currentPage === page
                ? 'z-10 bg-primary-50 dark:bg-primary-900/20 border-primary-500 text-primary-600 dark:text-primary-400'
                : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
            ]"
          >
            {{ page }}
          </button>

          <!-- 右侧省略号 -->
          <span v-if="showRightEllipsis" class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600">
            ...
          </span>

          <!-- 最后一页 -->
          <button
            v-if="showLastPage"
            @click="goToPage(totalPages)"
            :class="[
              'relative inline-flex items-center px-4 py-2 text-sm font-medium border',
              currentPage === totalPages
                ? 'z-10 bg-primary-50 dark:bg-primary-900/20 border-primary-500 text-primary-600 dark:text-primary-400'
                : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
            ]"
          >
            {{ totalPages }}
          </button>
        </div>

        <!-- 下一页 -->
        <button
          @click="goToNext"
          :disabled="currentPage >= totalPages"
          class="relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-r-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <div class="i-heroicons-chevron-right w-5 h-5"></div>
        </button>

        <!-- 跳转到指定页 -->
        <div class="flex items-center ml-4 space-x-2">
          <span class="text-sm text-gray-700 dark:text-gray-300">跳转到</span>
          <input
            v-model.number="jumpToPage"
            @keyup.enter="handleJumpToPage"
            type="number"
            :min="1"
            :max="totalPages"
            class="w-16 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          />
          <button
            @click="handleJumpToPage"
            class="px-3 py-1 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md transition-colors"
          >
            跳转
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Props
interface Props {
  currentPage: number
  totalPages: number
  total: number
  pageSize: number
}

const props = withDefaults(defineProps<Props>(), {
  currentPage: 1,
  totalPages: 1,
  total: 0,
  pageSize: 10
})

// Emits
const emit = defineEmits<{
  'page-change': [page: number]
}>()

// 跳转页码输入
const jumpToPage = ref<number>(props.currentPage)

// 计算属性
const startItem = computed(() => {
  return (props.currentPage - 1) * props.pageSize + 1
})

const endItem = computed(() => {
  return Math.min(props.currentPage * props.pageSize, props.total)
})

// 页码显示逻辑
const showFirstPage = computed(() => {
  return props.totalPages > 1 && props.currentPage > 3
})

const showLastPage = computed(() => {
  return props.totalPages > 1 && props.currentPage < props.totalPages - 2
})

const showLeftEllipsis = computed(() => {
  return props.currentPage > 4
})

const showRightEllipsis = computed(() => {
  return props.currentPage < props.totalPages - 3
})

const middlePages = computed(() => {
  const pages: number[] = []
  const start = Math.max(1, props.currentPage - 2)
  const end = Math.min(props.totalPages, props.currentPage + 2)
  
  for (let i = start; i <= end; i++) {
    if (i === 1 && showFirstPage.value) continue
    if (i === props.totalPages && showLastPage.value) continue
    pages.push(i)
  }
  
  return pages
})

// 方法
const goToPage = (page: number) => {
  if (page >= 1 && page <= props.totalPages && page !== props.currentPage) {
    emit('page-change', page)
  }
}

const goToPrevious = () => {
  if (props.currentPage > 1) {
    goToPage(props.currentPage - 1)
  }
}

const goToNext = () => {
  if (props.currentPage < props.totalPages) {
    goToPage(props.currentPage + 1)
  }
}

const handleJumpToPage = () => {
  if (jumpToPage.value >= 1 && jumpToPage.value <= props.totalPages) {
    goToPage(jumpToPage.value)
  }
}
</script>
