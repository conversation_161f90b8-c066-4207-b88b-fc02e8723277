<template>
  <div>
    <!-- 搜索输入框 -->
    <div class="relative">
      <BaseInput v-model="searchQuery" placeholder="搜索或创建标签..." prefix-icon="i-heroicons-hashtag" clearable
        :error="error" @keyup.enter="handleEnterKey" @input="handleInput" />

      <!-- 搜索结果下拉 -->
      <div v-if="showDropdown && (filteredTags.length > 0 || searchQuery)"
        class="absolute top-full left-0 right-0 z-10 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-h-48 overflow-y-auto">
        <!-- 创建新标签选项 -->
        <div v-if="searchQuery && !exactMatch"
          class="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-700"
          @click.stop="createAndAddTag(searchQuery)">
          <div class="flex items-center">
            <div class="i-heroicons-plus w-4 h-4 mr-2 text-primary-500"></div>
            <span class="text-sm">创建标签 "<strong>{{ searchQuery }}</strong>"</span>
          </div>
        </div>

        <!-- 现有标签列表 -->
        <div v-for="tag in filteredTags" :key="tag.id"
          class="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer flex items-center justify-between"
          @click.stop="toggleTag(tag)">
          <div class="flex items-center">
            <div class="w-3 h-3 rounded-full mr-2" :style="{ backgroundColor: tag.color }"></div>
            <span class="text-sm">{{ tag.name }}</span>
            <span class="text-xs text-gray-500 ml-2">({{ tag.resource_count }})</span>
          </div>
          <div v-if="isSelected(tag)" class="i-heroicons-check w-4 h-4 text-primary-500"></div>
        </div>

        <!-- 无结果提示 -->
        <div v-if="filteredTags.length === 0 && searchQuery && exactMatch"
          class="px-4 py-3 text-sm text-gray-500 dark:text-gray-400 text-center">
          未找到匹配的标签
        </div>
      </div>
    </div>

    <!-- 已选标签 -->
    <div v-if="selectedTags.length > 0" class="mt-3">
      <div class="flex flex-wrap gap-2">
        <span v-for="tag in selectedTags" :key="tag.id"
          class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium transition-all duration-200 hover:scale-105"
          :style="{
            backgroundColor: tag.color + '20',
            color: tag.color,
            borderColor: tag.color + '40'
          }">
          {{ tag.name }}
          <button type="button" class="ml-2 hover:bg-black/10 rounded-full p-0.5 transition-colors"
            @click.stop="removeTag(tag)">
            <div class="i-heroicons-x-mark w-3 h-3"></div>
          </button>
        </span>
      </div>
    </div>

    <!-- 热门标签推荐 -->
    <div v-if="!searchQuery && popularTags.length > 0" class="mt-4">
      <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">热门标签：</p>
      <div class="flex flex-wrap gap-2">
        <button type="button" v-for="tag in popularTags.slice(0, 8)" :key="tag.id" :class="[
          'px-2 py-1 rounded-full text-xs font-medium transition-all duration-200',
          isSelected(tag)
            ? 'bg-primary-500 text-white'
            : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
        ]" @click.stop="toggleTag(tag)">
          {{ tag.name }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useDebounceFn } from '@vueuse/core'
import BaseInput from '@/components/ui/BaseInput.vue'
import { tagService } from '@/services/tagService'
import type { Tag } from '@/types'

interface Props {
  modelValue: number[]
  allTags: Tag[]
  error?: string
  readonly?: boolean // 是否为只读模式（编辑时不自动保存）
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: number[]]
}>()

// 状态
const searchQuery = ref('')
const showDropdown = ref(false)
const popularTags = ref<Tag[]>([])

// 计算属性
const selectedTags = computed(() =>
  props.allTags.filter(tag => props.modelValue.includes(tag.id!))
)

const filteredTags = computed(() => {
  if (!searchQuery.value) return []

  const query = searchQuery.value.toLowerCase()
  return props.allTags
    .filter(tag =>
      tag.name.toLowerCase().includes(query) &&
      !props.modelValue.includes(tag.id!)
    )
    .slice(0, 10)
})

const exactMatch = computed(() =>
  props.allTags.some(tag =>
    tag.name.toLowerCase() === searchQuery.value.toLowerCase()
  )
)

// 防抖搜索
const debouncedSearch = useDebounceFn(() => {
  showDropdown.value = !!searchQuery.value
}, 300)

// 初始化
onMounted(async () => {
  try {
    popularTags.value = await tagService.getPopularTags(12)
  } catch (error) {
    console.error('加载热门标签失败:', error)
  }
})

// 监听搜索输入
watch(searchQuery, () => {
  debouncedSearch()
})

// 处理输入
const handleInput = () => {
  if (!searchQuery.value) {
    showDropdown.value = false
  }
}

// 处理回车键
const handleEnterKey = () => {
  if (searchQuery.value && !exactMatch.value) {
    createAndAddTag(searchQuery.value)
  } else if (filteredTags.value.length > 0) {
    toggleTag(filteredTags.value[0])
  }
}

// 切换标签选择状态
const toggleTag = (tag: Tag) => {
  const tagId = tag.id!
  const currentTags = [...props.modelValue]

  if (currentTags.includes(tagId)) {
    const index = currentTags.indexOf(tagId)
    currentTags.splice(index, 1)
  } else {
    currentTags.push(tagId)
  }

  emit('update:modelValue', currentTags)
  searchQuery.value = ''
  showDropdown.value = false
}

// 移除标签
const removeTag = (tag: Tag) => {
  const tagId = tag.id!
  const currentTags = props.modelValue.filter(id => id !== tagId)
  emit('update:modelValue', currentTags)
}

// 检查标签是否已选择
const isSelected = (tag: Tag) => {
  return props.modelValue.includes(tag.id!)
}

// 创建并添加新标签
const createAndAddTag = async (tagName: string) => {
  try {
    // 生成随机颜色
    const colors = [
      '#EF4444', '#F97316', '#F59E0B', '#EAB308', '#84CC16',
      '#22C55E', '#10B981', '#14B8A6', '#06B6D4', '#0EA5E9',
      '#3B82F6', '#6366F1', '#8B5CF6', '#A855F7', '#D946EF',
      '#EC4899', '#F43F5E'
    ]
    const randomColor = colors[Math.floor(Math.random() * colors.length)]

    let tagId: number
    let newTag: Tag

    if (props.readonly) {
      // 只读模式：创建临时标签，不保存到数据库
      tagId = Date.now() // 使用时间戳作为临时ID
      newTag = {
        id: tagId,
        name: tagName.trim(),
        color: randomColor,
        resource_count: 0,
        created_at: new Date(),
        isTemporary: true // 标记为临时标签
      }
    } else {
      // 正常模式：保存到数据库
      tagId = await tagService.createTag({
        name: tagName.trim(),
        color: randomColor
      })

      newTag = {
        id: tagId,
        name: tagName.trim(),
        color: randomColor,
        resource_count: 0,
        created_at: new Date()
      }
    }

    // 添加到所有标签列表
    props.allTags.push(newTag)

    // 添加到选中列表
    const currentTags = [...props.modelValue, tagId]
    emit('update:modelValue', currentTags)

    searchQuery.value = ''
    showDropdown.value = false
  } catch (error) {
    console.error('创建标签失败:', error)
    alert('创建标签失败，请重试')
  }
}

// 点击外部关闭下拉框
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    showDropdown.value = false
  }
}

// 添加全局点击监听
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

// 清理监听器
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
