# Vditor Markdown 编辑器安装指南

## 安装依赖

请运行以下命令安装 Vditor Markdown 编辑器所需的依赖：

```bash
npm install vditor
```

## 功能特性

### 1. 完整的 Markdown 支持

- ✅ **基础语法**：标题、段落、粗体、斜体、删除线
- ✅ **列表**：有序列表、无序列表、任务列表
- ✅ **链接和图片**：支持链接插入和图片上传
- ✅ **代码**：行内代码和代码块，支持语法高亮
- ✅ **表格**：可视化表格编辑
- ✅ **引用**：块引用支持
- ✅ **分割线**：水平分割线

### 2. 高级功能

- ✅ **数学公式**：支持 KaTeX 数学公式渲染
- ✅ **流程图**：支持 Mermaid 图表
- ✅ **目录**：自动生成文档目录
- ✅ **全屏编辑**：支持全屏和预览模式
- ✅ **实时预览**：左右分屏实时预览

### 3. 编辑体验

- ✅ **工具栏**：丰富的编辑工具栏
- ✅ **快捷键**：支持常用快捷键操作
- ✅ **自动保存**：Ctrl+S 保存功能
- ✅ **撤销重做**：完整的撤销重做支持
- ✅ **拖拽上传**：支持图片拖拽上传

### 4. 主题适配

- ✅ **亮色主题**：与 Ant Design 亮色主题一致
- ✅ **暗色主题**：完整的暗色主题支持
- ✅ **代码高亮**：多种代码高亮主题
- ✅ **预览主题**：GitHub 风格预览主题

## 使用方法

### 基础使用

```vue
<template>
  <MarkdownEditor
    v-model="content"
    placeholder="请输入内容..."
    height="400px"
    @change="handleChange"
    @save="handleSave"
    @upload-img="handleImageUpload"
  />
</template>

<script setup>
import MarkdownEditor from '@/components/common/MarkdownEditor.vue'

const content = ref('')

const handleChange = (value) => {
  console.log('内容变化:', value)
}

const handleSave = (value) => {
  console.log('保存内容:', value)
}

const handleImageUpload = async (files, callback) => {
  // 实现图片上传逻辑
  const urls = await uploadImages(files)
  callback(urls)
}
</script>
```

### 高级配置

```vue
<MarkdownEditor
  v-model="content"
  :height="'500px'"
  :language="'zh-CN'"
  :theme="'light'"
  :preview-theme="'github'"
  :code-theme="'github'"
  :readonly="false"
  :disabled="false"
  placeholder="支持 Markdown 语法..."
  @change="handleChange"
  @save="handleSave"
  @upload-img="handleImageUpload"
  @focus="handleFocus"
  @blur="handleBlur"
/>
```

## 图片上传

编辑器支持图片上传功能，你需要实现 `handleImageUpload` 方法：

```javascript
const handleImageUpload = async (files, callback) => {
  try {
    const urls = []

    for (const file of files) {
      // 上传到你的服务器
      const formData = new FormData()
      formData.append('image', file)

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
      })

      const result = await response.json()
      urls.push(result.url)
    }

    // 返回图片 URL 数组
    callback(urls)
  } catch (error) {
    console.error('图片上传失败:', error)
    callback([])
  }
}
```

## 样式自定义

编辑器已经与 Ant Design 主题完全集成，支持：

- CSS 变量自动适配
- 亮色/暗色主题切换
- 与现有界面风格一致
- 响应式设计

## 注意事项

1. **依赖安装**：确保已安装 `md-editor-v3` 依赖
2. **图片上传**：需要实现自己的图片上传逻辑
3. **主题切换**：编辑器会自动跟随系统主题
4. **性能优化**：大文档建议启用懒加载
5. **安全性**：生产环境建议配置 HTML 清理规则

## 故障排除

### 1. 样式问题

如果样式显示异常，请检查：

- 是否正确导入了 CSS 文件
- 主题变量是否正确配置
- 是否有样式冲突

### 2. 功能问题

如果某些功能不工作：

- 检查依赖版本是否正确
- 确认事件处理函数是否正确绑定
- 查看浏览器控制台错误信息

### 3. 性能问题

如果编辑器响应缓慢：

- 减少工具栏按钮数量
- 禁用不需要的功能（如 Mermaid、KaTeX）
- 优化图片上传逻辑
