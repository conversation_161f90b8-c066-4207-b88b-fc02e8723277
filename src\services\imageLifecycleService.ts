import { imageUploadService } from './imageUploadService'
import type { ImageRecord, ImageBackup } from '@/types/imageHost'

interface LifecycleCheckResult {
  totalImages: number
  activeImages: number
  expiredImages: number
  failedImages: number
  expiringImages: number
  checkedImages: number
  errors: string[]
}

class ImageLifecycleService {
  private checkInterval: number | null = null
  private readonly CHECK_INTERVAL_MS = 60 * 60 * 1000 // 1小时检查一次
  private isChecking = false

  // 启动生命周期管理
  startLifecycleManagement(): void {
    if (this.checkInterval) {
      this.stopLifecycleManagement()
    }

    // 立即执行一次检查
    this.performLifecycleCheck()

    // 设置定时检查
    this.checkInterval = window.setInterval(() => {
      this.performLifecycleCheck()
    }, this.CHECK_INTERVAL_MS)

    console.log('图片生命周期管理已启动')
  }

  // 停止生命周期管理
  stopLifecycleManagement(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
      console.log('图片生命周期管理已停止')
    }
  }

  // 执行生命周期检查
  async performLifecycleCheck(): Promise<LifecycleCheckResult> {
    if (this.isChecking) {
      console.log('生命周期检查正在进行中，跳过本次检查')
      return this.getEmptyResult()
    }

    this.isChecking = true
    console.log('开始执行图片生命周期检查...')

    const result: LifecycleCheckResult = {
      totalImages: 0,
      activeImages: 0,
      expiredImages: 0,
      failedImages: 0,
      expiringImages: 0,
      checkedImages: 0,
      errors: []
    }

    try {
      const records = await imageUploadService.getAllImageRecords()
      result.totalImages = records.length

      if (records.length === 0) {
        console.log('没有图片记录需要检查')
        return result
      }

      // 批量检查图片状态
      const batchSize = 5 // 每批检查5张图片，避免并发过多
      for (let i = 0; i < records.length; i += batchSize) {
        const batch = records.slice(i, i + batchSize)
        await Promise.all(batch.map(record => this.checkImageRecord(record, result)))
        
        // 批次间稍作延迟，避免请求过于频繁
        if (i + batchSize < records.length) {
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }

      // 保存更新后的记录
      await this.saveUpdatedRecords(records)

      console.log('生命周期检查完成:', result)
      return result

    } catch (error) {
      console.error('生命周期检查失败:', error)
      result.errors.push(`检查失败: ${(error as Error).message}`)
      return result
    } finally {
      this.isChecking = false
    }
  }

  // 检查单个图片记录
  private async checkImageRecord(record: ImageRecord, result: LifecycleCheckResult): Promise<void> {
    try {
      // 检查是否过期
      if (this.isImageExpired(record)) {
        record.status = 'expired'
        result.expiredImages++
        return
      }

      // 检查是否即将过期
      if (this.isImageExpiring(record)) {
        result.expiringImages++
      }

      // 检查图片链接状态
      let hasActiveBackup = false
      for (const backup of record.backups) {
        try {
          const isActive = await this.checkBackupStatus(backup)
          if (isActive) {
            backup.status = 'active'
            backup.lastChecked = new Date()
            hasActiveBackup = true
          } else {
            backup.status = 'failed'
            backup.lastChecked = new Date()
            backup.errorMessage = '链接无法访问'
          }
        } catch (error) {
          backup.status = 'failed'
          backup.lastChecked = new Date()
          backup.errorMessage = (error as Error).message
        }
      }

      // 更新记录状态
      if (hasActiveBackup) {
        record.status = 'active'
        result.activeImages++
      } else {
        record.status = 'failed'
        result.failedImages++
      }

      record.lastChecked = new Date()
      record.updatedAt = new Date()
      result.checkedImages++

    } catch (error) {
      console.error(`检查图片记录 ${record.id} 失败:`, error)
      result.errors.push(`检查图片 ${record.originalName} 失败: ${(error as Error).message}`)
    }
  }

  // 检查图片是否过期
  private isImageExpired(record: ImageRecord): boolean {
    if (!record.expirationSettings.enabled || !record.expirationSettings.expiresAt) {
      return false
    }
    return new Date() > record.expirationSettings.expiresAt
  }

  // 检查图片是否即将过期
  private isImageExpiring(record: ImageRecord): boolean {
    if (!record.expirationSettings.enabled || !record.expirationSettings.expiresAt) {
      return false
    }
    
    const notifyTime = new Date(
      record.expirationSettings.expiresAt.getTime() - 
      record.expirationSettings.notifyBefore * 24 * 60 * 60 * 1000
    )
    
    return new Date() >= notifyTime && new Date() < record.expirationSettings.expiresAt
  }

  // 检查备份状态
  private async checkBackupStatus(backup: ImageBackup): Promise<boolean> {
    try {
      // 使用HEAD请求检查链接是否可访问
      const response = await fetch(backup.url, {
        method: 'HEAD',
        signal: AbortSignal.timeout(10000) // 10秒超时
      })
      
      return response.ok
    } catch (error) {
      console.warn(`检查备份 ${backup.url} 失败:`, error)
      return false
    }
  }

  // 获取即将过期的图片
  async getExpiringImages(): Promise<ImageRecord[]> {
    const records = await imageUploadService.getAllImageRecords()
    return records.filter(record => this.isImageExpiring(record))
  }

  // 获取已过期的图片
  async getExpiredImages(): Promise<ImageRecord[]> {
    const records = await imageUploadService.getAllImageRecords()
    return records.filter(record => this.isImageExpired(record))
  }

  // 获取失效的图片
  async getFailedImages(): Promise<ImageRecord[]> {
    const records = await imageUploadService.getAllImageRecords()
    return records.filter(record => record.status === 'failed')
  }

  // 重新上传图片
  async reuploadImage(record: ImageRecord): Promise<ImageRecord> {
    // 这里需要重新获取原始文件，实际实现中可能需要用户重新选择文件
    // 或者从缓存中恢复文件数据
    throw new Error('重新上传功能需要用户提供原始文件')
  }

  // 批量重新上传
  async reuploadMultiple(records: ImageRecord[]): Promise<{ success: ImageRecord[], failed: string[] }> {
    const success: ImageRecord[] = []
    const failed: string[] = []

    for (const record of records) {
      try {
        const newRecord = await this.reuploadImage(record)
        success.push(newRecord)
      } catch (error) {
        failed.push(`${record.originalName}: ${(error as Error).message}`)
      }
    }

    return { success, failed }
  }

  // 删除图片记录
  async deleteImageRecord(recordId: string): Promise<void> {
    try {
      const records = await imageUploadService.getAllImageRecords()
      const filteredRecords = records.filter(r => r.id !== recordId)
      
      localStorage.setItem('image_records', JSON.stringify(filteredRecords))
    } catch (error) {
      console.error('删除图片记录失败:', error)
      throw new Error('删除图片记录失败')
    }
  }

  // 批量删除图片记录
  async deleteMultipleRecords(recordIds: string[]): Promise<void> {
    try {
      const records = await imageUploadService.getAllImageRecords()
      const filteredRecords = records.filter(r => !recordIds.includes(r.id))
      
      localStorage.setItem('image_records', JSON.stringify(filteredRecords))
    } catch (error) {
      console.error('批量删除图片记录失败:', error)
      throw new Error('批量删除图片记录失败')
    }
  }

  // 更新图片标签
  async updateImageTags(recordId: string, tags: string[]): Promise<void> {
    try {
      const records = await imageUploadService.getAllImageRecords()
      const record = records.find(r => r.id === recordId)
      
      if (!record) {
        throw new Error('图片记录不存在')
      }
      
      record.tags = tags
      record.updatedAt = new Date()
      
      localStorage.setItem('image_records', JSON.stringify(records))
    } catch (error) {
      console.error('更新图片标签失败:', error)
      throw new Error('更新图片标签失败')
    }
  }

  // 获取统计信息
  async getStatistics(): Promise<{
    total: number
    active: number
    expired: number
    failed: number
    expiring: number
    totalSize: number
  }> {
    const records = await imageUploadService.getAllImageRecords()
    
    return {
      total: records.length,
      active: records.filter(r => r.status === 'active').length,
      expired: records.filter(r => this.isImageExpired(r)).length,
      failed: records.filter(r => r.status === 'failed').length,
      expiring: records.filter(r => this.isImageExpiring(r)).length,
      totalSize: records.reduce((sum, r) => sum + r.size, 0)
    }
  }

  // 保存更新后的记录
  private async saveUpdatedRecords(records: ImageRecord[]): Promise<void> {
    try {
      localStorage.setItem('image_records', JSON.stringify(records))
    } catch (error) {
      console.error('保存更新记录失败:', error)
      throw new Error('保存更新记录失败')
    }
  }

  // 获取空结果
  private getEmptyResult(): LifecycleCheckResult {
    return {
      totalImages: 0,
      activeImages: 0,
      expiredImages: 0,
      failedImages: 0,
      expiringImages: 0,
      checkedImages: 0,
      errors: []
    }
  }
}

export const imageLifecycleService = new ImageLifecycleService()
