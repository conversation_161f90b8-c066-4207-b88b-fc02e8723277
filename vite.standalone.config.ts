import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import UnoCSS from '@unocss/vite'
import { viteSingleFile } from 'vite-plugin-singlefile'

// 专门用于生成独立运行HTML文件的配置
export default defineConfig({
  plugins: [
    vue(),
    UnoCSS(),
    viteSingleFile(), // 将所有资源内联到单个HTML文件中
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  build: {
    outDir: 'standalone-dist',
    rollupOptions: {
      output: {
        manualChunks: undefined,
        inlineDynamicImports: true,
      },
    },
    target: 'es2015',
  },
  base: './',
})
