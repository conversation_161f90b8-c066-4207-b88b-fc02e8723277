import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useKnowledgeFilterStore } from '@/stores/knowledgeFilter'
import { categoryService } from '@/services/categoryService'
import { tagService } from '@/services/tagService'
import type { Category, Tag, CategoryWithChildren } from '@/types'
import { debounce } from 'lodash-es'

export interface FilterOptions {
  syncWithUrl?: boolean
  debounceMs?: number
}

export interface UseFiltersResult {
  // 筛选状态
  selectedCategoryId: Ref<number | null>
  selectedTags: Ref<Tag[]>
  currentSortKey: Ref<string>
  searchKeyword: Ref<string>
  viewMode: Ref<'grid' | 'list'>

  // 辅助数据
  categories: Ref<Category[]>
  categoryTree: Ref<CategoryWithChildren[]>
  popularTags: Ref<Tag[]>

  // 计算属性
  hasActiveFilters: Ref<boolean>
  filterSummary: Ref<string>
  sortOptions: Ref<Array<{ key: string; label: string; field: string; order: string }>>

  // 筛选操作
  setCategory: (categoryId: number | null, category?: Category | null) => void
  addTag: (tag: Tag) => void
  removeTag: (tagId: number) => void
  toggleTag: (tag: Tag) => void
  setSortKey: (sortKey: string) => void
  setSearchKeyword: (keyword: string) => void
  setViewMode: (mode: 'grid' | 'list') => void
  clearFilters: () => void
  clearAll: () => void

  // 数据加载
  loadCategories: () => Promise<void>
  loadTags: () => Promise<void>

  // URL 同步
  syncFromUrl: () => Promise<void>
  syncToUrl: () => void
}

/**
 * 筛选管理 Hook
 * 统一处理所有筛选相关的逻辑
 */
export function useFilters(options: FilterOptions = {}): UseFiltersResult {
  const { syncWithUrl = true, debounceMs = 300 } = options

  const route = useRoute()
  const router = useRouter()
  const filterStore = useKnowledgeFilterStore()

  // 辅助数据
  const categories = ref<Category[]>([])
  const categoryTree = ref<CategoryWithChildren[]>([])
  const popularTags = ref<Tag[]>([])

  // 排序选项
  const sortOptions = ref([
    { key: 'created_at_desc', label: '创建时间（新到旧）', field: 'created_at', order: 'desc' },
    { key: 'created_at_asc', label: '创建时间（旧到新）', field: 'created_at', order: 'asc' },
    { key: 'updated_at_desc', label: '更新时间（新到旧）', field: 'updated_at', order: 'desc' },
    { key: 'updated_at_asc', label: '更新时间（旧到新）', field: 'updated_at', order: 'asc' },
    { key: 'title_asc', label: '标题（A-Z）', field: 'title', order: 'asc' },
    { key: 'title_desc', label: '标题（Z-A）', field: 'title', order: 'desc' },
    { key: 'view_count_desc', label: '浏览量（高到低）', field: 'view_count', order: 'desc' },
    { key: 'view_count_asc', label: '浏览量（低到高）', field: 'view_count', order: 'asc' },
  ])

  // 计算属性 - 直接使用 store 的状态
  const selectedCategoryId = computed({
    get: () => filterStore.selectedCategoryId,
    set: (value) => (filterStore.selectedCategoryId = value),
  })

  const selectedTags = computed({
    get: () => filterStore.selectedTags,
    set: (value) => (filterStore.selectedTags = value),
  })

  const currentSortKey = computed({
    get: () => filterStore.currentSortKey,
    set: (value) => (filterStore.currentSortKey = value),
  })

  const searchKeyword = computed({
    get: () => filterStore.searchKeyword,
    set: (value) => (filterStore.searchKeyword = value),
  })

  const viewMode = computed({
    get: () => filterStore.viewMode,
    set: (value) => (filterStore.viewMode = value),
  })

  const hasActiveFilters = computed(() => filterStore.hasActiveFilters)
  const filterSummary = computed(() => filterStore.filterSummary)

  // 筛选操作方法
  const setCategory = (categoryId: number | null, category: Category | null = null) => {
    filterStore.setCategory(categoryId, category)
    if (syncWithUrl) {
      debouncedSyncToUrl()
    }
  }

  const addTag = (tag: Tag) => {
    filterStore.addTag(tag)
    if (syncWithUrl) {
      debouncedSyncToUrl()
    }
  }

  const removeTag = (tagId: number) => {
    filterStore.removeTag(tagId)
    if (syncWithUrl) {
      debouncedSyncToUrl()
    }
  }

  const toggleTag = (tag: Tag) => {
    const isSelected = filterStore.selectedTags.some((t) => t.id === tag.id)
    if (isSelected) {
      removeTag(tag.id!)
    } else {
      addTag(tag)
    }
  }

  const setSortKey = (sortKey: string) => {
    filterStore.setSortKey(sortKey)
    if (syncWithUrl) {
      debouncedSyncToUrl()
    }
  }

  const setSearchKeyword = (keyword: string) => {
    filterStore.setSearchKeyword(keyword)
    if (syncWithUrl) {
      debouncedSyncToUrl()
    }
  }

  const setViewMode = (mode: 'grid' | 'list') => {
    filterStore.setViewMode(mode)
  }

  const clearFilters = () => {
    filterStore.clearFilters()
    if (syncWithUrl) {
      syncToUrl()
    }
  }

  const clearAll = () => {
    filterStore.clearAll()
    if (syncWithUrl) {
      syncToUrl()
    }
  }

  // 数据加载方法
  const loadCategories = async () => {
    try {
      const [allCategories, tree] = await Promise.all([
        categoryService.getAllCategories(),
        categoryService.getCategoryTree(),
      ])

      categories.value = allCategories
      categoryTree.value = tree

      // 验证当前选中的分类是否仍然有效
      filterStore.updateCategoryInfo(allCategories)
    } catch (error) {
      console.error('加载分类失败:', error)
    }
  }

  const loadTags = async () => {
    try {
      const [allTags, popular] = await Promise.all([
        tagService.getAllTags(),
        tagService.getPopularTags(20),
      ])

      popularTags.value = popular

      // 验证当前选中的标签是否仍然有效
      filterStore.validateTags(allTags)
    } catch (error) {
      console.error('加载标签失败:', error)
    }
  }

  // URL 同步方法
  const syncFromUrl = async () => {
    if (!syncWithUrl) return

    const { category, tag, sort, q, view } = route.query

    // 处理分类参数
    if (category && typeof category === 'string') {
      const categoryId = parseInt(category)
      const foundCategory = categories.value.find((cat) => cat.id === categoryId)
      if (foundCategory) {
        setCategory(categoryId, foundCategory)
      }
    }

    // 处理标签参数
    if (tag && typeof tag === 'string') {
      const tagId = parseInt(tag)
      const foundTag = popularTags.value.find((t) => t.id === tagId)
      if (foundTag) {
        addTag(foundTag)
      }
    }

    // 处理排序参数
    if (sort && typeof sort === 'string') {
      const validSort = sortOptions.value.find((opt) => opt.key === sort)
      if (validSort) {
        setSortKey(sort)
      }
    }

    // 处理搜索参数
    if (q && typeof q === 'string') {
      setSearchKeyword(q)
    }

    // 处理视图模式参数
    if (view && (view === 'grid' || view === 'list')) {
      setViewMode(view)
    }
  }

  const syncToUrl = () => {
    if (!syncWithUrl) return

    const query: Record<string, string> = {}

    if (filterStore.selectedCategoryId) {
      query.category = filterStore.selectedCategoryId.toString()
    }

    if (filterStore.selectedTags.length > 0) {
      // 如果有多个标签，只同步第一个到URL（保持简洁）
      const firstTagId = filterStore.selectedTags[0].id
      if (firstTagId !== undefined) {
        query.tag = firstTagId.toString()
      }
    }

    if (filterStore.currentSortKey !== 'created_at_desc') {
      query.sort = filterStore.currentSortKey
    }

    if (filterStore.searchKeyword.trim()) {
      query.q = filterStore.searchKeyword.trim()
    }

    if (filterStore.viewMode !== 'grid') {
      query.view = filterStore.viewMode
    }

    router.replace({ query })
  }

  // 防抖的URL同步
  const debouncedSyncToUrl = debounce(syncToUrl, debounceMs)

  // 监听路由变化
  watch(
    () => route.query,
    () => {
      if (syncWithUrl) {
        syncFromUrl()
      }
    },
    { immediate: false },
  )

  return {
    // 筛选状态
    selectedCategoryId,
    selectedTags,
    currentSortKey,
    searchKeyword,
    viewMode,

    // 辅助数据
    categories,
    categoryTree,
    popularTags,

    // 计算属性
    hasActiveFilters,
    filterSummary,
    sortOptions,

    // 筛选操作
    setCategory,
    addTag,
    removeTag,
    toggleTag,
    setSortKey,
    setSearchKeyword,
    setViewMode,
    clearFilters,
    clearAll,

    // 数据加载
    loadCategories,
    loadTags,

    // URL 同步
    syncFromUrl,
    syncToUrl,
  }
}
