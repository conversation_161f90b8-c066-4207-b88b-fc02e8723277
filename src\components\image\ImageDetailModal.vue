<template>
  <Teleport to="body">
    <Transition enter-active-class="transition-opacity duration-300" enter-from-class="opacity-0"
      enter-to-class="opacity-100" leave-active-class="transition-opacity duration-300" leave-from-class="opacity-100"
      leave-to-class="opacity-0">
      <div v-if="visible" class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
        @click="handleBackdropClick" @keydown.esc="close" tabindex="-1">
        <Transition enter-active-class="transition-all duration-300 ease-out" enter-from-class="opacity-0 scale-95"
          enter-to-class="opacity-100 scale-100" leave-active-class="transition-all duration-200 ease-in"
          leave-from-class="opacity-100 scale-100" leave-to-class="opacity-0 scale-95">
          <div v-if="visible"
            class="relative w-full max-w-6xl h-full max-h-[90vh] mx-4 bg-white dark:bg-gray-800 rounded-xl shadow-2xl overflow-hidden flex"
            @click.stop>
            <!-- 关闭按钮 -->
            <button @click="close"
              class="absolute top-4 right-4 z-10 p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-full transition-colors">
              <div class="i-heroicons-x-mark w-6 h-6"></div>
            </button>

            <!-- 左侧：图片预览区域 -->
            <div class="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900 relative overflow-hidden">
              <div ref="imageContainer" class="relative w-full h-full flex items-center justify-center cursor-move"
                @mousedown="startDrag" @wheel="handleWheel">
                <img ref="imageElement" :src="getPrimaryImageUrl(image)" :alt="image.originalName"
                  class="max-w-full max-h-full object-contain transition-transform duration-200"
                  :style="{ transform: `scale(${scale}) translate(${translateX}px, ${translateY}px)` }"
                  @load="handleImageLoad" />
              </div>

              <!-- 缩放控制 -->
              <div
                class="absolute bottom-4 left-4 flex items-center space-x-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg p-2">
                <button @click="zoomOut"
                  class="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                  :disabled="scale <= 0.1">
                  <div class="i-heroicons-minus w-4 h-4"></div>
                </button>
                <span class="text-sm text-gray-600 dark:text-gray-400 min-w-12 text-center">
                  {{ Math.round(scale * 100) }}%
                </span>
                <button @click="zoomIn"
                  class="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                  :disabled="scale >= 5">
                  <div class="i-heroicons-plus w-4 h-4"></div>
                </button>
                <button @click="resetZoom"
                  class="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors">
                  <div class="i-heroicons-arrow-path w-4 h-4"></div>
                </button>
              </div>
            </div>

            <!-- 右侧：详细信息面板 -->
            <div class="w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 flex flex-col">
              <div class="flex-1 overflow-y-auto p-6">
                <!-- 基本信息 -->
                <div class="mb-6">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">基本信息</h3>
                  <div class="space-y-3">
                    <div>
                      <label class="text-sm font-medium text-gray-500 dark:text-gray-400">文件名</label>
                      <div class="flex items-center group">
                        <div v-if="!isEditingName" class="flex-1 flex items-center">
                          <p class="text-sm text-gray-900 dark:text-gray-100 break-all flex-1">{{ image.originalName }}
                          </p>
                          <button @click="startEditingName"
                            class="ml-2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 opacity-0 group-hover:opacity-100 transition-all"
                            title="编辑文件名">
                            <div class="i-heroicons-pencil w-4 h-4"></div>
                          </button>
                        </div>
                        <div v-else class="flex-1 flex items-center space-x-2">
                          <input ref="nameInput" v-model="editingName" type="text"
                            class="flex-1 text-sm px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                            @keyup.enter="saveNameEdit" @keyup.esc="cancelNameEdit" @blur="saveNameEdit" />
                          <button @click="saveNameEdit"
                            class="p-1 text-green-600 hover:text-green-700 transition-colors" title="保存">
                            <div class="i-heroicons-check w-4 h-4"></div>
                          </button>
                          <button @click="cancelNameEdit"
                            class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                            title="取消">
                            <div class="i-heroicons-x-mark w-4 h-4"></div>
                          </button>
                        </div>
                      </div>
                    </div>
                    <div>
                      <label class="text-sm font-medium text-gray-500 dark:text-gray-400">文件大小</label>
                      <p class="text-sm text-gray-900 dark:text-gray-100">{{ formatFileSize(image.size) }}</p>
                    </div>
                    <div>
                      <label class="text-sm font-medium text-gray-500 dark:text-gray-400">上传时间</label>
                      <p class="text-sm text-gray-900 dark:text-gray-100">{{ formatDate(image.uploadTime) }}</p>
                    </div>
                    <div v-if="(image.width && image.height) || (actualImageSize.width && actualImageSize.height)">
                      <label class="text-sm font-medium text-gray-500 dark:text-gray-400">图片尺寸</label>
                      <p class="text-sm text-gray-900 dark:text-gray-100">
                        {{ (image.width || actualImageSize.width) }} × {{ (image.height || actualImageSize.height) }}
                      </p>
                    </div>
                  </div>
                </div>

                <!-- 状态信息 -->
                <div class="mb-6">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">状态信息</h3>
                  <div class="flex items-center space-x-2">
                    <div :class="[
                      'px-2 py-1 text-xs font-medium rounded',
                      getStatusClasses(image)
                    ]">
                      {{ getStatusText(image) }}
                    </div>
                  </div>
                </div>

                <!-- 备份信息 -->
                <div class="mb-6">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">备份信息</h3>
                    <div class="flex items-center space-x-2">
                      <!-- 添加URL按钮 -->
                      <button @click="showAddUrlModal = true"
                        class="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                        title="添加URL">
                        <div class="i-heroicons-plus w-4 h-4"></div>
                      </button>
                      <!-- 上传到图床按钮 -->
                      <button @click="uploadToRandomHost"
                        class="p-1 text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors"
                        title="上传到随机图床" :disabled="isUploading">
                        <div class="i-heroicons-cloud-arrow-up w-4 h-4"></div>
                      </button>
                    </div>
                  </div>
                  <div class="space-y-3">
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                      共 {{ image.urls?.length || 0 }} 个链接
                    </p>
                    <div v-if="image.urls?.length" class="space-y-3">
                      <div v-for="(urlRecord, index) in image.urls" :key="index"
                        class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border">
                        <!-- 图床信息 -->
                        <div class="flex items-center justify-between mb-2">
                          <div class="flex items-center space-x-2">
                            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {{ urlRecord.hostName }}
                            </span>
                            <span :class="[
                              'px-2 py-1 text-xs font-medium rounded',
                              urlRecord.status === 'active'
                                ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                                : urlRecord.status === 'failed'
                                  ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                                  : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
                            ]">
                              {{ urlRecord.status === 'active' ? '正常' : urlRecord.status === 'failed' ? '失效' : '未知' }}
                            </span>
                          </div>
                          <div class="flex items-center space-x-1">
                            <!-- 复制按钮 -->
                            <button @click="copyUrl(urlRecord.url)"
                              class="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                              title="复制链接">
                              <div class="i-heroicons-clipboard w-4 h-4"></div>
                            </button>
                            <!-- 删除按钮 -->
                            <button @click="deleteUrl(urlRecord, index)"
                              class="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
                              title="删除链接">
                              <div class="i-heroicons-trash w-4 h-4"></div>
                            </button>
                          </div>
                        </div>
                        <!-- URL显示 -->
                        <div class="text-xs text-gray-500 dark:text-gray-400 break-all">
                          {{ urlRecord.url }}
                        </div>
                        <!-- 上传时间 -->
                        <div class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                          上传时间: {{ formatDate(urlRecord.uploadTime) }}
                        </div>
                      </div>
                    </div>

                    <!-- 上传进度显示 -->
                    <div v-if="isUploading"
                      class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                      <div class="flex items-center space-x-2">
                        <div class="animate-spin w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full">
                        </div>
                        <span class="text-sm text-blue-700 dark:text-blue-300">正在上传到图床...</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 添加URL模态框 -->
                <div v-if="showAddUrlModal"
                  class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
                  @click="handleAddUrlBackdropClick">
                  <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4" @click.stop>
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">添加备份URL</h4>
                    <div class="space-y-4">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">URL地址</label>
                        <input v-model="newUrlInput" type="url"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          placeholder="请输入图片URL">
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">图床名称</label>
                        <input v-model="newUrlHostName" type="text"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          placeholder="请输入图床名称">
                      </div>
                    </div>
                    <div class="flex justify-end space-x-3 mt-6">
                      <button @click="showAddUrlModal = false"
                        class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors">
                        取消
                      </button>
                      <button @click="addNewUrl"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                        :disabled="!newUrlInput || !newUrlHostName">
                        添加
                      </button>
                    </div>
                  </div>
                </div>

                <!-- 标签信息 -->
                <div class="mb-6">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">标签</h3>
                    <button @click="toggleTagEditing"
                      class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                      :title="isEditingTags ? '完成编辑' : '编辑标签'">
                      <div v-if="isEditingTags" class="i-heroicons-check w-4 h-4"></div>
                      <div v-else class="i-heroicons-pencil w-4 h-4"></div>
                    </button>
                  </div>

                  <div v-if="!isEditingTags" class="flex flex-wrap gap-2">
                    <span v-for="tag in image.tags" :key="tag"
                      class="px-2 py-1 bg-primary-100 dark:bg-primary-800/30 text-primary-700 dark:text-primary-300 text-xs rounded-md">
                      {{ tag }}
                    </span>
                    <button v-if="!image.tags?.length" @click="toggleTagEditing"
                      class="px-2 py-1 border border-dashed border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 text-xs rounded-md hover:border-primary-300 hover:text-primary-600 transition-colors">
                      添加标签
                    </button>
                  </div>

                  <!-- 标签编辑模式 -->
                  <div v-else class="space-y-3">
                    <!-- 当前标签 -->
                    <div v-if="editingTags.length > 0" class="flex flex-wrap gap-2">
                      <div v-for="(tag, index) in editingTags" :key="index"
                        class="flex items-center px-2 py-1 bg-primary-100 dark:bg-primary-800/30 text-primary-700 dark:text-primary-300 text-xs rounded-md">
                        <span>{{ tag }}</span>
                        <button @click="removeTag(index)"
                          class="ml-1 text-primary-500 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-200">
                          <div class="i-heroicons-x-mark w-3 h-3"></div>
                        </button>
                      </div>
                    </div>

                    <!-- 添加新标签 -->
                    <div class="flex items-center space-x-2">
                      <input ref="tagInput" v-model="newTagInput" type="text" placeholder="输入标签名称..."
                        class="flex-1 text-sm px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                        @keyup.enter="addTag" @keyup.esc="newTagInput = ''" />
                      <button @click="addTag" :disabled="!newTagInput.trim()"
                        class="px-3 py-1 bg-primary-600 text-white text-xs rounded hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                        添加
                      </button>
                    </div>

                    <!-- 可用标签建议 -->
                    <div v-if="availableTags.length > 0" class="space-y-2">
                      <label class="text-xs font-medium text-gray-500 dark:text-gray-400">常用标签</label>
                      <div class="flex flex-wrap gap-1">
                        <button v-for="tag in availableTags" :key="tag" @click="addExistingTag(tag)"
                          class="px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 rounded hover:border-primary-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                          {{ tag }}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="border-t border-gray-200 dark:border-gray-700 p-6">
                <div class="grid grid-cols-2 gap-3">
                  <BaseButton variant="secondary" size="sm" @click="copyImageUrl">
                    <div class="i-heroicons-clipboard mr-2"></div>
                    复制链接
                  </BaseButton>
                  <BaseButton variant="secondary" size="sm" @click="downloadImage">
                    <div class="i-heroicons-arrow-down-tray mr-2"></div>
                    下载
                  </BaseButton>
                  <BaseButton variant="secondary" size="sm" @click="editImage">
                    <div class="i-heroicons-pencil mr-2"></div>
                    编辑
                  </BaseButton>
                  <BaseButton variant="danger" size="sm" @click="deleteImage">
                    <div class="i-heroicons-trash mr-2"></div>
                    删除
                  </BaseButton>
                </div>
              </div>
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import { useToast } from '@/composables/useToast'
import type { ImageRecord, ImageUrlRecord } from '@/services/imageDataService'
import { imageDataService } from '@/services/imageDataService'

interface Props {
  image: ImageRecord
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
  edit: [image: ImageRecord]
  delete: [imageId: string]
}>()

const { success, error } = useToast()

const visible = ref(false)
const scale = ref(1)
const translateX = ref(0)
const translateY = ref(0)
const isDragging = ref(false)
const dragStart = ref({ x: 0, y: 0 })
const imageContainer = ref<HTMLElement>()
const imageElement = ref<HTMLImageElement>()
const actualImageSize = ref({ width: 0, height: 0 })

// 备份管理相关状态
const showAddUrlModal = ref(false)
const newUrlInput = ref('')
const newUrlHostName = ref('')
const isUploading = ref(false)

// 编辑相关状态
const isEditingName = ref(false)
const editingName = ref('')
const nameInput = ref<HTMLInputElement>()

const isEditingTags = ref(false)
const editingTags = ref<string[]>([])
const newTagInput = ref('')
const tagInput = ref<HTMLInputElement>()
const availableTags = ref<string[]>([])

// 获取主要图片URL
const getPrimaryImageUrl = (image: ImageRecord): string => {
  const activeUrl = image.urls.find(url => url.status === 'active')
  return activeUrl?.url || image.urls[0]?.url || ''
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化日期
const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

// 获取状态样式
const getStatusClasses = (image: ImageRecord): string => {
  const hasActiveUrls = image.urls.some(url => url.status === 'active')
  if (!hasActiveUrls) return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
  return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
}

// 获取状态文本
const getStatusText = (image: ImageRecord): string => {
  const hasActiveUrls = image.urls.some(url => url.status === 'active')
  if (!hasActiveUrls) return '失效'
  return '正常'
}

// 缩放控制
const zoomIn = () => {
  if (scale.value < 5) {
    scale.value = Math.min(5, scale.value * 1.2)
  }
}

const zoomOut = () => {
  if (scale.value > 0.1) {
    scale.value = Math.max(0.1, scale.value / 1.2)
  }
}

const resetZoom = () => {
  scale.value = 1
  translateX.value = 0
  translateY.value = 0
}

// 鼠标滚轮缩放
const handleWheel = (event: WheelEvent) => {
  event.preventDefault()
  if (event.deltaY < 0) {
    zoomIn()
  } else {
    zoomOut()
  }
}

// 拖拽功能
const startDrag = (event: MouseEvent) => {
  if (scale.value <= 1) return

  isDragging.value = true
  dragStart.value = {
    x: event.clientX - translateX.value,
    y: event.clientY - translateY.value
  }

  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', stopDrag)
}

const handleDrag = (event: MouseEvent) => {
  if (!isDragging.value) return

  translateX.value = event.clientX - dragStart.value.x
  translateY.value = event.clientY - dragStart.value.y
}

const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 图片加载完成
const handleImageLoad = () => {
  resetZoom()

  // 获取实际图片尺寸
  if (imageElement.value) {
    actualImageSize.value = {
      width: imageElement.value.naturalWidth,
      height: imageElement.value.naturalHeight
    }
  }
}

// 复制图片链接
const copyImageUrl = async () => {
  const url = getPrimaryImageUrl(props.image)
  try {
    await navigator.clipboard.writeText(url)
    success('链接复制成功', url)
  } catch (err) {
    error('复制失败', '无法访问剪贴板')
  }
}

// 复制指定URL
const copyUrl = async (url: string) => {
  try {
    await navigator.clipboard.writeText(url)
    success('链接复制成功', url)
  } catch (err) {
    error('复制失败', '无法访问剪贴板')
  }
}

// 删除指定URL
const deleteUrl = async (urlRecord: any, index: number) => {
  if (!confirm(`确定要删除来自 ${urlRecord.hostName} 的链接吗？`)) {
    return
  }

  try {
    if (urlRecord.id && props.image.id) {
      await imageDataService.deleteImageUrl(props.image.id, urlRecord.id)
      // 从本地数组中移除
      props.image.urls.splice(index, 1)
      success('链接删除成功', `已删除来自 ${urlRecord.hostName} 的链接`)

      // 如果没有URL了，发出删除事件
      if (props.image.urls.length === 0) {
        emit('delete', props.image.id.toString())
      }
    } else {
      // 如果没有ID，只从本地数组中移除
      props.image.urls.splice(index, 1)
      success('链接删除成功', `已删除来自 ${urlRecord.hostName} 的链接`)
    }
  } catch (err) {
    error('删除失败', '无法删除链接')
  }
}

// 添加新URL
const addNewUrl = async () => {
  if (!newUrlInput.value || !newUrlHostName.value) {
    error('输入错误', '请填写完整的URL和图床名称')
    return
  }

  try {
    const newUrlRecord: any = {
      hostId: 'manual',
      hostName: newUrlHostName.value,
      url: newUrlInput.value,
      deleteUrl: undefined,
      uploadTime: new Date(),
      status: 'active' as const
    }

    if (props.image.id) {
      // 保存到数据库
      const urlId = await imageDataService.addImageUrl(props.image.id, newUrlRecord)
      newUrlRecord.id = urlId
    }

    // 添加到本地数组
    props.image.urls.push(newUrlRecord)

    // 重置表单
    newUrlInput.value = ''
    newUrlHostName.value = ''
    showAddUrlModal.value = false

    success('URL添加成功', `已添加来自 ${newUrlRecord.hostName} 的链接`)
  } catch (err) {
    error('添加失败', '无法添加URL')
  }
}

// 文件名编辑相关方法
const startEditingName = () => {
  isEditingName.value = true
  editingName.value = props.image.originalName
  nextTick(() => {
    nameInput.value?.focus()
    nameInput.value?.select()
  })
}

const saveNameEdit = async () => {
  if (!editingName.value.trim()) {
    cancelNameEdit()
    return
  }

  if (editingName.value === props.image.originalName) {
    cancelNameEdit()
    return
  }

  try {
    await imageDataService.updateImage(props.image.id, {
      originalName: editingName.value.trim()
    })

    // 更新本地数据
    props.image.originalName = editingName.value.trim()

    isEditingName.value = false
    success('文件名更新成功', '文件名已成功更新')
  } catch (err) {
    error('更新失败', '无法更新文件名')
    cancelNameEdit()
  }
}

const cancelNameEdit = () => {
  isEditingName.value = false
  editingName.value = ''
}

// 标签编辑相关方法
const toggleTagEditing = async () => {
  if (isEditingTags.value) {
    // 保存标签更改
    await saveTagChanges()
  } else {
    // 开始编辑
    isEditingTags.value = true
    editingTags.value = [...(props.image.tags || [])]
    newTagInput.value = ''

    // 加载可用标签
    await loadAvailableTags()
  }
}

const addTag = () => {
  const tagName = newTagInput.value.trim()
  if (!tagName) return

  if (editingTags.value.includes(tagName)) {
    error('标签已存在', '该标签已经添加过了')
    return
  }

  editingTags.value.push(tagName)
  newTagInput.value = ''

  // 从可用标签中移除
  const index = availableTags.value.indexOf(tagName)
  if (index > -1) {
    availableTags.value.splice(index, 1)
  }
}

const addExistingTag = (tagName: string) => {
  if (editingTags.value.includes(tagName)) return

  editingTags.value.push(tagName)

  // 从可用标签中移除
  const index = availableTags.value.indexOf(tagName)
  if (index > -1) {
    availableTags.value.splice(index, 1)
  }
}

const removeTag = (index: number) => {
  const removedTag = editingTags.value.splice(index, 1)[0]

  // 添加回可用标签（如果不在列表中）
  if (!availableTags.value.includes(removedTag)) {
    availableTags.value.push(removedTag)
    availableTags.value.sort()
  }
}

const saveTagChanges = async () => {
  try {
    await imageDataService.updateImageTags(props.image.id, editingTags.value)

    // 更新本地数据
    props.image.tags = [...editingTags.value]

    isEditingTags.value = false
    success('标签更新成功', '图片标签已成功更新')
  } catch (err) {
    error('更新失败', '无法更新标签')
  }
}

const loadAvailableTags = async () => {
  try {
    const allTags = await imageDataService.getAllTags()
    availableTags.value = allTags
      .map(tag => tag.name)
      .filter(tagName => !editingTags.value.includes(tagName))
      .sort()
  } catch (err) {
    console.error('加载可用标签失败:', err)
    availableTags.value = []
  }
}

// 上传到指定图床的辅助函数
const uploadToImageHost = async (file: File, config: any) => {
  return new Promise((resolve, reject) => {
    const formData = new FormData()
    formData.append(config.fileField, file)

    // 添加额外参数
    if (config.params) {
      Object.entries(config.params).forEach(([key, value]) => {
        formData.append(key, String(value))
      })
    }

    const xhr = new XMLHttpRequest()

    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        try {
          const data = config.responseType === 'json'
            ? JSON.parse(xhr.responseText)
            : xhr.responseText

          // 验证响应格式
          if (config.responseType === 'json' && config.successField) {
            const success = getNestedValue(data, config.successField)

            if (success !== config.successValue) {
              const error = config.errorField
                ? getNestedValue(data, config.errorField)
                : '上传失败'
              reject(new Error(String(error)))
              return
            }
          }

          // 提取URL
          const url = config.urlField
            ? getNestedValue(data, config.urlField)
            : data

          resolve({
            success: true,
            url: String(url),
            deleteUrl: undefined
          })
        } catch (error) {
          reject(new Error('响应解析失败'))
        }
      } else {
        reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`))
      }
    })

    xhr.addEventListener('error', () => {
      reject(new Error('网络错误'))
    })

    // 构建URL
    let url = config.apiUrl
    if (config.authType === 'query' && config.authKey) {
      url += `/${config.authKey}`
    }

    // 构建请求头
    const headers: Record<string, string> = {
      ...config.headers
    }

    if (config.authType === 'header' && config.authHeader && config.authKey) {
      const authValue = config.authPrefix ? `${config.authPrefix}${config.authKey}` : config.authKey
      headers[config.authHeader] = authValue
    }

    xhr.open(config.method || 'POST', url)

    // 设置请求头
    Object.entries(headers).forEach(([key, value]) => {
      xhr.setRequestHeader(key, value)
    })

    xhr.send(formData)
  })
}

// 获取嵌套对象值的辅助函数
const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}

// 上传到随机图床
const uploadToRandomHost = async () => {
  if (isUploading.value) return

  try {
    isUploading.value = true

    // 获取可用的图床配置
    const { imageHostService } = await import('@/services/imageHostService')
    const configs = await imageHostService.getEnabledConfigs()

    if (configs.length === 0) {
      error('上传失败', '没有可用的图床配置')
      return
    }

    // 随机选择一个图床
    const randomConfig = configs[Math.floor(Math.random() * configs.length)]

    // 获取图片数据
    const imageUrl = getPrimaryImageUrl(props.image)
    const response = await fetch(imageUrl)
    const blob = await response.blob()

    // 创建File对象
    const file = new File([blob], props.image.originalName, { type: props.image.type })

    // 上传到图床
    const uploadResult = await uploadToImageHost(file, randomConfig) as any

    if (uploadResult.success && uploadResult.url) {
      const newUrlRecord: any = {
        hostId: randomConfig.id,
        hostName: randomConfig.name,
        url: uploadResult.url,
        deleteUrl: uploadResult.deleteUrl,
        uploadTime: new Date(),
        status: 'active' as const
      }

      if (props.image.id) {
        // 保存到数据库
        const urlId = await imageDataService.addImageUrl(props.image.id, newUrlRecord)
        newUrlRecord.id = urlId
      }

      // 添加到本地数组
      props.image.urls.push(newUrlRecord)

      success('上传成功', `已上传到 ${randomConfig.name}`)
    } else {
      error('上传失败', uploadResult.error || '未知错误')
    }
  } catch (err) {
    console.error('上传到图床失败:', err)
    error('上传失败', '无法上传到图床')
  } finally {
    isUploading.value = false
  }
}

// 处理添加URL模态框背景点击
const handleAddUrlBackdropClick = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    showAddUrlModal.value = false
    newUrlInput.value = ''
    newUrlHostName.value = ''
  }
}

// 下载图片
const downloadImage = () => {
  const url = getPrimaryImageUrl(props.image)
  const link = document.createElement('a')
  link.href = url
  link.download = props.image.originalName
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 编辑图片
const editImage = () => {
  emit('edit', props.image)
}

// 删除图片
const deleteImage = () => {
  if (confirm('确定要删除这张图片吗？此操作不可撤销。')) {
    emit('delete', props.image.id)
  }
}

// 关闭模态框
const close = () => {
  visible.value = false
  setTimeout(() => {
    emit('close')
  }, 300)
}

// 点击背景关闭
const handleBackdropClick = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    close()
  }
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    close()
  }
}

onMounted(() => {
  visible.value = true
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
})
</script>
