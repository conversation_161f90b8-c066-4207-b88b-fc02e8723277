# Markdown 语法示例

这是一个展示所有支持的 Markdown 语法的示例文档。

## 基础文本格式

**粗体文本** 使用 `**粗体**` 或 `__粗体__`

*斜体文本* 使用 `*斜体*` 或 `_斜体_`

~~删除线文本~~ 使用 `~~删除线~~`

==高亮文本== 使用 `==高亮==`

++插入文本++ 使用 `++插入++`

## 标题层级

# 一级标题
## 二级标题
### 三级标题
#### 四级标题
##### 五级标题
###### 六级标题

## 列表

### 无序列表
- 项目 1
- 项目 2
  - 子项目 2.1
  - 子项目 2.2
- 项目 3

### 有序列表
1. 第一项
2. 第二项
   1. 子项目 2.1
   2. 子项目 2.2
3. 第三项

### 任务列表
- [x] 已完成的任务
- [ ] 未完成的任务
- [x] 另一个已完成的任务

## 链接和图片

[这是一个链接](https://example.com)

![这是一张图片](https://via.placeholder.com/300x200)

## 代码

### 行内代码
这是一段包含 `行内代码` 的文本。

### 代码块

```javascript
// JavaScript 代码示例
function hello(name) {
  console.log(`Hello, ${name}!`);
}

hello('World');
```

```python
# Python 代码示例
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

print(fibonacci(10))
```

```css
/* CSS 代码示例 */
.button {
  background-color: #007bff;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.button:hover {
  background-color: #0056b3;
}
```

## 表格

| 姓名 | 年龄 | 职业 | 城市 |
|------|------|------|------|
| 张三 | 25 | 工程师 | 北京 |
| 李四 | 30 | 设计师 | 上海 |
| 王五 | 28 | 产品经理 | 深圳 |

## 引用

> 这是一个引用块。
> 
> 引用可以包含多行文本，
> 甚至可以嵌套其他元素。
> 
> > 这是嵌套的引用。

## 分割线

---

## 上标和下标

水的化学式是 H~2~O

爱因斯坦的质能方程是 E = mc^2^

## 脚注

这是一个包含脚注的句子[^1]。

这是另一个脚注[^2]。

[^1]: 这是第一个脚注的内容。
[^2]: 这是第二个脚注的内容，可以包含更多详细信息。

## 缩写

HTML 是一种标记语言。

*[HTML]: HyperText Markup Language

## 定义列表

苹果
: 一种红色或绿色的水果
: 也是一家科技公司的名字

橙子
: 一种橙色的柑橘类水果
: 富含维生素C

## 警告框

::: info 信息
这是一个信息提示框，用于显示一般性信息。
:::

::: warning 警告
这是一个警告框，用于提醒用户注意某些重要事项。
:::

::: error 错误
这是一个错误提示框，用于显示错误信息。
:::

::: success 成功
这是一个成功提示框，用于显示操作成功的信息。
:::

## 键盘按键

按 <kbd>Ctrl</kbd> + <kbd>C</kbd> 复制文本。

按 <kbd>Ctrl</kbd> + <kbd>V</kbd> 粘贴文本。

## 数学公式（如果支持）

行内公式：$E = mc^2$

块级公式：
$$
\sum_{i=1}^{n} x_i = x_1 + x_2 + \cdots + x_n
$$

## 混合示例

这是一个包含 **粗体**、*斜体*、==高亮==、~~删除线~~ 和 `代码` 的复杂段落。

你可以创建包含链接的表格：

| 网站 | 链接 | 描述 |
|------|------|------|
| Google | [google.com](https://google.com) | 搜索引擎 |
| GitHub | [github.com](https://github.com) | 代码托管 |
| Stack Overflow | [stackoverflow.com](https://stackoverflow.com) | 编程问答 |

---

*这个示例展示了我们的 Markdown 渲染器支持的所有功能！*
