import { defineConfig, presetUno, presetIcons, presetTypography } from 'unocss'

export default defineConfig({
  presets: [
    presetUno(),
    presetIcons({
      collections: {
        heroicons: () => import('@iconify-json/heroicons/icons.json').then((i) => i.default),
        lucide: () => import('@iconify-json/lucide/icons.json').then((i) => i.default),
      },
    }),
    presetTypography(),
  ],
  theme: {
    colors: {
      primary: {
        25: 'var(--primary-25, #f0fdf4)',
        50: 'var(--primary-50, #ecfdf5)',
        100: 'var(--primary-100, #d1fae5)',
        200: 'var(--primary-200, #a7f3d0)',
        300: 'var(--primary-300, #6ee7b7)',
        400: 'var(--primary-400, #34d399)',
        500: 'var(--primary-500, #10b981)', // 主色调
        600: 'var(--primary-600, #059669)',
        700: 'var(--primary-700, #047857)',
        800: 'var(--primary-800, #065f46)',
        900: 'var(--primary-900, #064e3b)',
      },
      gray: {
        25: '#fcfcfc',
        50: '#f9fafb',
        100: '#f3f4f6',
        200: '#e5e7eb',
        300: '#d1d5db',
        400: '#9ca3af',
        500: '#6b7280',
        600: '#4b5563',
        700: '#374151',
        800: '#1f2937',
        900: '#111827',
        950: '#0c0c0c',
      },
      secondary: '#6b7280',
    },
    animation: {
      'fade-in': 'fadeIn 0.3s ease-in-out',
      'slide-up': 'slideUp 0.3s ease-out',
      'slide-down': 'slideDown 0.3s ease-out',
      'scale-in': 'scaleIn 0.2s ease-out',
      'bounce-in': 'bounceIn 0.5s ease-out',
      'pulse-slow': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
    },
    keyframes: {
      fadeIn: {
        '0%': { opacity: '0' },
        '100%': { opacity: '1' },
      },
      slideUp: {
        '0%': { transform: 'translateY(10px)', opacity: '0' },
        '100%': { transform: 'translateY(0)', opacity: '1' },
      },
      slideDown: {
        '0%': { transform: 'translateY(-10px)', opacity: '0' },
        '100%': { transform: 'translateY(0)', opacity: '1' },
      },
      scaleIn: {
        '0%': { transform: 'scale(0.95)', opacity: '0' },
        '100%': { transform: 'scale(1)', opacity: '1' },
      },
      bounceIn: {
        '0%': { transform: 'scale(0.3)', opacity: '0' },
        '50%': { transform: 'scale(1.05)' },
        '70%': { transform: 'scale(0.9)' },
        '100%': { transform: 'scale(1)', opacity: '1' },
      },
    },
  },
  shortcuts: {
    // 优化后的按钮样式 - 提高对比度和可访问性
    'btn-primary':
      'bg-primary-600 hover:bg-primary-700 active:bg-primary-800 text-white font-semibold px-4 py-2.5 rounded-xl transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-3 focus:ring-primary-300 dark:focus:ring-primary-600 border-0',
    'btn-secondary':
      'bg-primary-50 hover:bg-primary-100 active:bg-primary-200 text-primary-800 dark:bg-primary-900/40 dark:hover:bg-primary-800/50 dark:active:bg-primary-700/60 dark:text-primary-200 font-semibold px-4 py-2.5 rounded-xl transition-all duration-200 border border-primary-200 dark:border-primary-700 hover:border-primary-300 dark:hover:border-primary-600 focus:outline-none focus:ring-3 focus:ring-primary-300 dark:focus:ring-primary-600',
    'btn-outline':
      'bg-transparent hover:bg-primary-50 active:bg-primary-100 text-primary-700 dark:text-primary-300 dark:hover:bg-primary-900/20 dark:active:bg-primary-800/30 font-semibold px-4 py-2.5 rounded-xl transition-all duration-200 border-2 border-primary-300 dark:border-primary-600 hover:border-primary-400 dark:hover:border-primary-500 focus:outline-none focus:ring-3 focus:ring-primary-300 dark:focus:ring-primary-600',

    // 优化后的卡片样式 - 更好的层次和对比度
    card: 'bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200/60 dark:border-gray-700/60 hover:shadow-md hover:border-gray-300/80 dark:hover:border-gray-600/80 transition-all duration-200',
    'card-elevated':
      'bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200/60 dark:border-gray-700/60 hover:shadow-xl transition-all duration-200',
    'card-interactive':
      'bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200/60 dark:border-gray-700/60 hover:shadow-md hover:border-primary-300/60 dark:hover:border-primary-600/60 hover:-translate-y-0.5 transition-all duration-200 cursor-pointer',

    // 优化后的输入框样式 - 更好的焦点状态和对比度
    'input-field':
      'w-full bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-xl px-4 py-3 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-primary-500 dark:focus:border-primary-400 focus:ring-3 focus:ring-primary-200 dark:focus:ring-primary-800 transition-all duration-200 text-chinese min-h-[44px]',
    'input-sm':
      'w-full bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-primary-500 dark:focus:border-primary-400 focus:ring-2 focus:ring-primary-200 dark:focus:ring-primary-800 transition-all duration-200 text-chinese min-h-[36px]',
    'input-lg':
      'w-full bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-xl px-5 py-4 text-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-primary-500 dark:focus:border-primary-400 focus:ring-3 focus:ring-primary-200 dark:focus:ring-primary-800 transition-all duration-200 text-chinese min-h-[52px]',
    'input-error':
      'w-full bg-white dark:bg-gray-800 border-2 border-red-400 dark:border-red-500 rounded-xl px-4 py-3 text-gray-900 dark:text-gray-100 placeholder-red-400 dark:placeholder-red-400 focus:outline-none focus:border-red-500 dark:focus:border-red-400 focus:ring-3 focus:ring-red-200 dark:focus:ring-red-800 transition-all duration-200 text-chinese min-h-[44px]',
    'textarea-field':
      'w-full bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-xl px-4 py-3 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-primary-500 dark:focus:border-primary-400 focus:ring-3 focus:ring-primary-200 dark:focus:ring-primary-800 transition-all duration-200 resize-vertical min-h-[100px] text-chinese',

    // 优化后的文本颜色 - 更好的对比度
    'text-primary': 'text-gray-900 dark:text-gray-50',
    'text-secondary': 'text-gray-700 dark:text-gray-300',
    'text-tertiary': 'text-gray-600 dark:text-gray-400',
    'text-muted': 'text-gray-500 dark:text-gray-500',

    // 优化后的背景颜色
    'bg-primary': 'bg-white dark:bg-gray-900',
    'bg-secondary': 'bg-gray-50 dark:bg-gray-800',
    'bg-tertiary': 'bg-gray-100 dark:bg-gray-700',

    // 状态颜色 - 高对比度版本
    'text-success': 'text-green-700 dark:text-green-400',
    'text-warning': 'text-amber-700 dark:text-amber-400',
    'text-error': 'text-red-700 dark:text-red-400',
    'text-info': 'text-blue-700 dark:text-blue-400',

    'bg-success': 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800',
    'bg-warning': 'bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800',
    'bg-error': 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800',
    'bg-info': 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800',
  },
  safelist: [
    'animate-fade-in',
    'animate-slide-up',
    'animate-slide-down',
    'animate-scale-in',
    'animate-bounce-in',
    'animate-pulse-slow',
    'i-heroicons-adjustments-horizontal',
    'i-heroicons-cog-6-tooth',
    'i-heroicons-sun',
    'i-heroicons-moon',
    'i-heroicons-computer-desktop',
    'i-heroicons-academic-cap',
    'i-heroicons-home',
    'i-heroicons-book-open',
    'i-heroicons-magnifying-glass',
    'i-heroicons-plus',
    'i-heroicons-bold',
    'i-heroicons-italic',
    'i-heroicons-minus',
    'i-heroicons-code-bracket',
    'i-heroicons-code-bracket-square',
    'i-heroicons-link',
    'i-heroicons-photo',
    'i-heroicons-hashtag',
    'i-heroicons-list-bullet',
    'i-heroicons-numbered-list',
    'i-heroicons-chat-bubble-left-right',
    'i-heroicons-table-cells',
    'i-heroicons-eye',
    'i-heroicons-check-circle',
    'i-heroicons-sparkles',
    'i-heroicons-paint-brush',
    'i-heroicons-plus-circle',
    'i-heroicons-exclamation-triangle',
    'i-heroicons-question-mark-circle',
    'i-heroicons-chevron-right',
    'i-heroicons-folder',
    'i-heroicons-document',
    'i-heroicons-cog-6-tooth',
    'i-heroicons-wifi',
    'i-heroicons-information-circle',
    'i-heroicons-x-circle',
    'i-heroicons-swatch',
    'i-heroicons-x-mark',
  ],
})
