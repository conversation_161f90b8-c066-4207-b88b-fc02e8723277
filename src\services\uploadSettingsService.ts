// 上传设置服务
import { db } from '@/database'

export interface UploadSettings {
  // 默认上传设置
  defaultHostCount: number
  hostSelectionStrategy: 'random' | 'priority' | 'speed' | 'reliability'
  maxRetries: number
  retryDelay: number
  
  // 链接检测设置
  linkCheckEnabled: boolean
  checkInterval: number
  checkTimeout: number
  failureAction: 'mark' | 'notify' | 'backup' | 'remove'
  notifyMethods: string[]
  
  // 过期设置
  defaultExpiration: number
  expirationWarning: number
  expirationAction: 'keep' | 'archive' | 'delete'
  autoRenew: boolean
}

export interface LinkCheckResult {
  url: string
  status: 'active' | 'failed' | 'timeout'
  responseTime?: number
  statusCode?: number
  error?: string
  checkedAt: Date
}

class UploadSettingsService {
  private readonly SETTINGS_KEY = 'upload_settings'
  private checkTimer: number | null = null
  
  // 获取设置
  async getSettings(): Promise<UploadSettings> {
    try {
      const settings = localStorage.getItem(this.SETTINGS_KEY)
      if (settings) {
        return JSON.parse(settings)
      }
    } catch (error) {
      console.error('获取上传设置失败:', error)
    }
    
    // 返回默认设置
    return this.getDefaultSettings()
  }
  
  // 保存设置
  async saveSettings(settings: UploadSettings): Promise<void> {
    try {
      localStorage.setItem(this.SETTINGS_KEY, JSON.stringify(settings))
      
      // 重新启动链接检测定时器
      if (settings.linkCheckEnabled) {
        this.startLinkCheck(settings)
      } else {
        this.stopLinkCheck()
      }
    } catch (error) {
      console.error('保存上传设置失败:', error)
      throw error
    }
  }
  
  // 获取默认设置
  private getDefaultSettings(): UploadSettings {
    return {
      defaultHostCount: 2,
      hostSelectionStrategy: 'random',
      maxRetries: 3,
      retryDelay: 2,
      linkCheckEnabled: true,
      checkInterval: 7,
      checkTimeout: 15,
      failureAction: 'notify',
      notifyMethods: ['browser'],
      defaultExpiration: 0,
      expirationWarning: 7,
      expirationAction: 'keep',
      autoRenew: false
    }
  }
  
  // 启动链接检测
  async startLinkCheck(settings?: UploadSettings): Promise<void> {
    if (!settings) {
      settings = await this.getSettings()
    }
    
    if (!settings.linkCheckEnabled) {
      return
    }
    
    // 清除现有定时器
    this.stopLinkCheck()
    
    // 设置新的定时器
    const intervalMs = settings.checkInterval * 24 * 60 * 60 * 1000 // 转换为毫秒
    this.checkTimer = window.setInterval(() => {
      this.performLinkCheck(settings!)
    }, intervalMs)
    
    // 立即执行一次检测
    this.performLinkCheck(settings)
  }
  
  // 停止链接检测
  stopLinkCheck(): void {
    if (this.checkTimer) {
      clearInterval(this.checkTimer)
      this.checkTimer = null
    }
  }
  
  // 执行链接检测
  private async performLinkCheck(settings: UploadSettings): Promise<void> {
    try {
      console.log('开始执行链接检测...')
      
      // 获取所有图片的URL
      const images = await db.images.toArray()
      const allUrls: { imageId: number; urlId: number; url: string; hostName: string }[] = []
      
      for (const image of images) {
        const urls = await db.image_urls.where('image_id').equals(image.id!).toArray()
        for (const urlRecord of urls) {
          if (urlRecord.status === 'active') {
            allUrls.push({
              imageId: image.id!,
              urlId: urlRecord.id!,
              url: urlRecord.url,
              hostName: urlRecord.host_name
            })
          }
        }
      }
      
      console.log(`开始检测 ${allUrls.length} 个链接...`)
      
      // 批量检测链接
      const results = await this.checkUrls(allUrls.map(u => u.url), settings.checkTimeout)
      
      // 处理检测结果
      let failedCount = 0
      for (let i = 0; i < results.length; i++) {
        const result = results[i]
        const urlInfo = allUrls[i]
        
        if (result.status === 'failed' || result.status === 'timeout') {
          failedCount++
          
          // 更新数据库中的URL状态
          await db.image_urls.update(urlInfo.urlId, { status: 'failed' })
          
          // 根据设置执行相应操作
          await this.handleFailedUrl(urlInfo, result, settings)
        }
      }
      
      console.log(`链接检测完成，发现 ${failedCount} 个失效链接`)
      
      // 发送通知
      if (failedCount > 0 && settings.notifyMethods.includes('browser')) {
        this.sendNotification(`发现 ${failedCount} 个失效链接`, '点击查看详情')
      }
      
    } catch (error) {
      console.error('链接检测失败:', error)
    }
  }
  
  // 检测多个URL
  private async checkUrls(urls: string[], timeout: number): Promise<LinkCheckResult[]> {
    const results: LinkCheckResult[] = []
    
    // 并发检测，但限制并发数量
    const concurrency = 5
    for (let i = 0; i < urls.length; i += concurrency) {
      const batch = urls.slice(i, i + concurrency)
      const batchResults = await Promise.all(
        batch.map(url => this.checkSingleUrl(url, timeout))
      )
      results.push(...batchResults)
      
      // 添加延迟避免过于频繁的请求
      if (i + concurrency < urls.length) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }
    
    return results
  }
  
  // 检测单个URL
  private async checkSingleUrl(url: string, timeout: number): Promise<LinkCheckResult> {
    const startTime = Date.now()
    
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), timeout * 1000)
      
      const response = await fetch(url, {
        method: 'HEAD',
        mode: 'no-cors',
        signal: controller.signal
      })
      
      clearTimeout(timeoutId)
      
      const responseTime = Date.now() - startTime
      
      return {
        url,
        status: 'active',
        responseTime,
        statusCode: response.status,
        checkedAt: new Date()
      }
    } catch (error) {
      const responseTime = Date.now() - startTime
      
      if (error instanceof Error && error.name === 'AbortError') {
        return {
          url,
          status: 'timeout',
          responseTime,
          error: '请求超时',
          checkedAt: new Date()
        }
      }
      
      return {
        url,
        status: 'failed',
        responseTime,
        error: error instanceof Error ? error.message : '未知错误',
        checkedAt: new Date()
      }
    }
  }
  
  // 处理失效的URL
  private async handleFailedUrl(
    urlInfo: { imageId: number; urlId: number; url: string; hostName: string },
    result: LinkCheckResult,
    settings: UploadSettings
  ): Promise<void> {
    console.log(`处理失效链接: ${urlInfo.url} (${urlInfo.hostName})`)
    
    switch (settings.failureAction) {
      case 'mark':
        // 仅标记失效，已在上面更新了状态
        break
        
      case 'notify':
        // 标记并通知
        if (settings.notifyMethods.includes('console')) {
          console.warn(`链接失效: ${urlInfo.url} (${urlInfo.hostName})`)
        }
        break
        
      case 'backup':
        // 自动创建备份
        await this.createBackupForFailedUrl(urlInfo)
        break
        
      case 'remove':
        // 移除失效链接
        await db.image_urls.delete(urlInfo.urlId)
        console.log(`已移除失效链接: ${urlInfo.url}`)
        break
    }
  }
  
  // 为失效URL创建备份
  private async createBackupForFailedUrl(
    urlInfo: { imageId: number; urlId: number; url: string; hostName: string }
  ): Promise<void> {
    try {
      // 这里可以实现自动备份逻辑
      // 例如：重新上传到其他图床
      console.log(`尝试为失效链接创建备份: ${urlInfo.url}`)
      
      // 获取图片信息
      const image = await db.images.get(urlInfo.imageId)
      if (!image) return
      
      // 获取其他可用的URL
      const otherUrls = await db.image_urls
        .where('image_id').equals(urlInfo.imageId)
        .and(url => url.status === 'active' && url.id !== urlInfo.urlId)
        .toArray()
      
      if (otherUrls.length > 0) {
        // 如果有其他可用URL，可以尝试重新上传
        console.log(`找到 ${otherUrls.length} 个其他可用链接，可以用于创建备份`)
      }
      
    } catch (error) {
      console.error('创建备份失败:', error)
    }
  }
  
  // 发送浏览器通知
  private sendNotification(title: string, body: string): void {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(title, {
        body,
        icon: '/favicon.ico'
      })
    } else if ('Notification' in window && Notification.permission !== 'denied') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          new Notification(title, {
            body,
            icon: '/favicon.ico'
          })
        }
      })
    }
  }
  
  // 获取链接检测统计
  async getLinkCheckStats(): Promise<{
    total: number
    active: number
    failed: number
    lastCheck?: Date
  }> {
    try {
      const urls = await db.image_urls.toArray()
      const total = urls.length
      const active = urls.filter(url => url.status === 'active').length
      const failed = urls.filter(url => url.status === 'failed').length
      
      return {
        total,
        active,
        failed
      }
    } catch (error) {
      console.error('获取链接统计失败:', error)
      return {
        total: 0,
        active: 0,
        failed: 0
      }
    }
  }
}

// 创建单例实例
export const uploadSettingsService = new UploadSettingsService()
