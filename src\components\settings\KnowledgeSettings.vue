<template>
  <a-space direction="vertical" size="large" style="width: 100%">
    <!-- 知识库浏览设置 -->
    <a-card title="知识库浏览设置">
      <a-row :gutter="24">
        <!-- 标签加载设置 -->
        <a-col :span="12">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">标签加载配置</h4>

          <a-space direction="vertical" size="middle" style="width: 100%">
            <a-form-item label="初始加载标签数量">
              <a-space>
                <a-input-number v-model:value="settings.tags.initialLoad" :min="5" :max="50" style="width: 120px" />
                <span class="text-sm text-gray-500 dark:text-gray-400">个标签</span>
              </a-space>
              <template #extra>
                <div class="text-sm text-gray-500">页面首次加载时显示的标签数量</div>
              </template>
            </a-form-item>

            <a-form-item label="点击+号加载数量">
              <a-space>
                <a-input-number v-model:value="settings.tags.loadMore" :min="5" :max="30" style="width: 120px" />
                <span class="text-sm text-gray-500 dark:text-gray-400">个标签</span>
              </a-space>
              <template #extra>
                <div class="text-sm text-gray-500">点击显示更多按钮时加载的标签数量</div>
              </template>
            </a-form-item>
          </a-space>
        </a-col>

        <!-- 排序设置 -->
        <a-col :span="12">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">默认排序方式</h4>

          <a-space direction="vertical" size="middle" style="width: 100%">
            <a-form-item label="资源排序">
              <a-select v-model:value="settings.sorting.default" style="width: 100%" :options="[
                { label: '创建时间（最新优先）', value: 'created_desc' },
                { label: '创建时间（最早优先）', value: 'created_asc' },
                { label: '更新时间（最新优先）', value: 'updated_desc' },
                { label: '更新时间（最早优先）', value: 'updated_asc' },
                { label: '标题（A-Z）', value: 'title_asc' },
                { label: '标题（Z-A）', value: 'title_desc' },
                { label: '访问次数（高到低）', value: 'visits_desc' },
                { label: '访问次数（低到高）', value: 'visits_asc' }
              ]" />
            </a-form-item>

            <a-form-item label="标签排序">
              <a-select v-model:value="settings.sorting.tags" style="width: 100%" :options="[
                { label: '名称（A-Z）', value: 'name_asc' },
                { label: '名称（Z-A）', value: 'name_desc' },
                { label: '使用次数（高到低）', value: 'count_desc' },
                { label: '使用次数（低到高）', value: 'count_asc' },
                { label: '创建时间（最新优先）', value: 'created_desc' },
                { label: '创建时间（最早优先）', value: 'created_asc' }
              ]" />
            </a-form-item>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <!-- 卡片显示设置 -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">卡片显示设置</h3>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- 布局设置 -->
        <div class="space-y-4">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">布局配置</h4>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              每行卡片数量
            </label>
            <div class="grid grid-cols-4 gap-2">
              <button v-for="cols in [2, 3, 4, 5]" :key="cols" :class="[
                'px-3 py-2 text-sm rounded-lg border-2 transition-all',
                settings.layout.cardsPerRow === cols
                  ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 text-gray-700 dark:text-gray-300'
              ]" @click="settings.layout.cardsPerRow = cols">
                {{ cols }} 列
              </button>
            </div>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">在大屏幕上每行显示的卡片数量</p>
          </div>

          <BaseSelect v-model="settings.layout.cardSpacing" label="卡片间距" :options="[
            { label: '紧凑（4px）', value: 'compact' },
            { label: '正常（8px）', value: 'normal' },
            { label: '舒适（16px）', value: 'comfortable' },
            { label: '宽松（24px）', value: 'spacious' }
          ]" />
        </div>

        <!-- 加载设置 -->
        <div class="space-y-4">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">加载配置</h4>

          <BaseRadioGroup v-model="settings.loading.type" label="加载方式" :options="[
            { label: '分页加载', value: 'pagination' },
            { label: '懒加载（无限滚动）', value: 'infinite' }
          ]" />

          <div class="flex items-end space-x-2">
            <div class="flex-1">
              <BaseInput v-model="settings.loading.pageSize" type="number"
                :label="`${settings.loading.type === 'pagination' ? '每页' : '每次'}加载数量`" :min="6" :max="50" :step="6"
                :hint="settings.loading.type === 'pagination' ? '每页显示的卡片数量' : '滚动到底部时加载的卡片数量'" />
            </div>
            <span class="text-sm text-gray-500 dark:text-gray-400 pb-2">个卡片</span>
          </div>

          <div v-if="settings.loading.type === 'infinite'" class="flex items-end space-x-2">
            <div class="flex-1">
              <BaseInput v-model="settings.loading.preloadDistance" type="number" label="预加载距离" :min="100" :max="1000"
                :step="100" hint="距离底部多少像素时开始预加载" />
            </div>
            <span class="text-sm text-gray-500 dark:text-gray-400 pb-2">像素</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 性能设置 -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">性能优化</h3>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-4">
          <BaseToggle v-model="settings.performance.virtualScroll" label="启用虚拟滚动" hint="大量数据时提升性能" />

          <BaseToggle v-model="settings.performance.lazyImages" label="图片懒加载" hint="延迟加载卡片图片" />
        </div>

        <div class="space-y-4">
          <BaseSelect v-model="settings.performance.cacheStrategy" label="缓存策略" :options="[
            { label: '不缓存', value: 'none' },
            { label: '内存缓存', value: 'memory' },
            { label: '本地存储缓存', value: 'localStorage' },
            { label: 'IndexedDB缓存', value: 'indexedDB' }
          ]" />

          <BaseInput v-model="settings.performance.cacheTime" type="number" label="缓存时间（分钟）" :min="1" :max="1440" />
        </div>
      </div>
    </div>

    <!-- 保存按钮 -->
    <div class="flex justify-end space-x-3">
      <BaseButton variant="outline" @click="resetToDefaults">
        恢复默认
      </BaseButton>
      <BaseButton variant="primary" :loading="saving" @click="saveSettings">
        {{ saving ? '保存中...' : '保存设置' }}
      </BaseButton>
    </div>
  </a-space>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { knowledgeSettingsService, type KnowledgeSettings } from '@/services/knowledgeSettingsService'
import BaseInput from '@/components/ui/BaseInput.vue'
import BaseSelect from '@/components/ui/BaseSelect.vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import BaseRadioGroup from '@/components/ui/BaseRadioGroup.vue'
import BaseToggle from '@/components/ui/BaseToggle.vue'
import { showSuccess, showError } from '@/utils/notification'

// 状态
const settings = ref<KnowledgeSettings>(knowledgeSettingsService.getSettings())
const saving = ref(false)

// 设置变更监听器
const handleSettingsChange = (newSettings: KnowledgeSettings) => {
  settings.value = newSettings
}

// 保存设置
const saveSettings = async () => {
  try {
    saving.value = true
    knowledgeSettingsService.saveSettings(settings.value)

    // 应用CSS变量
    knowledgeSettingsService.applyCSSVariables()

    // 显示成功提示
    showSuccess('设置保存成功！', '您的知识库浏览设置已更新')
  } catch (error) {
    console.error('保存知识库设置失败:', error)
    showError('保存设置失败', '请检查网络连接后重试')
  } finally {
    saving.value = false
  }
}

// 恢复默认设置
const resetToDefaults = () => {
  if (confirm('确定要恢复默认设置吗？这将覆盖当前的所有配置。')) {
    knowledgeSettingsService.resetToDefaults()
    settings.value = knowledgeSettingsService.getSettings()
  }
}

// 初始化
onMounted(() => {
  // 添加设置变更监听器
  knowledgeSettingsService.addListener(handleSettingsChange)

  // 应用当前CSS变量
  knowledgeSettingsService.applyCSSVariables()
})



// 清理
onUnmounted(() => {
  knowledgeSettingsService.removeListener(handleSettingsChange)
})
</script>
