import { ref, computed, watch, readonly } from 'vue'
import type { ThemeMode } from '@/types'
import { themeSettingsService } from '@/services/themeSettingsService'

const THEME_KEY = 'knowledge-theme'

// 主题状态
const themeMode = ref<ThemeMode>('system')
const isDark = ref(false)

// 从localStorage加载主题设置
const loadTheme = () => {
  // 首先尝试加载用户当前会话的主题选择
  const saved = localStorage.getItem(THEME_KEY)
  if (saved && ['light', 'dark', 'system'].includes(saved)) {
    themeMode.value = saved as ThemeMode
  } else {
    // 如果没有当前会话的选择，使用默认主题设置
    const themeSettings = themeSettingsService.getSettings()
    themeMode.value = themeSettings.defaultMode
  }
}

// 保存主题设置到localStorage
const saveTheme = (mode: ThemeMode) => {
  localStorage.setItem(THEME_KEY, mode)
}

// 检测系统主题
const getSystemTheme = () => {
  return window.matchMedia('(prefers-color-scheme: dark)').matches
}

// 应用主题到DOM
const applyTheme = (dark: boolean) => {
  const html = document.documentElement
  if (dark) {
    html.classList.add('dark')
  } else {
    html.classList.remove('dark')
  }
}

// 更新实际的暗色模式状态
const updateDarkMode = () => {
  let dark = false

  switch (themeMode.value) {
    case 'dark':
      dark = true
      break
    case 'light':
      dark = false
      break
    case 'system':
      dark = getSystemTheme()
      break
  }

  isDark.value = dark
  applyTheme(dark)
}

// 监听主题模式变化
watch(
  themeMode,
  (newMode) => {
    saveTheme(newMode)
    updateDarkMode()
  },
  { immediate: true },
)

// 监听系统主题变化
const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
mediaQuery.addEventListener('change', () => {
  if (themeMode.value === 'system') {
    updateDarkMode()
  }
})

// 初始化主题
loadTheme()
updateDarkMode()

// 初始化自定义主题色
themeSettingsService.applyCustomColor()

export const useTheme = () => {
  // 切换主题模式
  const setThemeMode = (mode: ThemeMode) => {
    themeMode.value = mode
  }

  // 切换暗色模式（在light和dark之间切换）
  const toggleDarkMode = () => {
    if (themeMode.value === 'system') {
      // 如果当前是系统模式，根据系统主题决定切换方向
      setThemeMode(getSystemTheme() ? 'light' : 'dark')
    } else {
      // 在light和dark之间切换
      setThemeMode(themeMode.value === 'light' ? 'dark' : 'light')
    }
  }

  // 主题图标
  const themeIcon = computed(() => {
    switch (themeMode.value) {
      case 'light':
        return 'i-heroicons-sun'
      case 'dark':
        return 'i-heroicons-moon'
      case 'system':
        return 'i-heroicons-computer-desktop'
      default:
        return 'i-heroicons-sun'
    }
  })

  // 主题名称
  const themeName = computed(() => {
    switch (themeMode.value) {
      case 'light':
        return '浅色模式'
      case 'dark':
        return '深色模式'
      case 'system':
        return '跟随系统'
      default:
        return '浅色模式'
    }
  })

  return {
    themeMode: readonly(themeMode),
    isDark: readonly(isDark),
    themeIcon,
    themeName,
    setThemeMode,
    toggleDarkMode,
  }
}

// 导出只读的响应式引用，供其他地方使用
export { isDark as readonly }
