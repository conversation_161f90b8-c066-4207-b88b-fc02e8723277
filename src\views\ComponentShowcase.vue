<template>
  <div class="page-content-wrapper bg-gray-50 dark:bg-gray-900">
    <div class="content-container py-8">
      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-primary mb-4 heading-chinese">组件样式展示</h1>
        <div class="reading-container-wide">
          <p class="text-secondary text-lg text-chinese paragraph-chinese-no-indent">
            自定义圆角输入框设计，带清空功能
          </p>
        </div>
      </div>

      <!-- 输入框样式展示 -->
      <section class="mb-16">
        <div class="mb-8">
          <h2 class="text-3xl font-bold text-primary mb-4 heading-chinese">自定义圆角输入框</h2>
          <div class="reading-container">
            <p class="text-secondary text-chinese paragraph-chinese-no-indent">
              带清空功能的现代化输入框设计，提供更好的用户体验
            </p>
          </div>
        </div>

        <!-- 输入框展示 -->
        <div
          class="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 max-w-2xl mx-auto">
          <h3 class="text-lg font-semibold text-primary mb-6 heading-chinese">带清空功能的输入框</h3>
          <div class="space-y-6">
            <!-- 用户名输入框 -->
            <div>
              <label class="block text-sm font-medium text-secondary mb-2 text-chinese">用户名</label>
              <div class="custom-input-wrapper">
                <div class="custom-input-icon">
                  <div class="i-heroicons-user w-5 h-5 text-gray-500"></div>
                </div>
                <input type="text" placeholder="请输入用户名" class="custom-input-field" v-model="inputValues.username">
                <button v-if="inputValues.username" @click="clearInput('username')" class="custom-clear-button"
                  type="button" title="清空">
                  <div class="i-heroicons-x-mark w-4 h-4"></div>
                </button>
              </div>
            </div>

            <!-- 邮箱输入框 -->
            <div>
              <label class="block text-sm font-medium text-secondary mb-2 text-chinese">邮箱地址</label>
              <div class="custom-input-wrapper">
                <div class="custom-input-icon">
                  <div class="i-heroicons-envelope w-5 h-5 text-gray-500"></div>
                </div>
                <input type="email" placeholder="请输入邮箱地址" class="custom-input-field" v-model="inputValues.email">
                <button v-if="inputValues.email" @click="clearInput('email')" class="custom-clear-button" type="button"
                  title="清空">
                  <div class="i-heroicons-x-mark w-4 h-4"></div>
                </button>
              </div>
            </div>

            <!-- 密码输入框 -->
            <div>
              <label class="block text-sm font-medium text-secondary mb-2 text-chinese">密码</label>
              <div class="custom-input-wrapper">
                <div class="custom-input-icon">
                  <div class="i-heroicons-lock-closed w-5 h-5 text-gray-500"></div>
                </div>
                <input type="password" placeholder="请输入密码" class="custom-input-field" v-model="inputValues.password">
                <button v-if="inputValues.password" @click="clearInput('password')" class="custom-clear-button"
                  type="button" title="清空">
                  <div class="i-heroicons-x-mark w-4 h-4"></div>
                </button>
              </div>
            </div>

            <!-- 搜索输入框 -->
            <div>
              <label class="block text-sm font-medium text-secondary mb-2 text-chinese">搜索</label>
              <div class="custom-input-wrapper">
                <div class="custom-input-icon">
                  <div class="i-heroicons-magnifying-glass w-5 h-5 text-gray-500"></div>
                </div>
                <input type="search" placeholder="搜索内容..." class="custom-input-field" v-model="inputValues.search">
                <button v-if="inputValues.search" @click="clearInput('search')" class="custom-clear-button"
                  type="button" title="清空">
                  <div class="i-heroicons-x-mark w-4 h-4"></div>
                </button>
              </div>
            </div>

            <!-- 文本域 -->
            <div>
              <label class="block text-sm font-medium text-secondary mb-2 text-chinese">个人简介</label>
              <div class="relative">
                <textarea placeholder="请简单介绍一下自己..." class="custom-textarea-field" rows="4"
                  v-model="inputValues.bio"></textarea>
                <button v-if="inputValues.bio" @click="clearInput('bio')" class="custom-textarea-clear-button"
                  type="button" title="清空">
                  <div class="i-heroicons-x-mark w-4 h-4"></div>
                </button>
              </div>
            </div>
          </div>

          <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <p class="text-xs text-tertiary text-chinese mb-2">设计特点：</p>
            <ul class="text-xs text-tertiary text-chinese space-y-1">
              <li>• 12px圆角，无边框设计</li>
              <li>• 默认：gray-50背景，悬停：gray-100</li>
              <li>• 聚焦：白色背景 + 轻微主题色边框</li>
              <li>• 左侧图标，右侧圆形清空按钮</li>
              <li>• 智能显示：有内容时才显示清空按钮</li>
              <li>• 悬停效果：按钮放大1.1倍，增强交互反馈</li>
              <li>• 符合WCAG 2.1 AA标准</li>
            </ul>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const inputValues = ref({
  username: '',
  email: '',
  password: '',
  search: '',
  bio: ''
})

const clearInput = (field: keyof typeof inputValues.value) => {
  inputValues.value[field] = ''
}
</script>

<style scoped>
/* 输入框容器样式 */
.custom-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #f9fafb;
  border-radius: 12px;
  transition: all 0.2s ease;
  min-height: 44px;
}

.custom-input-wrapper:hover {
  background-color: #f3f4f6;
}

.custom-input-wrapper:focus-within {
  background-color: #ffffff;
  border: 1px solid var(--primary-500, #3b82f6);
  box-shadow: none;
}

/* 暗色模式 */
:root.dark .custom-input-wrapper {
  background-color: #374151;
}

:root.dark .custom-input-wrapper:hover {
  background-color: #4b5563;
}

:root.dark .custom-input-wrapper:focus-within {
  background-color: #1f2937;
  border: 1px solid var(--primary-400, #60a5fa);
  box-shadow: none;
}

/* 左侧图标样式 */
.custom-input-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 12px;
  padding-right: 8px;
  color: #6b7280;
}

:root.dark .custom-input-icon {
  color: #9ca3af;
}

/* 输入框样式 */
.custom-input-field {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  padding: 12px 8px 12px 0;
  font-size: 16px;
  color: #111827;
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei', '微软雅黑', sans-serif;
  line-height: 1.75;
  letter-spacing: 0.02em;
}

.custom-input-field::placeholder {
  color: #6b7280;
}

:root.dark .custom-input-field {
  color: #f9fafb;
}

:root.dark .custom-input-field::placeholder {
  color: #9ca3af;
}

/* 清空按钮样式 - 圆形设计 */
.custom-clear-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  color: #9ca3af;
  background: transparent;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.custom-clear-button:hover {
  color: #6b7280;
  background-color: rgba(0, 0, 0, 0.08);
  opacity: 1;
  transform: scale(1.1);
}

:root.dark .custom-clear-button {
  color: #6b7280;
}

:root.dark .custom-clear-button:hover {
  color: #9ca3af;
  background-color: rgba(255, 255, 255, 0.15);
  transform: scale(1.1);
}

/* 文本域样式 */
.custom-textarea-field {
  width: 100%;
  background-color: #f9fafb;
  border: none;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 16px;
  color: #111827;
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei', '微软雅黑', sans-serif;
  line-height: 1.75;
  letter-spacing: 0.02em;
  resize: vertical;
  min-height: 100px;
  transition: all 0.2s ease;
  outline: none;
}

.custom-textarea-field::placeholder {
  color: #6b7280;
}

.custom-textarea-field:hover {
  background-color: #f3f4f6;
}

.custom-textarea-field:focus {
  background-color: #ffffff;
  border: 1px solid var(--primary-500, #3b82f6);
  box-shadow: none;
}

:root.dark .custom-textarea-field {
  background-color: #374151;
  color: #f9fafb;
}

:root.dark .custom-textarea-field::placeholder {
  color: #9ca3af;
}

:root.dark .custom-textarea-field:hover {
  background-color: #4b5563;
}

:root.dark .custom-textarea-field:focus {
  background-color: #1f2937;
  border: 1px solid var(--primary-400, #60a5fa);
  box-shadow: none;
}

/* 文本域清空按钮 - 圆形设计 */
.custom-textarea-clear-button {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: #9ca3af;
  background: transparent;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.custom-textarea-clear-button:hover {
  color: #6b7280;
  background-color: rgba(0, 0, 0, 0.08);
  opacity: 1;
  transform: scale(1.1);
}

:root.dark .custom-textarea-clear-button {
  color: #6b7280;
}

:root.dark .custom-textarea-clear-button:hover {
  color: #9ca3af;
  background-color: rgba(255, 255, 255, 0.15);
  transform: scale(1.1);
}
</style>