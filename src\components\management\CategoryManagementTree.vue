<template>
  <div class="divide-y divide-gray-200 dark:divide-gray-700">
    <draggable v-model="localCategories" group="categories" item-key="id" handle=".drag-handle" :animation="200"
      ghost-class="ghost" chosen-class="chosen" drag-class="drag" @end="handleDragEnd">
      <template #item="{ element: category }">
        <CategoryManagementItem :key="category.id" :category="category" :level="0" @edit="$emit('edit', $event)"
          @delete="$emit('delete', $event)" @move="$emit('move', $event.categoryId, $event.newParentId)"
          @reorder="handleReorder" />
      </template>
    </draggable>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import draggable from 'vuedraggable'
import CategoryManagementItem from './CategoryManagementItem.vue'
import type { CategoryWithChildren } from '@/types'

interface Props {
  categories: CategoryWithChildren[]
}

interface Emits {
  (e: 'edit', category: CategoryWithChildren): void
  (e: 'delete', category: CategoryWithChildren): void
  (e: 'move', data: { categoryId: number; newParentId: number }): void
  (e: 'reorder', data: { categoryIds: number[]; parentId: number }): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 本地分类数据，用于拖拽
const localCategories = ref<CategoryWithChildren[]>([])

// 监听props变化，更新本地数据
watch(() => props.categories, (newCategories) => {
  localCategories.value = [...newCategories]
}, { immediate: true, deep: true })

// 处理拖拽结束
const handleDragEnd = (evt: any) => {
  const { oldIndex, newIndex } = evt

  if (oldIndex !== newIndex) {
    // 发出重新排序事件
    const categoryIds = localCategories.value.map(cat => cat.id!)
    emit('reorder', { categoryIds, parentId: 0 })
  }
}

// 处理子分类重新排序
const handleReorder = (data: { categoryIds: number[]; parentId: number }) => {
  emit('reorder', data)
}
</script>

<style scoped>
/* 拖拽样式 */
.ghost {
  opacity: 0.5;
  background: #f3f4f6;
  border: 2px dashed #d1d5db;
}

.chosen {
  background: #eff6ff;
  border: 1px solid #3b82f6;
}

.drag {
  transform: rotate(5deg);
  opacity: 0.8;
}

/* 拖拽手柄样式 */
.drag-handle {
  cursor: grab;
  color: #9ca3af;
  transition: color 0.2s;
}

.drag-handle:hover {
  color: #6b7280;
}

.drag-handle:active {
  cursor: grabbing;
}
</style>
