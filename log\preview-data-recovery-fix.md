# 编辑资源预览数据丢失问题修复

## 🐛 问题描述

在编辑资源界面，用户填写表单内容后点击"预览效果"按钮跳转到预览页面，当从预览页面返回编辑界面时，所有已填写的表单内容都会丢失，用户需要重新填写所有信息。

## 🔍 问题分析

### 1. **问题原因**
- 点击预览时，表单数据被保存到 `sessionStorage`
- 跳转到预览页面后，再返回编辑页面时会重新初始化组件
- 初始化时只检查是否为编辑模式，直接调用 `loadResource()` 加载原始数据
- 没有检查 `sessionStorage` 中是否有用户修改后的预览数据
- 导致用户的修改内容被原始数据覆盖

### 2. **数据流程问题**
```
用户填写表单 → 点击预览 → 保存到sessionStorage → 跳转预览页面
                                                    ↓
返回编辑页面 ← 重新初始化 ← 加载原始数据 ← 忽略sessionStorage数据
```

## 🔧 修复方案

### 1. **改进数据保存逻辑**

**修复前**：只保存基本表单数据
```typescript
const handlePreview = () => {
  const previewData = {
    ...form.value,
    selectedTags: selectedTags.value,
    selectedCategory: selectedCategory.value,
    isPreview: true,
    previewId: Date.now().toString()
  }
  
  sessionStorage.setItem('resource-preview-data', JSON.stringify(previewData))
}
```

**修复后**：保存完整的上下文信息
```typescript
const handlePreview = () => {
  const previewData = {
    ...form.value,
    selectedTags: selectedTags.value,
    selectedCategory: selectedCategory.value,
    isPreview: true,
    isEditing: isEditing.value,        // 保存编辑状态
    originalResourceId: route.query.id, // 保存原始资源ID
    previewId: Date.now().toString()
  }
  
  sessionStorage.setItem('resource-preview-data', JSON.stringify(previewData))
}
```

### 2. **添加数据恢复方法**

```typescript
// 恢复预览数据
const restorePreviewData = () => {
  const previewData = sessionStorage.getItem('resource-preview-data')
  if (!previewData) return false

  try {
    const parsedData = JSON.parse(previewData)
    if (parsedData.isPreview && parsedData.originalResourceId === route.query.id) {
      // 恢复表单数据
      form.value = {
        url: parsedData.url || '',
        title: parsedData.title || '',
        description: parsedData.description || '',
        cover_image_url: parsedData.cover_image_url || '',
        category_id: parsedData.category_id || null,
        tag_ids: parsedData.tag_ids || []
      }
      
      // 清除预览数据
      sessionStorage.removeItem('resource-preview-data')
      console.log('已恢复预览数据到表单')
      return true
    }
  } catch (error) {
    console.error('恢复预览数据失败:', error)
    sessionStorage.removeItem('resource-preview-data')
  }
  
  return false
}
```

### 3. **改进初始化逻辑**

**修复前**：直接加载原始数据
```typescript
onMounted(async () => {
  await Promise.all([loadCategories(), loadTags()])
  
  if (isEditing.value) {
    await loadResource() // 直接加载原始数据，覆盖用户修改
  }
})
```

**修复后**：优先恢复预览数据
```typescript
onMounted(async () => {
  await Promise.all([loadCategories(), loadTags()])
  
  // 如果是编辑模式，先尝试恢复预览数据，如果没有则正常加载资源
  if (isEditing.value) {
    const restored = restorePreviewData()
    if (!restored) {
      await loadResource()
    }
  }
})
```

## ✨ 修复效果

### 1. **数据流程优化**
```
用户填写表单 → 点击预览 → 保存到sessionStorage → 跳转预览页面
                                                    ↓
返回编辑页面 ← 恢复用户数据 ← 检查sessionStorage ← 优先恢复预览数据
```

### 2. **用户体验提升**
- ✅ **数据保持**：从预览页面返回时，所有用户填写的内容都会保持
- ✅ **无缝切换**：预览和编辑之间的切换更加流畅
- ✅ **防止丢失**：避免用户辛苦填写的内容意外丢失
- ✅ **智能恢复**：只在相同资源的编辑会话中恢复数据

### 3. **安全性保障**
- ✅ **ID 验证**：通过 `originalResourceId` 确保只恢复对应资源的数据
- ✅ **状态检查**：通过 `isPreview` 标记确保只恢复预览数据
- ✅ **错误处理**：解析失败时自动清理无效数据
- ✅ **自动清理**：恢复后自动清除 sessionStorage 中的临时数据

## 🔧 技术实现

### 1. **数据结构**
```typescript
interface PreviewData {
  // 表单数据
  url: string
  title: string
  description: string
  cover_image_url: string
  category_id: number | null
  tag_ids: number[]
  
  // 上下文信息
  selectedTags: Tag[]
  selectedCategory: Category
  isPreview: boolean
  isEditing: boolean
  originalResourceId: string
  previewId: string
}
```

### 2. **恢复条件**
- 存在预览数据
- 数据标记为预览状态 (`isPreview: true`)
- 原始资源ID匹配当前编辑的资源ID
- 数据解析成功

### 3. **错误处理**
- JSON 解析失败时自动清理数据
- 数据验证失败时回退到原始加载逻辑
- 提供详细的控制台日志用于调试

## 🎯 测试场景

### 1. **正常预览流程**
1. 编辑资源，填写表单内容
2. 点击"预览效果"按钮
3. 查看预览页面
4. 返回编辑页面
5. ✅ 验证：所有填写的内容都应该保持不变

### 2. **多次预览**
1. 编辑资源，填写内容
2. 预览 → 返回 → 修改内容 → 再次预览 → 返回
3. ✅ 验证：每次返回都应该保持最新的修改内容

### 3. **页面刷新**
1. 编辑资源，填写内容
2. 点击预览
3. 在预览页面刷新浏览器
4. 返回编辑页面
5. ✅ 验证：应该加载原始数据（因为预览数据已清理）

### 4. **不同资源编辑**
1. 编辑资源A，点击预览
2. 直接访问资源B的编辑页面
3. ✅ 验证：不应该恢复资源A的预览数据

## 🎉 修复成果

1. **彻底解决数据丢失问题**：用户从预览返回时不再丢失填写的内容
2. **提升用户体验**：预览功能变得真正实用，用户可以放心使用
3. **增强数据安全性**：通过多重验证确保数据恢复的准确性
4. **保持向后兼容**：不影响现有的编辑和创建功能
5. **提供调试支持**：详细的日志输出便于问题排查

现在编辑资源的预览功能已经完全可靠，用户可以安心地在编辑和预览之间切换！
