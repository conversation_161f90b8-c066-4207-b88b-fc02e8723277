<template>
  <BaseDropdown>
    <template #trigger>
      <BaseButton variant="outline" size="sm">
        <div class="i-heroicons-folder mr-2"></div>
        {{ selectedCategory ? getCategoryPath(selectedCategory) : '所有分类' }}
        <div class="i-heroicons-chevron-down ml-2"></div>
      </BaseButton>
    </template>

    <div class="w-64 max-h-80 overflow-y-auto">
      <!-- 所有分类选项 -->
      <DropdownItem 
        text="所有分类" 
        :icon="!selectedCategory ? 'i-heroicons-check' : ''"
        @click="handleSelect(null)" 
      />
      
      <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
      
      <!-- 分类树 -->
      <CategoryTreeItem
        v-for="category in categories"
        :key="category.id"
        :category="category"
        :selected-category="selectedCategory"
        :level="0"
        @select="handleSelect"
      />
    </div>
  </BaseDropdown>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import BaseDropdown from '@/components/ui/BaseDropdown.vue'
import DropdownItem from '@/components/ui/DropdownItem.vue'
import CategoryTreeItem from './CategoryTreeItem.vue'
import type { Category, CategoryWithChildren } from '@/types'

interface Props {
  modelValue?: Category | null
  categories: CategoryWithChildren[]
}

interface Emits {
  (e: 'update:modelValue', value: Category | null): void
  (e: 'change', value: Category | null): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const selectedCategory = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 获取分类路径字符串
const getCategoryPath = (category: Category): string => {
  // 这里简化处理，只显示分类名称
  // 如果需要显示完整路径，可以通过递归查找父分类
  return `${category.name} (${category.resource_count})`
}

// 处理选择
const handleSelect = (category: Category | null) => {
  selectedCategory.value = category
  emit('change', category)
}
</script>
