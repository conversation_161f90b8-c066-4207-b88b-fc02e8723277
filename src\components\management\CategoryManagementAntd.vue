<template>
  <a-space direction="vertical" size="middle" style="width: 100%">


    <!-- 分类管理 -->
    <a-card size="small" class="category-management-card">
      <template #title>
        <div class="card-title-wrapper">
          <span class="card-title">分类管理</span>
          <a-tooltip title="查看操作说明" placement="bottom">
            <a-button type="text" size="small" class="help-icon-btn" @click="showHelpModal = true">
              <QuestionCircleOutlined />
            </a-button>
          </a-tooltip>
        </div>
      </template>

      <!-- 分类统计 -->
      <a-row :gutter="12" class="mb-3">
        <a-col :span="6">
          <div class="stat-card">
            <div class="stat-icon stat-icon-primary">
              <FolderOutlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ totalCategories }}</div>
              <div class="stat-label">总分类数</div>
            </div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-card">
            <div class="stat-icon stat-icon-success">
              <FolderOpenOutlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ topLevelCategories }}</div>
              <div class="stat-label">顶级分类</div>
            </div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-card">
            <div class="stat-icon stat-icon-info">
              <FolderAddOutlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ subCategories }}</div>
              <div class="stat-label">子分类</div>
            </div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-card">
            <div class="stat-icon stat-icon-warning">
              <FolderOutlined />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ emptyCategories }}</div>
              <div class="stat-label">空分类</div>
            </div>
          </div>
        </a-col>
      </a-row>

      <!-- 操作工具栏 -->
      <div class="toolbar-section">
        <div class="toolbar-left">
          <a-button type="primary" @click="showAddForm = !showAddForm">
            <PlusOutlined />
            {{ showAddForm ? '收起表单' : '添加新分类' }}
          </a-button>
        </div>
        <div class="toolbar-right">
          <a-space>
            <a-input-search v-model:value="searchText" placeholder="搜索分类" @search="handleSearch" />
            <a-button @click="handleRefresh" :loading="loading">
              <ReloadOutlined v-if="!loading" />
              刷新
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 添加分类表单 -->
      <div class="add-form-container">
        <a-collapse-transition>
          <div v-show="showAddForm" class="add-category-form">
            <a-form :model="newCategory" :rules="newCategoryRules" layout="vertical" @finish="handleAddCategory"
              class="form-content">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="分类名称" name="name">
                    <a-input v-model:value="newCategory.name" placeholder="请输入分类名称" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="父分类" name="parent_id">
                    <a-tree-select v-model:value="newCategory.parent_id" :tree-data="categoryTreeData"
                      placeholder="选择父分类（可选）" allow-clear tree-default-expand-all />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-form-item>
                <a-space>
                  <a-button type="primary" html-type="submit" :loading="adding">
                    <PlusOutlined v-if="!adding" />
                    {{ adding ? '添加中...' : '添加分类' }}
                  </a-button>
                  <a-button @click="resetForm">
                    重置
                  </a-button>
                  <a-button @click="showAddForm = false">
                    取消
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </div>
        </a-collapse-transition>
      </div>

      <!-- 批量操作工具栏 -->
      <div v-if="selectedRowKeys.length > 0"
        class="mb-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
        <div class="flex items-center justify-between">
          <span class="text-blue-700 dark:text-blue-300">
            已选择 {{ selectedRowKeys.length }} 个分类
          </span>
          <a-space>
            <a-button type="primary" size="small" @click="showBatchMoveModal = true">
              <ArrowRightOutlined />
              批量移动
            </a-button>
            <a-button danger size="small" @click="handleBatchDelete">
              <DeleteOutlined />
              批量删除
            </a-button>
            <a-button size="small" @click="selectedRowKeys = []">
              取消选择
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 分类树形表格 -->
      <a-table :columns="columns" :data-source="filteredCategories" :pagination="false" :loading="loading" row-key="id"
        :default-expand-all-rows="!searchText" :expanded-row-keys="searchText ? expandedRowKeys : undefined"
        size="small" :indent-size="20" :row-selection="{
          selectedRowKeys: selectedRowKeys,
          onChange: onSelectChange,
          onSelectAll: onSelectAll,
        }" :expand-icon="customExpandIcon" :custom-row="customRowProps" @expand="handleRowExpand">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <div class="flex items-center">
              <FolderOutlined :class="[
                'mr-2',
                record.children && record.children.length > 0 ? 'text-blue-500' : 'text-gray-400'
              ]" />

              <!-- 原地编辑输入框 -->
              <a-input v-if="editingId === record.id" v-model:value="editingName" class="inline-edit-input" size="small"
                @pressEnter="handleSaveEdit(record)" @keyup.esc="handleCancelEdit" :bordered="false" :auto-focus="true"
                ref="editInputRef" tabindex="0" />

              <!-- 普通显示 -->
              <span v-else class="font-medium">{{ record.name }}</span>

              <a-tag v-if="record.parent_id === 0" color="green" size="small" class="ml-2">
                根分类
              </a-tag>
            </div>
          </template>



          <template v-if="column.key === 'count'">
            <a-tag :color="record.resource_count > 0 ? 'blue' : 'default'">
              {{ record.resource_count }} 项
            </a-tag>
          </template>

          <template v-if="column.key === 'created_at'">
            <span class="text-sm text-gray-500">
              {{ formatDate(record.created_at) }}
            </span>
          </template>

          <template v-if="column.key === 'actions'">
            <a-space>
              <a-button type="text" @click="handleEditCategory(record)" data-action="edit">
                <EditOutlined />
              </a-button>
              <a-button type="text" danger @click="handleDeleteCategory(record)">
                <DeleteOutlined />
              </a-button>
              <a-dropdown>
                <a-button type="text">
                  <MoreOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="handleMoveUp(record)">
                      <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                          d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"
                          clip-rule="evenodd" />
                      </svg>
                      上移
                    </a-menu-item>
                    <a-menu-item @click="handleMoveDown(record)">
                      <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                          d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                          clip-rule="evenodd" />
                      </svg>
                      下移
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="handleMoveCategory(record)">
                      <ArrowRightOutlined />
                      移动分类
                    </a-menu-item>
                    <a-menu-item @click="handleDuplicateCategory(record)">
                      <CopyOutlined />
                      复制分类
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 编辑分类模态框 -->
    <a-modal v-model:open="showEditModal" title="编辑分类" @cancel="resetEditForm" :footer="null">
      <a-form :model="editingCategory" :rules="editCategoryRules" layout="vertical" @finish="handleUpdateCategory">
        <a-form-item label="分类名称" name="name">
          <a-input v-model:value="editingCategory.name" placeholder="请输入分类名称" />
        </a-form-item>

        <a-form-item label="父分类" name="parent_id">
          <a-tree-select v-model:value="editingCategory.parent_id" :tree-data="categoryTreeData" placeholder="选择父分类（可选）"
            allow-clear tree-default-expand-all />
        </a-form-item>



        <a-form-item>
          <a-space>
            <a-button @click="resetEditForm">
              取消
            </a-button>
            <a-button type="primary" html-type="submit" :loading="updating">
              {{ updating ? '更新中...' : '更新分类' }}
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 移动分类模态框 -->
    <a-modal v-model:open="showMoveModal" title="移动分类" @cancel="resetMoveForm" :footer="null">
      <div class="mb-4">
        <p class="text-gray-600 dark:text-gray-400">
          将分类 "<strong>{{ movingCategory?.name }}</strong>" 移动到：
        </p>
      </div>

      <a-form @finish="handleConfirmMove">
        <a-form-item label="目标父分类">
          <a-tree-select v-model:value="moveTarget" :tree-data="categoryTreeData" placeholder="选择目标父分类" allow-clear
            tree-default-expand-all />
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button @click="resetMoveForm">
              取消
            </a-button>
            <a-button type="primary" html-type="submit" :loading="moving">
              {{ moving ? '移动中...' : '确认移动' }}
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 批量移动模态框 -->
    <a-modal v-model:open="showBatchMoveModal" title="批量移动分类" @ok="handleBatchMove" :confirm-loading="moving">
      <p class="mb-4">将选中的 {{ selectedRowKeys.length }} 个分类移动到：</p>
      <a-tree-select v-model:value="batchMoveTarget" style="width: 100%" :tree-data="categoryTreeData"
        placeholder="请选择目标分类" tree-default-expand-all allow-clear />
    </a-modal>

    <!-- 帮助说明模态框 -->
    <a-modal v-model:open="showHelpModal" title="分类管理操作说明" :footer="null" width="700px" class="help-modal">
      <div class="help-content">
        <!-- 功能概览 -->
        <div class="help-section help-section-overview">
          <div class="help-section-header">
            <div class="help-icon-wrapper overview">
              <FolderOutlined />
            </div>
            <h3 class="help-section-title">功能概览</h3>
          </div>
          <div class="help-section-content">
            <p class="help-description">
              分类管理帮助您组织和管理知识库的分类结构，支持多层级分类和丰富的操作功能。
            </p>
          </div>
        </div>

        <!-- 添加分类 -->
        <div class="help-section help-section-add">
          <div class="help-section-header">
            <div class="help-icon-wrapper add">
              <PlusOutlined />
            </div>
            <h3 class="help-section-title">添加分类</h3>
          </div>
          <div class="help-section-content">
            <div class="help-steps">
              <div class="help-step">
                <span class="step-number">1</span>
                <span class="step-text">点击"添加新分类"按钮展开添加表单</span>
              </div>
              <div class="help-step">
                <span class="step-number">2</span>
                <span class="step-text">输入分类名称（必填，1-50个字符）</span>
              </div>
              <div class="help-step">
                <span class="step-number">3</span>
                <span class="step-text">选择父分类（可选，不选择则为根分类）</span>
              </div>
              <div class="help-step">
                <span class="step-number">4</span>
                <span class="step-text">点击"添加分类"完成创建</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 编辑分类 -->
        <div class="help-section help-section-edit">
          <div class="help-section-header">
            <div class="help-icon-wrapper edit">
              <EditOutlined />
            </div>
            <h3 class="help-section-title">编辑分类</h3>
          </div>
          <div class="help-section-content">
            <div class="help-steps">
              <div class="help-step">
                <span class="step-number">1</span>
                <span class="step-text">点击分类名称旁的编辑按钮进入编辑模式</span>
              </div>
              <div class="help-step">
                <span class="step-number">2</span>
                <span class="step-text">直接在输入框中修改分类名称</span>
              </div>
              <div class="help-step">
                <span class="step-number">3</span>
                <span class="step-text">按Enter键保存，按Esc键取消</span>
              </div>
              <div class="help-step">
                <span class="step-number">4</span>
                <span class="step-text">点击其他位置自动保存修改</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 移动和排序 -->
        <div class="help-section help-section-move">
          <div class="help-section-header">
            <div class="help-icon-wrapper move">
              <ArrowRightOutlined />
            </div>
            <h3 class="help-section-title">移动和排序</h3>
          </div>
          <div class="help-section-content">
            <div class="help-steps">
              <div class="help-step">
                <span class="step-number">1</span>
                <span class="step-text">使用上移/下移按钮调整同级分类的排序</span>
              </div>
              <div class="help-step">
                <span class="step-number">2</span>
                <span class="step-text">点击"移动"按钮可将分类移动到其他父分类下</span>
              </div>
              <div class="help-step">
                <span class="step-number">3</span>
                <span class="step-text">支持批量选择和批量移动操作</span>
              </div>
              <div class="help-step">
                <span class="step-number">4</span>
                <span class="step-text">系统会自动检查循环引用，确保分类结构正确</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 删除分类 -->
        <div class="help-section help-section-delete">
          <div class="help-section-header">
            <div class="help-icon-wrapper delete">
              <DeleteOutlined />
            </div>
            <h3 class="help-section-title">删除分类</h3>
          </div>
          <div class="help-section-content">
            <div class="help-steps">
              <div class="help-step">
                <span class="step-number">1</span>
                <span class="step-text">点击删除按钮会显示确认对话框</span>
              </div>
              <div class="help-step">
                <span class="step-number">2</span>
                <span class="step-text">如果分类包含子分类或关联资源，会显示警告信息</span>
              </div>
              <div class="help-step">
                <span class="step-number">3</span>
                <span class="step-text">删除操作不可恢复，请谨慎操作</span>
              </div>
              <div class="help-step">
                <span class="step-number">4</span>
                <span class="step-text">支持批量删除多个分类</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="help-section help-section-search">
          <div class="help-section-header">
            <div class="help-icon-wrapper search">
              <ReloadOutlined />
            </div>
            <h3 class="help-section-title">搜索和筛选</h3>
          </div>
          <div class="help-section-content">
            <div class="help-steps">
              <div class="help-step">
                <span class="step-number">1</span>
                <span class="step-text">在搜索框中输入关键词搜索分类</span>
              </div>
              <div class="help-step">
                <span class="step-number">2</span>
                <span class="step-text">搜索结果会自动展开包含匹配子分类的父分类</span>
              </div>
              <div class="help-step">
                <span class="step-number">3</span>
                <span class="step-text">点击刷新按钮重新加载最新数据</span>
              </div>
              <div class="help-step">
                <span class="step-number">4</span>
                <span class="step-text">统计卡片显示分类的数量信息</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 注意事项 -->
        <div class="help-section help-section-warning">
          <div class="help-section-header">
            <div class="help-icon-wrapper warning">
              <QuestionCircleOutlined />
            </div>
            <h3 class="help-section-title">注意事项</h3>
          </div>
          <div class="help-section-content">
            <div class="help-warnings">
              <div class="help-warning">
                <span class="warning-icon">⚠️</span>
                <span class="warning-text">分类名称在同一层级下必须唯一</span>
              </div>
              <div class="help-warning">
                <span class="warning-icon">⚠️</span>
                <span class="warning-text">不能将分类移动到其子分类下（避免循环引用）</span>
              </div>
              <div class="help-warning">
                <span class="warning-icon">⚠️</span>
                <span class="warning-text">删除分类时，其子分类和关联资源也会被删除</span>
              </div>
              <div class="help-warning">
                <span class="warning-icon">💡</span>
                <span class="warning-text">建议定期整理分类结构，保持层次清晰</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-modal>

  </a-space>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, provide, h } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  FolderOutlined,
  FolderOpenOutlined,
  FolderAddOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  ArrowRightOutlined,
  CopyOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons-vue'
import { categoryService } from '@/services/categoryService'
import type { Category, CategoryWithChildren } from '@/types'

// 状态
const loading = ref(false)
const adding = ref(false)
const updating = ref(false)
const moving = ref(false)
const searchText = ref('')

const showEditModal = ref(false)
const showMoveModal = ref(false)
const showBatchMoveModal = ref(false)

// 批量操作相关状态
const selectedRowKeys = ref<number[]>([])
const batchMoveTarget = ref<number | null>(null)

// 原地编辑相关状态
const editingId = ref<number | null>(null)
const editingName = ref('')
const originalName = ref('') // 保存原始名称，用于比较是否有修改
const editInputRef = ref<any>(null)

// 展开行控制
const expandedRowKeys = ref<number[]>([])
const autoExpandOnSearch = ref(true)

// 添加分类表单控制
const showAddForm = ref(false)

// 帮助说明模态框控制
const showHelpModal = ref(false)

// 数据
const categories = ref<Category[]>([])
const categoryTree = ref<CategoryWithChildren[]>([])
const editingCategory = reactive({
  id: 0,
  name: '',
  parent_id: 0
})
const movingCategory = ref<Category | null>(null)
const moveTarget = ref<number | null>(null)

// 新分类表单
const newCategory = reactive({
  name: '',
  parent_id: 0
})

// 提供所有分类数据给子组件
provide('allCategories', categories)

// 表单验证规则
const newCategoryRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 50, message: '分类名称长度应在1-50个字符之间', trigger: 'blur' },
    {
      validator: async (rule: any, value: string) => {
        if (value && categories.value.some(cat => cat.name === value.trim())) {
          throw new Error('分类名称已存在')
        }
      },
      trigger: 'blur'
    }
  ]
}

const editCategoryRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 50, message: '分类名称长度应在1-50个字符之间', trigger: 'blur' },
    {
      validator: async (rule: any, value: string) => {
        if (value && categories.value.some(cat => cat.name === value.trim() && cat.id !== editingCategory.id)) {
          throw new Error('分类名称已存在')
        }
      },
      trigger: 'blur'
    }
  ]
}

// 表格列定义
const columns = [
  {
    title: '分类名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '项目数量',
    dataIndex: 'resource_count',
    key: 'count',
    width: 100,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 150
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    align: 'center'
  }
]

// 计算属性
const categoryTreeData = computed(() => {
  const treeData = buildTreeData(categoryTree.value)
  // 添加根目录选项
  return [
    {
      title: '根目录',
      value: 0,
      key: 0,
      children: []
    },
    ...treeData
  ]
})

const filteredCategories = computed(() => {
  if (!searchText.value) {
    expandedRowKeys.value = []
    return categoryTree.value
  }

  // 递归过滤树形结构，保留匹配的节点及其父节点
  const result: CategoryWithChildren[] = []
  const expandKeys: number[] = []

  const filterTree = (nodes: CategoryWithChildren[], parentShouldExpand = false): CategoryWithChildren[] => {
    const filteredNodes: CategoryWithChildren[] = []

    for (const node of nodes) {
      const matchesSearch = node.name.toLowerCase().includes(searchText.value.toLowerCase())
      const filteredChildren = node.children ? filterTree(node.children, matchesSearch) : []
      const hasMatchingChildren = filteredChildren.length > 0

      // 如果当前节点匹配或有匹配的子节点，则保留
      if (matchesSearch || hasMatchingChildren) {
        filteredNodes.push({
          ...node,
          children: filteredChildren
        })

        // 如果有匹配的子节点，需要展开当前节点
        if (hasMatchingChildren && node.id) {
          expandKeys.push(node.id)
        }
      }
    }

    return filteredNodes
  }

  const filtered = filterTree(categoryTree.value)

  // 更新展开的行键
  expandedRowKeys.value = expandKeys

  return filtered
})

const totalCategories = computed(() => categories.value.length)
const topLevelCategories = computed(() => categories.value.filter(cat => cat.parent_id === 0).length)
const subCategories = computed(() => categories.value.filter(cat => cat.parent_id !== 0).length)
const emptyCategories = computed(() => categories.value.filter(cat => cat.resource_count === 0).length)

// 方法
const buildTreeData = (cats: CategoryWithChildren[]) => {
  return cats.map(cat => ({
    title: cat.name,
    value: cat.id,
    key: cat.id,
    children: cat.children ? buildTreeData(cat.children) : []
  }))
}

const formatDate = (date: Date | string) => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj.toLocaleDateString()
}

// 检查循环引用
const checkCircularReference = (categoryId: number, targetParentId: number): boolean => {
  if (categoryId === targetParentId) return true

  const findParent = (id: number): Category | undefined => {
    return categories.value.find(cat => cat.id === id)
  }

  let currentParent = findParent(targetParentId)
  while (currentParent && currentParent.parent_id !== 0) {
    if (currentParent.parent_id === categoryId) return true
    currentParent = findParent(currentParent.parent_id)
  }

  return false
}

// 验证分类名称唯一性
const validateCategoryName = (name: string, excludeId?: number): boolean => {
  return !categories.value.some(cat =>
    cat.name.trim().toLowerCase() === name.trim().toLowerCase() &&
    cat.id !== excludeId
  )
}

// 加载分类数据
const loadCategories = async () => {
  try {
    loading.value = true
    const [allCategories, tree] = await Promise.all([
      categoryService.getAllCategories(),
      categoryService.getCategoryTree()
    ])
    categories.value = allCategories
    categoryTree.value = tree
  } catch (error) {
    console.error('加载分类失败:', error)
    message.error('加载分类数据失败')
  } finally {
    loading.value = false
  }
}

const handleAddCategory = async () => {
  // 验证分类名称
  if (!validateCategoryName(newCategory.name)) {
    message.error('分类名称已存在')
    return
  }

  adding.value = true
  try {
    await categoryService.createCategory({
      name: newCategory.name.trim(),
      parent_id: newCategory.parent_id,
      sort_order: 999
    })

    message.success('分类添加成功!')
    resetForm()
    showAddForm.value = false // 关闭表单
    await loadCategories() // 重新加载数据
  } catch (error) {
    console.error('添加分类失败:', error)
    message.error('分类添加失败，请重试')
  } finally {
    adding.value = false
  }
}

const resetForm = () => {
  newCategory.name = ''
  newCategory.parent_id = 0
}

const handleEditCategory = (category: CategoryWithChildren) => {
  // 启动原地编辑
  editingId.value = category.id!
  editingName.value = category.name
  originalName.value = category.name // 保存原始名称
}

// 保存原地编辑
const handleSaveEdit = async (category: CategoryWithChildren) => {
  const trimmedName = editingName.value.trim()

  // 如果名称没有变化，直接取消编辑
  if (trimmedName === originalName.value) {
    handleCancelEdit()
    return
  }

  if (!trimmedName) {
    message.error('分类名称不能为空')
    return
  }

  // 验证分类名称唯一性
  if (!validateCategoryName(trimmedName, category.id)) {
    message.error('分类名称已存在')
    return
  }

  try {
    await categoryService.updateCategory(category.id!, {
      name: trimmedName,
      parent_id: category.parent_id
    })

    message.success('分类名称更新成功!')
    editingId.value = null
    editingName.value = ''
    originalName.value = ''
    await loadCategories() // 重新加载数据
  } catch (error) {
    console.error('更新分类失败:', error)
    message.error('分类更新失败，请重试')
  }
}

// 取消原地编辑
const handleCancelEdit = () => {
  editingId.value = null
  editingName.value = ''
  originalName.value = ''
}

const handleUpdateCategory = async () => {
  // 验证分类名称
  if (!validateCategoryName(editingCategory.name, editingCategory.id)) {
    message.error('分类名称已存在')
    return
  }

  // 检查循环引用
  if (editingCategory.parent_id !== 0 && checkCircularReference(editingCategory.id, editingCategory.parent_id)) {
    message.error('不能将分类移动到其子分类下，这会造成循环引用')
    return
  }

  updating.value = true
  try {
    await categoryService.updateCategory(editingCategory.id, {
      name: editingCategory.name.trim(),
      parent_id: editingCategory.parent_id
    })

    message.success('分类更新成功!')
    resetEditForm()
    await loadCategories() // 重新加载数据
  } catch (error) {
    console.error('更新分类失败:', error)
    message.error('分类更新失败，请重试')
  } finally {
    updating.value = false
  }
}

const resetEditForm = () => {
  showEditModal.value = false
  editingCategory.id = 0
  editingCategory.name = ''
  editingCategory.parent_id = 0
}

const handleDeleteCategory = async (category: CategoryWithChildren) => {
  // 检查是否有子分类
  const hasChildren = category.children && category.children.length > 0
  // 检查是否有关联资源
  const hasResources = category.resource_count > 0

  let content = `确定要删除分类"${category.name}"吗？`
  let warningMessages = []

  if (hasChildren) {
    warningMessages.push(`该分类包含 ${category.children!.length} 个子分类`)
  }

  if (hasResources) {
    warningMessages.push(`该分类包含 ${category.resource_count} 个关联资源`)
  }

  if (warningMessages.length > 0) {
    content += `\n\n⚠️ 警告：\n${warningMessages.join('\n')}\n\n删除后这些内容将一并被删除，此操作不可恢复！`
  }

  Modal.confirm({
    title: '确认删除分类',
    content,
    okText: '确定删除',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        await categoryService.deleteCategory(category.id!)
        message.success('分类删除成功!')
        await loadCategories() // 重新加载数据
      } catch (error: any) {
        console.error('删除分类失败:', error)
        message.error(error.message || '分类删除失败，请重试')
      }
    }
  })
}

const handleMoveCategory = (category: CategoryWithChildren) => {
  movingCategory.value = category
  moveTarget.value = null
  showMoveModal.value = true
}

const handleConfirmMove = async () => {
  if (!movingCategory.value || moveTarget.value === null) return

  // 检查循环引用
  if (moveTarget.value !== 0 && checkCircularReference(movingCategory.value.id!, moveTarget.value)) {
    message.error('不能将分类移动到其子分类下，这会造成循环引用')
    return
  }

  moving.value = true
  try {
    await categoryService.moveCategory(movingCategory.value.id!, moveTarget.value)
    message.success('分类移动成功!')
    resetMoveForm()
    await loadCategories() // 重新加载数据
  } catch (error: any) {
    console.error('移动分类失败:', error)
    message.error(error.message || '分类移动失败，请重试')
  } finally {
    moving.value = false
  }
}

const resetMoveForm = () => {
  showMoveModal.value = false
  movingCategory.value = null
  moveTarget.value = null
}

const handleDuplicateCategory = async (category: CategoryWithChildren) => {
  try {
    await categoryService.createCategory({
      name: `${category.name} (副本)`,
      parent_id: category.parent_id,
      sort_order: 999
    })
    message.success('分类复制成功!')
    await loadCategories() // 重新加载数据
  } catch (error) {
    console.error('复制分类失败:', error)
    message.error('分类复制失败，请重试')
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

const handleRefresh = async () => {
  await loadCategories()
  message.success('数据刷新成功!')
}

// 自定义展开图标 - 只有包含子分类的才显示展开图标
const customExpandIcon = ({ expanded, onExpand, record }: any) => {
  // 如果没有子分类，不显示展开图标
  if (!record.children || record.children.length === 0) {
    return null
  }

  // 有子分类时显示展开/收起图标
  return h('span', {
    class: [
      'ant-table-row-expand-icon',
      expanded ? 'ant-table-row-expand-icon-expanded' : 'ant-table-row-expand-icon-collapsed'
    ],
    onClick: (e: Event) => onExpand(record, e)
  })
}

// 拖拽排序处理
const handleReorderCategories = async (categoryIds: number[], parentId: number = 0) => {
  try {
    await categoryService.reorderCategories(parentId, categoryIds)
    message.success('分类排序更新成功!')
    await loadCategories() // 重新加载数据
  } catch (error) {
    console.error('排序分类失败:', error)
    message.error('分类排序失败，请重试')
  }
}

// 上移分类
const handleMoveUp = async (category: CategoryWithChildren) => {
  try {
    // 获取同级分类
    const siblings = categories.value.filter(cat => cat.parent_id === category.parent_id)
    const currentIndex = siblings.findIndex(cat => cat.id === category.id)

    if (currentIndex > 0) {
      // 交换位置
      const newOrder = [...siblings]
        ;[newOrder[currentIndex - 1], newOrder[currentIndex]] = [newOrder[currentIndex], newOrder[currentIndex - 1]]

      // 更新排序
      const categoryIds = newOrder.map(cat => cat.id!)
      await handleReorderCategories(categoryIds, category.parent_id)
    } else {
      message.info('已经是第一个了')
    }
  } catch (error) {
    console.error('上移分类失败:', error)
    message.error('上移分类失败，请重试')
  }
}

// 下移分类
const handleMoveDown = async (category: CategoryWithChildren) => {
  try {
    // 获取同级分类
    const siblings = categories.value.filter(cat => cat.parent_id === category.parent_id)
    const currentIndex = siblings.findIndex(cat => cat.id === category.id)

    if (currentIndex < siblings.length - 1) {
      // 交换位置
      const newOrder = [...siblings]
        ;[newOrder[currentIndex], newOrder[currentIndex + 1]] = [newOrder[currentIndex + 1], newOrder[currentIndex]]

      // 更新排序
      const categoryIds = newOrder.map(cat => cat.id!)
      await handleReorderCategories(categoryIds, category.parent_id)
    } else {
      message.info('已经是最后一个了')
    }
  } catch (error) {
    console.error('下移分类失败:', error)
    message.error('下移分类失败，请重试')
  }
}

// 批量操作相关方法
const onSelectChange = (selectedKeys: number[]) => {
  selectedRowKeys.value = selectedKeys
}

const onSelectAll = (selected: boolean, selectedRows: CategoryWithChildren[], changeRows: CategoryWithChildren[]) => {
  if (selected) {
    selectedRowKeys.value = [...selectedRowKeys.value, ...changeRows.map(row => row.id!)]
  } else {
    const changeIds = changeRows.map(row => row.id!)
    selectedRowKeys.value = selectedRowKeys.value.filter(key => !changeIds.includes(key))
  }
}

// 批量删除
const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要删除的分类')
    return
  }

  const selectedCategories = categories.value.filter(cat => selectedRowKeys.value.includes(cat.id!))
  const categoryNames = selectedCategories.map(cat => cat.name).join('、')

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除以下分类吗？\n${categoryNames}`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        for (const categoryId of selectedRowKeys.value) {
          await categoryService.deleteCategory(categoryId)
        }
        message.success(`成功删除 ${selectedRowKeys.value.length} 个分类`)
        selectedRowKeys.value = []
        await loadCategories()
      } catch (error: any) {
        console.error('批量删除失败:', error)
        message.error(error.message || '批量删除失败，请重试')
      }
    }
  })
}

// 批量移动
const handleBatchMove = async () => {
  if (!batchMoveTarget.value) {
    message.warning('请选择目标分类')
    return
  }

  try {
    for (const categoryId of selectedRowKeys.value) {
      await categoryService.moveCategory(categoryId, batchMoveTarget.value)
    }
    message.success(`成功移动 ${selectedRowKeys.value.length} 个分类`)
    selectedRowKeys.value = []
    batchMoveTarget.value = null
    showBatchMoveModal.value = false
    await loadCategories()
  } catch (error: any) {
    console.error('批量移动失败:', error)
    message.error(error.message || '批量移动失败，请重试')
  }
}

// 自定义行属性，为每行添加data-category-id
const customRowProps = (record: any) => {
  return {
    'data-category-id': record.id
  }
}

// 处理行展开/收起
const handleRowExpand = (expanded: boolean, record: CategoryWithChildren) => {
  if (expanded) {
    if (!expandedRowKeys.value.includes(record.id!)) {
      expandedRowKeys.value.push(record.id!)
    }
  } else {
    const index = expandedRowKeys.value.indexOf(record.id!)
    if (index > -1) {
      expandedRowKeys.value.splice(index, 1)
    }
  }
}

// 全局点击事件处理，点击其他位置自动保存或取消编辑
const handleGlobalClick = async (event: MouseEvent) => {
  if (editingId.value === null) return

  const target = event.target as HTMLElement

  // 如果点击的是编辑输入框或其子元素，不处理
  if (target.closest('.inline-edit-input')) {
    return
  }

  // 如果点击的是编辑按钮，不处理（让编辑按钮自己处理）
  if (target.closest('[data-action="edit"]')) {
    return
  }

  // 点击其他位置，检查是否有修改
  const trimmedName = editingName.value.trim()
  const currentCategory = categories.value.find(cat => cat.id === editingId.value)

  if (!currentCategory) {
    handleCancelEdit()
    return
  }

  // 如果名称没有变化，直接取消编辑
  if (trimmedName === originalName.value) {
    handleCancelEdit()
    return
  }

  // 如果有修改，自动保存
  await handleSaveEdit(currentCategory)
}

onMounted(() => {
  loadCategories()

  // 添加全局点击事件监听器
  document.addEventListener('click', handleGlobalClick)
})

// 组件卸载时移除事件监听器
onUnmounted(() => {
  document.removeEventListener('click', handleGlobalClick)
})
</script>

<style scoped>
/* 分类管理卡片 */
.category-management-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.dark .category-management-card {
  background: #1f1f1f;
  border-color: #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 工具栏样式 - 紧凑型统一标准 */
.toolbar-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 8px 0;
  gap: 12px;
}

.toolbar-left {
  flex: 0 0 auto;
}

.toolbar-right {
  flex: 0 0 auto;
}



/* 添加分类表单容器 - 紧凑型统一标准 */
.add-form-container {
  margin-bottom: 12px;
}

.add-category-form {
  margin-top: 8px;
  padding: 16px;
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.dark .add-category-form {
  background: #1f1f1f;
  border-color: #303030;
}

.form-content {
  margin: 0;
}

/* 统计卡片紧凑样式 - 统一标准 */
.stat-card {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
  position: relative;
}

.stat-card:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.dark .stat-card {
  background: #1f1f1f;
  border-color: #303030;
}

.dark .stat-card:hover {
  border-color: #434343;
  box-shadow: 0 2px 6px rgba(255, 255, 255, 0.02);
}

.stat-icon {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-right: 10px;
  color: white;
}

.stat-icon-primary {
  background: #1890ff;
}

.stat-icon-success {
  background: #52c41a;
}

.stat-icon-info {
  background: #13c2c2;
}

.stat-icon-warning {
  background: #faad14;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  line-height: 1;
  margin-bottom: 2px;
}

.dark .stat-value {
  color: #fff;
}

.stat-label {
  font-size: 13px;
  color: #8c8c8c;
  font-weight: 400;
}

.dark .stat-label {
  color: #a6a6a6;
}

/* 卡片标题 */
.card-title-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.dark .card-title {
  color: #f0f0f0;
}

.help-icon-btn {
  color: #8c8c8c;
  font-size: 14px;
  width: 22px;
  height: 22px;
  padding: 0;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  /* 防止图标被压缩 */
}

.help-icon-btn:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.dark .help-icon-btn {
  color: #a6a6a6;
}

.dark .help-icon-btn:hover {
  color: #40a9ff;
  background: rgba(64, 169, 255, 0.1);
}

/* 帮助模态框样式 */
.help-modal :deep(.ant-modal-header) {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.help-modal :deep(.ant-modal-body) {
  padding: 0;
  max-height: 70vh;
  overflow: hidden;
}

.help-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 24px;
  line-height: 1.6;
}

/* 自定义滚动条样式 */
.help-content::-webkit-scrollbar {
  width: 6px;
}

.help-content::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.help-content::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.help-content::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 帮助章节样式 - 紧凑型统一标准 */
.help-section {
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  overflow: hidden;
  transition: all 0.2s ease;
}

.help-section:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.help-section:last-child {
  margin-bottom: 0;
}

/* 章节头部 - 紧凑型统一标准 */
.help-section-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.help-icon-wrapper {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-size: 16px;
  color: white;
}

.help-icon-wrapper.overview {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.help-icon-wrapper.add {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.help-icon-wrapper.edit {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.help-icon-wrapper.move {
  background: linear-gradient(135deg, #13c2c2, #36cfc9);
}

.help-icon-wrapper.delete {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.help-icon-wrapper.search {
  background: linear-gradient(135deg, #722ed1, #9254de);
}

.help-icon-wrapper.warning {
  background: linear-gradient(135deg, #faad14, #ffc53d);
}

.help-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

/* 章节内容 - 紧凑型统一标准 */
.help-section-content {
  padding: 16px;
}

.help-description {
  color: #595959;
  margin: 0;
  line-height: 1.6;
}

/* 操作步骤样式 - 紧凑型统一标准 */
.help-steps {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.help-step {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 10px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
}

.step-number {
  width: 20px;
  height: 20px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
  margin-top: 1px;
}

.step-text {
  color: #262626;
  line-height: 1.5;
  font-size: 14px;
}

/* 警告信息样式 - 紧凑型统一标准 */
.help-warnings {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.help-warning {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 10px 12px;
  background: #fff7e6;
  border-radius: 6px;
  border-left: 3px solid #faad14;
}

.warning-icon {
  font-size: 16px;
  flex-shrink: 0;
  margin-top: 1px;
}

.warning-text {
  color: #262626;
  line-height: 1.5;
  font-size: 14px;
}

/* 不同主题色的步骤样式 */
.help-section-add .help-step {
  border-left-color: #52c41a;
}

.help-section-add .step-number {
  background: #52c41a;
}

.help-section-edit .help-step {
  border-left-color: #1890ff;
}

.help-section-edit .step-number {
  background: #1890ff;
}

.help-section-move .help-step {
  border-left-color: #13c2c2;
}

.help-section-move .step-number {
  background: #13c2c2;
}

.help-section-delete .help-step {
  border-left-color: #ff4d4f;
}

.help-section-delete .step-number {
  background: #ff4d4f;
}

.help-section-search .help-step {
  border-left-color: #722ed1;
}

.help-section-search .step-number {
  background: #722ed1;
}

/* 暗黑模式下的帮助模态框 */
.dark .help-modal :deep(.ant-modal-header) {
  border-bottom-color: #303030;
  background: #1f1f1f;
}

.dark .help-modal :deep(.ant-modal-content) {
  background: #1f1f1f;
}

.dark .help-content::-webkit-scrollbar-track {
  background: #262626;
}

.dark .help-content::-webkit-scrollbar-thumb {
  background: #434343;
}

.dark .help-content::-webkit-scrollbar-thumb:hover {
  background: #595959;
}

.dark .help-section {
  border-color: #303030;
}

.dark .help-section:hover {
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.04);
}

.dark .help-section-header {
  background: #262626;
  border-bottom-color: #303030;
}

.dark .help-section-title {
  color: #fff;
}

.dark .help-description {
  color: #a6a6a6;
}

.dark .help-step {
  background: #262626;
}

.dark .step-text {
  color: #a6a6a6;
}

.dark .help-warning {
  background: #2a2a2a;
}

.dark .warning-text {
  color: #a6a6a6;
}

/* 原地编辑输入框样式 */
.inline-edit-input {
  border: none !important;
  border-bottom: 2px solid #1890ff !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  background: transparent !important;
  padding: 2px 4px !important;
  font-weight: 500;
  min-width: 120px;
  transition: all 0.2s ease;
}

.inline-edit-input:hover {
  border-bottom-color: #40a9ff !important;
}

.inline-edit-input:focus,
.inline-edit-input:focus-within {
  border-bottom-color: #1890ff !important;
  box-shadow: 0 2px 0 0 rgba(24, 144, 255, 0.2) !important;
  outline: none !important;
}

.inline-edit-input .ant-input {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
  padding: 0 !important;
  font-weight: 500;
  color: inherit !important;
  outline: none !important;
}

.inline-edit-input .ant-input:focus {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

/* 确保光标可见 */
.inline-edit-input .ant-input::selection {
  background-color: #1890ff;
  color: white;
}
</style>