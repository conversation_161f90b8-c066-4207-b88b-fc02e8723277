<template>
  <a-space direction="vertical" size="large" style="width: 100%">
    <!-- 知识库浏览设置 -->
    <a-card title="知识库浏览设置">
      <a-row :gutter="24">
        <!-- 标签加载设置 -->
        <a-col :span="12">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">标签加载配置</h4>
          
          <a-space direction="vertical" size="middle" style="width: 100%">
            <a-form-item label="初始加载标签数量">
              <a-space>
                <a-input-number
                  v-model:value="settings.tags.initialLoad"
                  :min="5"
                  :max="50"
                  style="width: 120px"
                />
                <span class="text-sm text-gray-500 dark:text-gray-400">个标签</span>
              </a-space>
              <template #extra>
                <div class="text-sm text-gray-500">页面首次加载时显示的标签数量</div>
              </template>
            </a-form-item>

            <a-form-item label="点击+号加载数量">
              <a-space>
                <a-input-number
                  v-model:value="settings.tags.loadMore"
                  :min="5"
                  :max="30"
                  style="width: 120px"
                />
                <span class="text-sm text-gray-500 dark:text-gray-400">个标签</span>
              </a-space>
              <template #extra>
                <div class="text-sm text-gray-500">点击显示更多按钮时加载的标签数量</div>
              </template>
            </a-form-item>
          </a-space>
        </a-col>

        <!-- 排序设置 -->
        <a-col :span="12">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">默认排序方式</h4>

          <a-space direction="vertical" size="middle" style="width: 100%">
            <a-form-item label="资源排序">
              <a-select
                v-model:value="settings.sorting.default"
                style="width: 100%"
                :options="resourceSortOptions"
              />
            </a-form-item>

            <a-form-item label="标签排序">
              <a-select
                v-model:value="settings.sorting.tags"
                style="width: 100%"
                :options="tagSortOptions"
              />
            </a-form-item>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <!-- 卡片显示设置 -->
    <a-card title="卡片显示设置">
      <a-row :gutter="24">
        <!-- 布局设置 -->
        <a-col :span="12">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">布局配置</h4>
          
          <a-space direction="vertical" size="middle" style="width: 100%">
            <a-form-item label="每行卡片数量">
              <a-radio-group v-model:value="settings.layout.cardsPerRow">
                <a-radio-button :value="2">2 列</a-radio-button>
                <a-radio-button :value="3">3 列</a-radio-button>
                <a-radio-button :value="4">4 列</a-radio-button>
                <a-radio-button :value="5">5 列</a-radio-button>
              </a-radio-group>
              <template #extra>
                <div class="text-sm text-gray-500">在大屏幕上每行显示的卡片数量</div>
              </template>
            </a-form-item>

            <a-form-item label="卡片间距">
              <a-select
                v-model:value="settings.layout.cardSpacing"
                style="width: 100%"
                :options="[
                  { label: '紧凑（4px）', value: 'compact' },
                  { label: '正常（8px）', value: 'normal' },
                  { label: '舒适（16px）', value: 'comfortable' },
                  { label: '宽松（24px）', value: 'spacious' }
                ]"
              />
            </a-form-item>
          </a-space>
        </a-col>

        <!-- 加载设置 -->
        <a-col :span="12">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">加载配置</h4>
          
          <a-space direction="vertical" size="middle" style="width: 100%">
            <a-form-item label="加载方式">
              <a-radio-group v-model:value="settings.loading.type">
                <a-radio value="pagination">分页加载</a-radio>
                <a-radio value="infinite">懒加载（无限滚动）</a-radio>
              </a-radio-group>
            </a-form-item>

            <a-form-item label="每页显示数量">
              <a-space>
                <a-input-number
                  v-model:value="settings.loading.pageSize"
                  :min="10"
                  :max="100"
                  :step="10"
                  style="width: 120px"
                />
                <span class="text-sm text-gray-500 dark:text-gray-400">个资源</span>
              </a-space>
              <template #extra>
                <div class="text-sm text-gray-500">每页或每次加载显示的资源数量</div>
              </template>
            </a-form-item>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <!-- 搜索设置 -->
    <a-card title="搜索设置">
      <a-row :gutter="24">
        <a-col :span="12">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">搜索行为</h4>
          
          <a-space direction="vertical" size="middle" style="width: 100%">
            <a-form-item>
              <a-checkbox v-model:checked="settings.search.realTimeSearch">
                实时搜索
              </a-checkbox>
              <template #extra>
                <div class="text-sm text-gray-500">输入时立即搜索，无需按回车</div>
              </template>
            </a-form-item>

            <a-form-item>
              <a-checkbox v-model:checked="settings.search.highlightResults">
                高亮搜索结果
              </a-checkbox>
              <template #extra>
                <div class="text-sm text-gray-500">在搜索结果中高亮显示关键词</div>
              </template>
            </a-form-item>

            <a-form-item label="搜索延迟">
              <a-space>
                <a-input-number
                  v-model:value="settings.search.debounceDelay"
                  :min="100"
                  :max="2000"
                  :step="100"
                  style="width: 120px"
                />
                <span class="text-sm text-gray-500 dark:text-gray-400">毫秒</span>
              </a-space>
              <template #extra>
                <div class="text-sm text-gray-500">实时搜索的延迟时间，避免频繁请求</div>
              </template>
            </a-form-item>
          </a-space>
        </a-col>

        <a-col :span="12">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">搜索范围</h4>
          
          <a-space direction="vertical" size="middle" style="width: 100%">
            <a-form-item>
              <a-checkbox-group v-model:value="settings.search.searchFields">
                <a-space direction="vertical">
                  <a-checkbox value="title">标题</a-checkbox>
                  <a-checkbox value="description">描述</a-checkbox>
                  <a-checkbox value="content">内容</a-checkbox>
                  <a-checkbox value="tags">标签</a-checkbox>
                  <a-checkbox value="url">URL</a-checkbox>
                </a-space>
              </a-checkbox-group>
              <template #extra>
                <div class="text-sm text-gray-500">选择搜索时包含的字段</div>
              </template>
            </a-form-item>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <!-- 缓存设置 -->
    <a-card title="缓存设置">
      <a-row :gutter="24">
        <a-col :span="24">
          <a-space direction="vertical" size="middle" style="width: 100%">
            <a-form-item>
              <a-checkbox v-model:checked="settings.cache.enabled">
                启用本地缓存
              </a-checkbox>
              <template #extra>
                <div class="text-sm text-gray-500">缓存资源数据以提高加载速度</div>
              </template>
            </a-form-item>

            <a-form-item label="缓存过期时间">
              <a-space>
                <a-input-number
                  v-model:value="settings.cache.expireTime"
                  :min="5"
                  :max="1440"
                  :step="5"
                  style="width: 120px"
                  :disabled="!settings.cache.enabled"
                />
                <span class="text-sm text-gray-500 dark:text-gray-400">分钟</span>
              </a-space>
              <template #extra>
                <div class="text-sm text-gray-500">缓存数据的有效期，超过后自动刷新</div>
              </template>
            </a-form-item>

            <a-form-item>
              <a-space>
                <a-button @click="clearCache" :disabled="!settings.cache.enabled">
                  清空缓存
                </a-button>
                <span class="text-sm text-gray-500 dark:text-gray-400">
                  当前缓存大小: {{ cacheSize }}
                </span>
              </a-space>
            </a-form-item>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <!-- 保存按钮 -->
    <div class="flex justify-end">
      <a-space>
        <a-button @click="resetSettings">
          重置默认
        </a-button>
        <a-button type="primary" :loading="saving" @click="saveSettings">
          {{ saving ? '保存中...' : '保存设置' }}
        </a-button>
      </a-space>
    </div>
  </a-space>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'

// 设置数据结构
const settings = reactive({
  tags: {
    initialLoad: 20,
    loadMore: 10
  },
  sorting: {
    default: 'created_desc',
    tags: 'name_asc'
  },
  layout: {
    cardsPerRow: 4,
    cardSpacing: 'normal'
  },
  loading: {
    type: 'pagination',
    pageSize: 20
  },
  search: {
    realTimeSearch: true,
    highlightResults: true,
    debounceDelay: 300,
    searchFields: ['title', 'description', 'tags']
  },
  cache: {
    enabled: true,
    expireTime: 60
  }
})

// 状态
const saving = ref(false)
const cacheSize = ref('2.3 MB')

// 选项数据
const resourceSortOptions = [
  { label: '创建时间（最新优先）', value: 'created_desc' },
  { label: '创建时间（最早优先）', value: 'created_asc' },
  { label: '更新时间（最新优先）', value: 'updated_desc' },
  { label: '更新时间（最早优先）', value: 'updated_asc' },
  { label: '标题（A-Z）', value: 'title_asc' },
  { label: '标题（Z-A）', value: 'title_desc' },
  { label: '访问次数（高到低）', value: 'visits_desc' },
  { label: '访问次数（低到高）', value: 'visits_asc' }
]

const tagSortOptions = [
  { label: '名称（A-Z）', value: 'name_asc' },
  { label: '名称（Z-A）', value: 'name_desc' },
  { label: '使用次数（高到低）', value: 'count_desc' },
  { label: '使用次数（低到高）', value: 'count_asc' },
  { label: '创建时间（最新优先）', value: 'created_desc' },
  { label: '创建时间（最早优先）', value: 'created_asc' }
]

// 方法
const saveSettings = async () => {
  saving.value = true
  try {
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('设置保存成功!')
  } catch (error) {
    message.error('设置保存失败!')
  } finally {
    saving.value = false
  }
}

const resetSettings = () => {
  Object.assign(settings, {
    tags: {
      initialLoad: 20,
      loadMore: 10
    },
    sorting: {
      default: 'created_desc',
      tags: 'name_asc'
    },
    layout: {
      cardsPerRow: 4,
      cardSpacing: 'normal'
    },
    loading: {
      type: 'pagination',
      pageSize: 20
    },
    search: {
      realTimeSearch: true,
      highlightResults: true,
      debounceDelay: 300,
      searchFields: ['title', 'description', 'tags']
    },
    cache: {
      enabled: true,
      expireTime: 60
    }
  })
  message.info('设置已重置为默认值')
}

const clearCache = () => {
  // 模拟清空缓存
  cacheSize.value = '0 KB'
  message.success('缓存已清空!')
  
  // 2秒后恢复显示
  setTimeout(() => {
    cacheSize.value = '2.3 MB'
  }, 2000)
}

onMounted(() => {
  // 加载设置
  console.log('知识库设置页面已加载')
})
</script>
