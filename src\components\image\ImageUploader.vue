<template>
  <!-- 图片上传管理卡片 - 统一紧凑型布局 -->
  <a-card size="small" class="image-uploader-card">
    <template #title>
      <div class="card-title-wrapper">
        <span class="card-title">图片上传</span>
        <a-tooltip title="支持拖拽上传、批量上传，自动备份到多个图床" placement="bottom">
          <a-button type="text" size="small" class="help-icon-btn">
            <QuestionCircleOutlined />
          </a-button>
        </a-tooltip>
      </div>
    </template>

    <!-- 拖拽上传区域 -->
    <div class="upload-area" :class="{
      'drag-over': isDragOver,
      'uploading': uploading,
      'disabled': disabled
    }" @drop="handleDrop" @dragover.prevent="handleDragOver" @dragenter.prevent="handleDragEnter"
      @dragleave.prevent="handleDragLeave" @click="triggerFileSelect">
      <input ref="fileInput" type="file" multiple accept="image/*" @change="handleFileSelect" class="hidden"
        :disabled="disabled" />

      <div class="upload-content">
        <div v-if="uploading" class="upload-progress">
          <a-spin size="large" />
          <p class="upload-progress-text">
            正在上传图片...
          </p>
          <p class="upload-status-text">
            {{ uploadStatus }}
          </p>

          <!-- 进度条 -->
          <div v-if="uploadProgress > 0" class="progress-container">
            <a-progress :percent="uploadProgress" :show-info="true" size="small"
              :stroke-color="{ '0%': '#108ee9', '100%': '#87d068' }" />
          </div>
        </div>

        <div v-else class="upload-placeholder">
          <CloudUploadOutlined class="upload-icon" />
          <h3 class="upload-title">
            上传图片
          </h3>
          <p class="upload-description">
            拖拽图片到此处或点击选择文件
          </p>
          <div class="upload-tips">
            <a-tag color="blue" size="small">支持 JPG、PNG、GIF、WebP</a-tag>
            <a-tag color="green" size="small">最大 {{ maxFileSize }}MB</a-tag>
            <a-tag color="purple" size="small">支持批量上传</a-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传设置 - 紧凑型布局 -->
    <div v-if="showSettings" class="upload-settings-section">
      <div class="settings-header">
        <span class="settings-title">上传设置</span>
        <a-tag color="processing" size="small">高级选项</a-tag>
      </div>

      <a-row :gutter="12" class="settings-grid">
        <a-col :span="8">
          <div class="setting-item-compact">
            <label class="setting-label-compact">备份数量</label>
            <a-select v-model:value="settings.backupCount" size="small" class="setting-select">
              <a-select-option v-for="option in backupCountOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </a-select-option>
            </a-select>
          </div>
        </a-col>

        <a-col :span="8">
          <div class="setting-item-compact">
            <label class="setting-label-compact">过期时间</label>
            <a-select v-model:value="settings.defaultExpiration" size="small" class="setting-select">
              <a-select-option v-for="option in expirationOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </a-select-option>
            </a-select>
          </div>
        </a-col>

        <a-col :span="8">
          <div class="setting-item-compact">
            <label class="setting-label-compact">自动重试</label>
            <a-switch v-model:checked="settings.autoRetry" size="small" :checked-children="'开'"
              :un-checked-children="'关'" />
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 上传结果 - 紧凑型布局 -->
    <div v-if="uploadResults.length > 0" class="upload-results-section">
      <div class="results-header">
        <span class="results-title">上传结果</span>
        <a-tag :color="uploadResults.length > 0 ? 'success' : 'default'" size="small">
          {{ uploadResults.length }} 张图片
        </a-tag>
      </div>

      <div class="results-list-compact">
        <div v-for="result in uploadResults" :key="result.id" class="result-item-compact">
          <div class="result-image-compact">
            <img :src="result.backups[0]?.url" :alt="result.originalName" class="thumbnail-compact"
              @error="handleImageError" />
          </div>

          <div class="result-content-compact">
            <div class="result-header-compact">
              <h5 class="result-name-compact">{{ result.originalName }}</h5>
              <div class="result-meta-tags">
                <a-tag color="blue" size="small">{{ formatFileSize(result.size) }}</a-tag>
                <a-tag color="green" size="small">{{ result.backups.length }} 备份</a-tag>
                <a-tag color="purple" size="small">{{ formatDate(result.uploadTime) }}</a-tag>
              </div>
            </div>

            <div class="result-backups-compact">
              <div v-for="backup in result.backups" :key="backup.id" class="backup-item-compact">
                <span class="backup-name-compact">{{ backup.hostName }}</span>
                <div class="backup-actions-compact">
                  <a-button @click="copyUrl(backup.url)" type="text" size="small" title="复制链接">
                    <CopyOutlined />
                  </a-button>
                  <a-button @click="copyMarkdown(result, backup)" type="text" size="small" title="复制Markdown">
                    <CodeOutlined />
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="results-actions-compact">
        <a-space>
          <a-button @click="clearResults" size="small">
            <ClearOutlined />
            清空结果
          </a-button>
          <a-button @click="$emit('view-gallery')" type="primary" size="small">
            <PictureOutlined />
            查看图库
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 错误提示 - 紧凑型布局 -->
    <div v-if="errors.length > 0" class="upload-errors-section">
      <a-alert type="error" show-icon closable @close="clearErrors">
        <template #message>
          <div class="errors-header">
            <span class="errors-title">上传错误 ({{ errors.length }})</span>
          </div>
        </template>
        <template #description>
          <ul class="errors-list-compact">
            <li v-for="(error, index) in errors" :key="index" class="error-item-compact">
              {{ error }}
            </li>
          </ul>
        </template>
      </a-alert>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  QuestionCircleOutlined,
  CloudUploadOutlined,
  CopyOutlined,
  CodeOutlined,
  ClearOutlined,
  PictureOutlined
} from '@ant-design/icons-vue'
// 临时类型定义，保持原有功能完整
interface ImageRecord {
  id: string
  originalName: string
  size: number
  uploadTime: Date
  backups: Array<{
    id: string
    url: string
    hostName: string
  }>
}

interface ImageUploadSettings {
  backupCount: number
  defaultExpiration: number
  notifyBefore: number
  autoRetry: boolean
  maxRetries: number
  retryDelay: number
}

// 临时服务对象，保持原有功能
const imageUploadService = {
  saveSettings: (settings: ImageUploadSettings) => {
    localStorage.setItem('imageUploadSettings', JSON.stringify(settings))
  },
  loadSettings: (): ImageUploadSettings => {
    const saved = localStorage.getItem('imageUploadSettings')
    return saved ? JSON.parse(saved) : {
      backupCount: 2,
      defaultExpiration: 0,
      notifyBefore: 7,
      autoRetry: true,
      maxRetries: 3,
      retryDelay: 2
    }
  },
  getSettings: (): ImageUploadSettings => {
    const saved = localStorage.getItem('imageUploadSettings')
    return saved ? JSON.parse(saved) : {
      backupCount: 2,
      defaultExpiration: 0,
      notifyBefore: 7,
      autoRetry: true,
      maxRetries: 3,
      retryDelay: 2
    }
  },
  uploadImage: async (file: File): Promise<ImageRecord> => {
    // 模拟上传功能，保持原有接口
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id: Date.now().toString(),
          originalName: file.name,
          size: file.size,
          uploadTime: new Date(),
          backups: [{
            id: '1',
            url: URL.createObjectURL(file),
            hostName: '示例图床'
          }]
        })
      }, 1000)
    })
  }
}

// Props
interface Props {
  disabled?: boolean
  showSettings?: boolean
  maxFileSize?: number
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  showSettings: true,
  maxFileSize: 10
})

// Emits
const emit = defineEmits<{
  'upload-success': [results: ImageRecord[]]
  'upload-error': [errors: string[]]
  'view-gallery': []
}>()

// 响应式数据
const fileInput = ref<HTMLInputElement>()
const isDragOver = ref(false)
const uploading = ref(false)
const uploadProgress = ref(0)
const uploadStatus = ref('')
const uploadResults = ref<ImageRecord[]>([])
const errors = ref<string[]>([])

// 上传设置
const settings = ref<ImageUploadSettings>({
  backupCount: 2,
  defaultExpiration: 0,
  notifyBefore: 7,
  autoRetry: true,
  maxRetries: 3,
  retryDelay: 2
})

// 选项数据
const backupCountOptions = [
  { label: '1个图床', value: 1 },
  { label: '2个图床', value: 2 },
  { label: '3个图床', value: 3 },
  { label: '4个图床', value: 4 },
  { label: '5个图床', value: 5 }
]

const expirationOptions = [
  { label: '永久保存', value: 0 },
  { label: '30天', value: 30 },
  { label: '60天', value: 60 },
  { label: '90天', value: 90 },
  { label: '180天', value: 180 },
  { label: '365天', value: 365 }
]

// 拖拽事件处理
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = true
}

const handleDragEnter = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault()
  // 只有当离开整个拖拽区域时才设置为false
  const target = e.currentTarget as HTMLElement
  if (target && !target.contains(e.relatedTarget as Node)) {
    isDragOver.value = false
  }
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = false

  if (props.disabled || uploading.value) return

  const files = Array.from(e.dataTransfer?.files || [])
  const imageFiles = files.filter(file => file.type.startsWith('image/'))

  if (imageFiles.length > 0) {
    uploadFiles(imageFiles)
  }
}

// 文件选择处理
const triggerFileSelect = () => {
  if (props.disabled || uploading.value) return
  fileInput.value?.click()
}

const handleFileSelect = (e: Event) => {
  const target = e.target as HTMLInputElement
  const files = Array.from(target.files || [])

  if (files.length > 0) {
    uploadFiles(files)
  }

  // 清空input值，允许重复选择同一文件
  target.value = ''
}

// 上传文件
const uploadFiles = async (files: File[]) => {
  if (uploading.value) return

  uploading.value = true
  uploadProgress.value = 0
  errors.value = []

  try {
    // 保存设置
    imageUploadService.saveSettings(settings.value)

    const results: ImageRecord[] = []
    const uploadErrors: string[] = []

    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      uploadStatus.value = `正在上传 ${file.name} (${i + 1}/${files.length})`

      try {
        const result = await imageUploadService.uploadImage(file)
        results.push(result)
      } catch (error) {
        console.error(`上传文件 ${file.name} 失败:`, error)
        uploadErrors.push(`${file.name}: ${(error as Error).message}`)
      }

      uploadProgress.value = Math.round(((i + 1) / files.length) * 100)
    }

    uploadResults.value.unshift(...results)

    if (uploadErrors.length > 0) {
      errors.value = uploadErrors
      emit('upload-error', uploadErrors)
    }

    if (results.length > 0) {
      emit('upload-success', results)
    }

  } catch (error) {
    console.error('批量上传失败:', error)
    errors.value = [`批量上传失败: ${(error as Error).message}`]
    emit('upload-error', errors.value)
  } finally {
    uploading.value = false
    uploadProgress.value = 0
    uploadStatus.value = ''
  }
}

// 工具方法
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const copyUrl = async (url: string) => {
  try {
    await navigator.clipboard.writeText(url)
    // 这里可以添加成功提示
  } catch (error) {
    console.error('复制链接失败:', error)
  }
}

const copyMarkdown = async (result: ImageRecord, backup: any) => {
  const markdown = `![${result.originalName}](${backup.url})`
  try {
    await navigator.clipboard.writeText(markdown)
    // 这里可以添加成功提示
  } catch (error) {
    console.error('复制Markdown失败:', error)
  }
}

const handleImageError = (e: Event) => {
  const img = e.target as HTMLImageElement
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMyNCA0IDI4IDggMjggMTJDMjggMTYgMjQgMjAgMjAgMjBDMTYgMjAgMTIgMTYgMTIgMTJDMTIgOCAxNiA0IDIwIDRaIiBmaWxsPSIjOUI5QkEwIi8+Cjwvc3ZnPgo='
}

const clearResults = () => {
  uploadResults.value = []
}

const clearErrors = () => {
  errors.value = []
}

// 初始化
onMounted(() => {
  settings.value = imageUploadService.getSettings()
})
</script>

<style scoped>
/* 图片上传管理卡片 - 统一紧凑型布局 */
.image-uploader-card {
  margin-bottom: 16px;
}

/* 卡片标题样式 - 与AI配置统一 */
.card-title-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.dark .card-title {
  color: #fff;
}

.help-icon-btn {
  color: #8c8c8c;
  border: none;
  background: transparent;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.help-icon-btn:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.06);
}

.dark .help-icon-btn:hover {
  color: #40a9ff;
  background: rgba(64, 169, 255, 0.1);
}

/* 上传区域 - 紧凑型设计 */
.upload-area {
  position: relative;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 32px 16px;
  cursor: pointer;
  background: #fafafa;
  transition: all 0.2s ease;
  text-align: center;
}

.upload-area:hover {
  border-color: #40a9ff;
  background: #f0f9ff;
}

.dark .upload-area {
  background: #1f1f1f;
  border-color: #434343;
}

.dark .upload-area:hover {
  border-color: #177ddc;
  background: #111b26;
}

.upload-area.drag-over {
  border-color: #1890ff;
  background: #e6f7ff;
}

.dark .upload-area.drag-over {
  border-color: #177ddc;
  background: #111b26;
}

.upload-area.uploading {
  cursor: not-allowed;
  opacity: 0.75;
}

.upload-area.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.upload-area.disabled:hover {
  border-color: #d9d9d9;
  background: #fafafa;
}

.dark .upload-area.disabled:hover {
  border-color: #434343;
  background: #1f1f1f;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.upload-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.upload-progress-text {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin: 0;
}

.dark .upload-progress-text {
  color: #fff;
}

.upload-status-text {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0;
}

.dark .upload-status-text {
  color: #a6a6a6;
}

.progress-container {
  width: 100%;
  max-width: 300px;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.upload-icon {
  font-size: 48px;
  color: #d9d9d9;
}

.upload-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.dark .upload-title {
  color: #fff;
}

.upload-description {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0;
}

.dark .upload-description {
  color: #a6a6a6;
}

.upload-tips {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 8px;
}

.hidden {
  display: none;
}

/* 上传设置区域 - 紧凑型布局 */
.upload-settings-section {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-top: 12px;
}

.dark .upload-settings-section {
  background: #1f1f1f;
  border-color: #303030;
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.settings-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.dark .settings-title {
  color: #fff;
}

.settings-grid {
  margin-bottom: 0;
}

.setting-item-compact {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.setting-label-compact {
  font-size: 12px;
  font-weight: 500;
  color: #8c8c8c;
  margin: 0;
}

.dark .setting-label-compact {
  color: #a6a6a6;
}

.setting-select {
  width: 100%;
}

/* 上传结果区域 - 紧凑型布局 */
.upload-results-section {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-top: 12px;
}

.dark .upload-results-section {
  background: #1f1f1f;
  border-color: #303030;
}

.results-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.results-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.dark .results-title {
  color: #fff;
}

.results-list-compact {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.result-item-compact {
  display: flex;
  gap: 12px;
  padding: 12px;
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.dark .result-item-compact {
  background: #262626;
  border-color: #303030;
}

.result-image-compact {
  flex-shrink: 0;
}

.thumbnail-compact {
  width: 48px;
  height: 48px;
  object-fit: cover;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.dark .thumbnail-compact {
  border-color: #303030;
}

.result-content-compact {
  flex: 1;
  min-width: 0;
}

.result-header-compact {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 8px;
  gap: 8px;
}

.result-name-compact {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.dark .result-name-compact {
  color: #fff;
}

.result-meta-tags {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
  flex-wrap: wrap;
}

.result-backups-compact {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.backup-item-compact {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 8px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
}

.dark .backup-item-compact {
  background: #1a1a1a;
  border-color: #2a2a2a;
}

.backup-name-compact {
  font-size: 12px;
  font-weight: 500;
  color: #666;
}

.dark .backup-name-compact {
  color: #a6a6a6;
}

.backup-actions-compact {
  display: flex;
  gap: 4px;
}

.results-actions-compact {
  display: flex;
  justify-content: flex-end;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.dark .results-actions-compact {
  border-top-color: #303030;
}

/* 错误提示区域 */
.upload-errors-section {
  margin-top: 12px;
}

.errors-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.errors-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
}

.errors-list-compact {
  margin: 8px 0 0 0;
  padding: 0;
  list-style: none;
}

.error-item-compact {
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 4px;
}

.error-item-compact:last-child {
  margin-bottom: 0;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .upload-area {
    padding: 24px 12px;
  }

  .upload-icon {
    font-size: 36px;
  }

  .upload-title {
    font-size: 16px;
  }

  .result-header-compact {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .result-meta-tags {
    align-self: flex-start;
  }
}

.upload-area.disabled {
  @apply cursor-not-allowed opacity-50 hover:border-gray-300 dark:hover:border-gray-600;
}

.upload-content {
  @apply text-center;
}

.upload-placeholder {
  @apply space-y-4;
}

.upload-tips {
  @apply flex flex-wrap justify-center gap-4 text-xs text-gray-500 dark:text-gray-400;
}

.tip-item {
  @apply px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded;
}

.progress-bar {
  @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden;
}

.progress-fill {
  @apply h-full bg-primary-600 transition-all duration-300 ease-out;
}

.upload-settings {
  @apply bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6;
}

.settings-title {
  @apply text-lg font-medium text-gray-900 dark:text-gray-100 mb-4;
}

.settings-grid {
  @apply grid grid-cols-1 md:grid-cols-3 gap-4;
}

.setting-item {
  @apply space-y-2;
}

.setting-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.upload-results {
  @apply bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6;
}

.results-title {
  @apply text-lg font-medium text-gray-900 dark:text-gray-100 mb-4;
}

.results-list {
  @apply space-y-4 mb-6;
}

.result-item {
  @apply flex space-x-4 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg;
}

.result-image {
  @apply flex-shrink-0;
}

.thumbnail {
  @apply w-16 h-16 object-cover rounded-lg border border-gray-200 dark:border-gray-600;
}

.result-info {
  @apply flex-1 min-w-0;
}

.result-name {
  @apply font-medium text-gray-900 dark:text-gray-100 truncate;
}

.result-details {
  @apply flex flex-wrap gap-3 mt-1 text-sm text-gray-500 dark:text-gray-400;
}

.detail {
  @apply flex items-center;
}

.result-backups {
  @apply mt-3 space-y-2;
}

.backup-item {
  @apply flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600;
}

.backup-name {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}

.backup-actions {
  @apply flex space-x-1;
}

.action-btn {
  @apply p-1.5 text-gray-500 hover:text-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded transition-colors;
}

.results-actions {
  @apply flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700;
}

.upload-errors {
  @apply bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-6;
}

.errors-title {
  @apply text-lg font-medium text-red-900 dark:text-red-100 mb-3;
}

.errors-list {
  @apply space-y-2;
}

.error-item {
  @apply text-sm text-red-700 dark:text-red-300;
}

.btn {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-primary {
  @apply text-white bg-primary-600 hover:bg-primary-700 focus:ring-primary-500;
}

.btn-secondary {
  @apply text-gray-700 bg-gray-100 hover:bg-gray-200 focus:ring-gray-500;
  @apply dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}
</style>
