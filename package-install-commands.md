# 知识库重构所需依赖

## 安装命令
```bash
# 数据管理和查询
npm install @tanstack/vue-query

# 表单管理和验证
npm install vee-validate @vee-validate/zod zod

# 虚拟滚动（性能优化）
npm install @tanstack/vue-virtual

# 工具库
npm install lodash-es
npm install @types/lodash-es -D
```

## 依赖说明

### @tanstack/vue-query
- 替代手动的数据加载和缓存逻辑
- 自动处理加载状态、错误状态、重试等
- 提供强大的缓存和同步机制

### vee-validate + zod
- 替代手动的表单验证逻辑
- 类型安全的验证规则
- 更好的用户体验和错误处理

### @tanstack/vue-virtual
- 处理大量数据的虚拟滚动
- 提升性能，支持数千条记录的流畅滚动

### lodash-es
- 提供常用的工具函数
- 减少重复的数据处理逻辑
