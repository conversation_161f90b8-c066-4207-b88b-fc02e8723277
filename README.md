# KnowlEdge - 个人知识库管理系统

一个基于Vue.js的纯前端知识库管理系统，支持离线使用，数据存储在浏览器IndexedDB中。

## ✨ 特性

- 🚀 **纯前端实现** - 无需后端服务器，可打包为静态HTML文件独立运行
- 💾 **离线存储** - 数据存储在浏览器IndexedDB中，支持完全离线使用
- 🎨 **现代UI设计** - 基于UnoCSS原子化CSS，支持明暗主题切换
- 📱 **响应式布局** - 完美适配桌面端，优雅的交互动画
- 🔍 **智能搜索** - 支持全文搜索，多维度筛选
- 🏷️ **标签系统** - 灵活的标签管理，支持颜色标记
- 📁 **分类管理** - 树形分类结构，层次化组织
- ✍️ **Markdown支持** - 内置Markdown编辑器和预览
- 🎯 **实时预览** - 资源创建时支持实时预览效果

## 🛠️ 技术栈

- **前端框架**: Vue 3 + Composition API
- **构建工具**: Vite
- **类型支持**: TypeScript
- **样式方案**: UnoCSS 原子化CSS
- **数据存储**: Dexie.js (IndexedDB封装)
- **路由管理**: Vue Router
- **状态管理**: Pinia
- **时间处理**: dayjs
- **Markdown**: markdown-it + highlight.js
- **图标库**: Heroicons + Lucide

## 🚀 快速开始

### 环境要求

- Node.js 20.19.0+ (推荐使用最新LTS版本)
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览构建结果

```bash
npm run preview
```

## 📱 使用说明

### 1. 首次使用

- 访问首页查看系统概览
- 点击"浏览知识库"进入主界面
- 使用右下角悬浮按钮添加第一个资源

### 2. 添加资源

- 填写资源URL和标题（必填）
- 添加Markdown格式的描述
- 选择分类和标签
- 实时预览资源卡片效果

### 3. 管理资源

- 在知识库界面浏览所有资源
- 使用筛选和排序功能快速定位
- 点击资源卡片查看详情
- 使用操作菜单编辑或删除资源

### 4. 主题切换

- 点击顶部导航栏的主题按钮
- 支持浅色/深色/跟随系统三种模式
- 设置会自动保存到本地

## 🔧 项目结构

```
src/
├── components/          # 组件目录
│   ├── ui/             # 基础UI组件
│   ├── layout/         # 布局组件
│   └── knowledge/      # 知识库相关组件
├── views/              # 页面组件
├── services/           # 数据服务层
├── database/           # 数据库配置
├── composables/        # 组合式函数
├── types/              # 类型定义
└── router/             # 路由配置
```

## 📄 许可证

MIT License
