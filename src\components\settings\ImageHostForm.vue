<template>
    <!-- 图床配置表单组件 -->
    <a-form ref="formRef" :model="form" :rules="rules" layout="vertical" @finish="handleSubmit" class="form-content">
        <!-- 基础信息 -->
        <div class="form-section">
            <h4 class="section-title">基础信息</h4>

            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item label="配置名称" name="name" data-tutorial="config-name">
                        <a-input v-model:value="form.name" placeholder="如：我的PICUI图床" class="form-input" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="图床标识" name="provider" data-tutorial="provider">
                        <a-input v-model:value="form.provider" placeholder="如：picui" class="form-input" />
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item label="优先级" name="priority">
                        <a-input-number v-model:value="form.priority" placeholder="1-10，数字越小优先级越高" :min="1" :max="10"
                            style="width: 100%" class="form-input" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="状态">
                        <div class="form-switches">
                            <a-switch v-model:checked="form.enabled" />
                            <span class="switch-label">{{ form.enabled ? '启用' : '禁用' }}</span>
                        </div>
                    </a-form-item>
                </a-col>
            </a-row>
        </div>

        <!-- API配置 -->
        <div class="form-section">
            <h4 class="section-title">API配置</h4>

            <a-form-item label="API接口地址" name="apiUrl" data-tutorial="api-url">
                <a-input v-model:value="form.apiUrl" placeholder="https://example.com/api/upload" class="form-input" />
            </a-form-item>

            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item label="请求方法" name="method">
                        <a-select v-model:value="form.method" class="form-input">
                            <a-select-option v-for="option in methodOptions" :key="option.value" :value="option.value">
                                {{ option.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="响应格式" name="responseType">
                        <a-select v-model:value="form.responseType" class="form-input">
                            <a-select-option v-for="option in responseTypeOptions" :key="option.value"
                                :value="option.value">
                                {{ option.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-item>
                </a-col>
            </a-row>

            <a-form-item label="文件字段名" name="fileField">
                <a-input v-model:value="form.fileField" placeholder="file" class="form-input" />
            </a-form-item>
        </div>

        <!-- 认证配置 -->
        <div class="form-section">
            <h4 class="section-title">认证配置</h4>

            <a-form-item label="认证方式" name="authType" data-tutorial="auth-type">
                <a-select v-model:value="form.authType" class="form-input">
                    <a-select-option v-for="option in authTypeOptions" :key="option.value" :value="option.value">
                        {{ option.label }}
                    </a-select-option>
                </a-select>
            </a-form-item>

            <div v-if="form.authType !== 'none'" class="auth-config">
                <a-form-item label="认证密钥" name="authKey" data-tutorial="auth-key">
                    <a-input-password v-model:value="form.authKey" placeholder="输入API Key或Token" class="form-input" />
                </a-form-item>

                <div v-if="form.authType === 'header'">
                    <a-row :gutter="16">
                        <a-col :span="12">
                            <a-form-item label="认证头名称" name="authHeader">
                                <a-input v-model:value="form.authHeader" placeholder="Authorization"
                                    class="form-input" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="认证前缀" name="authPrefix">
                                <a-input v-model:value="form.authPrefix" placeholder="Bearer " class="form-input" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </div>
            </div>
        </div>

        <!-- 响应配置 -->
        <div class="form-section">
            <h4 class="section-title">响应配置</h4>

            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item label="成功标识字段" name="successField">
                        <a-input v-model:value="form.successField" placeholder="status" class="form-input" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="成功标识值" name="successValue">
                        <a-input v-model:value="form.successValue" placeholder="true" class="form-input" />
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item label="图片URL字段路径" name="urlField" data-tutorial="url-field">
                        <a-input v-model:value="form.urlField" placeholder="data.url" class="form-input" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="错误信息字段" name="errorField">
                        <a-input v-model:value="form.errorField" placeholder="message" class="form-input" />
                    </a-form-item>
                </a-col>
            </a-row>
        </div>

        <!-- 限制配置 -->
        <div class="form-section">
            <h4 class="section-title">限制配置</h4>

            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item label="最大文件大小(MB)" name="maxFileSize">
                        <a-input-number v-model:value="form.maxFileSize" placeholder="10" :min="1" :max="100"
                            style="width: 100%" class="form-input" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="支持的格式" name="allowedFormats">
                        <a-input v-model:value="allowedFormatsText" placeholder="jpg,png,gif,webp" class="form-input" />
                        <template #extra>
                            用逗号分隔多个格式
                        </template>
                    </a-form-item>
                </a-col>
            </a-row>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
            <a-space>
                <a-button @click="$emit('cancel')" class="form-button">
                    取消
                </a-button>
                <a-button type="primary" html-type="submit" :loading="saving" data-tutorial="save-btn"
                    class="form-button">
                    {{ saving ? '保存中...' : '保存' }}
                </a-button>
            </a-space>
        </div>
    </a-form>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import type { ImageHostConfig, ImageHostConfigForm } from '@/types/imageHost'

// 定义组件名称
defineOptions({
    name: 'ImageHostForm'
})

// Props
interface Props {
    config?: ImageHostConfig | null
    saving?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    saving: false
})

// Emits
const emit = defineEmits<{
    submit: [formData: ImageHostConfigForm]
    cancel: []
}>()

// 响应式数据
const formRef = ref()
const errors = ref<Record<string, string>>({})

const isEditing = computed(() => !!props.config)

// 表单数据
const form = ref<ImageHostConfigForm>({
    name: '',
    provider: '',
    enabled: true,
    priority: 1,
    apiUrl: '',
    method: 'POST',
    authType: 'none',
    authKey: '',
    authHeader: 'Authorization',
    authPrefix: 'Bearer ',
    fileField: 'file',
    responseType: 'json',
    successField: 'status',
    successValue: 'true',
    urlField: 'url',
    errorField: 'message',
    maxFileSize: 10,
    allowedFormats: ['jpg', 'jpeg', 'png', 'gif', 'webp']
})

// 支持的格式文本
const allowedFormatsText = computed({
    get: () => form.value.allowedFormats?.join(',') || '',
    set: (value: string) => {
        form.value.allowedFormats = value.split(',').map(f => f.trim()).filter(f => f)
    }
})

// 选项数据
const methodOptions = [
    { label: 'POST', value: 'POST' },
    { label: 'GET', value: 'GET' }
]

const responseTypeOptions = [
    { label: 'JSON', value: 'json' },
    { label: 'Text', value: 'text' }
]

const authTypeOptions = [
    { label: '无需认证', value: 'none' },
    { label: 'Header认证', value: 'header' },
    { label: 'Token认证', value: 'token' },
    { label: 'URL参数认证', value: 'query' }
]

// 初始化表单
const initForm = () => {
    if (props.config) {
        Object.assign(form.value, {
            name: props.config.name,
            provider: props.config.provider,
            enabled: props.config.enabled,
            priority: props.config.priority,
            apiUrl: props.config.apiUrl,
            method: props.config.method,
            authType: props.config.authType,
            authKey: props.config.authKey || '',
            authHeader: props.config.authHeader || 'Authorization',
            authPrefix: props.config.authPrefix || 'Bearer ',
            fileField: props.config.fileField,
            responseType: props.config.responseType,
            successField: props.config.successField || '',
            successValue: props.config.successValue !== undefined ? props.config.successValue : '',
            urlField: props.config.urlField,
            errorField: props.config.errorField || '',
            maxFileSize: props.config.maxFileSize || 10,
            allowedFormats: props.config.allowedFormats || []
        })
    }
}

// 验证表单
const validateForm = (): boolean => {
    errors.value = {}

    if (!form.value.name.trim()) {
        errors.value.name = '请输入配置名称'
    }

    if (!form.value.provider.trim()) {
        errors.value.provider = '请输入图床标识'
    }

    if (!form.value.apiUrl.trim()) {
        errors.value.apiUrl = '请输入API接口地址'
    }

    if (!form.value.fileField.trim()) {
        errors.value.fileField = '请输入文件字段名'
    }

    if (!form.value.urlField.trim()) {
        errors.value.urlField = '请输入图片URL字段路径'
    }

    if (form.value.authType !== 'none' && !form.value.authKey?.trim()) {
        errors.value.authKey = '请输入认证密钥'
    }

    if (form.value.priority < 1 || form.value.priority > 10) {
        errors.value.priority = '优先级必须在1-10之间'
    }

    return Object.keys(errors.value).length === 0
}

// 处理提交
const handleSubmit = async () => {
    if (!validateForm()) return

    // 处理successValue的类型转换
    let successValue = form.value.successValue
    if (successValue === 'true') successValue = true
    else if (successValue === 'false') successValue = false
    else if (successValue !== '' && !isNaN(Number(successValue))) successValue = Number(successValue)

    const formData = {
        ...form.value,
        successValue
    }

    emit('submit', formData)
}

// 表单验证规则
const rules = {
    name: [{ required: true, message: '请输入配置名称' }],
    provider: [{ required: true, message: '请输入图床标识' }],
    priority: [{ required: true, message: '请输入优先级' }],
    apiUrl: [{ required: true, message: '请输入API接口地址' }],
    method: [{ required: true, message: '请选择请求方法' }],
    responseType: [{ required: true, message: '请选择响应格式' }],
    fileField: [{ required: true, message: '请输入文件字段名' }],
    authType: [{ required: true, message: '请选择认证方式' }],
    urlField: [{ required: true, message: '请输入图片URL字段路径' }]
}

// 组件挂载时初始化表单
onMounted(() => {
    initForm()
})

// 监听配置变化
watch(() => props.config, initForm, { immediate: true })
</script>

<style scoped>
/* 图床配置表单样式 - 统一标准 */
.form-content {
    margin: 0;
}

.form-section {
    margin-bottom: 20px;
}

.section-title {
    color: var(--ant-color-text);
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    padding-bottom: 6px;
    border-bottom: 1px solid var(--ant-color-border);
}

.auth-config {
    margin-top: 12px;
    padding: 12px;
    background: var(--ant-color-fill-quaternary);
    border-radius: 6px;
    border: 1px solid var(--ant-color-border);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid var(--ant-color-border);
}

/* 表单输入框统一样式 */
.form-input {
    height: 36px;
    border-radius: 6px;
    font-size: 14px;
}

.form-input :deep(.ant-input) {
    height: 36px;
    border-radius: 6px;
    font-size: 14px;
}

.form-input :deep(.ant-select-selector) {
    height: 36px !important;
    border-radius: 6px;
    font-size: 14px;
}

.form-input :deep(.ant-input-number) {
    height: 36px;
    border-radius: 6px;
    font-size: 14px;
    width: 100%;
}

.form-input :deep(.ant-input-password) {
    height: 36px;
    border-radius: 6px;
    font-size: 14px;
}

/* 开关组件样式 */
.form-switches {
    display: flex;
    align-items: center;
    height: 36px;
    gap: 8px;
}

.switch-label {
    font-size: 14px;
    color: var(--ant-color-text-secondary);
}

/* 按钮样式 */
.form-button {
    height: 36px;
    border-radius: 6px;
    font-size: 14px;
    min-width: 80px;
}

/* 暗黑模式适配 */
.dark .auth-config {
    background: #1f1f1f;
    border-color: #303030;
}

.dark .section-title {
    color: #f0f0f0;
    border-bottom-color: #303030;
}

.dark .switch-label {
    color: #a6a6a6;
}
</style>
