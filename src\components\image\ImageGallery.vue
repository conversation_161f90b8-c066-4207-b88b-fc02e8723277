<template>
  <!-- 图片库页面 - 参考知识库界面设计 -->
  <div class="image-gallery-container">
    <!-- 紧凑型功能区 -->
    <a-card class="function-card" size="small">
      <div class="function-content">
        <!-- 第一行：统计信息和操作按钮 -->
        <div class="function-row function-controls">
          <!-- 左侧统计信息 -->
          <div class="stats-info">
            <span class="stats-text">共 {{ filteredImages.length }} 张图片</span>
            <span v-if="filters.status || filters.tags.length > 0" class="filter-summary">
              (已筛选)
            </span>
          </div>

          <!-- 右侧操作按钮 -->
          <div class="action-controls">
            <a-button @click="refreshImages" :loading="loading" size="small">
              <template #icon>
                <div class="i-heroicons-arrow-path"></div>
              </template>
              刷新
            </a-button>
          </div>
        </div>

        <!-- 第二行：筛选控件 -->
        <div class="function-row filter-controls-row">
          <!-- 状态筛选 -->
          <div class="filter-item">
            <span class="filter-label">状态：</span>
            <a-select v-model:value="filters.status" placeholder="选择状态" allow-clear class="status-selector"
              size="small">
              <a-select-option v-for="status in statusFilters" :key="status.value" :value="status.value">
                {{ status.label }}
                <span v-if="status.count > 0" class="option-count">({{ status.count }})</span>
              </a-select-option>
            </a-select>
          </div>

          <!-- 排序选择 -->
          <div class="filter-item">
            <span class="filter-label">排序：</span>
            <a-select v-model:value="filters.sortBy" class="sort-selector" size="small">
              <a-select-option v-for="option in sortOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </a-select-option>
            </a-select>
          </div>
        </div>

        <!-- 第三行：标签筛选 -->
        <div class="function-row tags-row" v-if="displayedTags.length > 0">
          <div class="tags-container">
            <span class="filter-label">标签：</span>
            <div class="tags-list">
              <a-tag v-for="tag in displayedTags" :key="tag.id"
                :color="selectedTags.includes(tag.id || 0) ? 'blue' : 'default'" :checkable="true"
                :checked="selectedTags.includes(tag.id || 0)" @change="toggleTag(tag.id || 0)" class="tag-item">
                {{ tag.name }} ({{ tag.resource_count }})
              </a-tag>

              <a-tag v-if="hasMoreTags" @click="loadMoreTags" class="more-tags-btn">
                +{{ remainingTagsCount }}
              </a-tag>

              <a-button v-if="selectedTags.length > 0" type="link" size="small" @click="clearTagFilters"
                class="clear-tags-btn">
                <template #icon>
                  <div class="i-heroicons-x-mark"></div>
                </template>
                清除筛选
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 批量操作栏 -->
    <a-card v-if="selectedImages.length > 0" class="batch-actions-card" size="small">
      <div class="batch-actions-content">
        <span class="batch-info">已选择 {{ selectedImages.length }} 张图片</span>
        <div class="batch-buttons">
          <a-button size="small" @click="batchAddTags">
            <template #icon>
              <div class="i-heroicons-tag"></div>
            </template>
            编辑标签
          </a-button>
          <a-button size="small" danger @click="batchDelete">
            <template #icon>
              <div class="i-heroicons-trash"></div>
            </template>
            删除
          </a-button>
        </div>
      </div>
    </a-card>

    <!-- 主要内容区域 -->
    <div class="content-area">
      <!-- 加载状态 -->
      <div v-if="loading && images.length === 0" class="loading-state">
        <a-spin size="large" />
        <p class="loading-text">加载图片中...</p>
      </div>

      <!-- 空状态 -->
      <div v-else-if="filteredImages.length === 0" class="empty-state">
        <a-empty :description="images.length === 0 ? '还没有上传任何图片' : '没有找到匹配的图片'">
          <template #image>
            <div class="i-heroicons-photo empty-icon"></div>
          </template>
          <a-button v-if="images.length === 0" type="primary" @click="showUploader = true">
            <template #icon>
              <div class="i-heroicons-plus"></div>
            </template>
            上传图片
          </a-button>
        </a-empty>
      </div>

      <!-- 瀑布流图片网格 -->
      <div v-else class="image-grid" ref="imageContainer">
        <div v-for="image in paginatedImages" :key="image.id" class="image-item" @click="viewImageDetail(image)">
          <!-- 图片容器 -->
          <div class="image-wrapper">
            <img :src="getPrimaryImageUrl(image)" :alt="image.originalName" @error="handleImageError" loading="lazy"
              class="image-photo" />

            <!-- 悬停时显示的图片名称 -->
            <div class="image-name-overlay">
              <p class="image-name-text">{{ image.originalName }}</p>
            </div>

            <!-- 悬停操作按钮 -->
            <div class="image-hover-actions">
              <button @click.stop="copyImageUrl(image)" class="hover-action-btn copy-btn" title="复制链接">
                <div class="i-heroicons-clipboard w-4 h-4"></div>
              </button>
              <button @click.stop="editImage(image)" class="hover-action-btn edit-btn" title="编辑">
                <div class="i-heroicons-pencil w-4 h-4"></div>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="pagination">
        <button @click="currentPage = Math.max(1, currentPage - 1)" :disabled="currentPage === 1"
          class="pagination-btn">
          <div class="i-heroicons-chevron-left w-4 h-4"></div>
        </button>

        <span class="pagination-info">
          第 {{ currentPage }} 页，共 {{ totalPages }} 页
        </span>

        <button @click="currentPage = Math.min(totalPages, currentPage + 1)" :disabled="currentPage === totalPages"
          class="pagination-btn">
          <div class="i-heroicons-chevron-right w-4 h-4"></div>
        </button>
      </div>
    </div>

    <!-- 分页控件 -->
    <div v-if="totalPages > 1" class="pagination-area">
      <a-pagination v-model:current="currentPage" :total="filteredImages.length" :page-size="pageSize"
        :show-size-changer="false" :show-quick-jumper="true"
        :show-total="(total, range) => `第 ${range[0]}-${range[1]} 项，共 ${total} 项`" size="small" />
    </div>

    <!-- 图片详情模态框 -->
    <ImageDetailModal v-if="showImageDetail && selectedImage" :image="selectedImage" @close="closeImageDetail"
      @edit="handleImageEdit" @delete="handleImageDelete" />

    <!-- 标签编辑模态框 -->
    <TagEditModal v-if="showTagModal" :images="selectedImageRecords" @close="showTagModal = false"
      @save="handleTagsUpdate" />

    <!-- 图片上传模态框 -->
    <ImageUploadModal v-if="showUploader" @close="showUploader = false" @success="handleUploadSuccess" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { imageDataService } from '@/services/imageDataService'
import { imageLifecycleService } from '@/services/imageLifecycleService'
import type { ImageRecord } from '@/services/imageDataService'
import ImageDetailModal from './ImageDetailModal.vue'
import TagEditModal from './TagEditModal.vue'
import ImageUploadModal from './ImageUploadModal.vue'

// 响应式数据
const loading = ref(false)
const images = ref<ImageRecord[]>([])
const selectedImages = ref<number[]>([])
const selectedImage = ref<ImageRecord | null>(null)
const showUploader = ref(false)
const showTagModal = ref(false)
const showImageDetail = ref(false)
const currentPage = ref(1)
const pageSize = 24 // 增加每页显示数量以适应瀑布流
const masonryContainer = ref<HTMLElement | null>(null)

// 筛选条件
const filters = ref({
  status: '',
  tags: [] as string[],
  sortBy: 'uploadTime-desc'
})

// 标签相关状态
const selectedTags = ref<number[]>([])
const displayedTagsCount = ref(10)
const allTags = ref<Array<{ id: number, name: string, resource_count: number }>>([])

// 计算属性
const displayedTags = computed(() => allTags.value.slice(0, displayedTagsCount.value))
const hasMoreTags = computed(() => allTags.value.length > displayedTagsCount.value)
const remainingTagsCount = computed(() => allTags.value.length - displayedTagsCount.value)

// 统计信息
const statistics = ref({
  total: 0,
  active: 0,
  expired: 0,
  failed: 0,
  expiring: 0,
  totalSize: 0
})

// 检查图片是否有活跃的URL
const hasActiveUrls = (image: ImageRecord): boolean => {
  return image.urls && image.urls.some(url => url.status === 'active')
}

// 状态筛选选项
const statusFilters = computed(() => [
  { label: '全部', value: '', count: images.value.length },
  { label: '正常', value: 'active', count: images.value.filter(img => hasActiveUrls(img)).length },
  { label: '即将过期', value: 'expiring', count: images.value.filter(img => isExpiring(img)).length },
  { label: '已过期', value: 'expired', count: images.value.filter(img => isExpired(img)).length },
  { label: '失效', value: 'failed', count: images.value.filter(img => !hasActiveUrls(img)).length }
])

// 热门标签
const popularTags = computed(() => {
  const tagCounts = new Map<string, number>()
  images.value.forEach(image => {
    image.tags.forEach(tag => {
      tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1)
    })
  })

  return Array.from(tagCounts.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .map(([tag]) => tag)
})

const sortOptions = [
  { label: '上传时间 ↓', value: 'uploadTime-desc' },
  { label: '上传时间 ↑', value: 'uploadTime-asc' },
  { label: '文件大小 ↓', value: 'size-desc' },
  { label: '文件大小 ↑', value: 'size-asc' },
  { label: '文件名 A-Z', value: 'name-asc' },
  { label: '文件名 Z-A', value: 'name-desc' }
]

// 计算属性

const filteredImages = computed(() => {
  let filtered = images.value

  // 状态筛选
  if (filters.value.status) {
    filtered = filtered.filter(image => {
      switch (filters.value.status) {
        case 'active':
          return hasActiveUrls(image) && !isExpiring(image) && !isExpired(image)
        case 'expiring':
          return isExpiring(image)
        case 'expired':
          return isExpired(image)
        case 'failed':
          return !hasActiveUrls(image)
        default:
          return true
      }
    })
  }

  // 标签筛选
  if (filters.value.tags.length > 0) {
    filtered = filtered.filter(image =>
      filters.value.tags.some(tag => image.tags.includes(tag))
    )
  }

  // 排序
  const [sortField, sortOrder] = filters.value.sortBy.split('-')
  filtered.sort((a, b) => {
    let aValue: any, bValue: any

    switch (sortField) {
      case 'uploadTime':
        aValue = a.uploadTime.getTime()
        bValue = b.uploadTime.getTime()
        break
      case 'size':
        aValue = a.size
        bValue = b.size
        break
      case 'name':
        aValue = a.originalName.toLowerCase()
        bValue = b.originalName.toLowerCase()
        break
      default:
        return 0
    }

    if (sortOrder === 'desc') {
      return bValue > aValue ? 1 : bValue < aValue ? -1 : 0
    } else {
      return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
    }
  })

  return filtered
})

const totalPages = computed(() => Math.ceil(filteredImages.value.length / pageSize))

const paginatedImages = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filteredImages.value.slice(start, end)
})

// 选中的图片记录
const selectedImageRecords = computed(() => {
  return images.value.filter(image => selectedImages.value.includes(image.id || 0))
})

// 方法
const loadImages = async () => {
  loading.value = true
  try {
    images.value = await imageDataService.getImages()
    await updateStatistics()
  } catch (error) {
    console.error('加载图片失败:', error)
  } finally {
    loading.value = false
  }
}

const updateStatistics = async () => {
  statistics.value = await imageLifecycleService.getStatistics()
}

const refreshImages = async () => {
  await loadImages()
  // 执行生命周期检查（服务层已禁用，不会执行实际检查）
  await imageLifecycleService.performLifecycleCheck()
  await updateStatistics()
}

// 标签筛选切换
const toggleTagFilter = (tag: string) => {
  const index = filters.value.tags.indexOf(tag)
  if (index > -1) {
    filters.value.tags.splice(index, 1)
  } else {
    filters.value.tags.push(tag)
  }
}

// 排序变更
const handleSortChange = (sortValue: string) => {
  filters.value.sortBy = sortValue
}

// 标签相关方法
const toggleTag = (tagId: number) => {
  const index = selectedTags.value.indexOf(tagId)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else {
    selectedTags.value.push(tagId)
  }
}

const loadMoreTags = () => {
  displayedTagsCount.value += 10
}

const clearTagFilters = () => {
  selectedTags.value = []
}

const toggleImageSelection = (imageId: number) => {
  const index = selectedImages.value.indexOf(imageId)
  if (index > -1) {
    selectedImages.value.splice(index, 1)
  } else {
    selectedImages.value.push(imageId)
  }
}

// 查看图片详情
const viewImageDetail = (image: ImageRecord) => {
  selectedImage.value = image
  showImageDetail.value = true
}

// 关闭图片详情模态框
const closeImageDetail = () => {
  showImageDetail.value = false
  selectedImage.value = null
}

// 处理图片编辑
const handleImageEdit = (image: ImageRecord) => {
  // 这里可以实现编辑逻辑
  console.log('编辑图片:', image)
  closeImageDetail()
}

// 处理图片删除
const handleImageDelete = async (imageId: number) => {
  try {
    // 调用删除API
    await imageDataService.deleteImage(imageId)
    console.log('删除图片:', imageId)
    // 从列表中移除图片
    const index = images.value.findIndex(img => img.id === imageId)
    if (index > -1) {
      images.value.splice(index, 1)
    }
    closeImageDetail()
  } catch (error) {
    console.error('删除图片失败:', error)
  }
}

// 复制图片链接
const copyImageUrl = async (image: ImageRecord) => {
  const url = getPrimaryImageUrl(image)
  try {
    await navigator.clipboard.writeText(url)
    // 显示成功通知
    const { useToast } = await import('@/composables/useToast')
    const { success } = useToast()
    success('链接复制成功', url)
  } catch (error) {
    console.error('复制失败:', error)
    // 显示错误通知
    const { useToast } = await import('@/composables/useToast')
    const { error: showError } = useToast()
    showError('复制失败', '无法访问剪贴板')
  }
}

const editImage = (image: ImageRecord) => {
  selectedImage.value = image
  // 编辑图片逻辑
  console.log('编辑图片:', image)
}

const getPrimaryImageUrl = (image: ImageRecord): string => {
  const activeUrl = image.urls.find(url => url.status === 'active')
  return activeUrl?.url || image.urls[0]?.url || ''
}

const isExpired = (image: ImageRecord): boolean => {
  // 简化版本：暂时返回false，后续可以根据需要添加过期逻辑
  return false
}

const isExpiring = (image: ImageRecord): boolean => {
  // 简化版本：暂时返回false，后续可以根据需要添加即将过期逻辑
  return false
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const copyUrl = async (url: string) => {
  try {
    await navigator.clipboard.writeText(url)
    // 这里可以添加成功提示
  } catch (error) {
    console.error('复制链接失败:', error)
  }
}

const handleImageError = (e: Event) => {
  const img = e.target as HTMLImageElement
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMyNCA0IDI4IDggMjggMTJDMjggMTYgMjQgMjAgMjAgMjBDMTYgMjAgMTIgMTYgMTIgMTJDMTIgOCAxNiA0IDIwIDRaIiBmaWxsPSIjOUI5QkEwIi8+Cjwvc3ZnPgo='
}

// 批量操作
const batchAddTags = () => {
  if (selectedImages.value.length === 0) return
  showTagModal.value = true
}



const batchDelete = async () => {
  if (selectedImages.value.length === 0) return

  if (!confirm(`确定要删除选中的 ${selectedImages.value.length} 张图片吗？此操作不可恢复。`)) {
    return
  }

  try {
    await imageLifecycleService.deleteMultipleRecords(selectedImages.value)
    selectedImages.value = []
    await loadImages()
  } catch (error) {
    console.error('批量删除失败:', error)
    alert('批量删除失败')
  }
}

const handleTagsUpdate = async (imageIds: string[], tags: string[]) => {
  try {
    for (const imageId of imageIds) {
      await imageLifecycleService.updateImageTags(imageId, tags)
    }
    await loadImages()
    showTagModal.value = false
    selectedImages.value = []
  } catch (error) {
    console.error('更新标签失败:', error)
    alert('更新标签失败')
  }
}

// 监听筛选条件变化，重置分页
watch(filters, () => {
  currentPage.value = 1
}, { deep: true })

// 组件挂载时加载数据
onMounted(() => {
  loadImages()
  // 启动生命周期管理（服务层已禁用，不会执行实际检查）
  imageLifecycleService.startLifecycleManagement()
})

// 上传成功处理
const handleUploadSuccess = (results: ImageRecord[]) => {
  showUploader.value = false
  loadImages() // 重新加载图片列表
}



// 暴露方法给父组件
defineExpose({
  refreshImages,
  loadImages
})
</script>

<style scoped>
/* 图片库页面 - 参考知识库界面设计风格 */
.image-gallery-container {
  padding: 16px;
  background: var(--ant-color-bg-layout);
  min-height: 100vh;
}

/* 功能卡片 - 参考知识库设计 */
.function-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.function-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.function-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 12px;
}

/* 统计信息样式 */
.stats-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-text {
  color: var(--ant-color-text-secondary);
  font-size: 14px;
}

.filter-summary {
  color: var(--ant-color-primary);
  font-weight: 500;
  font-size: 12px;
}

/* 操作控件 */
.action-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 筛选控件行 */
.filter-controls-row {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  color: var(--ant-color-text);
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.status-selector,
.sort-selector {
  min-width: 120px;
}

.option-count {
  color: var(--ant-color-text-tertiary);
  font-size: 12px;
}

/* 标签行 */
.tags-row {
  align-items: flex-start;
}

.tags-container {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  flex: 1;
}

.tag-item {
  cursor: pointer;
  transition: all 0.2s;
}

.more-tags-btn {
  cursor: pointer;
  border-style: dashed;
}

.clear-tags-btn {
  padding: 0 8px;
  height: 24px;
}

/* 批量操作卡片 */
.batch-actions-card {
  margin-bottom: 16px;
  border: 1px solid var(--ant-color-primary-border);
  background: var(--ant-color-primary-bg);
}

.batch-actions-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 12px;
}

.batch-info {
  color: var(--ant-color-primary);
  font-weight: 500;
  font-size: 14px;
}

.batch-buttons {
  display: flex;
  gap: 8px;
}

/* 内容区域 */
.content-area {
  margin-top: 16px;
}

/* 加载和空状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 16px;
  gap: 16px;
}

.loading-text {
  color: var(--ant-color-text-secondary);
  margin: 0;
}

.empty-state {
  padding: 64px 16px;
}

.empty-icon {
  font-size: 48px;
  color: var(--ant-color-text-tertiary);
}

/* 分页区域 */
.pagination-area {
  display: flex;
  justify-content: center;
  padding: 24px 0;
  margin-top: 24px;
  border-top: 1px solid var(--ant-color-border-secondary);
}



/* 图片网格布局 - 保持瀑布流设计，优化样式 */
.image-grid {
  column-count: 2;
  column-gap: 16px;
  column-fill: balance;
  margin-top: 16px;
}

@media (min-width: 640px) {
  .image-grid {
    column-count: 3;
  }
}

@media (min-width: 768px) {
  .image-grid {
    column-count: 4;
  }
}

@media (min-width: 1024px) {
  .image-grid {
    column-count: 5;
  }
}

@media (min-width: 1280px) {
  .image-grid {
    column-count: 6;
  }
}

/* 图片项 - 参考ResourceCard设计 */
.image-item {
  break-inside: avoid;
  margin-bottom: 16px;
  cursor: pointer;
}

.image-wrapper {
  position: relative;
  overflow: hidden;
  background: var(--ant-color-bg-container);
  border: 1px solid var(--ant-color-border);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.image-wrapper:hover {
  border-color: var(--ant-color-primary-border);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.image-photo {
  width: 100%;
  height: auto;
  display: block;
  object-fit: cover;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.image-wrapper:hover .image-photo {
  transform: scale(1.02);
}

/* 悬停操作按钮 */
.image-hover-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.image-wrapper:hover .image-hover-actions {
  opacity: 1;
}

/* 图片名称悬停显示 */
.image-name-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 20px 12px 12px;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.image-wrapper:hover .image-name-overlay {
  opacity: 1;
}

.image-name-text {
  color: white;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.4;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  word-break: break-all;
}

/* 悬停操作按钮样式 */
.hover-action-btn {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  backdrop-filter: blur(8px);
  background: var(--ant-color-bg-elevated);
  color: var(--ant-color-text);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hover-action-btn:hover {
  background: var(--ant-color-primary);
  color: var(--ant-color-white);
  transform: scale(1.1);
}

.copy-btn {
  background-color: rgba(16, 185, 129, 0.9);
  color: white;
}

.copy-btn:hover {
  background-color: rgba(16, 185, 129, 1);
}

.edit-btn {
  background-color: rgba(59, 130, 246, 0.9);
  color: white;
}

.edit-btn:hover {
  background-color: rgba(59, 130, 246, 1);
}

/* 图片选择框 */
.image-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 20;
}

.checkbox-input {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  border: 1px solid var(--ant-color-border);
  background: var(--ant-color-bg-container);
}

/* 图片状态标签 */
.image-status {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 20;
}

.status-badge {
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 4px;
  backdrop-filter: blur(8px);
}

.status-expired {
  background: rgba(255, 77, 79, 0.9);
  color: white;
}

.status-expiring {
  background: rgba(250, 173, 20, 0.9);
  color: white;
}

.status-failed {
  background: rgba(255, 77, 79, 0.9);
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .function-row {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-controls-row {
    justify-content: space-between;
  }

  .action-controls {
    justify-content: center;
  }

  .tags-container {
    flex-direction: column;
    align-items: stretch;
  }

  .tags-list {
    margin-top: 8px;
  }
}
</style>
