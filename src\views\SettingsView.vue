<template>
  <!-- 设置页面内容容器 - 背景占满屏幕，内容限制1440px -->
  <div class="page-content-wrapper bg-gray-50 dark:bg-gray-900 settings-page">
    <!-- 移动端顶部导航 -->
    <div class="lg:hidden">
      <div class="content-container pt-8 pb-4">
        <a-card size="small">
          <a-menu v-model:selectedKeys="selectedKeys" mode="horizontal" :items="menuItems" @click="handleMenuClick"
            class="border-none bg-transparent" />
        </a-card>
      </div>
    </div>

    <!-- 桌面端布局 -->
    <div class="flex">
      <!-- 左侧导航栏 - 全高度，不受容器限制 -->
      <div class="hidden lg:block w-64 flex-shrink-0">
        <div class="sticky z-10" style="top: 96px;">
          <div class="ml-8">
            <a-card size="small" class="shadow-lg">
              <a-menu v-model:selectedKeys="selectedKeys" mode="vertical" :items="menuItems" @click="handleMenuClick"
                class="border-none bg-transparent" />
            </a-card>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="flex-1 min-w-0">
        <div class="content-container py-8">
          <!-- 页面标题栏 - 固定在内容区域顶部 -->
          <div class="sticky z-20 mb-6" style="top: 96px;">
            <a-card size="small" class="shadow-lg border-b-2 border-primary-200 dark:border-primary-600">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <component :is="getCurrentPageIcon()" class="w-6 h-6 text-primary-600 dark:text-primary-400" />
                  <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {{ getCurrentPageTitle() }}
                  </h1>
                </div>
                <a-tag :color="getPageTypeColor()" class="text-sm">
                  {{ getPageType() }}
                </a-tag>
              </div>
            </a-card>
          </div>

          <!-- 内容区域 -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-primary-200 dark:border-primary-600">

            <!-- 分类管理 -->
            <div v-if="activeTab === 'category'" class="p-6">
              <CategoryManagementAntd />
            </div>

            <!-- 标签管理 -->
            <div v-if="activeTab === 'tag'" class="p-6">
              <TagManagementAntd />
            </div>

            <!-- AI配置管理 -->
            <div v-if="activeTab === 'ai'" class="p-6">
              <AiConfigManagementAntd />
            </div>

            <!-- 搜索引擎设置 -->
            <div v-if="activeTab === 'search-engine'" class="p-6">
              <SearchEngineManagementAntd />
            </div>

            <!-- 知识库浏览设置 -->
            <div v-if="activeTab === 'knowledge'" class="p-6">
              <KnowledgeSettingsAntd />
            </div>

            <!-- 数据管理 -->
            <div v-if="activeTab === 'data'" class="p-6">
              <DataManagementAntd />
            </div>

            <!-- 图床管理 -->
            <div v-if="activeTab === 'image-host'" class="p-6">
              <ImageHostManagement />
            </div>

            <!-- 主题设置 -->
            <div v-if="activeTab === 'theme'" class="p-6">
              <ThemeSettingsAntd />
            </div>

            <!-- 关于 -->
            <div v-if="activeTab === 'about'" class="p-6">
              <div class="space-y-6">
                <div class="text-center">
                  <div
                    class="w-20 h-20 bg-primary-100 dark:bg-primary-900/30 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <InfoCircleOutlined class="text-4xl text-primary-600 dark:text-primary-400" />
                  </div>
                  <h3 class="text-2xl font-bold text-primary mb-2">KnowlEdge</h3>
                  <p class="text-gray-600 dark:text-gray-400 mb-4">智能知识库管理系统</p>
                  <div
                    class="inline-flex items-center px-3 py-1 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded-full text-sm font-medium">
                    版本 1.0.0
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useRoute } from 'vue-router'
import {
  FolderOutlined,
  TagOutlined,
  RobotOutlined,
  SearchOutlined,
  BookOutlined,
  DatabaseOutlined,
  PictureOutlined,
  BgColorsOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue'
import CategoryManagementAntd from '@/components/management/CategoryManagementAntd.vue'
import TagManagementAntd from '@/components/management/TagManagementAntd.vue'
import AiConfigManagementAntd from '@/components/settings/AiConfigManagementAntd.vue'
import SearchEngineManagementAntd from '@/components/settings/SearchEngineManagementAntd.vue'
import KnowledgeSettingsAntd from '@/components/settings/KnowledgeSettingsAntd.vue'
import ThemeSettingsAntd from '@/components/settings/ThemeSettingsAntd.vue'
import DataManagementAntd from '@/components/settings/DataManagementAntd.vue'
import ImageHostManagement from '@/components/settings/ImageHostManagement.vue'

// 路由
const route = useRoute()

// 状态
const activeTab = ref('category')

// 选中的菜单项
const selectedKeys = computed(() => [activeTab.value])

// 菜单项配置
const menuItems = computed(() => [
  {
    key: 'category',
    icon: () => h(FolderOutlined),
    label: '分类管理'
  },
  {
    key: 'tag',
    icon: () => h(TagOutlined),
    label: '标签管理'
  },
  {
    key: 'ai',
    icon: () => h(RobotOutlined),
    label: 'AI配置'
  },
  {
    key: 'search-engine',
    icon: () => h(SearchOutlined),
    label: '搜索引擎设置'
  },
  {
    key: 'knowledge',
    icon: () => h(BookOutlined),
    label: '知识库浏览'
  },
  {
    key: 'data',
    icon: () => h(DatabaseOutlined),
    label: '数据管理'
  },
  {
    key: 'image-host',
    icon: () => h(PictureOutlined),
    label: '图床管理'
  },
  {
    key: 'theme',
    icon: () => h(BgColorsOutlined),
    label: '主题设置'
  },
  {
    key: 'about',
    icon: () => h(InfoCircleOutlined),
    label: '关于'
  }
])

// 初始化时检查URL参数
onMounted(() => {
  const tabParam = route.query.tab as string
  const validTabs = ['category', 'tag', 'ai', 'search-engine', 'knowledge', 'data', 'image-host', 'theme', 'about']
  if (tabParam && validTabs.includes(tabParam)) {
    activeTab.value = tabParam
  }
})

// 菜单点击处理
const handleMenuClick = ({ key }: { key: string }) => {
  activeTab.value = key
}

// 页面信息配置
const pageConfig = {
  'category': {
    title: '分类管理',
    icon: FolderOutlined,
    type: '内容管理',
    color: 'blue'
  },
  'tag': {
    title: '标签管理',
    icon: TagOutlined,
    type: '内容管理',
    color: 'green'
  },
  'ai': {
    title: 'AI配置',
    icon: RobotOutlined,
    type: '系统配置',
    color: 'purple'
  },
  'search-engine': {
    title: '搜索引擎设置',
    icon: SearchOutlined,
    type: '系统配置',
    color: 'orange'
  },
  'knowledge': {
    title: '知识库浏览设置',
    icon: BookOutlined,
    type: '系统配置',
    color: 'cyan'
  },
  'data': {
    title: '数据管理',
    icon: DatabaseOutlined,
    type: '系统管理',
    color: 'red'
  },
  'image-host': {
    title: '图床管理',
    icon: PictureOutlined,
    type: '系统配置',
    color: 'magenta'
  },
  'theme': {
    title: '主题设置',
    icon: BgColorsOutlined,
    type: '个性化',
    color: 'gold'
  },
  'about': {
    title: '关于',
    icon: InfoCircleOutlined,
    type: '系统信息',
    color: 'geekblue'
  }
}

// 获取当前页面标题
const getCurrentPageTitle = () => {
  return pageConfig[activeTab.value as keyof typeof pageConfig]?.title || '设置'
}

// 获取当前页面图标
const getCurrentPageIcon = () => {
  return pageConfig[activeTab.value as keyof typeof pageConfig]?.icon || InfoCircleOutlined
}

// 获取页面类型
const getPageType = () => {
  return pageConfig[activeTab.value as keyof typeof pageConfig]?.type || '设置'
}

// 获取页面类型颜色
const getPageTypeColor = () => {
  return pageConfig[activeTab.value as keyof typeof pageConfig]?.color || 'default'
}

// 保持向后兼容的方法
const handleTabChange = (tabId: string) => {
  activeTab.value = tabId
}
</script>