<template>
  <div :class="cardClasses" @click="handleClick">
    <div v-if="$slots.header" class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <slot name="header"></slot>
    </div>

    <div :class="bodyClasses">
      <slot></slot>
    </div>

    <div v-if="$slots.footer" class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  variant?: 'default' | 'bordered' | 'shadow' | 'elevated'
  padding?: 'none' | 'sm' | 'md' | 'lg'
  hoverable?: boolean
  clickable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  padding: 'md',
  hoverable: false,
  clickable: false
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const cardClasses = computed(() => {
  const baseClasses = [
    'bg-white dark:bg-gray-800 rounded-xl transition-all duration-200'
  ]

  // 变体样式 - 优化边框和阴影对比度
  const variantClasses = {
    default: 'border border-gray-200/60 dark:border-gray-700/60',
    bordered: 'border-2 border-primary-200 dark:border-primary-700',
    shadow: [
      'shadow-md border border-gray-200/60 dark:border-gray-700/60',
      'shadow-gray-200/50 dark:shadow-gray-900/20'
    ].join(' '),
    elevated: [
      'shadow-lg border border-gray-200/60 dark:border-gray-700/60',
      'shadow-gray-300/50 dark:shadow-gray-900/30'
    ].join(' ')
  }

  // 交互样式 - 增强视觉反馈
  const interactionClasses = []
  if (props.hoverable) {
    interactionClasses.push(
      'hover:shadow-lg hover:-translate-y-0.5',
      'hover:border-gray-300/80 dark:hover:border-gray-600/80',
      'hover:shadow-gray-300/60 dark:hover:shadow-gray-900/40'
    )
  }
  if (props.clickable) {
    interactionClasses.push(
      'cursor-pointer hover:shadow-md active:scale-[0.98]',
      'hover:border-primary-300/60 dark:hover:border-primary-600/60',
      'focus:outline-none focus:ring-3 focus:ring-primary-200 dark:focus:ring-primary-800'
    )
  }

  return [
    ...baseClasses,
    variantClasses[props.variant],
    ...interactionClasses
  ]
})

const bodyClasses = computed(() => {
  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  }

  return paddingClasses[props.padding]
})

const handleClick = (event: MouseEvent) => {
  if (props.clickable) {
    emit('click', event)
  }
}
</script>
