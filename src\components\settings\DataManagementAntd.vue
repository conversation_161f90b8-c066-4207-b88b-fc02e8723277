<template>
  <a-space direction="vertical" size="large" style="width: 100%">
    <!-- 说明信息 -->
    <a-alert message="数据管理" description="管理您的知识库数据，包括本地导出导入和云端同步功能。所有操作都会保留数据完整性。" type="info" show-icon />

    <!-- 本地数据管理 -->
    <a-card title="本地数据管理" size="small">
      <a-space direction="vertical" size="middle" style="width: 100%">
        <!-- 数据导出 -->
        <a-card size="small">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-base font-medium text-gray-900 dark:text-gray-100">数据导出</h4>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                将所有数据导出为压缩文件，包括知识库、分类、标签、设置、AI配置和对话历史
              </p>
            </div>
            <a-button type="primary" :loading="exporting" @click="exportData">
              <DownloadOutlined v-if="!exporting" />
              {{ exporting ? '导出中...' : '导出数据' }}
            </a-button>
          </div>

          <a-alert v-if="exportStatus" :message="exportStatus.message"
            :type="exportStatus.success ? 'success' : 'error'" show-icon class="mt-4" />
        </a-card>

        <!-- 数据导入 -->
        <a-card size="small">
          <div class="flex items-center justify-between mb-4">
            <div>
              <h4 class="text-base font-medium text-gray-900 dark:text-gray-100">数据导入</h4>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                从备份文件恢复数据，支持完整导入或选择性导入
              </p>
            </div>
            <a-upload :before-upload="beforeUpload" :show-upload-list="false" accept=".zip,.json">
              <a-button :loading="importing">
                <UploadOutlined v-if="!importing" />
                {{ importing ? '导入中...' : '选择文件' }}
              </a-button>
            </a-upload>
          </div>

          <a-alert v-if="importStatus" :message="importStatus.message"
            :type="importStatus.success ? 'success' : 'error'" show-icon class="mt-4" />

          <!-- 导入选项 -->
          <div v-if="importFile" class="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <h5 class="font-medium mb-3">导入选项</h5>
            <a-checkbox-group v-model:value="importOptions">
              <a-space direction="vertical">
                <a-checkbox value="knowledge">知识库数据</a-checkbox>
                <a-checkbox value="categories">分类和标签</a-checkbox>
                <a-checkbox value="settings">应用设置</a-checkbox>
                <a-checkbox value="ai">AI配置</a-checkbox>
                <a-checkbox value="history">对话历史</a-checkbox>
              </a-space>
            </a-checkbox-group>

            <div class="mt-4">
              <a-space>
                <a-button type="primary" @click="confirmImport" :loading="importing">
                  确认导入
                </a-button>
                <a-button @click="cancelImport">
                  取消
                </a-button>
              </a-space>
            </div>
          </div>
        </a-card>

        <!-- 数据清理 -->
        <a-card size="small">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-base font-medium text-gray-900 dark:text-gray-100">数据清理</h4>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                清理无效数据和缓存，释放存储空间
              </p>
            </div>
            <a-button :loading="cleaning" @click="cleanData">
              <ClearOutlined v-if="!cleaning" />
              {{ cleaning ? '清理中...' : '清理数据' }}
            </a-button>
          </div>
        </a-card>
      </a-space>
    </a-card>

    <!-- 云端同步 -->
    <a-card title="云端同步" size="small">
      <a-space direction="vertical" size="middle" style="width: 100%">
        <!-- 同步状态 -->
        <a-card size="small">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-base font-medium text-gray-900 dark:text-gray-100">同步状态</h4>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                上次同步：{{ lastSyncTime || '从未同步' }}
              </p>
            </div>
            <a-space>
              <a-tag :color="syncStatus === 'synced' ? 'green' : syncStatus === 'syncing' ? 'blue' : 'orange'">
                {{ syncStatusText }}
              </a-tag>
              <a-button type="primary" :loading="syncing" @click="syncData">
                <SyncOutlined v-if="!syncing" />
                {{ syncing ? '同步中...' : '立即同步' }}
              </a-button>
            </a-space>
          </div>
        </a-card>

        <!-- 自动同步设置 -->
        <a-card size="small">
          <a-space direction="vertical" size="middle" style="width: 100%">
            <a-form-item label="自动同步">
              <a-switch v-model:checked="autoSync" checked-children="开启" un-checked-children="关闭" />
              <template #extra>
                <div class="text-sm text-gray-500">启用后将定期自动同步数据到云端</div>
              </template>
            </a-form-item>

            <a-form-item label="同步间隔" v-if="autoSync">
              <a-select v-model:value="syncInterval" style="width: 200px" :options="[
                { label: '每5分钟', value: 5 },
                { label: '每15分钟', value: 15 },
                { label: '每30分钟', value: 30 },
                { label: '每小时', value: 60 },
                { label: '每天', value: 1440 }
              ]" />
            </a-form-item>

            <a-form-item label="同步内容">
              <a-checkbox-group v-model:value="syncContent">
                <a-space direction="vertical">
                  <a-checkbox value="knowledge">知识库数据</a-checkbox>
                  <a-checkbox value="settings">应用设置</a-checkbox>
                  <a-checkbox value="ai">AI配置</a-checkbox>
                </a-space>
              </a-checkbox-group>
            </a-form-item>
          </a-space>
        </a-card>
      </a-space>
    </a-card>

    <!-- 存储统计 -->
    <a-card title="存储统计" size="small">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic title="知识库条目" :value="storageStats.knowledge" suffix="条" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="分类数量" :value="storageStats.categories" suffix="个" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="标签数量" :value="storageStats.tags" suffix="个" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="存储大小" :value="storageStats.size" suffix="MB" :precision="2" />
        </a-col>
      </a-row>
    </a-card>

    <!-- 操作按钮 -->
    <div class="flex justify-end">
      <a-space>
        <a-button @click="refreshStats">
          刷新统计
        </a-button>
        <a-button type="primary" :loading="saving" @click="saveSettings">
          {{ saving ? '保存中...' : '保存设置' }}
        </a-button>
      </a-space>
    </div>
  </a-space>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  DownloadOutlined,
  UploadOutlined,
  ClearOutlined,
  SyncOutlined
} from '@ant-design/icons-vue'

// 状态
const exporting = ref(false)
const importing = ref(false)
const cleaning = ref(false)
const syncing = ref(false)
const saving = ref(false)

const exportStatus = ref<{ success: boolean; message: string } | null>(null)
const importStatus = ref<{ success: boolean; message: string } | null>(null)
const importFile = ref<File | null>(null)

// 设置数据
const autoSync = ref(false)
const syncInterval = ref(30)
const syncContent = ref(['knowledge', 'settings'])
const importOptions = ref(['knowledge', 'categories', 'settings', 'ai', 'history'])

const syncStatus = ref<'synced' | 'syncing' | 'pending'>('pending')
const lastSyncTime = ref<string>('')

// 存储统计
const storageStats = reactive({
  knowledge: 1234,
  categories: 56,
  tags: 189,
  size: 12.5
})

// 计算属性
const syncStatusText = computed(() => {
  switch (syncStatus.value) {
    case 'synced': return '已同步'
    case 'syncing': return '同步中'
    case 'pending': return '待同步'
    default: return '未知'
  }
})

// 方法
const exportData = async () => {
  exporting.value = true
  exportStatus.value = null

  try {
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 模拟导出
    const blob = new Blob(['导出的数据内容'], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `knowledge-backup-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)

    exportStatus.value = { success: true, message: '数据导出成功！' }
    message.success('数据导出成功！')
  } catch (error) {
    exportStatus.value = { success: false, message: '数据导出失败！' }
    message.error('数据导出失败！')
  } finally {
    exporting.value = false
  }
}

const beforeUpload = (file: File) => {
  importFile.value = file
  importStatus.value = null
  return false // 阻止自动上传
}

const confirmImport = async () => {
  if (!importFile.value) return

  importing.value = true
  importStatus.value = null

  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    importStatus.value = { success: true, message: '数据导入成功！' }
    message.success('数据导入成功！')
    importFile.value = null
  } catch (error) {
    importStatus.value = { success: false, message: '数据导入失败！' }
    message.error('数据导入失败！')
  } finally {
    importing.value = false
  }
}

const cancelImport = () => {
  importFile.value = null
  importStatus.value = null
}

const cleanData = async () => {
  cleaning.value = true

  try {
    await new Promise(resolve => setTimeout(resolve, 1500))
    message.success('数据清理完成！')
  } catch (error) {
    message.error('数据清理失败！')
  } finally {
    cleaning.value = false
  }
}

const syncData = async () => {
  syncing.value = true
  syncStatus.value = 'syncing'

  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    syncStatus.value = 'synced'
    lastSyncTime.value = new Date().toLocaleString()
    message.success('数据同步成功！')
  } catch (error) {
    syncStatus.value = 'pending'
    message.error('数据同步失败！')
  } finally {
    syncing.value = false
  }
}

const refreshStats = async () => {
  // 模拟刷新统计数据
  storageStats.knowledge = Math.floor(Math.random() * 2000) + 1000
  storageStats.categories = Math.floor(Math.random() * 100) + 20
  storageStats.tags = Math.floor(Math.random() * 300) + 100
  storageStats.size = Math.random() * 50 + 5

  message.success('统计数据已刷新')
}

const saveSettings = async () => {
  saving.value = true

  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('设置保存成功！')
  } catch (error) {
    message.error('设置保存失败！')
  } finally {
    saving.value = false
  }
}

onMounted(() => {
  // 加载设置
  console.log('数据管理页面已加载')
})
</script>
