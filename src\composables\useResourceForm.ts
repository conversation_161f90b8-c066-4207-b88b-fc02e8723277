import { computed, ref } from 'vue'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { z } from 'zod'
import { message } from 'ant-design-vue'
import { categoryService } from '@/services/categoryService'
import { tagService } from '@/services/tagService'
import { useResourceMutations } from './useResourceList'
import type { ResourceForm, Category, Tag } from '@/types'

// Zod 验证模式
const resourceSchema = z.object({
  url: z.string()
    .min(1, '请输入资源链接')
    .url('请输入有效的URL地址'),
  title: z.string()
    .min(1, '请输入资源标题')
    .max(200, '标题长度不能超过200个字符'),
  description: z.string()
    .max(5000, '描述长度不能超过5000个字符')
    .optional()
    .default(''),
  cover_image_url: z.string()
    .url('请输入有效的图片URL')
    .optional()
    .or(z.literal(''))
    .default(''),
  category_id: z.number()
    .min(1, '请选择资源分类'),
  tag_ids: z.array(z.number())
    .default([])
})

export type ResourceFormData = z.infer<typeof resourceSchema>

export interface UseResourceFormOptions {
  initialData?: Partial<ResourceFormData>
  isEditing?: boolean
  resourceId?: number
}

export interface UseResourceFormResult {
  // 表单状态
  values: Ref<ResourceFormData>
  errors: Ref<Record<string, string>>
  isSubmitting: Ref<boolean>
  isValid: Ref<boolean>
  
  // 表单方法
  setFieldValue: (field: keyof ResourceFormData, value: any) => void
  setFieldError: (field: keyof ResourceFormData, error: string) => void
  resetForm: () => void
  handleSubmit: (onSubmit: (values: ResourceFormData) => void | Promise<void>) => Promise<void>
  
  // 辅助数据
  categories: Ref<Category[]>
  tags: Ref<Tag[]>
  selectedTags: Ref<Tag[]>
  
  // 辅助方法
  loadCategories: () => Promise<void>
  loadTags: () => Promise<void>
  addTag: (tag: Tag) => void
  removeTag: (tagId: number) => void
  createAndAddTag: (tagName: string, color?: string) => Promise<void>
  
  // 预设操作
  fillFromUrl: (url: string) => Promise<void>
  previewData: Ref<ResourceFormData | null>
}

/**
 * 资源表单管理 Hook
 * 统一处理资源创建和编辑表单的逻辑
 */
export function useResourceForm(options: UseResourceFormOptions = {}): UseResourceFormResult {
  const {
    initialData = {},
    isEditing = false,
    resourceId
  } = options

  const { createResource, updateResource } = useResourceMutations()

  // 辅助数据
  const categories = ref<Category[]>([])
  const tags = ref<Tag[]>([])
  const selectedTags = ref<Tag[]>([])
  const previewData = ref<ResourceFormData | null>(null)

  // 表单配置
  const { 
    values, 
    errors, 
    setFieldValue, 
    setFieldError, 
    resetForm: originalResetForm,
    handleSubmit: originalHandleSubmit,
    meta
  } = useForm<ResourceFormData>({
    validationSchema: toTypedSchema(resourceSchema),
    initialValues: {
      url: '',
      title: '',
      description: '',
      cover_image_url: '',
      category_id: 1,
      tag_ids: [],
      ...initialData
    }
  })

  // 计算属性
  const isSubmitting = ref(false)
  const isValid = computed(() => meta.value.valid)

  // 加载分类数据
  const loadCategories = async () => {
    try {
      categories.value = await categoryService.getAllCategories()
    } catch (error) {
      console.error('加载分类失败:', error)
      message.error('加载分类失败')
    }
  }

  // 加载标签数据
  const loadTags = async () => {
    try {
      tags.value = await tagService.getAllTags()
      // 根据 tag_ids 设置选中的标签
      if (values.tag_ids && values.tag_ids.length > 0) {
        selectedTags.value = tags.value.filter(tag => 
          values.tag_ids.includes(tag.id!)
        )
      }
    } catch (error) {
      console.error('加载标签失败:', error)
      message.error('加载标签失败')
    }
  }

  // 添加标签
  const addTag = (tag: Tag) => {
    if (!selectedTags.value.find(t => t.id === tag.id)) {
      selectedTags.value.push(tag)
      setFieldValue('tag_ids', selectedTags.value.map(t => t.id!))
    }
  }

  // 移除标签
  const removeTag = (tagId: number) => {
    selectedTags.value = selectedTags.value.filter(tag => tag.id !== tagId)
    setFieldValue('tag_ids', selectedTags.value.map(t => t.id!))
  }

  // 创建并添加新标签
  const createAndAddTag = async (tagName: string, color = '#1677ff') => {
    try {
      const tagId = await tagService.createTag({ name: tagName, color })
      const newTag = await tagService.getTagById(tagId)
      if (newTag) {
        tags.value.push(newTag)
        addTag(newTag)
        message.success(`标签"${tagName}"创建成功`)
      }
    } catch (error) {
      console.error('创建标签失败:', error)
      message.error('创建标签失败')
    }
  }

  // 从URL自动填充信息
  const fillFromUrl = async (url: string) => {
    try {
      // 这里可以实现URL解析逻辑，获取网页标题、描述等
      // 暂时只设置URL
      setFieldValue('url', url)
      
      // 可以扩展为调用API获取网页元信息
      // const metaInfo = await fetchUrlMetaInfo(url)
      // if (metaInfo.title) setFieldValue('title', metaInfo.title)
      // if (metaInfo.description) setFieldValue('description', metaInfo.description)
      // if (metaInfo.image) setFieldValue('cover_image_url', metaInfo.image)
      
    } catch (error) {
      console.error('解析URL失败:', error)
    }
  }

  // 重置表单
  const resetForm = () => {
    originalResetForm()
    selectedTags.value = []
    previewData.value = null
  }

  // 处理表单提交
  const handleSubmit = async (onSubmit?: (values: ResourceFormData) => void | Promise<void>) => {
    return originalHandleSubmit(async (formValues) => {
      isSubmitting.value = true
      
      try {
        // 确保 tag_ids 与 selectedTags 同步
        const submitData = {
          ...formValues,
          tag_ids: selectedTags.value.map(tag => tag.id!)
        }

        if (onSubmit) {
          await onSubmit(submitData)
        } else {
          // 默认提交逻辑
          if (isEditing && resourceId) {
            await updateResource(resourceId, submitData)
            message.success('资源更新成功')
          } else {
            await createResource(submitData)
            message.success('资源创建成功')
          }
        }
      } catch (error) {
        console.error('提交失败:', error)
        message.error(isEditing ? '更新失败' : '创建失败')
        throw error
      } finally {
        isSubmitting.value = false
      }
    })()
  }

  // 生成预览数据
  const generatePreviewData = () => {
    if (isValid.value) {
      previewData.value = {
        ...values,
        tag_ids: selectedTags.value.map(tag => tag.id!)
      }
    }
    return previewData.value
  }

  return {
    // 表单状态
    values,
    errors,
    isSubmitting,
    isValid,
    
    // 表单方法
    setFieldValue,
    setFieldError,
    resetForm,
    handleSubmit,
    
    // 辅助数据
    categories,
    tags,
    selectedTags,
    
    // 辅助方法
    loadCategories,
    loadTags,
    addTag,
    removeTag,
    createAndAddTag,
    fillFromUrl,
    previewData
  }
}

/**
 * 分类表单管理 Hook
 */
export function useCategoryForm(initialData?: Partial<any>) {
  const categorySchema = z.object({
    name: z.string().min(1, '请输入分类名称').max(50, '分类名称不能超过50个字符'),
    parent_id: z.number().default(0),
    sort_order: z.number().default(0)
  })

  const { values, errors, setFieldValue, handleSubmit, resetForm } = useForm({
    validationSchema: toTypedSchema(categorySchema),
    initialValues: {
      name: '',
      parent_id: 0,
      sort_order: 0,
      ...initialData
    }
  })

  return {
    values,
    errors,
    setFieldValue,
    handleSubmit,
    resetForm
  }
}

/**
 * 标签表单管理 Hook
 */
export function useTagForm(initialData?: Partial<any>) {
  const tagSchema = z.object({
    name: z.string().min(1, '请输入标签名称').max(30, '标签名称不能超过30个字符'),
    color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, '请选择有效的颜色').default('#1677ff')
  })

  const { values, errors, setFieldValue, handleSubmit, resetForm } = useForm({
    validationSchema: toTypedSchema(tagSchema),
    initialValues: {
      name: '',
      color: '#1677ff',
      ...initialData
    }
  })

  return {
    values,
    errors,
    setFieldValue,
    handleSubmit,
    resetForm
  }
}
