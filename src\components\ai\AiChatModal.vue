<template>
  <BaseModal v-model="isOpen" title="AI对话助手" size="xl" :show-close="false" class="ai-chat-modal">
    <div class="flex h-[600px]">
      <!-- 左侧会话列表 -->
      <div class="w-64 border-r border-gray-200 dark:border-gray-700 flex flex-col">
        <!-- 会话列表头部 -->
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">
              对话历史
            </h3>
            <BaseButton size="sm" variant="outline" @click="handleNewChat" :disabled="aiStore.chatStatus !== 'idle'">
              <div class="i-heroicons-plus w-4 h-4"></div>
            </BaseButton>
          </div>

          <!-- 搜索框 -->
          <BaseInput v-model="searchKeyword" placeholder="搜索对话..." size="sm" class="mb-2">
            <template #prefix>
              <div class="i-heroicons-magnifying-glass w-4 h-4 text-gray-400"></div>
            </template>
          </BaseInput>
        </div>

        <!-- 会话列表 -->
        <div class="flex-1 overflow-y-auto">
          <div v-if="filteredSessions.length === 0" class="p-4 text-center text-gray-500 dark:text-gray-400 text-sm">
            {{ searchKeyword ? '未找到匹配的对话' : '暂无对话历史' }}
          </div>

          <div v-else class="space-y-1 p-2">
            <div v-for="session in filteredSessions" :key="session.id" @click="handleSelectSession(session.id)" :class="[
              'p-3 rounded-lg cursor-pointer transition-colors group',
              session.id === aiStore.currentSession?.id
                ? 'bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800'
                : 'hover:bg-gray-50 dark:hover:bg-gray-800'
            ]">
              <div class="flex items-start justify-between">
                <div class="flex-1 min-w-0">
                  <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                    {{ session.title }}
                  </h4>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {{ formatDate(session.updatedAt) }}
                  </p>
                  <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                    {{ session.messages.length }} 条消息
                  </p>
                </div>

                <BaseDropdown placement="bottom-end" class="opacity-0 group-hover:opacity-100 transition-opacity">
                  <template #trigger>
                    <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded">
                      <div class="i-heroicons-ellipsis-vertical w-4 h-4"></div>
                    </button>
                  </template>

                  <div class="w-32">
                    <DropdownItem text="重命名" icon="i-heroicons-pencil" @click="handleRenameSession(session)" />
                    <DropdownItem text="删除" icon="i-heroicons-trash" @click="handleDeleteSession(session.id)"
                      class="text-red-600 dark:text-red-400" />
                  </div>
                </BaseDropdown>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部操作 -->
        <div class="p-4 border-t border-gray-200 dark:border-gray-700">
          <BaseButton variant="outline" size="sm" @click="handleClearHistory" :disabled="aiStore.sessions.length === 0"
            class="w-full">
            <div class="i-heroicons-trash mr-2"></div>
            清空历史
          </BaseButton>
        </div>
      </div>

      <!-- 右侧对话区域 -->
      <div class="flex-1 flex flex-col">
        <!-- 对话头部 -->
        <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div
              class="w-8 h-8 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full flex items-center justify-center">
              <div class="i-heroicons-chat-bubble-left-right text-white w-4 h-4"></div>
            </div>
            <div>
              <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ aiStore.currentSession?.title || '新对话' }}
              </h3>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                {{ aiStore.config?.modelName || 'AI助手' }}
              </p>
            </div>
          </div>

          <div class="flex items-center space-x-2">
            <BaseButton size="sm" variant="outline" @click="handleOpenSettings">
              <div class="i-heroicons-cog-6-tooth w-4 h-4"></div>
            </BaseButton>

            <BaseButton size="sm" variant="outline" @click="handleClose">
              <div class="i-heroicons-x-mark w-4 h-4"></div>
            </BaseButton>
          </div>
        </div>

        <!-- 消息列表 -->
        <div ref="messagesContainer" class="flex-1 overflow-y-auto">
          <div v-if="aiStore.currentMessages.length === 0" class="flex items-center justify-center h-full p-6">
            <div class="text-center max-w-sm">
              <div
                class="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                <div class="i-heroicons-sparkles w-6 h-6 text-white"></div>
              </div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                开始新对话
              </h3>
              <p class="text-gray-500 dark:text-gray-400 text-sm">
                向AI助手提问任何问题，开始智能对话
              </p>
            </div>
          </div>

          <div v-else class="space-y-0">
            <div v-for="message in aiStore.currentMessages" :key="message.id" :class="[
              'px-4 py-4 border-b border-gray-100 dark:border-gray-800 last:border-b-0 group',
              message.role === 'user' ? 'bg-gray-50/50 dark:bg-gray-800/30' : 'bg-white dark:bg-gray-900'
            ]">
              <div class="flex items-start space-x-3">
                <!-- 头像 -->
                <div class="flex-shrink-0">
                  <div :class="[
                    'w-7 h-7 rounded-full flex items-center justify-center',
                    message.role === 'user'
                      ? 'bg-primary-500 text-white'
                      : 'bg-gradient-to-br from-emerald-400 to-emerald-600 text-white'
                  ]">
                    <div :class="[
                      'w-3.5 h-3.5',
                      message.role === 'user' ? 'i-heroicons-user' : 'i-heroicons-sparkles'
                    ]"></div>
                  </div>
                </div>

                <!-- 消息内容 -->
                <div class="flex-1 min-w-0">
                  <!-- 角色和时间 -->
                  <div class="flex items-center mb-1">
                    <span class="text-xs font-medium text-gray-900 dark:text-gray-100">
                      {{ message.role === 'user' ? '你' : 'AI助手' }}
                    </span>
                    <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">
                      {{ formatTime(message.timestamp) }}
                    </span>
                    <span v-if="message.tokens" class="ml-2 text-xs text-gray-400 dark:text-gray-500">
                      {{ message.tokens }} tokens
                    </span>
                  </div>

                  <!-- 消息内容 -->
                  <div class="text-sm text-gray-800 dark:text-gray-200">
                    <div v-if="message.error" class="text-red-600 dark:text-red-400">
                      <div
                        class="flex items-center mb-2 p-2 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                        <div class="i-heroicons-exclamation-triangle w-3 h-3 mr-2"></div>
                        <span class="text-xs font-medium">发送失败</span>
                      </div>
                      <p class="text-xs ml-2">{{ message.error }}</p>
                    </div>

                    <div v-else-if="message.content" class="prose prose-sm prose-gray dark:prose-invert max-w-none">
                      <MarkdownPreview :content="message.content" />
                    </div>

                    <div v-else-if="message.role === 'assistant'"
                      class="flex items-center space-x-2 text-gray-500 dark:text-gray-400 py-1">
                      <LoadingSpinner size="sm" />
                      <span class="text-xs">正在思考...</span>
                    </div>
                  </div>

                  <!-- 操作按钮 -->
                  <div v-if="message.content && !message.error"
                    class="flex items-center space-x-1 mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button @click="copyMessage(message.content)"
                      class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                      title="复制">
                      <div class="i-heroicons-clipboard w-3 h-3"></div>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="p-4 border-t border-gray-200 dark:border-gray-700">
          <div class="flex items-end space-x-3">
            <div class="flex-1">
              <BaseTextarea ref="messageInput" v-model="aiStore.currentMessage" @keydown="handleKeyDown"
                placeholder="输入消息... (Shift+Enter换行，Enter发送)"
                :disabled="!aiStore.canSendMessage || aiStore.chatStatus !== 'idle'" :rows="1" size="sm" clearable />
            </div>

            <BaseButton @click="handleSendMessage" :disabled="!aiStore.canSendMessage"
              :loading="aiStore.chatStatus === 'sending' || aiStore.chatStatus === 'receiving'">
              <div v-if="aiStore.chatStatus === 'idle'" class="i-heroicons-paper-airplane w-4 h-4"></div>
              <div v-else class="i-heroicons-stop w-4 h-4"></div>
            </BaseButton>
          </div>

          <!-- 状态提示 -->
          <div v-if="!aiStore.isConfigured" class="mt-2 text-xs text-amber-600 dark:text-amber-400">
            <div class="i-heroicons-exclamation-triangle w-3 h-3 inline mr-1"></div>
            请先配置AI服务
          </div>
          <div v-else-if="!aiStore.isEnabled" class="mt-2 text-xs text-amber-600 dark:text-amber-400">
            <div class="i-heroicons-exclamation-triangle w-3 h-3 inline mr-1"></div>
            AI服务未启用
          </div>
        </div>
      </div>
    </div>

    <!-- AI设置模态框 -->
    <AiSettingsModal v-model="showSettings" />
  </BaseModal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useAiStore } from '@/stores/aiStore'
import type { AiChatSession } from '@/types'
import BaseModal from '@/components/ui/BaseModal.vue'
import BaseInput from '@/components/ui/BaseInput.vue'
import BaseTextarea from '@/components/ui/BaseTextarea.vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import BaseDropdown from '@/components/ui/BaseDropdown.vue'
import DropdownItem from '@/components/ui/DropdownItem.vue'
import LoadingSpinner from '@/components/ui/LoadingSpinner.vue'
import MarkdownPreview from '@/components/knowledge/MarkdownPreview.vue'
import AiSettingsModal from './AiSettingsModalAntd.vue'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// Store
const aiStore = useAiStore()

// 状态
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const searchKeyword = ref('')
const showSettings = ref(false)
const messagesContainer = ref<HTMLElement>()
const messageInput = ref<HTMLTextAreaElement>()

// 计算属性
const filteredSessions = computed(() => {
  if (!searchKeyword.value.trim()) {
    return aiStore.sessions
  }

  const keyword = searchKeyword.value.toLowerCase()
  return aiStore.sessions.filter(session =>
    session.title.toLowerCase().includes(keyword) ||
    session.messages.some(msg => msg.content.toLowerCase().includes(keyword))
  )
})

// 监听消息变化，自动滚动到底部
watch(() => aiStore.currentMessages, () => {
  nextTick(() => {
    scrollToBottom()
  })
}, { deep: true })

// 监听模态框打开
watch(isOpen, (newValue) => {
  if (newValue) {
    nextTick(() => {
      messageInput.value?.focus()
    })
  }
})

// 方法
const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const formatDate = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days === 0) {
    return '今天'
  } else if (days === 1) {
    return '昨天'
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString()
  }
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const handleNewChat = async () => {
  try {
    await aiStore.createNewSession()
    searchKeyword.value = ''
    nextTick(() => {
      messageInput.value?.focus()
    })
  } catch (error) {
    console.error('创建新对话失败:', error)
  }
}

const handleSelectSession = async (sessionId: string) => {
  try {
    await aiStore.selectSession(sessionId)
    nextTick(() => {
      scrollToBottom()
      messageInput.value?.focus()
    })
  } catch (error) {
    console.error('选择对话失败:', error)
  }
}

const handleRenameSession = async (session: AiChatSession) => {
  const newTitle = prompt('请输入新的对话标题:', session.title)
  if (newTitle && newTitle.trim() !== session.title) {
    try {
      await aiStore.updateSessionTitle(session.id, newTitle.trim())
    } catch (error) {
      console.error('重命名对话失败:', error)
    }
  }
}

const handleDeleteSession = async (sessionId: string) => {
  if (confirm('确定要删除这个对话吗？此操作不可撤销。')) {
    try {
      await aiStore.deleteSession(sessionId)
    } catch (error) {
      console.error('删除对话失败:', error)
    }
  }
}

const handleClearHistory = async () => {
  if (confirm('确定要清空所有对话历史吗？此操作不可撤销。')) {
    try {
      await aiStore.clearAllHistory()
      searchKeyword.value = ''
    } catch (error) {
      console.error('清空历史失败:', error)
    }
  }
}

const handleSendMessage = async () => {
  if (!aiStore.canSendMessage) return

  const message = aiStore.currentMessage.trim()
  if (!message) return

  try {
    await aiStore.sendMessage(message)
    aiStore.currentMessage = ''

    // 自动调整输入框高度
    if (messageInput.value) {
      messageInput.value.style.height = '40px'
    }
  } catch (error) {
    console.error('发送消息失败:', error)
  }
}

const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    handleSendMessage()
  }

  // 自动调整输入框高度
  nextTick(() => {
    if (messageInput.value) {
      messageInput.value.style.height = '40px'
      messageInput.value.style.height = messageInput.value.scrollHeight + 'px'
    }
  })
}

const handleOpenSettings = () => {
  showSettings.value = true
}

const handleClose = () => {
  isOpen.value = false
}

// 复制消息内容
const copyMessage = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content)
    console.log('消息已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
  }
}
</script>

<style scoped>
.ai-chat-modal :deep(.modal-content) {
  padding: 0;
}

.prose {
  font-size: 0.875rem;
}

.prose p {
  margin: 0.5rem 0;
}

.prose p:first-child {
  margin-top: 0;
}

.prose p:last-child {
  margin-bottom: 0;
}
</style>
