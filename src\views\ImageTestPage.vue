<template>
  <div class="image-test-page">
    <div class="container mx-auto p-6">
      <h1 class="text-2xl font-bold mb-6">图片预览和编辑功能测试</h1>
      
      <!-- 测试按钮 -->
      <div class="mb-6">
        <a-button type="primary" @click="openUploadModal">
          打开图片上传模态框
        </a-button>
      </div>

      <!-- 测试图片网格 -->
      <div class="mb-6">
        <h2 class="text-xl font-semibold mb-4">图片预览测试</h2>
        <ImageViewer
          :images="testImages"
          :columns="5"
          :show-info="true"
          :show-delete="true"
          :aspect-ratio="1"
          @edit="handleEdit"
          @delete="handleDelete"
          @preview="handlePreview"
        />
      </div>

      <!-- 图片上传模态框 -->
      <ImageUploadModal
        v-model:visible="uploadModalVisible"
        @uploaded="handleUploaded"
        @close="handleClose"
      />

      <!-- 图片编辑器 -->
      <ImageEditor
        v-model:visible="editorVisible"
        :image-src="editingImageSrc"
        :image-name="editingImageName"
        @save="handleEditorSave"
        @cancel="handleEditorCancel"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ImageViewer from '@/components/image/ImageViewer.vue'
import ImageEditor from '@/components/image/ImageEditor.vue'
import ImageUploadModal from '@/components/image/ImageUploadModal.vue'
import type { ImageItem } from '@/components/image/ImageViewer.vue'

// 响应式数据
const uploadModalVisible = ref(false)
const editorVisible = ref(false)
const editingImageSrc = ref('')
const editingImageName = ref('')

// 测试图片数据
const testImages = ref<ImageItem[]>([
  {
    id: '1',
    src: 'https://picsum.photos/800/600?random=1',
    thumbnail: 'https://picsum.photos/200/200?random=1',
    name: '测试图片1.jpg',
    width: 800,
    height: 600,
    size: 102400,
    alt: '测试图片1'
  },
  {
    id: '2',
    src: 'https://picsum.photos/800/600?random=2',
    thumbnail: 'https://picsum.photos/200/200?random=2',
    name: '测试图片2.jpg',
    width: 800,
    height: 600,
    size: 153600,
    alt: '测试图片2'
  },
  {
    id: '3',
    src: 'https://picsum.photos/800/600?random=3',
    thumbnail: 'https://picsum.photos/200/200?random=3',
    name: '测试图片3.jpg',
    width: 800,
    height: 600,
    size: 204800,
    alt: '测试图片3'
  },
  {
    id: '4',
    src: 'https://picsum.photos/800/600?random=4',
    thumbnail: 'https://picsum.photos/200/200?random=4',
    name: '测试图片4.jpg',
    width: 800,
    height: 600,
    size: 256000,
    alt: '测试图片4'
  },
  {
    id: '5',
    src: 'https://picsum.photos/800/600?random=5',
    thumbnail: 'https://picsum.photos/200/200?random=5',
    name: '测试图片5.jpg',
    width: 800,
    height: 600,
    size: 307200,
    alt: '测试图片5'
  }
])

// 方法
const openUploadModal = () => {
  uploadModalVisible.value = true
}

const handleEdit = (image: ImageItem, index: number) => {
  console.log('编辑图片:', image, index)
  editingImageSrc.value = image.src
  editingImageName.value = image.name || `图片${index + 1}`
  editorVisible.value = true
}

const handleDelete = (image: ImageItem, index: number) => {
  console.log('删除图片:', image, index)
  testImages.value.splice(index, 1)
}

const handlePreview = (image: ImageItem, index: number) => {
  console.log('预览图片:', image, index)
}

const handleUploaded = (results: any[]) => {
  console.log('上传完成:', results)
}

const handleClose = () => {
  console.log('模态框关闭')
}

const handleEditorSave = (result: { canvas: HTMLCanvasElement; blob: Blob; dataUrl: string }) => {
  console.log('编辑保存:', result)
  editorVisible.value = false
}

const handleEditorCancel = () => {
  console.log('编辑取消')
  editorVisible.value = false
}
</script>

<style scoped>
.image-test-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.dark .image-test-page {
  background-color: #141414;
}

.container {
  max-width: 1200px;
}
</style>
