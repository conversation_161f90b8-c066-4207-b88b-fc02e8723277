/**
 * 数据加密工具类
 * 用于加密敏感的AI配置数据，如API密钥
 */

// 生成随机盐值
export function generateSalt(): string {
  const array = new Uint8Array(16)
  crypto.getRandomValues(array)
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
}

// 生成密钥
async function deriveKey(password: string, salt: string): Promise<CryptoKey> {
  const encoder = new TextEncoder()
  const keyMaterial = await crypto.subtle.importKey(
    'raw',
    encoder.encode(password),
    { name: 'PBKDF2' },
    false,
    ['deriveKey']
  )

  return crypto.subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt: encoder.encode(salt),
      iterations: 100000,
      hash: 'SHA-256'
    },
    keyMaterial,
    { name: 'AES-GCM', length: 256 },
    false,
    ['encrypt', 'decrypt']
  )
}

// 加密数据
export async function encryptData(data: string, password: string, salt: string): Promise<string> {
  try {
    const encoder = new TextEncoder()
    const key = await derive<PERSON>ey(password, salt)
    
    // 生成随机IV
    const iv = crypto.getRandomValues(new Uint8Array(12))
    
    // 加密数据
    const encrypted = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      key,
      encoder.encode(data)
    )
    
    // 将IV和加密数据组合
    const combined = new Uint8Array(iv.length + encrypted.byteLength)
    combined.set(iv)
    combined.set(new Uint8Array(encrypted), iv.length)
    
    // 转换为Base64
    return btoa(String.fromCharCode(...combined))
  } catch (error) {
    console.error('数据加密失败:', error)
    throw new Error('数据加密失败')
  }
}

// 解密数据
export async function decryptData(encryptedData: string, password: string, salt: string): Promise<string> {
  try {
    const decoder = new TextDecoder()
    const key = await deriveKey(password, salt)
    
    // 从Base64解码
    const combined = new Uint8Array(
      atob(encryptedData).split('').map(char => char.charCodeAt(0))
    )
    
    // 分离IV和加密数据
    const iv = combined.slice(0, 12)
    const encrypted = combined.slice(12)
    
    // 解密数据
    const decrypted = await crypto.subtle.decrypt(
      { name: 'AES-GCM', iv },
      key,
      encrypted
    )
    
    return decoder.decode(decrypted)
  } catch (error) {
    console.error('数据解密失败:', error)
    throw new Error('数据解密失败')
  }
}

// 生成设备指纹作为加密密码
export function generateDeviceFingerprint(): string {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  if (ctx) {
    ctx.textBaseline = 'top'
    ctx.font = '14px Arial'
    ctx.fillText('Device fingerprint', 2, 2)
  }
  
  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    screen.width + 'x' + screen.height,
    new Date().getTimezoneOffset(),
    canvas.toDataURL(),
    localStorage.getItem('device_id') || generateDeviceId()
  ].join('|')
  
  return btoa(fingerprint).slice(0, 32)
}

// 生成设备ID
function generateDeviceId(): string {
  const deviceId = 'device_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36)
  localStorage.setItem('device_id', deviceId)
  return deviceId
}

// 验证加密数据完整性
export async function verifyEncryptedData(encryptedData: string, password: string, salt: string): Promise<boolean> {
  try {
    await decryptData(encryptedData, password, salt)
    return true
  } catch {
    return false
  }
}
