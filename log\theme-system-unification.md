# 主题系统统一修复日志

## 问题描述
用户反馈暗黑模式下分类选择器仍然显示为白色，没有应用暗黑主题样式。

## 问题分析
通过代码检查发现项目中存在**两套主题系统冲突**：

1. **`useTheme.ts`** - 标准的主题管理系统
   - 使用 `dark` 类名应用到 `document.documentElement`
   - 支持 light/dark/system 三种模式
   - 有完整的状态管理和持久化

2. **`AppLayout.vue`** - 自定义的主题切换逻辑
   - 使用 `localStorage.getItem('theme')` 独立管理
   - 有自己的 `currentTheme` 状态
   - 导致主题状态不同步

## 根本原因
- `AppLayout.vue` 没有使用统一的 `useTheme` composable
- 两套主题系统状态不同步，导致暗黑模式的 CSS 样式无法正确应用
- 虽然 `KnowledgeView.vue` 中写了完整的暗黑模式样式，但 `dark` 类名没有正确添加到 DOM 上

## 解决方案

### 1. 统一主题系统
```typescript
// AppLayout.vue - 修改前
const currentTheme = ref('light')
const handleThemeToggle = () => {
  const newTheme = currentTheme.value === 'light' ? 'dark' : 'light'
  currentTheme.value = newTheme
  // 手动操作 DOM...
}

// AppLayout.vue - 修改后
import { useTheme } from '@/composables/useTheme'
const { isDark, toggleDarkMode } = useTheme()
const handleThemeToggle = () => {
  toggleDarkMode()
}
```

### 2. 移除重复的主题初始化逻辑
- 移除了 `AppLayout.vue` 中的 `initTheme()` 函数
- 移除了手动的 DOM 操作代码
- 统一使用 `useTheme` 的自动初始化

### 3. 更新模板引用
```vue
<!-- 修改前 -->
:title="currentTheme === 'dark' ? '切换到明亮模式' : '切换到暗色模式'"
{{ currentTheme === 'dark' ? '明亮模式' : '暗色模式' }}

<!-- 修改后 -->
:title="isDark ? '切换到明亮模式' : '切换到暗色模式'"
{{ isDark ? '明亮模式' : '暗色模式' }}
```

## 修复的文件
- `src/components/layout/AppLayout.vue`
  - 导入 `useTheme` composable
  - 替换 `currentTheme` 为 `isDark`
  - 简化 `handleThemeToggle` 函数
  - 移除 `initTheme` 函数
  - 更新模板中的主题状态引用

## 预期效果
1. ✅ 主题切换状态统一管理
2. ✅ 暗黑模式下 `dark` 类名正确应用到 `document.documentElement`
3. ✅ 分类选择器在暗黑模式下显示正确的暗色样式
4. ✅ 所有组件的暗黑模式样式都能正常工作

## 技术要点
- **状态管理统一**：避免多套状态系统冲突
- **DOM 操作集中**：通过 composable 统一管理 DOM 类名
- **响应式同步**：确保所有组件都能响应主题变化

## 测试建议
1. 切换到暗黑模式，检查分类选择器是否显示暗色样式
2. 刷新页面，检查主题状态是否正确恢复
3. 测试系统主题跟随功能是否正常
4. 检查其他页面的暗黑模式样式是否正常
