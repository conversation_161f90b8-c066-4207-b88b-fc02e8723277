import { db } from '@/database'
import type { Resource, ResourceWithDetails, SearchOptions, ResourceForm } from '@/types'

export class ResourceService {
  // 创建资源
  async createResource(resourceData: ResourceForm): Promise<number> {
    const resource: Omit<Resource, 'id'> = {
      url: resourceData.url,
      title: resourceData.title,
      description: resourceData.description,
      cover_image_url: resourceData.cover_image_url || '',
      category_id: resourceData.category_id || 1,
      view_count: 0,
      created_at: new Date(),
      updated_at: new Date(),
    }

    const resourceId = await db.resources.add(resource)

    // 添加标签关联
    if (resourceData.tag_ids && resourceData.tag_ids.length > 0) {
      const resourceTags = resourceData.tag_ids.map((tagId) => ({
        resource_id: resourceId as number,
        tag_id: tagId,
      }))
      await db.resource_tags.bulkAdd(resourceTags)

      // 更新标签的资源计数
      for (const tagId of resourceData.tag_ids) {
        await this.updateTagResourceCount(tagId)
      }
    }

    // 更新分类的资源计数
    if (resourceData.category_id) {
      await this.updateCategoryResourceCount(resourceData.category_id)
    }

    return resourceId as number
  }

  // 获取资源详情
  async getResourceById(id: number): Promise<ResourceWithDetails | undefined> {
    const resource = await db.resources.get(id)
    if (!resource) return undefined

    return await this.enrichResourceWithDetails(resource)
  }

  // 更新资源
  async updateResource(id: number, resourceData: Partial<ResourceForm>): Promise<void> {
    const oldResource = await db.resources.get(id)
    if (!oldResource) throw new Error('资源不存在')

    // 更新资源基本信息
    const updateData: Partial<Resource> = {
      updated_at: new Date(),
    }

    if (resourceData.url !== undefined) updateData.url = resourceData.url
    if (resourceData.title !== undefined) updateData.title = resourceData.title
    if (resourceData.description !== undefined) updateData.description = resourceData.description
    if (resourceData.cover_image_url !== undefined)
      updateData.cover_image_url = resourceData.cover_image_url
    if (resourceData.category_id !== undefined) updateData.category_id = resourceData.category_id

    await db.resources.update(id, updateData)

    // 处理标签变更
    if (resourceData.tag_ids !== undefined) {
      // 删除旧的标签关联
      const oldTagIds = await db.resource_tags
        .where('resource_id')
        .equals(id)
        .toArray()
        .then((tags) => tags.map((t) => t.tag_id))

      await db.resource_tags.where('resource_id').equals(id).delete()

      // 添加新的标签关联
      if (resourceData.tag_ids.length > 0) {
        const resourceTags = resourceData.tag_ids.map((tagId) => ({
          resource_id: id,
          tag_id: tagId,
        }))
        await db.resource_tags.bulkAdd(resourceTags)
      }

      // 更新标签计数
      const allTagIds = [...new Set([...oldTagIds, ...resourceData.tag_ids])]
      for (const tagId of allTagIds) {
        await this.updateTagResourceCount(tagId)
      }
    }

    // 处理分类变更
    if (
      resourceData.category_id !== undefined &&
      resourceData.category_id !== oldResource.category_id
    ) {
      await this.updateCategoryResourceCount(oldResource.category_id)
      await this.updateCategoryResourceCount(resourceData.category_id)
    }
  }

  // 删除资源
  async deleteResource(id: number): Promise<void> {
    const resource = await db.resources.get(id)
    if (!resource) return

    // 获取关联的标签
    const tagIds = await db.resource_tags
      .where('resource_id')
      .equals(id)
      .toArray()
      .then((tags) => tags.map((t) => t.tag_id))

    // 删除资源
    await db.resources.delete(id)

    // 删除标签关联
    await db.resource_tags.where('resource_id').equals(id).delete()

    // 更新标签计数
    for (const tagId of tagIds) {
      await this.updateTagResourceCount(tagId)
    }

    // 更新分类计数
    await this.updateCategoryResourceCount(resource.category_id)
  }

  // 搜索资源
  async searchResources(options: SearchOptions = {}): Promise<ResourceWithDetails[]> {
    // 获取所有资源，然后在内存中排序
    let resources = await db.resources.toArray()

    // 排序
    const sortBy = options.sort_by || 'created_at'
    const sortOrder = options.sort_order || 'desc'

    resources.sort((a, b) => {
      let aValue: any, bValue: any

      switch (sortBy) {
        case 'created_at':
        case 'updated_at':
          aValue = new Date(a[sortBy]).getTime()
          bValue = new Date(b[sortBy]).getTime()
          break
        case 'title':
          aValue = a.title.toLowerCase()
          bValue = b.title.toLowerCase()
          break
        case 'view_count':
          aValue = a.view_count
          bValue = b.view_count
          break
        default:
          aValue = a.created_at
          bValue = b.created_at
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
      } else {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
      }
    })

    // 关键词搜索
    if (options.keyword) {
      const keyword = options.keyword.toLowerCase()
      resources = resources.filter(
        (resource) =>
          resource.title.toLowerCase().includes(keyword) ||
          resource.description.toLowerCase().includes(keyword) ||
          resource.url.toLowerCase().includes(keyword),
      )
    }

    // 分类筛选（包含子分类）
    if (options.category_id) {
      const { categoryService } = await import('./categoryService')
      const descendantIds = await categoryService.getAllDescendantIds(options.category_id)
      const categoryIds = [options.category_id, ...descendantIds]
      resources = resources.filter((resource) => categoryIds.includes(resource.category_id))
    }

    // 标签筛选
    if (options.tag_ids && options.tag_ids.length > 0) {
      const resourceIds = await db.resource_tags
        .where('tag_id')
        .anyOf(options.tag_ids)
        .toArray()
        .then((tags) => tags.map((t) => t.resource_id))

      resources = resources.filter((resource) => resource.id && resourceIds.includes(resource.id))
    }

    // 分页
    const offset = options.offset || 0
    const limit = options.limit || resources.length
    resources = resources.slice(offset, offset + limit)

    // 丰富资源详情
    return await Promise.all(resources.map((resource) => this.enrichResourceWithDetails(resource)))
  }

  // 搜索资源并返回总数
  async searchResourcesWithCount(
    options: SearchOptions = {},
  ): Promise<{ resources: ResourceWithDetails[]; total: number }> {
    console.log('resourceService.searchResourcesWithCount: 查询参数', options)
    // 获取所有资源，然后在内存中排序
    let resources = await db.resources.toArray()
    console.log('resourceService.searchResourcesWithCount: 数据库中的资源总数', resources.length)

    // 排序
    const sortBy = options.sort_by || 'created_at'
    const sortOrder = options.sort_order || 'desc'

    resources.sort((a, b) => {
      let aValue: any, bValue: any

      switch (sortBy) {
        case 'created_at':
        case 'updated_at':
          aValue = new Date(a[sortBy]).getTime()
          bValue = new Date(b[sortBy]).getTime()
          break
        case 'title':
          aValue = a.title.toLowerCase()
          bValue = b.title.toLowerCase()
          break
        case 'view_count':
          aValue = a.view_count
          bValue = b.view_count
          break
        default:
          aValue = a.created_at
          bValue = b.created_at
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
      } else {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
      }
    })

    // 筛选
    let filteredResources = resources

    // 按分类筛选
    if (options.category_id) {
      filteredResources = filteredResources.filter(
        (resource) => resource.category_id === options.category_id,
      )
    }

    // 按标签筛选
    if (options.tag_ids && options.tag_ids.length > 0) {
      console.log(
        'resourceService.searchResourcesWithCount: 开始标签筛选，tag_ids:',
        options.tag_ids,
      )
      const resourceTagRelations = await db.resource_tags
        .where('tag_id')
        .anyOf(options.tag_ids)
        .toArray()
      console.log('resourceService.searchResourcesWithCount: 找到的标签关系:', resourceTagRelations)

      const resourceIds = resourceTagRelations.map((t) => t.resource_id)
      console.log('resourceService.searchResourcesWithCount: 关联的资源ID:', resourceIds)

      const beforeFilterCount = filteredResources.length
      filteredResources = filteredResources.filter(
        (resource) => resource.id && resourceIds.includes(resource.id),
      )
      console.log(
        'resourceService.searchResourcesWithCount: 标签筛选前资源数量:',
        beforeFilterCount,
        '筛选后:',
        filteredResources.length,
      )
    }

    // 按关键词筛选
    if (options.keyword) {
      const keyword = options.keyword.toLowerCase()
      filteredResources = filteredResources.filter(
        (resource) =>
          resource.title.toLowerCase().includes(keyword) ||
          (resource.description && resource.description.toLowerCase().includes(keyword)),
      )
    }

    // 获取总数
    const total = filteredResources.length
    console.log('resourceService.searchResourcesWithCount: 筛选后的资源数量', total)

    // 分页
    const offset = options.offset || 0
    const limit = options.limit || filteredResources.length
    console.log('resourceService.searchResourcesWithCount: 分页参数', { offset, limit })
    filteredResources = filteredResources.slice(offset, offset + limit)
    console.log(
      'resourceService.searchResourcesWithCount: 分页后的资源数量',
      filteredResources.length,
    )

    // 丰富资源详情
    const enrichedResources = await Promise.all(
      filteredResources.map((resource) => this.enrichResourceWithDetails(resource)),
    )

    return {
      resources: enrichedResources,
      total,
    }
  }

  // 增加浏览次数
  async incrementViewCount(id: number): Promise<void> {
    const resource = await db.resources.get(id)
    if (resource) {
      await db.resources.update(id, { view_count: resource.view_count + 1 })
    }
  }

  // 丰富资源详情（添加分类和标签信息）
  private async enrichResourceWithDetails(resource: Resource): Promise<ResourceWithDetails> {
    const [category, tagRelations] = await Promise.all([
      db.categories.get(resource.category_id),
      db.resource_tags.where('resource_id').equals(resource.id!).toArray(),
    ])

    const tags = await Promise.all(
      tagRelations.map((relation) => db.tags.get(relation.tag_id)),
    ).then((tags) => tags.filter(Boolean))

    return {
      ...resource,
      category,
      tags: tags as any[],
    }
  }

  // 更新标签的资源计数
  private async updateTagResourceCount(tagId: number): Promise<void> {
    const count = await db.resource_tags.where('tag_id').equals(tagId).count()
    await db.tags.update(tagId, { resource_count: count })
  }

  // 更新分类的资源计数
  private async updateCategoryResourceCount(categoryId: number): Promise<void> {
    const count = await db.resources.where('category_id').equals(categoryId).count()
    await db.categories.update(categoryId, { resource_count: count })
  }
}

export const resourceService = new ResourceService()
