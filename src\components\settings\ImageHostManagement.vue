<template>
  <!-- 图床管理页面 - 参考AI配置界面设计 -->
  <a-space direction="vertical" size="middle" style="width: 100%">
    <!-- 图床配置管理 -->
    <a-card size="small" class="image-host-management-card">
      <template #title>
        <div class="card-title-wrapper">
          <span class="card-title">图床管理</span>
          <a-tooltip title="查看操作说明" placement="bottom">
            <a-button type="text" size="small" class="help-icon-btn" @click="showTutorial = true">
              <div class="i-heroicons-question-mark-circle"></div>
            </a-button>
          </a-tooltip>
        </div>
      </template>

      <!-- 统计卡片 - 参考分类管理、标签管理样式 -->
      <a-row :gutter="12" class="mb-3">
        <a-col :span="6">
          <div class="stat-card">
            <div class="stat-icon stat-icon-primary">
              <div class="i-heroicons-server"></div>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ configs.length }}</div>
              <div class="stat-label">总配置数</div>
            </div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-card">
            <div class="stat-icon stat-icon-success">
              <div class="i-heroicons-check-circle"></div>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{configs.filter(c => c.enabled).length}}</div>
              <div class="stat-label">已启用</div>
            </div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-card">
            <div class="stat-icon stat-icon-info">
              <div class="i-heroicons-signal"></div>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{Object.values(testResults).filter(r => r?.success).length}}</div>
              <div class="stat-label">连接正常</div>
            </div>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="stat-card">
            <div class="stat-icon stat-icon-warning">
              <div class="i-heroicons-building-office"></div>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{new Set(configs.map(c => c.provider)).size}}</div>
              <div class="stat-label">服务商数</div>
            </div>
          </div>
        </a-col>
      </a-row>

      <!-- 添加图床按钮 -->
      <div class="toolbar-section">
        <div class="toolbar-left">
          <a-button type="primary" @click="showAddForm = !showAddForm">
            <div class="i-heroicons-plus"></div>
            {{ showAddForm ? '取消添加' : '添加图床' }}
          </a-button>
        </div>
      </div>

      <!-- 添加图床表单 -->
      <div class="add-form-container">
        <Transition name="collapse" mode="out-in">
          <div v-show="showAddForm" class="add-config-form">
            <ImageHostConfigModal :config="null" @save="handleSaveConfig" @close="showAddForm = false" :inline="true" />
          </div>
        </Transition>
      </div>

      <!-- 批量测试组件 -->
      <BatchTestProgress />

      <!-- 已配置的图床列表 -->
      <div class="config-list-section">

        <!-- 空状态 -->
        <div v-if="configs.length === 0" class="empty-state">
          <a-empty description="还没有配置图床服务">
            <template #image>
              <div class="i-heroicons-cloud-arrow-up empty-icon"></div>
            </template>
            <template #description>
              <p class="empty-description">配置图床服务后，即可开始上传和管理图片</p>
            </template>
            <div class="empty-actions">
              <a-button @click="showTutorial = true">
                <template #icon>
                  <div class="i-heroicons-question-mark-circle"></div>
                </template>
                查看教程
              </a-button>
              <a-button @click="showAddForm = true" type="primary">
                <template #icon>
                  <div class="i-heroicons-plus"></div>
                </template>
                添加图床
              </a-button>
            </div>
          </a-empty>
        </div>

        <!-- 配置网格 - 完全参考AI配置界面 -->
        <div v-else class="config-grid">
          <div v-for="config in configs" :key="config.id" class="config-card">
            <!-- 紧凑式头部：标题、状态、操作在一行 -->
            <div class="config-header-compact">
              <div class="config-title-compact">
                <span class="config-name-compact">{{ config.name }}</span>
                <a-tag :color="getProviderColor(config.provider)" size="small">
                  {{ config.provider }}
                </a-tag>
                <a-tag color="default" size="small">优先级: {{ config.priority }}</a-tag>
              </div>
              <div class="config-actions-compact">
                <a-switch :checked="config.enabled" @change="(checked) => handleToggleEnabled(config, checked)"
                  size="small" :loading="testing[config.id]" />
                <a-dropdown>
                  <a-button type="text" size="small" class="action-btn">
                    <div class="i-heroicons-ellipsis-horizontal"></div>
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="testConfig(config, true)">
                        <template #icon>
                          <div class="i-heroicons-signal"></div>
                        </template>
                        {{ testing[config.id] ? '测试中...' : '测试连接' }}
                      </a-menu-item>
                      <a-menu-item @click="editConfig(config)">
                        <template #icon>
                          <div class="i-heroicons-pencil"></div>
                        </template>
                        编辑配置
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item @click="handleDeleteConfig(config.id)" class="danger-item">
                        <template #icon>
                          <div class="i-heroicons-trash"></div>
                        </template>
                        删除配置
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </div>

            <!-- 紧凑式内容：两列布局 -->
            <div class="config-content-compact">
              <div class="config-info-left">
                <div class="info-item-compact">
                  <div class="i-heroicons-link info-icon"></div>
                  <span class="info-text config-url" :title="config.apiUrl">{{ formatUrl(config.apiUrl) }}</span>
                </div>
                <div class="info-item-compact">
                  <div class="i-heroicons-shield-check info-icon"></div>
                  <span class="info-text">{{ getAuthTypeText(config.authType) }}</span>
                </div>
                <div class="info-item-compact" v-if="config.fileField">
                  <div class="i-heroicons-document info-icon"></div>
                  <span class="info-text">字段: {{ config.fileField }}</span>
                </div>
              </div>
              <div class="config-info-right">
                <div class="info-item-compact">
                  <div class="i-heroicons-scale info-icon"></div>
                  <span class="info-text">{{ config.maxFileSize || 10 }}MB</span>
                </div>
                <div class="info-item-compact">
                  <div class="i-heroicons-photo info-icon"></div>
                  <span class="info-text">{{ config.allowedFormats?.join(', ') || 'jpg,png,gif,webp' }}</span>
                </div>
                <div class="info-item-compact" v-if="testResults[config.id]">
                  <div class="i-heroicons-signal info-icon"></div>
                  <span class="info-text" :class="testResults[config.id].success ? 'status-success' : 'status-error'">
                    {{ testResults[config.id].success ? '连接正常' : '连接失败' }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 错误信息 -->
            <div v-if="testResults[config.id] && !testResults[config.id].success" class="error-message-compact">
              <a-alert :message="testResults[config.id].message" type="error" size="small" show-icon />
            </div>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 模态框组件 -->
    <!-- 图片标签管理模态框 -->
    <ImageTagManagementModal v-model="showTagManagement" />

    <!-- 统计分析模态框 -->
    <a-modal v-model:open="showAnalytics" title="统计分析" width="80%" :footer="null" @cancel="showAnalytics = false">
      <ImageHostAnalytics />
    </a-modal>

    <!-- 配置教程模态框 -->
    <ImageHostTutorialModal v-if="showTutorial" @close="closeTutorial" @start-config="startConfig" />

    <!-- 配置模态框 -->
    <ImageHostConfigModal v-if="showConfigModal" :config="editingConfig" @save="handleSaveConfig"
      @close="closeConfigModal" />

    <!-- 交互式教程 -->
    <Teleport to="body">
      <ImageHostTutorial ref="tutorialRef" :steps="tutorialSteps" @complete="handleTutorialComplete"
        @skip="handleTutorialSkip" @close="handleTutorialClose" />
    </Teleport>
  </a-space>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, Transition } from 'vue'
import { message } from 'ant-design-vue'
import { storeToRefs } from 'pinia'
import { useImageHostStore } from '@/stores/imageHostStore'
import type { ImageHostConfig } from '@/types/imageHost'
import ImageHostConfigModal from './ImageHostConfigModal.vue'
import ImageHostTutorial from './ImageHostTutorial.vue'
import ImageHostTutorialModal from './ImageHostTutorialModal.vue'
import ImageTagManagementModal from '@/components/image/ImageTagManagementModal.vue'
import ImageHostAnalytics from './ImageHostAnalytics.vue'
import BatchTestProgress from './BatchTestProgress.vue'

// 使用 Pinia store
const imageHostStore = useImageHostStore()

// 本地 UI 状态
const showConfigModal = ref(false)
const showAddForm = ref(false) // 新增：控制添加表单的显示
const showTutorial = ref(false)
const showTagManagement = ref(false)
const showAnalytics = ref(false)
const editingConfig = ref<ImageHostConfig | null>(null)
const tutorialRef = ref<InstanceType<typeof ImageHostTutorial> | null>(null)

// 从 store 中获取状态和方法（使用 storeToRefs 保持响应性）

const {
  configs,
  testResults,
  testing,
  loading,
  error,
  enabledConfigs,
  sortedConfigs,
  configsCount,
  hasEnabledConfigs,
  getTestResult,
  isTesting
} = storeToRefs(imageHostStore)

const {
  loadConfigs,
  saveConfig,
  deleteConfig,
  testConfig,
  testConfigWithEnhancedNotification,
  toggleConfigEnabled,
  clearError
} = imageHostStore

// 交互式教程步骤
const tutorialSteps = [
  {
    id: 'welcome',
    title: '🎉 欢迎使用图床配置向导',
    description: '本教程将一步步引导您配置图床服务，让您轻松实现图片上传和管理功能。整个过程大约需要2-3分钟。',
    target: '#add-host-btn',
    position: 'bottom' as const,
    tips: [
      '💡 您可以随时按ESC键退出教程',
      '⌨️ 使用左右箭头键或点击按钮切换步骤',
      '📖 建议先阅读配置教程了解基础概念'
    ]
  },
  {
    id: 'tutorial-button',
    title: '📚 配置教程按钮',
    description: '点击"配置教程"按钮可以查看详细的图床配置指南，包括推荐的图床服务和配置参数说明。',
    target: 'button:has(.i-heroicons-question-mark-circle)',
    position: 'bottom' as const,
    tips: [
      '📋 教程包含完整的配置步骤',
      '🔗 提供推荐的图床服务链接',
      '⚙️ 详细的参数配置说明'
    ]
  },
  {
    id: 'add-button',
    title: '➕ 添加图床按钮',
    description: '点击"添加图床"按钮打开配置表单，开始创建您的第一个图床配置。',
    target: '#add-host-btn',
    position: 'bottom' as const,
    tips: [
      '🆕 创建新的图床配置',
      '📝 填写必要的配置信息',
      '🔧 支持多种图床服务'
    ],
    action: () => {
      // 延迟打开模态框，让用户看到高亮效果
      setTimeout(() => {
        showConfigModal.value = true
      }, 1000)
    }
  },
  {
    id: 'config-name',
    title: '📝 填写配置名称',
    description: '为您的图床配置起一个容易识别的名称，这样您就能快速区分不同的图床服务。',
    target: '[data-tutorial="config-name"]',
    position: 'right' as const,
    example: '我的SM.MS图床',
    tips: [
      '💡 名称只是用于识别，可以随意填写',
      '🏷️ 建议使用有意义的名称，如"主图床"、"备用图床"',
      '📋 支持中文和英文'
    ]
  },
  {
    id: 'provider',
    title: '🔖 设置图床标识',
    description: '图床标识是系统内部使用的唯一标识符，用于程序识别不同的图床配置。',
    target: '[data-tutorial="provider"]',
    position: 'right' as const,
    example: 'smms',
    tips: [
      '⚠️ 标识符必须唯一，不能重复',
      '📝 建议使用英文小写字母',
      '🏷️ 推荐使用图床服务的简称'
    ]
  },
  {
    id: 'api-url',
    title: '🌐 配置API接口地址',
    description: '输入图床服务的上传API地址，这是图片上传的目标URL。不同的图床服务有不同的API地址。',
    target: '[data-tutorial="api-url"]',
    position: 'right' as const,
    example: 'https://sm.ms/api/v2/upload',
    tips: [
      '🔗 请确保URL格式正确，以https://开头',
      '📖 可以从图床服务的API文档中找到',
      '⚡ 常见服务：SM.MS、ImgBB、Imgur等'
    ]
  },
  {
    id: 'auth-type',
    title: '🔐 选择认证方式',
    description: '根据图床服务的要求选择合适的认证方式。不同的服务支持不同的认证方法。',
    target: '[data-tutorial="auth-type"]',
    position: 'left' as const,
    tips: [
      '🔑 Header认证：最常用，如SM.MS、ImgBB',
      '🔗 URL参数认证：将密钥作为URL参数',
      '🚫 无需认证：部分服务支持匿名上传'
    ]
  },
  {
    id: 'auth-key',
    title: '🔑 输入认证密钥',
    description: '输入从图床服务获取的API Key或Token。这是您访问图床服务的重要凭证。',
    target: '[data-tutorial="auth-key"]',
    position: 'left' as const,
    example: 'your-api-key-here',
    tips: [
      '🔒 请妥善保管您的API Key',
      '⚠️ 不要与他人分享您的密钥',
      '📋 通常在图床服务的用户设置中获取'
    ]
  },
  {
    id: 'url-field',
    title: '🎯 配置URL字段路径',
    description: '指定从API响应中提取图片URL的字段路径。这告诉系统在哪里找到上传后的图片链接。',
    target: '[data-tutorial="url-field"]',
    position: 'left' as const,
    example: 'data.url',
    tips: [
      '📍 常见格式：data.url、result.image_url',
      '📖 可以查看API文档了解响应格式',
      '🔍 支持嵌套字段，如data.links.url'
    ]
  },
  {
    id: 'save-config',
    title: '💾 保存配置',
    description: '检查所有配置信息无误后，点击保存按钮完成配置。系统会自动验证配置的正确性。',
    target: '[data-tutorial="save-btn"]',
    position: 'top' as const,
    tips: [
      '✅ 保存后会自动进行连接测试',
      '🚀 测试成功后即可开始使用',
      '🔧 如有问题可以随时编辑配置'
    ]
  },
  {
    id: 'complete',
    title: '🎉 配置完成！',
    description: '恭喜您成功配置了图床服务！现在您可以开始上传和管理图片了。建议配置多个图床实现备份。',
    target: '#add-host-btn',
    position: 'bottom' as const,
    tips: [
      '🔄 建议配置2-3个图床实现备份',
      '⚙️ 可以通过优先级控制使用顺序',
      '📊 在图片管理页面查看上传统计'
    ]
  }
]

// 关闭教程
const closeTutorial = () => {
  showTutorial.value = false
}

// 开始配置
const startConfig = () => {
  showTutorial.value = false
  showConfigModal.value = true
}

// 如果没有配置的图床，自动显示教程
const checkAndShowTutorial = () => {
  if (configs.value.length === 0) {
    showTutorial.value = true
  }
}

// 启动交互式教程
const startInteractiveTutorial = () => {
  tutorialRef.value?.startTutorial()
}

// 教程事件处理
const handleTutorialComplete = () => {
  console.log('教程完成')
  // 可以显示完成提示或跳转到测试页面
}

const handleTutorialSkip = () => {
  console.log('跳过教程')
}

const handleTutorialClose = () => {
  console.log('关闭教程')
}

// 加载配置（包装 store 方法以添加教程检查）
const loadConfigsWithTutorial = async () => {
  await loadConfigs()
  // 检查是否需要显示教程
  checkAndShowTutorial()
}

// 所有图床相关的核心功能现在都在 Pinia store 中

// 编辑配置
const editConfig = (config: ImageHostConfig) => {
  editingConfig.value = config
  showConfigModal.value = true
}

// 删除配置的包装函数（添加确认对话框）
const handleDeleteConfig = async (id: string) => {
  if (!confirm('确定要删除这个图床配置吗？')) return

  try {
    await deleteConfig(id)
  } catch (error) {
    console.error('删除配置失败:', error)
  }
}

// 处理保存配置（包装 store 方法）
const handleSaveConfig = async (config: ImageHostConfig) => {
  try {
    await saveConfig(config, editingConfig.value?.id)
    closeConfigModal()
    // 自动测试连接
    await testConfig(config, true)
  } catch (error) {
    console.error('保存配置失败:', error)
  }
}

// 关闭配置模态框
const closeConfigModal = () => {
  showConfigModal.value = false
  showAddForm.value = false // 同时关闭内联表单
  editingConfig.value = null
}

// 获取认证类型文本
const getAuthTypeText = (authType: string) => {
  const typeMap = {
    none: '无需认证',
    token: 'Token认证',
    header: 'Header认证',
    query: 'URL参数认证'
  }
  return typeMap[authType as keyof typeof typeMap] || authType
}

// 获取提供商颜色 - 参考AI配置界面
const getProviderColor = (provider: string) => {
  const colors = {
    'picui': 'blue',
    'picgo': 'green',
    'smms': 'orange',
    'imgur': 'purple',
    'github': 'cyan',
    'qiniu': 'red',
    'aliyun': 'gold',
    'tencent': 'lime',
    'custom': 'default'
  }
  return colors[provider as keyof typeof colors] || 'default'
}

// 格式化URL显示
const formatUrl = (url: string) => {
  if (!url) return '未设置'
  try {
    const urlObj = new URL(url)
    return urlObj.hostname
  } catch {
    return url.length > 30 ? url.substring(0, 30) + '...' : url
  }
}

// 处理启用/禁用切换（使用 store 中的方法）
const handleToggleEnabled = async (config: ImageHostConfig, checked: boolean) => {
  console.log(`图床管理界面：${checked ? '启用' : '禁用'}图床 "${config.name}"`)
  try {
    await toggleConfigEnabled(config.id, checked)
    console.log(`图床管理界面：${checked ? '启用' : '禁用'}操作完成`)
  } catch (error) {
    console.error('图床管理界面：操作失败', error)
  }
}

// 组件挂载时加载配置
onMounted(() => {
  loadConfigsWithTutorial()
})
</script>

<style scoped>
/* 图床管理界面 - 完全参考分类管理、标签管理、AI配置管理的统一标准 */
.image-host-management-card {
  margin-bottom: 16px;
}

.card-title-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--ant-color-text);
}

.help-icon-btn {
  padding: 2px 4px;
  height: 24px;
  width: 24px;
}

/* 统计卡片紧凑样式 - 统一标准（完全参考分类管理、标签管理） */
.stat-card {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
  position: relative;
}

.stat-card:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.dark .stat-card {
  background: #1f1f1f;
  border-color: #303030;
}

.dark .stat-card:hover {
  border-color: #434343;
  box-shadow: 0 2px 6px rgba(255, 255, 255, 0.02);
}

.stat-icon {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-right: 10px;
  color: white;
}

.stat-icon-primary {
  background: #1890ff;
}

.stat-icon-success {
  background: #52c41a;
}

.stat-icon-info {
  background: #13c2c2;
}

.stat-icon-warning {
  background: #faad14;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  line-height: 1.2;
  margin-bottom: 2px;
}

.dark .stat-value {
  color: #f0f0f0;
}

.stat-label {
  font-size: 13px;
  color: #8c8c8c;
  font-weight: 400;
}

.dark .stat-label {
  color: #a6a6a6;
}

/* 工具栏 - 紧凑型统一标准（完全参考AI配置管理） */
.toolbar-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 8px 0;
  gap: 12px;
}

.toolbar-left {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
}

.toolbar-right {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
}

/* 添加表单 - 紧凑型统一标准（完全参考AI配置管理、分类管理） */
.add-form-container {
  margin-bottom: 12px;
}

.add-config-form {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-top: 8px;
  transition: all 0.3s ease;
}

.dark .add-config-form {
  background: #1f1f1f;
  border-color: #303030;
}

.form-content {
  margin: 0;
}

/* 配置列表 - 完全参考AI配置管理 */
.config-list-section {
  margin-top: 16px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 0;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 0;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 12px;
}

@media (max-width: 768px) {
  .config-grid {
    grid-template-columns: 1fr;
  }
}

.config-card {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.config-card:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.dark .config-card {
  background: #262626;
  border-color: #303030;
}

.dark .config-card:hover {
  border-color: #434343;
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.04);
}

/* 紧凑式头部 - 完全参考AI配置管理 */
.config-header-compact {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.config-title-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.config-name-compact {
  font-weight: 600;
  font-size: 15px;
  color: #262626;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
}

.dark .config-name-compact {
  color: #f0f0f0;
}

.config-actions-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.action-btn {
  padding: 4px;
  height: 28px;
  width: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8c8c8c;
  transition: all 0.2s ease;
}

.action-btn:hover {
  color: #262626;
  background: #f5f5f5;
}

.dark .action-btn {
  color: #a6a6a6;
}

.dark .action-btn:hover {
  color: #f0f0f0;
  background: #3a3a3a;
}

/* 紧凑式内容 - 完全参考AI配置管理 */
.config-content-compact {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
}

.config-info-left,
.config-info-right {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.info-item-compact {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  line-height: 1.4;
}

.info-icon {
  width: 14px;
  height: 14px;
  color: #8c8c8c;
  flex-shrink: 0;
}

.dark .info-icon {
  color: #a6a6a6;
}

.info-text {
  color: #595959;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.dark .info-text {
  color: #bfbfbf;
}

.config-url {
  color: #1890ff;
  cursor: pointer;
}

.config-url:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.status-success {
  color: #52c41a;
}

.status-error {
  color: #ff4d4f;
}

.error-message-compact {
  margin-top: 12px;
}

/* 危险操作菜单项 - 完全参考AI配置管理 */
.danger-item {
  color: #ff4d4f !important;
}

.danger-item:hover {
  background: #fff2f0 !important;
}

.dark .danger-item:hover {
  background: #2a1215 !important;
}

/* 响应式优化 - 完全参考AI配置管理 */
@media (max-width: 768px) {
  .toolbar-section {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .config-content-compact {
    grid-template-columns: 1fr;
  }

  .config-name-compact {
    max-width: 120px;
  }

  .stat-value {
    font-size: 18px;
  }

  .stat-icon {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
}

/* 空状态 - 完全参考AI配置管理 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #8c8c8c;
}

.dark .empty-state {
  color: #a6a6a6;
}

.empty-icon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.dark .empty-icon {
  color: #434343;
}

.empty-description {
  color: #8c8c8c;
  margin: 16px 0;
  font-size: 14px;
}

.dark .empty-description {
  color: #a6a6a6;
}

.empty-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 20px;
  justify-content: center;
  margin-top: 16px;
}

/* 配置列表 */
.config-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.config-item-wrapper {
  width: 100%;
}

.config-item {
  border: 1px solid var(--ant-color-border);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.config-item:hover {
  border-color: var(--ant-color-primary-border);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.config-disabled {
  opacity: 0.6;
}

/* 配置头部 */
.config-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 16px;
}

.config-info {
  flex: 1;
  min-width: 0;
}

.config-title-row {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 8px;
}

.config-name {
  color: var(--ant-color-text);
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.config-badges {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.config-url {
  color: var(--ant-color-text-secondary);
  font-size: 14px;
  margin: 0;
  word-break: break-all;
}

.config-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

/* 配置详情 */
.config-details {
  margin-top: 12px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.detail-label {
  color: var(--ant-color-text-secondary);
  font-size: 14px;
  font-weight: 500;
}

.detail-value {
  color: var(--ant-color-text);
  font-size: 14px;
  text-align: right;
  word-break: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .function-row {
    flex-direction: column;
    align-items: stretch;
  }

  .page-header {
    align-items: stretch;
  }

  .action-controls {
    justify-content: center;
  }

  .secondary-controls {
    justify-content: center;
  }

  .config-header {
    flex-direction: column;
    align-items: stretch;
  }

  .config-actions {
    justify-content: center;
    margin-top: 12px;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .detail-value {
    text-align: left;
  }
}

/* 折叠过渡动画 */
.collapse-enter-active,
.collapse-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.collapse-enter-from,
.collapse-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

.collapse-enter-to,
.collapse-leave-from {
  opacity: 1;
  max-height: 500px;
  transform: translateY(0);
}
</style>