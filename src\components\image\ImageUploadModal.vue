<template>
  <Teleport to="body">
    <Transition enter-active-class="transition-opacity duration-300" enter-from-class="opacity-0"
      enter-to-class="opacity-100" leave-active-class="transition-opacity duration-300" leave-from-class="opacity-100"
      leave-to-class="opacity-0">
      <div v-if="visible" class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm pt-16"
        @click="handleBackdropClick" @keydown.esc="handleClose" tabindex="-1">
        <Transition enter-active-class="transition-all duration-300 ease-out" enter-from-class="opacity-0 scale-95"
          enter-to-class="opacity-100 scale-100" leave-active-class="transition-all duration-200 ease-in"
          leave-from-class="opacity-100 scale-100" leave-to-class="opacity-0 scale-95">
          <div
            class="relative w-full max-w-5xl h-[calc(100vh-80px)] mx-4 bg-white dark:bg-gray-800 rounded-lg shadow-2xl overflow-hidden flex flex-col"
            @click.stop>
            <!-- 头部 - 紧凑型设计 -->
            <div
              class="flex items-center justify-between px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
              <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">图片上传</h2>
              <button @click="handleClose"
                class="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                <div class="i-heroicons-x-mark w-5 h-5"></div>
              </button>
            </div>

            <!-- 上传方式选项卡 - Ant Design Tabs 组件 -->
            <div class="border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
              <a-tabs v-model:activeKey="activeTab" size="small" class="upload-tabs"
                :tab-bar-style="{ margin: 0, padding: '0 16px' }">
                <a-tab-pane key="local" tab="本地资源">
                  <template #tab>
                    <span class="flex items-center">
                      <div class="i-heroicons-folder-open w-4 h-4 mr-1.5"></div>
                      本地资源
                    </span>
                  </template>
                </a-tab-pane>
                <a-tab-pane key="url" tab="URL上传">
                  <template #tab>
                    <span class="flex items-center">
                      <div class="i-heroicons-link w-4 h-4 mr-1.5"></div>
                      URL上传
                    </span>
                  </template>
                </a-tab-pane>
              </a-tabs>
            </div>

            <!-- 内容区域 - 黄金比例布局 -->
            <div class="flex-1 overflow-hidden flex">
              <!-- 左侧：上传区域 - 黄金比例 61.8% -->
              <div class="upload-area p-4 overflow-y-auto border-r border-gray-200 dark:border-gray-700">
                <!-- 本地资源上传 -->
                <div v-if="activeTab === 'local'" class="space-y-3">
                  <!-- Ant Design Upload 组件样式 -->
                  <a-upload-dragger v-model:fileList="fileList" name="file" :multiple="true" accept="image/*"
                    :before-upload="beforeUpload" @change="handleUploadChange" :showUploadList="false"
                    class="compact-upload-dragger">
                    <div class="ant-upload-drag-icon">
                      <div class="i-heroicons-cloud-arrow-up w-8 h-8 mx-auto text-gray-400"></div>
                    </div>
                    <p class="ant-upload-text text-sm font-medium text-gray-900 dark:text-gray-100">
                      点击或拖拽图片到此区域
                    </p>
                    <p class="ant-upload-hint text-xs text-gray-500 dark:text-gray-400">
                      支持 JPG、PNG、GIF、WebP，单张最大 10MB
                    </p>
                  </a-upload-dragger>

                  <!-- 已选择图片预览 - 响应式网格布局 -->
                  <div v-if="selectedFiles.length > 0" class="space-y-4">
                    <div class="flex items-center justify-between">
                      <h3 class="text-base font-medium text-gray-900 dark:text-gray-100">
                        已选择图片 ({{ selectedFiles.length }})
                      </h3>
                      <a-button @click="clearFiles" type="text" size="small" danger>
                        <template #icon>
                          <div class="i-heroicons-trash w-4 h-4"></div>
                        </template>
                        清空
                      </a-button>
                    </div>

                    <!-- 响应式图片网格 -->
                    <div class="image-preview-grid">
                      <div v-for="(file, index) in selectedFiles" :key="file.id" class="image-preview-item group">
                        <!-- 图片容器 -->
                        <div class="image-container">
                          <img :src="file.preview" :alt="file.name" class="image-thumbnail"
                            @error="handlePreviewImageError" />

                          <!-- 悬浮遮罩和操作按钮 -->
                          <div class="image-overlay">
                            <div class="overlay-actions">
                              <button class="action-btn preview-btn"
                                @click="handlePreviewSelectedImage(selectedFilesForViewer[index], index)" title="预览">
                                <div class="i-heroicons-eye w-4 h-4"></div>
                              </button>
                              <button class="action-btn edit-btn"
                                @click="handleEditSelectedImage(selectedFilesForViewer[index], index)" title="编辑">
                                <div class="i-heroicons-pencil-square w-4 h-4"></div>
                              </button>
                              <button class="action-btn delete-btn" @click="removeFile(index)" title="删除">
                                <div class="i-heroicons-trash w-4 h-4"></div>
                              </button>
                            </div>
                          </div>

                          <!-- 上传状态指示器 - 增强版 -->
                          <div v-if="file.uploadStatus === 'uploading'" class="upload-progress-overlay">
                            <div class="progress-content">
                              <!-- 圆形进度条 -->
                              <div class="progress-circle">
                                <svg class="progress-ring" width="40" height="40">
                                  <circle class="progress-ring-circle" stroke="currentColor" stroke-width="3"
                                    fill="transparent" r="16" cx="20" cy="20" :stroke-dasharray="`${2 * Math.PI * 16}`"
                                    :stroke-dashoffset="`${2 * Math.PI * 16 * (1 - (file.uploadProgress || 0) / 100)}`" />
                                </svg>
                                <div class="progress-percentage">{{ file.uploadProgress || 0 }}%</div>
                              </div>

                              <!-- 上传信息 -->
                              <div class="upload-info">
                                <p class="upload-speed" v-if="file.uploadSpeed">{{ file.uploadSpeed }}</p>
                                <p class="upload-remaining" v-if="file.uploadRemaining">剩余 {{ file.uploadRemaining }}
                                </p>
                              </div>
                            </div>
                          </div>

                          <!-- 上传成功指示器 -->
                          <div v-else-if="file.uploadStatus === 'success'" class="status-indicator success">
                            <div class="i-heroicons-check w-3 h-3"></div>
                          </div>

                          <!-- 上传失败指示器 -->
                          <div v-else-if="file.uploadStatus === 'error'" class="status-indicator error">
                            <div class="i-heroicons-exclamation-triangle w-3 h-3"></div>
                          </div>
                        </div>

                        <!-- 文件名 -->
                        <div class="file-info">
                          <p class="file-name" :title="file.name">{{ file.displayName || file.name }}</p>
                          <div class="file-details">
                            <p class="file-size">{{ formatFileSize(file.size) }}</p>
                            <!-- 备份状态显示 -->
                            <div class="backup-status">
                              <span v-if="file.uploadStatus === 'success'" class="backup-info success">
                                ✅ {{ getSuccessBackupCount(file) }}/{{ batchSettings.backupCount }} 图床
                              </span>
                              <span v-else-if="file.uploadStatus === 'uploading'" class="backup-info uploading">
                                🔄 备份到 {{ batchSettings.backupCount }} 个图床
                              </span>
                              <span v-else-if="file.uploadStatus === 'error'" class="backup-info error">
                                ❌ 备份失败
                              </span>
                              <span v-else class="backup-info pending">
                                📤 将备份到 {{ batchSettings.backupCount }} 个图床
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- URL上传 - 紧凑型设计 -->
                <div v-else-if="activeTab === 'url'" class="space-y-4">
                  <div class="space-y-3">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      图片链接
                    </label>
                    <a-textarea v-model:value="urlInput" placeholder="请输入图片链接，每行一个链接" :rows="5" allow-clear />
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                      支持批量输入，每行一个图片链接
                    </p>
                  </div>

                  <a-button @click="parseUrls" :disabled="!urlInput.trim()" size="small">
                    <template #icon>
                      <div class="i-heroicons-link w-4 h-4"></div>
                    </template>
                    解析链接
                  </a-button>

                  <!-- URL解析结果 - 紧凑型设计 -->
                  <div v-if="parsedUrls.length > 0" class="space-y-3">
                    <h3 class="text-base font-medium text-gray-900 dark:text-gray-100">
                      解析结果 ({{ parsedUrls.length }})
                    </h3>
                    <div class="space-y-2">
                      <div v-for="(url, index) in parsedUrls" :key="index"
                        class="flex items-center space-x-2 p-2 bg-gray-50 dark:bg-gray-700 rounded-md">
                        <div class="flex-shrink-0 w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded overflow-hidden">
                          <img :src="url" :alt="`图片 ${index + 1}`" class="w-full h-full object-cover"
                            @error="handleImageError(index)" />
                        </div>
                        <div class="flex-1 min-w-0">
                          <p class="text-xs text-gray-900 dark:text-gray-100 truncate">{{ url }}</p>
                          <!-- URL上传状态 -->
                          <div v-if="urlUploadStatuses[index]" class="mt-1">
                            <div v-if="urlUploadStatuses[index].status === 'uploading'"
                              class="flex items-center space-x-1">
                              <div class="w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse"></div>
                              <span class="text-xs text-blue-600 dark:text-blue-400">保存中...</span>
                            </div>
                            <div v-else-if="urlUploadStatuses[index].status === 'success'"
                              class="flex items-center space-x-1">
                              <div class="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                              <span class="text-xs text-green-600 dark:text-green-400">保存成功</span>
                            </div>
                            <div v-else-if="urlUploadStatuses[index].status === 'error'"
                              class="flex items-center space-x-1">
                              <div class="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                              <span class="text-xs text-red-600 dark:text-red-400">{{ urlUploadStatuses[index].error ||
                                '保存失败' }}</span>
                            </div>
                          </div>
                        </div>
                        <button @click="removeParsedUrl(index)"
                          class="flex-shrink-0 p-1 text-red-500 hover:text-red-700 transition-colors"
                          :disabled="isUploading">
                          <div class="i-heroicons-trash w-3 h-3"></div>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 右侧：配置面板 - 黄金比例 38.2% -->
              <div class="config-panel flex flex-col bg-white dark:bg-gray-800">
                <!-- 配置面板头部 - 参考设置界面设计 -->
                <div
                  class="config-header px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                  <div class="flex items-center justify-between">
                    <div class="card-title-wrapper">
                      <span class="card-title text-base font-semibold text-gray-900 dark:text-gray-100">图片配置</span>
                      <a-tooltip title="配置图片的名称、标签、描述和上传目标" placement="bottom">
                        <a-button type="text" size="small" class="help-icon-btn ml-2">
                          <div class="i-heroicons-question-mark-circle w-4 h-4"></div>
                        </a-button>
                      </a-tooltip>
                    </div>
                    <div class="flex items-center space-x-2">
                      <a-button @click="showBatchConfig = !showBatchConfig"
                        :type="showBatchConfig ? 'primary' : 'default'" size="small">
                        <div class="i-heroicons-adjustments-horizontal w-4 h-4 mr-1"></div>
                        {{ showBatchConfig ? '单张配置' : '批量配置' }}
                      </a-button>
                    </div>
                  </div>
                </div>

                <!-- 配置内容 -->
                <div class="flex-1 overflow-y-auto">
                  <!-- 批量配置模式 - 参考设置界面设计 -->
                  <div v-if="showBatchConfig" class="config-content p-4">
                    <!-- 图床备份设置 - 仅在本地文件上传时显示 -->
                    <div v-if="activeTab === 'local'" class="config-section">
                      <div class="section-header mb-3">
                        <div class="flex items-center justify-between">
                          <div>
                            <h4 class="section-title">图床备份</h4>
                            <p class="section-description">
                              上传到 {{ batchSettings.backupCount }} 个图床确保安全
                            </p>
                          </div>
                          <a-tooltip title="多图床备份可以防止图片链接失效" placement="left">
                            <div class="i-heroicons-information-circle w-4 h-4 text-gray-400"></div>
                          </a-tooltip>
                        </div>
                      </div>

                      <!-- 备份数量滑条 -->
                      <div class="backup-slider mb-4">
                        <label class="form-label mb-2">备份图床数量: {{ batchSettings.backupCount }}</label>
                        <a-slider
                          v-model:value="batchSettings.backupCount"
                          :min="1"
                          :max="5"
                          :marks="{ 1: '1', 2: '2', 3: '3', 4: '4', 5: '5' }"
                          :tooltip-formatter="(value) => `${value}个图床`"
                        />
                        <div class="slider-description">
                          {{ batchSettings.backupCount === 1 ? '单图床上传' : `备份到${batchSettings.backupCount}个图床` }}
                        </div>
                      </div>
                    </div>

                    <!-- 图床选择 - 仅在本地文件上传时显示 -->
                    <div v-if="activeTab === 'local'" class="config-section">
                      <div class="section-header mb-3">
                        <div class="flex items-center justify-between">
                          <div>
                            <h4 class="section-title">图床选择</h4>
                            <p class="section-description">
                              {{ batchSettings.selectedHosts.length === 0 ? '未选择图床将随机上传' : `已选择
                              ${batchSettings.selectedHosts.length} 个图床` }}
                            </p>
                          </div>
                          <a-button type="text" size="small" @click="hostSelectionExpanded = !hostSelectionExpanded"
                            class="flex items-center">
                            <div :class="hostSelectionExpanded ? 'i-heroicons-chevron-up' : 'i-heroicons-chevron-down'"
                              class="w-4 h-4"></div>
                          </a-button>
                        </div>
                      </div>

                      <!-- 可收起的图床选择区域 -->
                      <div v-show="hostSelectionExpanded" class="host-selection-area">
                        <!-- 快速操作 -->
                        <div class="flex items-center justify-between mb-3 p-2 bg-gray-50 dark:bg-gray-700 rounded">
                          <span class="text-xs text-gray-600 dark:text-gray-400">
                            共 {{ imageLibraries.length }} 个可用图床
                          </span>
                          <div class="flex gap-2">
                            <a-button size="small" type="text" @click="selectAllHosts">全选</a-button>
                            <a-button size="small" type="text" @click="clearHostSelection">清空</a-button>
                            <a-button size="small" type="text" @click="selectRandomHosts">随机选择</a-button>
                          </div>
                        </div>

                        <!-- 图床卡片列表 - 内部滚动容器 -->
                        <div
                          class="host-cards-container max-h-64 overflow-y-auto pr-2 space-y-2 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent">
                          <div v-for="host in imageLibraries" :key="host.id" class="host-card">
                            <div
                              class="flex items-start space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-blue-300 dark:hover:border-blue-500 transition-colors">
                              <!-- 选择框 -->
                              <a-checkbox :checked="batchSettings.selectedHosts.includes(host.id)"
                                @change="(e) => toggleHostSelection(host.id, e.target.checked)" class="mt-1" />

                              <!-- 图床信息 -->
                              <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between mb-1">
                                  <h5 class="font-medium text-sm text-gray-900 dark:text-gray-100">{{ host.name }}</h5>
                                  <a-tag :color="host.enabled ? 'success' : 'error'" size="small">
                                    {{ host.enabled ? '已启用' : '已禁用' }}
                                  </a-tag>
                                </div>

                                <div class="text-xs text-gray-500 dark:text-gray-400 space-y-1">
                                  <div class="flex items-center space-x-4">
                                    <span>提供商: {{ host.provider }}</span>
                                    <span>优先级: {{ host.priority }}</span>
                                  </div>
                                  <div class="flex items-center space-x-4">
                                    <span>最大文件: {{ host.maxFileSize }}MB</span>
                                    <span>支持格式: {{ host.allowedFormats?.join(', ') || '未知' }}</span>
                                  </div>
                                  <div class="text-xs text-gray-400 truncate">
                                    API: {{ host.apiUrl }}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- 空状态 -->
                        <div v-if="imageLibraries.length === 0"
                          class="text-center py-8 text-gray-500 dark:text-gray-400">
                          <div class="i-heroicons-photo w-12 h-12 mx-auto mb-2 opacity-50"></div>
                          <p class="text-sm">暂无可用的图床配置</p>
                          <p class="text-xs mt-1">请先在设置中配置图床服务</p>
                        </div>
                      </div>
                    </div>

                    <!-- 批量设置 -->
                    <div class="config-section">
                      <div class="section-header mb-3">
                        <h4 class="section-title">批量设置</h4>
                        <p class="section-description">为所有图片设置统一的属性</p>
                      </div>
                      <div class="form-grid space-y-4">
                        <div class="form-item">
                          <label class="form-label">名称前缀</label>
                          <a-input v-model:value="batchSettings.namePrefix" placeholder="为图片名称添加前缀（可选）" size="small"
                            allow-clear />
                        </div>
                        <div class="form-item">
                          <label class="form-label">标签</label>
                          <a-select v-model:value="batchSettings.tags" mode="tags" placeholder="选择或输入标签" size="small"
                            :options="availableTags.map(tag => ({ label: tag, value: tag }))" style="width: 100%" />
                          <div class="form-help">用于分类和搜索图片</div>
                        </div>
                        <div class="form-item">
                          <label class="form-label">描述</label>
                          <a-textarea v-model:value="batchSettings.description" placeholder="为图片添加描述信息（可选）" :rows="2"
                            size="small" allow-clear />
                        </div>
                      </div>

                      <!-- 应用到所有图片 -->
                      <div class="action-section mt-4">
                        <a-button @click="applyBatchSettings" type="primary" size="small" class="w-full"
                          :disabled="selectedFiles.length === 0">
                          <template #icon>
                            <div class="i-heroicons-arrow-down w-4 h-4"></div>
                          </template>
                          应用到所有图片 ({{ selectedFiles.length }})
                        </a-button>
                      </div>
                    </div>
                  </div>

                  <!-- 单张图片配置模式 - 紧凑型设计 -->
                  <div v-else class="space-y-3">
                    <!-- 图片选择器 -->
                    <div v-if="selectedFiles.length > 0" class="p-3 border-b border-gray-200 dark:border-gray-700">
                      <label class="block text-xs text-gray-500 dark:text-gray-400 mb-2">
                        选择要配置的图片 ({{ currentImageIndex + 1 }}/{{ selectedFiles.length }})
                      </label>
                      <div class="flex items-center space-x-2">
                        <a-button @click="previousImage" :disabled="currentImageIndex === 0" size="small" type="text">
                          <template #icon>
                            <div class="i-heroicons-chevron-left w-3 h-3"></div>
                          </template>
                        </a-button>
                        <div
                          class="flex-1 flex items-center space-x-2 p-2 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600">
                          <img :src="currentImage?.preview" :alt="currentImage?.name"
                            class="w-6 h-6 object-cover rounded" />
                          <span class="text-xs text-gray-900 dark:text-gray-100 truncate">
                            {{ currentImage?.name }}
                          </span>
                        </div>
                        <a-button @click="nextImage" :disabled="currentImageIndex === selectedFiles.length - 1"
                          size="small" type="text">
                          <template #icon>
                            <div class="i-heroicons-chevron-right w-3 h-3"></div>
                          </template>
                        </a-button>
                      </div>
                    </div>

                    <!-- 单张图片配置 -->
                    <div v-if="currentImage" class="p-3 space-y-3">
                      <!-- 图片名称 -->
                      <div>
                        <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                          图片名称
                        </label>
                        <a-input v-model:value="currentImage.displayName" placeholder="输入图片显示名称" size="small"
                          allow-clear>
                          <template #prefix>
                            <div class="i-heroicons-photo w-3 h-3"></div>
                          </template>
                        </a-input>
                      </div>

                      <!-- 图床选择 -->
                      <div>
                        <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                          图床选择
                        </label>
                        <div class="space-y-1">
                          <div v-for="host in imageLibraries" :key="host.id" class="flex items-center space-x-2">
                            <a-checkbox :checked="currentImage.selectedHosts.includes(host.id)"
                              @change="(e) => toggleCurrentImageHostSelection(host.id, e.target.checked)">
                              <span class="text-xs">{{ host.name }}</span>
                            </a-checkbox>
                            <a-tag :color="host.enabled ? 'green' : 'red'" size="small">
                              {{ host.enabled ? '在线' : '离线' }}
                            </a-tag>
                          </div>
                        </div>
                      </div>

                      <!-- 标签选择 -->
                      <div>
                        <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                          标签
                        </label>
                        <a-select v-model:value="currentImage.tags" mode="tags" placeholder="选择或创建标签" size="small"
                          :options="availableTags.map(tag => ({ label: tag, value: tag }))" style="width: 100%" />
                      </div>

                      <!-- 描述 -->
                      <div>
                        <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                          描述
                        </label>
                        <a-textarea v-model:value="currentImage.description" placeholder="输入图片描述（可选）" :rows="2"
                          size="small" allow-clear />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 底部操作栏 - 紧凑型设计 -->
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-3 bg-gray-50 dark:bg-gray-900">
              <div class="flex items-center justify-between">
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  <span v-if="activeTab === 'local'">
                    已选择 {{ selectedFiles.length }} 张图片
                  </span>
                  <span v-else-if="activeTab === 'url'">
                    已解析 {{ parsedUrls.length }} 个链接
                  </span>
                </div>
                <div class="flex justify-between items-center">
                  <!-- 上传完成提示 -->
                  <div v-if="uploadedCount > 0 && !isUploading" class="text-sm text-green-600 dark:text-green-400">
                    ✅ 已上传 {{ uploadedCount }} 张图片，可继续添加更多
                  </div>
                  <div v-else class="flex-1"></div>

                  <div class="flex space-x-2">
                    <a-button @click="handleClose">
                      {{ uploadedCount > 0 && !isUploading ? '完成' : '取消' }}
                    </a-button>

                    <!-- 重置按钮 - 只在有内容时显示 -->
                    <a-button v-if="(selectedFiles.length > 0 || parsedUrls.length > 0) && !isUploading"
                      @click="handleReset" type="default">
                      <template #icon>
                        <div class="i-heroicons-arrow-path w-4 h-4"></div>
                      </template>
                      重置
                    </a-button>

                    <a-button @click="startUpload" type="primary" :disabled="!canUpload" :loading="isUploading">
                      <template #icon>
                        <div class="i-heroicons-cloud-arrow-up w-4 h-4"></div>
                      </template>
                      {{ uploadedCount > 0 && !isUploading ? '继续上传' : '开始上传' }}
                    </a-button>
                  </div>
                </div>
              </div>

              <!-- 上传进度 - 紧凑型设计 -->
              <div v-if="isUploading" class="mt-3">
                <div class="flex items-center justify-between mb-1">
                  <span class="text-xs font-medium text-gray-700 dark:text-gray-300">
                    上传进度
                  </span>
                  <span class="text-xs text-gray-500 dark:text-gray-400">
                    {{ uploadedCount }}/{{ totalCount }}
                  </span>
                </div>
                <a-progress :percent="overallProgress" size="small" :show-info="false" stroke-color="#1890ff" />
                <div class="flex items-center justify-between mt-1 text-xs text-gray-500 dark:text-gray-400">
                  <span>{{ uploadSpeed }}</span>
                  <span>{{ remainingTime }}</span>
                </div>
              </div>

              <!-- 上传日志 - 实时显示 -->
              <div v-if="uploadLogs.length > 0" class="mt-3">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-xs font-medium text-gray-700 dark:text-gray-300">
                    上传日志
                  </span>
                  <div class="flex items-center space-x-2">
                    <a-button size="small" type="text" @click="showUploadLogs = !showUploadLogs">
                      <div :class="showUploadLogs ? 'i-heroicons-eye-slash' : 'i-heroicons-eye'" class="w-3 h-3"></div>
                    </a-button>
                    <a-button size="small" type="text" @click="clearUploadLogs">
                      <div class="i-heroicons-trash w-3 h-3"></div>
                    </a-button>
                  </div>
                </div>

                <div v-show="showUploadLogs"
                  class="upload-logs-container max-h-32 overflow-y-auto bg-gray-50 dark:bg-gray-800 rounded border p-2 space-y-1">
                  <div v-for="log in uploadLogs.slice(-20)" :key="log.id"
                    class="upload-log-item text-xs flex items-start space-x-2" :class="{
                      'text-blue-600 dark:text-blue-400': log.level === 'info',
                      'text-green-600 dark:text-green-400': log.level === 'success',
                      'text-yellow-600 dark:text-yellow-400': log.level === 'warning',
                      'text-red-600 dark:text-red-400': log.level === 'error'
                    }">
                    <span class="text-gray-400 dark:text-gray-500 font-mono text-xs flex-shrink-0">
                      {{ log.timestamp.toLocaleTimeString() }}
                    </span>
                    <span class="flex-shrink-0">
                      <span v-if="log.level === 'info'">ℹ️</span>
                      <span v-else-if="log.level === 'success'">✅</span>
                      <span v-else-if="log.level === 'warning'">⚠️</span>
                      <span v-else-if="log.level === 'error'">❌</span>
                    </span>
                    <span class="flex-1 break-words">{{ log.message }}</span>
                  </div>

                  <!-- 空状态 -->
                  <div v-if="uploadLogs.length === 0" class="text-center py-2 text-gray-400 dark:text-gray-500">
                    暂无上传日志
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>

  <!-- 图片编辑器 -->
  <ImageEditor v-model:visible="editorVisible" :image-src="editingImage?.preview || ''"
    :image-name="editingImage?.name || ''" @save="handleEditorSave" @cancel="handleEditorCancel" />
</template>

<style scoped>
/* 黄金比例布局 */
.upload-area {
  width: 61.8%;
}

.config-panel {
  width: 38.2%;
}

/* 备份滑条样式 */
.backup-slider {
  margin-bottom: 16px;
}

.slider-description {
  font-size: 12px;
  color: #6b7280;
  margin-top: 8px;
  text-align: center;
  padding: 6px 12px;
  background: #f3f4f6;
  border-radius: 6px;
  border-left: 3px solid #3b82f6;
}

.dark .slider-description {
  background: #374151;
  color: #d1d5db;
}

/* 文件信息备份状态样式 */
.file-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.backup-status {
  margin-top: 2px;
}

.backup-info {
  font-size: 10px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

.backup-info.success {
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.backup-info.uploading {
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

.backup-info.error {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.backup-info.pending {
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
}

/* 暗黑模式适配 */
.dark .backup-info.success {
  color: #34d399;
  background: rgba(52, 211, 153, 0.2);
}

.dark .backup-info.uploading {
  color: #60a5fa;
  background: rgba(96, 165, 250, 0.2);
}

.dark .backup-info.error {
  color: #f87171;
  background: rgba(248, 113, 113, 0.2);
}

.dark .backup-info.pending {
  color: #9ca3af;
  background: rgba(156, 163, 175, 0.2);
}

.backup-count-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #f9fafb;
}

.backup-count-option:hover {
  border-color: #3b82f6;
  background: #eff6ff;
  transform: translateY(-1px);
}

.backup-count-option.active {
  border-color: #3b82f6;
  background: #dbeafe;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.count-number {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.count-label {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 2px;
}

.count-description {
  font-size: 10px;
  color: #6b7280;
  text-align: center;
}

.backup-count-option.active .count-number {
  color: #3b82f6;
}

.backup-count-option.active .count-label {
  color: #2563eb;
}

.backup-count-option.active .count-description {
  color: #3b82f6;
}

/* 选择策略样式 */
.selection-strategy {
  margin-bottom: 16px;
}

.strategy-description {
  font-size: 12px;
  color: #6b7280;
  margin-top: 8px;
  padding: 8px 12px;
  background: #f3f4f6;
  border-radius: 6px;
  border-left: 3px solid #3b82f6;
}

/* 暗黑模式适配 */
.dark .backup-count-option {
  background: #374151;
  border-color: #4b5563;
}

.dark .backup-count-option:hover {
  background: #4b5563;
  border-color: #3b82f6;
}

.dark .backup-count-option.active {
  background: #1e3a8a;
  border-color: #3b82f6;
}

.dark .count-number {
  color: #f9fafb;
}

.dark .count-label {
  color: #e5e7eb;
}

.dark .count-description {
  color: #9ca3af;
}

.dark .backup-count-option.active .count-number,
.dark .backup-count-option.active .count-label,
.dark .backup-count-option.active .count-description {
  color: #60a5fa;
}

.dark .strategy-description {
  background: #374151;
  color: #d1d5db;
  border-left-color: #3b82f6;
}

/* Ant Design Tabs 样式优化 */
.upload-tabs :deep(.ant-tabs-tab) {
  padding: 8px 16px !important;
  margin: 0 !important;
}

.upload-tabs :deep(.ant-tabs-tab-btn) {
  font-size: 14px !important;
  font-weight: 500 !important;
}

.upload-tabs :deep(.ant-tabs-ink-bar) {
  height: 2px !important;
}

/* 紧凑型 Upload 组件样式 */
.compact-upload-dragger {
  height: 100px !important;
  border-radius: 8px !important;
  margin-bottom: 12px !important;
}

.compact-upload-dragger .ant-upload-drag-icon {
  margin-bottom: 8px !important;
}

.compact-upload-dragger .ant-upload-text {
  margin-bottom: 4px !important;
  font-size: 14px !important;
}

.compact-upload-dragger .ant-upload-hint {
  font-size: 12px !important;
  color: #8c8c8c !important;
}

/* 配置面板样式 - 参考设置界面设计 */
.config-header {
  background: #fafafa;
}

.dark .config-header {
  background: #1f1f1f;
}

.card-title-wrapper {
  display: flex;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.dark .card-title {
  color: #ffffff;
}

.help-icon-btn {
  color: #8c8c8c !important;
  border: none !important;
  box-shadow: none !important;
}

.help-icon-btn:hover {
  color: #1890ff !important;
  background: rgba(24, 144, 255, 0.06) !important;
}

/* 配置内容样式 */
.config-content {
  background: #ffffff;
}

.dark .config-content {
  background: #141414;
}

.config-section {
  margin-bottom: 24px;
}

.config-section:last-child {
  margin-bottom: 0;
}

.section-header {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.dark .section-header {
  border-bottom-color: #303030;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 4px 0;
}

.dark .section-title {
  color: #ffffff;
}

.section-description {
  font-size: 12px;
  color: #8c8c8c;
  margin: 0;
  line-height: 1.4;
}

/* 图床选择网格 */
.host-selection-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.host-option {
  padding: 8px 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  transition: all 0.2s;
}

.host-option:hover {
  border-color: #d9d9d9;
  background: #fafafa;
}

.dark .host-option {
  border-color: #303030;
  background: #1f1f1f;
}

.dark .host-option:hover {
  border-color: #434343;
  background: #262626;
}

.host-checkbox :deep(.ant-checkbox-wrapper) {
  width: 100%;
}

.host-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-left: 8px;
}

.host-name {
  font-size: 13px;
  font-weight: 500;
  color: #262626;
}

.dark .host-name {
  color: #ffffff;
}

.host-status {
  margin-left: 8px;
}

/* 表单样式 */
.form-grid {
  display: flex;
  flex-direction: column;
}

.form-item {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-size: 13px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 6px;
  display: block;
}

.dark .form-label {
  color: #ffffff;
}

.form-help {
  font-size: 11px;
  color: #8c8c8c;
  margin-top: 4px;
  line-height: 1.3;
}

.action-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.dark .action-section {
  border-top-color: #303030;
}

/* 暗黑模式适配 */
.dark .compact-upload-dragger .ant-upload-text {
  color: #fff !important;
}

.dark .compact-upload-dragger .ant-upload-hint {
  color: #a6a6a6 !important;
}

/* 拖拽状态样式 */
.compact-upload-dragger.ant-upload-drag-hover {
  border-color: #40a9ff !important;
  background-color: #f0f9ff !important;
}

.dark .compact-upload-dragger.ant-upload-drag-hover {
  background-color: #111b26 !important;
}

/* 图片预览网格样式 */
.image-preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

@media (max-width: 768px) {
  .image-preview-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 12px;
  }
}

.image-preview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.image-container {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f5f5f5;
  border: 2px solid #e5e7eb;
  transition: all 0.2s ease;
}

.image-container:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.dark .image-container {
  background-color: #374151;
  border-color: #4b5563;
}

.dark .image-container:hover {
  border-color: #60a5fa;
  box-shadow: 0 4px 12px rgba(96, 165, 250, 0.15);
}

.image-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.group:hover .image-thumbnail {
  transform: scale(1.05);
}

/* 悬浮遮罩 */
.image-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.group:hover .image-overlay {
  opacity: 1;
}

.overlay-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  color: white;
}

.preview-btn {
  background-color: #3b82f6;
}

.preview-btn:hover {
  background-color: #2563eb;
  transform: scale(1.1);
}

.edit-btn {
  background-color: #10b981;
}

.edit-btn:hover {
  background-color: #059669;
  transform: scale(1.1);
}

.delete-btn {
  background-color: #ef4444;
}

.delete-btn:hover {
  background-color: #dc2626;
  transform: scale(1.1);
}

/* 上传进度遮罩 */
.upload-progress-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: white;
}

/* 图床选择区域滚动条样式 */
.host-cards-container {
  /* 自定义滚动条 */
  scrollbar-width: thin;
  scrollbar-color: #d1d5db transparent;
}

.host-cards-container::-webkit-scrollbar {
  width: 6px;
}

.host-cards-container::-webkit-scrollbar-track {
  background: transparent;
}

.host-cards-container::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.host-cards-container::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

/* 暗黑模式滚动条 */
.dark .host-cards-container {
  scrollbar-color: #4b5563 transparent;
}

.dark .host-cards-container::-webkit-scrollbar-thumb {
  background-color: #4b5563;
}

.dark .host-cards-container::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}

/* 上传日志容器样式 */
.upload-logs-container {
  /* 自定义滚动条 */
  scrollbar-width: thin;
  scrollbar-color: #d1d5db transparent;
}

.upload-logs-container::-webkit-scrollbar {
  width: 4px;
}

.upload-logs-container::-webkit-scrollbar-track {
  background: transparent;
}

.upload-logs-container::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

.upload-logs-container::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

/* 暗黑模式日志滚动条 */
.dark .upload-logs-container {
  scrollbar-color: #4b5563 transparent;
}

.dark .upload-logs-container::-webkit-scrollbar-thumb {
  background-color: #4b5563;
}

.dark .upload-logs-container::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}

/* 日志项动画 */
.upload-log-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 圆形进度条 */
.progress-circle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-circle {
  transition: stroke-dashoffset 0.3s ease;
  stroke: #3b82f6;
  stroke-linecap: round;
}

.progress-percentage {
  position: absolute;
  font-size: 10px;
  font-weight: 600;
  color: white;
}

/* 上传信息 */
.upload-info {
  text-align: center;
  font-size: 10px;
  opacity: 0.9;
}

.upload-speed {
  margin: 0;
  color: #60a5fa;
  font-weight: 500;
}

.upload-remaining {
  margin: 2px 0 0 0;
  color: #fbbf24;
}

.progress-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #374151;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.progress-text {
  color: white;
  font-size: 12px;
  font-weight: 500;
  margin: 0;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 状态指示器 */
.status-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
}

.status-indicator.success {
  background-color: #10b981;
}

.status-indicator.error {
  background-color: #ef4444;
}

/* 文件信息 */
.file-info {
  width: 100%;
  margin-top: 8px;
  text-align: center;
}

.file-name {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  margin: 0 0 2px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.dark .file-name {
  color: #d1d5db;
}

.file-size {
  font-size: 11px;
  color: #6b7280;
  margin: 0;
}

.dark .file-size {
  color: #9ca3af;
}
</style>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import pLimit from 'p-limit'
import ImageEditor from './ImageEditor.vue'
import type { ImageItem } from './ImageViewer.vue'
import { imageDataService } from '@/services/imageDataService'
import { imageUploadService, type UploadProgressCallback } from '@/services/imageUploadService'
import { imageHostService } from '@/services/imageHostService'
import type { ImageHostConfig } from '@/types/imageHost'

interface UploadFile {
  id: string
  file: File
  name: string
  displayName: string
  size: number
  preview: string
  tags: string[]
  description: string
  selectedHosts: string[]
  uploadProgress?: number
  uploadStatus?: 'pending' | 'uploading' | 'success' | 'error'
  uploadResults?: Array<{
    hostId: string
    hostName: string
    url?: string
    deleteUrl?: string
    error?: string
  }>
  // 新增进度相关字段
  uploadSpeed?: string
  uploadRemaining?: string
  uploadLoaded?: number
  uploadTotal?: number
}

interface ImageLibrary {
  id: string
  name: string
  type: string
  status: 'online' | 'offline'
  speed: string
  quota?: string
  sizeLimit: string
  supportedFormats: string[]
  cdnEnabled: boolean
  enabled: boolean
}

interface Tag {
  id: number
  name: string
  resource_count: number
}

const emit = defineEmits<{
  close: []
  uploaded: [results: any[]]
}>()

// 工具方法
const showSuccess = (msg: string) => message.success(msg)
const showError = (msg: string) => message.error(msg)

// 图床选择切换方法
const toggleHostSelection = (hostId: string, checked: boolean) => {
  if (checked) {
    if (!batchSettings.value.selectedHosts.includes(hostId)) {
      batchSettings.value.selectedHosts.push(hostId)
    }
  } else {
    const index = batchSettings.value.selectedHosts.indexOf(hostId)
    if (index > -1) {
      batchSettings.value.selectedHosts.splice(index, 1)
    }
  }
}

const toggleCurrentImageHostSelection = (hostId: string, checked: boolean) => {
  if (!currentImage.value) return

  if (checked) {
    if (!currentImage.value.selectedHosts.includes(hostId)) {
      currentImage.value.selectedHosts.push(hostId)
    }
  } else {
    const index = currentImage.value.selectedHosts.indexOf(hostId)
    if (index > -1) {
      currentImage.value.selectedHosts.splice(index, 1)
    }
  }
}

// 状态管理
const visible = ref(false)
const activeTab = ref<'local' | 'url'>('local')
const isDragOver = ref(false)
const isUploading = ref(false)

// 文件相关
const selectedFiles = ref<UploadFile[]>([])
const fileInput = ref<HTMLInputElement>()
const dropZone = ref<HTMLElement>()

// 图片编辑器相关
const editorVisible = ref(false)
const editingImage = ref<UploadFile | null>(null)
const editingIndex = ref(-1)

// Ant Design Upload 相关
const fileList = ref([])
const processedFileCount = ref(0) // 跟踪已处理的文件数量

// Upload 组件方法
const beforeUpload = (file: File) => {
  // 阻止自动上传，我们手动处理
  return false
}

const handleUploadChange = (info: any) => {
  console.log('handleUploadChange called:', info.fileList.length, 'files, processed:', processedFileCount.value)

  // 只处理新增的文件（从已处理数量之后的文件）
  const newFiles = info.fileList
    .slice(processedFileCount.value) // 只取新增的文件
    .filter((item: any) => item.originFileObj) // 确保有原始文件对象
    .map((item: any) => item.originFileObj)

  console.log('New files to process:', newFiles.length)

  if (newFiles.length > 0) {
    addFiles(newFiles)
    processedFileCount.value = info.fileList.length // 更新已处理数量
  }

  // 更新 fileList，保持 Ant Design Upload 的状态
  fileList.value = info.fileList
}

// URL相关
const urlInput = ref('')
const parsedUrls = ref<string[]>([])

// URL上传状态
interface UrlUploadStatus {
  url: string
  status: 'pending' | 'uploading' | 'success' | 'error'
  progress: number
  result?: any
  error?: string
}

const urlUploadStatuses = ref<UrlUploadStatus[]>([])

// 配置相关
const selectedLibrary = ref<string[]>([])
const showBatchConfig = ref(true)
const currentImageIndex = ref(0)
const batchSettings = ref({
  namePrefix: '',
  tags: [] as string[],
  description: '',
  selectedHosts: [] as string[],
  backupCount: 2 // 默认备份到2个图床
})
const uploadSettings = ref({
  autoCompress: true,
  loadBalance: true
})

// 上传进度
const uploadedCount = ref(0)
const totalCount = ref(0)
const overallProgress = ref(0)
const uploadSpeed = ref('')
const remainingTime = ref('')

// 选项卡配置
const uploadTabs = [
  {
    key: 'local' as const,
    label: '本地资源上传',
    icon: 'i-heroicons-folder-open'
  },
  {
    key: 'url' as const,
    label: 'URL上传',
    icon: 'i-heroicons-link'
  }
]

// 图床配置数据
const imageLibraries = ref<ImageHostConfig[]>([])

// 图床选择展开状态
const hostSelectionExpanded = ref(true)

// 上传日志系统
interface UploadLog {
  id: string
  fileId: string
  timestamp: Date
  level: 'info' | 'success' | 'warning' | 'error'
  message: string
}

const uploadLogs = ref<UploadLog[]>([])
const showUploadLogs = ref(true)

// 日志管理函数
const addUploadLog = (fileId: string, level: UploadLog['level'], message: string) => {
  const log: UploadLog = {
    id: `log_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
    fileId,
    timestamp: new Date(),
    level,
    message
  }

  uploadLogs.value.push(log)

  // 控制台输出
  const timestamp = log.timestamp.toLocaleTimeString()
  const prefix = `[${timestamp}] [${fileId.substring(0, 8)}]`

  switch (level) {
    case 'info':
      console.log(`${prefix} ℹ️ ${message}`)
      break
    case 'success':
      console.log(`${prefix} ✅ ${message}`)
      break
    case 'warning':
      console.warn(`${prefix} ⚠️ ${message}`)
      break
    case 'error':
      console.error(`${prefix} ❌ ${message}`)
      break
  }

  // 限制日志数量，避免内存泄漏
  if (uploadLogs.value.length > 1000) {
    uploadLogs.value = uploadLogs.value.slice(-500)
  }
}

const clearUploadLogs = () => {
  uploadLogs.value = []
}

const getLogsForFile = (fileId: string) => {
  return uploadLogs.value.filter(log => log.fileId === fileId)
}

// 上传到单个图床的函数
const uploadToSingleHost = async (
  file: File,
  hostConfig: ImageHostConfig,
  onProgress?: UploadProgressCallback
): Promise<{ url: string; deleteUrl?: string }> => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()
    const formData = new FormData()

    // 构建表单数据
    formData.append(hostConfig.fileField || 'file', file)

    // 添加额外参数
    if (hostConfig.params) {
      Object.entries(hostConfig.params).forEach(([key, value]) => {
        formData.append(key, String(value))
      })
    }

    // 上传进度监控
    if (onProgress) {
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = {
            loaded: event.loaded,
            total: event.total,
            percentage: Math.round((event.loaded / event.total) * 100),
            speed: 0, // 可以计算速度
            remainingTime: 0 // 可以计算剩余时间
          }
          onProgress(progress)
        }
      })
    }

    // 请求完成
    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const responseText = xhr.responseText
          console.log(`🔍 ${hostConfig.name} 原始响应:`, responseText)
          console.log(`🔍 ${hostConfig.name} 配置信息:`, {
            responseType: hostConfig.responseType,
            successField: hostConfig.successField,
            successValue: hostConfig.successValue,
            urlField: hostConfig.urlField,
            errorField: hostConfig.errorField
          })

          let data: any

          if (hostConfig.responseType === 'json') {
            data = JSON.parse(responseText)
            console.log(`🔍 ${hostConfig.name} 解析后数据:`, data)
          } else {
            data = responseText
          }

          // 检查是否成功
          if (hostConfig.responseType === 'json' && hostConfig.successField) {
            const success = getNestedValue(data, hostConfig.successField)
            console.log(`🔍 ${hostConfig.name} 成功字段值:`, success, '期望值:', hostConfig.successValue)

            // 智能类型转换和比较
            let isSuccess = false

            // 严格相等比较
            if (success === hostConfig.successValue) {
              isSuccess = true
            }
            // 类型转换比较（处理 true/1, false/0 等情况）
            else if (String(success) === String(hostConfig.successValue)) {
              isSuccess = true
              console.log(`🔧 ${hostConfig.name} 通过字符串转换匹配成功`)
            }
            // 布尔值和数字的特殊处理
            else if (
              (success === true && (hostConfig.successValue === 1 || hostConfig.successValue === '1')) ||
              (success === false && (hostConfig.successValue === 0 || hostConfig.successValue === '0')) ||
              (success === 1 && hostConfig.successValue === true) ||
              (success === 0 && hostConfig.successValue === false)
            ) {
              isSuccess = true
              console.log(`🔧 ${hostConfig.name} 通过布尔/数字转换匹配成功`)
            }

            if (!isSuccess) {
              const error = hostConfig.errorField
                ? getNestedValue(data, hostConfig.errorField)
                : '上传失败'
              console.log(`❌ ${hostConfig.name} 成功检查失败，错误信息:`, error)
              throw new Error(error)
            } else {
              console.log(`✅ ${hostConfig.name} 成功检查通过`)
            }
          }

          // 提取URL
          let url: string
          if (hostConfig.responseType === 'json') {
            url = getNestedValue(data, hostConfig.urlField)
            console.log(`🔍 ${hostConfig.name} 提取的URL:`, url)
            if (!url) {
              throw new Error('响应中未找到图片URL')
            }
          } else {
            url = responseText.trim()
          }

          console.log(`✅ ${hostConfig.name} 解析成功，URL:`, url)
          resolve({
            url: url,
            deleteUrl: undefined // 可以根据需要解析删除URL
          })
        } catch (error) {
          console.error(`❌ ${hostConfig.name} 解析失败:`, error)
          reject(new Error(`解析响应失败: ${error instanceof Error ? error.message : '未知错误'}`))
        }
      } else {
        reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`))
      }
    })

    // 请求错误
    xhr.addEventListener('error', () => {
      reject(new Error('网络请求失败'))
    })

    // 请求超时
    xhr.addEventListener('timeout', () => {
      reject(new Error('请求超时'))
    })

    // 配置请求
    xhr.open(hostConfig.method || 'POST', hostConfig.apiUrl)
    xhr.timeout = 30000

    // 设置请求头
    if (hostConfig.authType === 'header' && hostConfig.authKey) {
      const authValue = hostConfig.authPrefix
        ? `${hostConfig.authPrefix}${hostConfig.authKey}`
        : hostConfig.authKey
      xhr.setRequestHeader(hostConfig.authHeader!, authValue)
    }

    if (hostConfig.headers) {
      Object.entries(hostConfig.headers).forEach(([key, value]) => {
        xhr.setRequestHeader(key, String(value))
      })
    }

    // 发送请求
    xhr.send(formData)
  })
}

// 获取嵌套对象值的辅助函数
const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}





// 加载图床配置
const loadImageHosts = async () => {
  try {
    console.log('🔄 开始加载用户已启用的图床配置...')

    // 获取用户已启用的图床配置
    const enabledConfigs = await imageHostService.getEnabledConfigs()
    console.log('✅ 加载到的已启用图床配置:', enabledConfigs)

    imageLibraries.value = enabledConfigs

    if (enabledConfigs.length === 0) {
      console.warn('⚠️ 没有找到已启用的图床配置')
      showError('没有可用的图床配置，请先在设置中配置图床')
    } else {
      console.log(`✅ 成功加载 ${enabledConfigs.length} 个已启用的图床配置`)
    }
  } catch (err) {
    console.error('❌ 加载图床配置失败:', err)
    showError('加载图床配置失败')
  }
}

// 图床选择快速操作
const selectAllHosts = () => {
  batchSettings.value.selectedHosts = imageLibraries.value.map(host => host.id)
  showSuccess(`已选择所有 ${imageLibraries.value.length} 个图床`)
}

const clearHostSelection = () => {
  batchSettings.value.selectedHosts = []
  showSuccess('已清空图床选择，将使用随机上传')
}

const selectRandomHosts = () => {
  const availableHosts = imageLibraries.value.filter(host => host.enabled)
  if (availableHosts.length === 0) {
    showError('没有可用的图床')
    return
  }

  // 根据备份数量选择图床
  const count = Math.min(batchSettings.value.backupCount, availableHosts.length)
  const shuffled = [...availableHosts]

  // Fisher-Yates 洗牌算法
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }

  batchSettings.value.selectedHosts = shuffled.slice(0, count).map(host => host.id)

  const selectedNames = shuffled.slice(0, count).map(host => host.name).join(', ')
  showSuccess(`已随机选择 ${count} 个图床: ${selectedNames}`)
}

// 获取成功备份的图床数量
const getSuccessBackupCount = (file: UploadFile): number => {
  if (!file.uploadResults || !Array.isArray(file.uploadResults)) {
    return 0
  }
  return file.uploadResults.filter(result => result.success).length
}

// 获取最优图床配置
const getOptimalHostConfigs = (availableConfigs: ImageHostConfig[], count: number, strategy: string): ImageHostConfig[] => {
  if (availableConfigs.length === 0) return []

  let sortedConfigs: ImageHostConfig[]

  switch (strategy) {
    case 'priority':
      // 按优先级排序
      sortedConfigs = [...availableConfigs].sort((a, b) => (b.priority || 0) - (a.priority || 0))
      break

    case 'auto':
    default:
      // 智能选择：综合考虑优先级、启用状态等
      sortedConfigs = [...availableConfigs].sort((a, b) => {
        const scoreA = (a.priority || 0) + (a.enabled ? 10 : 0)
        const scoreB = (b.priority || 0) + (b.enabled ? 10 : 0)
        return scoreB - scoreA
      })

      // 添加一些随机性，避免总是选择相同的图床
      if (sortedConfigs.length > count) {
        const topConfigs = sortedConfigs.slice(0, Math.min(count * 2, sortedConfigs.length))
        // Fisher-Yates 洗牌前几个最优选项
        for (let i = topConfigs.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [topConfigs[i], topConfigs[j]] = [topConfigs[j], topConfigs[i]]
        }
        sortedConfigs = topConfigs
      }
      break
  }

  return sortedConfigs.slice(0, count)
}

// 根据设置自动选择图床
const autoSelectHosts = async () => {
  try {
    const availableHosts = imageLibraries.value.filter(host => host.enabled)
    if (availableHosts.length === 0) return

    // 默认选择前两个可用图床
    batchSettings.value.selectedHosts = availableHosts.slice(0, 2).map(host => host.id)
  } catch (err) {
    console.error('自动选择图床失败:', err)
  }
}

// 模拟标签数据
const availableTags = ref<Tag[]>([
  { id: 1, name: '截图', resource_count: 25 },
  { id: 2, name: '设计', resource_count: 18 },
  { id: 3, name: '头像', resource_count: 12 }
])

// 计算属性
const canUpload = computed(() => {
  if (activeTab.value === 'local') {
    return selectedFiles.value.length > 0 && selectedLibrary.value.length > 0
  } else {
    // URL上传不需要选择图床，只需要有解析的URL即可
    return parsedUrls.value.length > 0
  }
})

const currentImage = computed(() => {
  return selectedFiles.value[currentImageIndex.value] || null
})

// 转换 selectedFiles 为 ImageViewer 需要的格式
const selectedFilesForViewer = computed(() => {
  return selectedFiles.value.map((file) => ({
    id: file.id,
    src: file.preview,
    thumbnail: file.preview,
    name: file.name,
    width: 800, // 默认宽度
    height: 600, // 默认高度
    size: file.size,
    alt: file.name
  }))
})

// 文件处理方法
const selectFiles = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = Array.from(target.files || [])
  addFiles(files)
}



const addFiles = async (files: File[]) => {
  console.log('addFiles called with', files.length, 'files')

  for (const file of files) {
    if (!file.type.startsWith('image/')) {
      console.log(`跳过非图片文件: ${file.name}`)
      continue
    }
    if (file.size > 10 * 1024 * 1024) {
      showError(`${file.name} 超过 10MB 限制`)
      continue
    }

    console.log(`处理文件: ${file.name}`)

    const uploadFile: UploadFile = {
      id: Date.now() + Math.random().toString(),
      file,
      name: file.name,
      displayName: file.name.replace(/\.[^/.]+$/, ''), // 移除文件扩展名
      size: file.size,
      preview: await createPreview(file),
      tags: batchSettings.value.tags.map(String),
      description: batchSettings.value.description,
      selectedHosts: [...batchSettings.value.selectedHosts],
      uploadStatus: 'pending'
    }

    selectedFiles.value.push(uploadFile)
    console.log(`已添加文件: ${file.name}, 当前总数: ${selectedFiles.value.length}`)
  }
}

const createPreview = (file: File): Promise<string> => {
  return new Promise((resolve) => {
    const reader = new FileReader()
    reader.onload = (e) => resolve(e.target?.result as string)
    reader.readAsDataURL(file)
  })
}

const removeFile = (index: number) => {
  selectedFiles.value.splice(index, 1)
}

const clearFiles = () => {
  selectedFiles.value = []
  fileList.value = []
  processedFileCount.value = 0
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 处理图片预览加载错误
const handlePreviewImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  // 设置一个默认的占位图或错误图标
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFY0NEgyMFYyMFoiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTI4IDI4TDM2IDM2TDQwIDMyIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo='
}

// ImageViewer 事件处理方法
const handleEditSelectedImage = (image: ImageItem, index: number) => {
  // 根据 image.id 找到对应的 selectedFiles
  const fileIndex = selectedFiles.value.findIndex(file => file.id === image.id)
  if (fileIndex !== -1) {
    editingImage.value = selectedFiles.value[fileIndex]
    editingIndex.value = fileIndex
    editorVisible.value = true
  }
}

const handleDeleteSelectedImage = (image: ImageItem, index: number) => {
  // 根据 image.id 找到对应的 selectedFiles 索引
  const fileIndex = selectedFiles.value.findIndex(file => file.id === image.id)
  if (fileIndex !== -1) {
    removeFile(fileIndex)
  }
}

const handlePreviewSelectedImage = (image: ImageItem, index: number) => {
  console.log('预览图片:', image, index)
  // PhotoSwipe 预览已在 ImageViewer 组件中处理
}

// 图片编辑器事件处理
const handleEditorSave = (result: { canvas: HTMLCanvasElement; blob: Blob; dataUrl: string }) => {
  if (editingImage.value && editingIndex.value !== -1) {
    // 更新预览图片
    editingImage.value.preview = result.dataUrl

    // 创建新的 File 对象
    const newFile = new File([result.blob], editingImage.value.name, {
      type: 'image/jpeg'
    })

    // 更新文件对象
    editingImage.value.file = newFile
    editingImage.value.size = result.blob.size

    // 关闭编辑器
    editorVisible.value = false
    editingImage.value = null
    editingIndex.value = -1

    message.success('图片编辑完成')
  }
}

const handleEditorCancel = () => {
  editorVisible.value = false
  editingImage.value = null
  editingIndex.value = -1
}



// URL处理方法
const parseUrls = () => {
  const urls = urlInput.value
    .split('\n')
    .map(url => url.trim())
    .filter(url => url && isValidImageUrl(url))

  parsedUrls.value = [...new Set(urls)] // 去重

  // 初始化URL上传状态
  urlUploadStatuses.value = parsedUrls.value.map(url => ({
    url,
    status: 'pending' as const,
    progress: 0
  }))

  if (parsedUrls.value.length === 0) {
    showError('未找到有效的图片链接')
  } else {
    showSuccess(`找到 ${parsedUrls.value.length} 个有效链接`)
  }
}

const isValidImageUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url)

    // 检查协议是否为http或https
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return false
    }

    // 1. 检查URL路径中是否包含图片扩展名
    const pathname = urlObj.pathname.toLowerCase()
    const imageExtensions = /\.(jpg|jpeg|png|gif|webp|svg|bmp|tiff|tif|ico|avif|heic|heif)$/i
    if (imageExtensions.test(pathname)) {
      return true
    }

    // 2. 检查查询参数中是否包含图片扩展名
    const searchParams = urlObj.search.toLowerCase()
    if (imageExtensions.test(searchParams)) {
      return true
    }

    // 3. 检查常见的图床域名模式
    const hostname = urlObj.hostname.toLowerCase()
    const imageHostPatterns = [
      /imgur\.com/,
      /i\.imgur\.com/,
      /github\.com.*\.(jpg|jpeg|png|gif|webp|svg)/i,
      /githubusercontent\.com/,
      /cloudinary\.com/,
      /unsplash\.com/,
      /pexels\.com/,
      /pixabay\.com/,
      /picsum\.photos/,
      /via\.placeholder\.com/,
      /dummyimage\.com/,
      /placehold\.it/,
      /placeholder\.com/,
      /picui\.cn/,
      /sm\.ms/,
      /imgbb\.com/,
      /postimg\.cc/,
      /imgbox\.com/,
      /imageban\.ru/,
      /tinypic\.com/,
      /photobucket\.com/,
      /flickr\.com/,
      /500px\.com/,
      /deviantart\.com/,
      /artstation\.com/,
      /behance\.net/,
      /dribbble\.com/,
      /pinterest\.com/,
      /instagram\.com/,
      /facebook\.com/,
      /twitter\.com/,
      /weibo\.com/,
      /qpic\.cn/,
      /sinaimg\.cn/,
      /126\.net/,
      /163\.com/,
      /qq\.com/,
      /baidu\.com/,
      /douban\.com/,
      /zhihu\.com/,
      /jianshu\.com/,
      /csdn\.net/,
      /cnblogs\.com/,
      /oschina\.net/,
      /gitee\.com/,
      /coding\.net/,
      /aliyuncs\.com/,
      /qiniudn\.com/,
      /upyun\.com/,
      /baidubce\.com/,
      /myqcloud\.com/,
      /amazonaws\.com/,
      /googleusercontent\.com/,
      /dropbox\.com/,
      /onedrive\.com/,
      /icloud\.com/
    ]

    for (const pattern of imageHostPatterns) {
      if (pattern.test(hostname) || pattern.test(url)) {
        return true
      }
    }

    // 4. 检查URL中是否包含图片相关的关键词
    const imageKeywords = [
      'image', 'img', 'photo', 'pic', 'picture', 'avatar', 'thumb', 'thumbnail',
      'cover', 'banner', 'logo', 'icon', 'media', 'upload', 'file', 'asset',
      'static', 'cdn', 'storage', 'gallery', 'album'
    ]

    const fullUrl = url.toLowerCase()
    for (const keyword of imageKeywords) {
      if (fullUrl.includes(keyword)) {
        // 如果包含图片关键词，进一步检查是否可能是图片
        // 排除明显的非图片URL（如包含.html, .php, .asp等）
        const nonImageExtensions = /\.(html|htm|php|asp|aspx|jsp|js|css|xml|json|txt|pdf|doc|docx|xls|xlsx|zip|rar|exe|dmg)$/i
        if (!nonImageExtensions.test(pathname) && !nonImageExtensions.test(searchParams)) {
          return true
        }
      }
    }

    // 5. 检查是否是base64图片数据URL
    if (url.startsWith('data:image/')) {
      return true
    }

    // 6. 如果URL看起来像是API端点或动态生成的图片URL
    const dynamicImagePatterns = [
      /\/api\/.*image/i,
      /\/image\/\d+/i,
      /\/thumb\/\d+/i,
      /\/avatar\/\d+/i,
      /\/photo\/\d+/i,
      /\/pic\/\d+/i,
      /\?.*format=(jpg|jpeg|png|gif|webp)/i,
      /\?.*type=(jpg|jpeg|png|gif|webp)/i,
      /\?.*ext=(jpg|jpeg|png|gif|webp)/i
    ]

    for (const pattern of dynamicImagePatterns) {
      if (pattern.test(url)) {
        return true
      }
    }

    return false
  } catch {
    return false
  }
}

const removeParsedUrl = (index: number) => {
  parsedUrls.value.splice(index, 1)
  urlUploadStatuses.value.splice(index, 1)
}

const handleImageError = (index: number) => {
  // 处理图片加载错误
  console.warn(`图片加载失败: ${parsedUrls.value[index]}`)

  // 更新URL状态为错误
  if (urlUploadStatuses.value[index]) {
    urlUploadStatuses.value[index].error = '图片预览加载失败'
  }
}

// 异步验证URL是否为有效图片（可选功能）
const validateImageUrl = async (url: string): Promise<boolean> => {
  try {
    // 使用HEAD请求检查URL是否可访问且为图片
    const response = await fetch(url, {
      method: 'HEAD',
      mode: 'no-cors' // 避免CORS问题
    })

    // 如果是no-cors模式，response.ok可能不准确，但至少能检查网络连通性
    const contentType = response.headers.get('content-type')
    if (contentType && contentType.startsWith('image/')) {
      return true
    }

    // 如果无法获取content-type，仍然认为可能是有效图片
    return true
  } catch {
    // 网络错误或CORS限制，仍然允许用户尝试
    return true
  }
}

// 上传方法
const startUpload = async () => {
  if (!canUpload.value) return

  // 使用 setTimeout 避免递归更新
  await new Promise(resolve => setTimeout(resolve, 0))
  isUploading.value = true
  uploadedCount.value = 0

  try {
    if (activeTab.value === 'local') {
      totalCount.value = selectedFiles.value.length
      await uploadFiles()
    } else {
      totalCount.value = parsedUrls.value.length
      await uploadUrls()
    }
  } catch (err) {
    showError('上传过程中发生错误')
  } finally {
    await new Promise(resolve => setTimeout(resolve, 0))
    isUploading.value = false
  }
}

const uploadFiles = async () => {
  const results = []

  // 🚀 使用 p-limit 控制并发上传数量，避免同时上传过多文件
  const limit = pLimit(3) // 最多同时上传3个文件

  addUploadLog('batch', 'info', `开始批量上传 ${selectedFiles.value.length} 个文件，并发限制: 3`)

  const uploadPromises = selectedFiles.value.map((file, index) =>
    limit(async () => {
      // 使用 setTimeout 避免递归更新
      await new Promise(resolve => setTimeout(resolve, 0))
      file.uploadStatus = 'uploading'
      file.uploadProgress = 0

      try {
        addUploadLog(file.id, 'info', `开始上传文件: ${file.name} (${index + 1}/${selectedFiles.value.length})`)

        const result = await uploadSingleFile(file)

        // 🔍 详细输出上传结果信息
        console.log('=== 上传结果详情 ===')
        console.log('文件名:', file.name)
        console.log('上传结果:', result)
        console.log('结果类型:', typeof result)
        console.log('是否为数组:', Array.isArray(result))

        if (Array.isArray(result)) {
          console.log('结果数组长度:', result.length)
          result.forEach((item, index) => {
            console.log(`结果[${index}]:`, item)
            console.log(`  - URL: ${item?.url}`)
            console.log(`  - 成功状态: ${item?.success}`)
            console.log(`  - 图床名称: ${item?.hostName}`)
          })
        } else if (result && typeof result === 'object') {
          console.log('单个结果URL:', result.url)
          console.log('成功状态:', result.success)
          console.log('图床名称:', result.hostName)
        }
        console.log('==================')

        results.push(result)

        // 延迟更新状态避免递归更新
        setTimeout(async () => {
          file.uploadStatus = 'success'
          file.uploadProgress = 100
          file.uploadResults = result
          uploadedCount.value++

          // 更新整体进度
          overallProgress.value = Math.round((uploadedCount.value / totalCount.value) * 100)

          addUploadLog(file.id, 'success', `文件上传完成: ${file.name}`)

          // 保存到数据库
          try {
            // 确保 result 是数组格式
            const uploadResults = Array.isArray(result) ? result : [result]
            await saveToDatabase(file, uploadResults)
          } catch (err) {
            console.error('保存到数据库失败:', err)
            addUploadLog(file.id, 'error', `数据库保存失败: ${err instanceof Error ? err.message : '未知错误'}`)
          }
        }, 10)

        return { file, result, success: true }
      } catch (err) {
        setTimeout(() => {
          file.uploadStatus = 'error'

          // 更新整体进度
          overallProgress.value = Math.round((uploadedCount.value / totalCount.value) * 100)
        }, 10)

        const errorMessage = err instanceof Error ? err.message : '未知错误'
        console.error(`上传失败: ${file.name}`, err)
        addUploadLog(file.id, 'error', `文件上传失败: ${file.name} - ${errorMessage}`)

        return { file, result: null, success: false, error: errorMessage }
      }
    })
  )

  // 等待所有上传完成
  const uploadResults = await Promise.allSettled(uploadPromises)

  // 统计结果
  const successCount = uploadResults.filter(r => r.status === 'fulfilled' && r.value.success).length
  const failedCount = uploadResults.length - successCount

  addUploadLog('batch', 'success', `批量上传完成！成功: ${successCount}个，失败: ${failedCount}个`)

  emit('uploaded', results)
  showSuccess(`成功上传 ${successCount} 张图片！${failedCount > 0 ? `失败 ${failedCount} 张。` : ''}可以继续上传更多图片或手动重置。`)
}

const uploadSingleFile = async (uploadFile: UploadFile): Promise<any> => {
  // 🚀 实现多图床备份上传逻辑
  addUploadLog(uploadFile.id, 'info', `开始上传文件: ${uploadFile.file.name} (${(uploadFile.file.size / 1024 / 1024).toFixed(2)} MB)`)

  // 获取可用的图床列表
  let availableHosts = []
  const currentSelectedHosts = batchSettings.value.selectedHosts
  const backupCount = batchSettings.value.backupCount

  if (currentSelectedHosts.length > 0) {
    // 使用用户选择的图床
    availableHosts = imageLibraries.value.filter(host =>
      currentSelectedHosts.includes(host.id) && host.enabled
    )
    addUploadLog(uploadFile.id, 'info', `使用用户指定的图床: ${availableHosts.map(h => h.name).join(', ')}`)
  } else {
    // 使用所有可用图床
    availableHosts = imageLibraries.value.filter(host => host.enabled)
    addUploadLog(uploadFile.id, 'info', `使用所有可用图床: ${availableHosts.map(h => h.name).join(', ')}`)
  }

  if (availableHosts.length === 0) {
    const error = '没有可用的图床配置'
    addUploadLog(uploadFile.id, 'error', error)
    throw new Error(error)
  }

  // 随机排序图床列表
  for (let i = availableHosts.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [availableHosts[i], availableHosts[j]] = [availableHosts[j], availableHosts[i]]
  }

  // 选择要上传的图床数量（不超过可用图床数量）
  const hostsToUse = availableHosts.slice(0, Math.min(backupCount, availableHosts.length))

  const results = []
  const maxRetries = 3 // 每个图床最多重试3次
  let totalAttempts = 0

  // 创建进度回调函数
  const onProgress: UploadProgressCallback = (progress) => {
    uploadFile.uploadProgress = progress.percentage
    uploadFile.uploadLoaded = progress.loaded
    uploadFile.uploadTotal = progress.total

    if (progress.speed && progress.speed > 0) {
      const speedKB = progress.speed / 1024
      const speedMB = speedKB / 1024
      uploadFile.uploadSpeed = speedMB > 1
        ? `${speedMB.toFixed(1)} MB/s`
        : `${speedKB.toFixed(1)} KB/s`
    }

    if (progress.remainingTime && progress.remainingTime > 0) {
      const minutes = Math.floor(progress.remainingTime / 60)
      const seconds = Math.floor(progress.remainingTime % 60)
      uploadFile.uploadRemaining = minutes > 0
        ? `${minutes}分${seconds}秒`
        : `${seconds}秒`
    }
  }

  // 使用 p-limit 控制并发上传数量
  const hostLimit = pLimit(2) // 同时最多上传到2个图床

  // 并发上传到多个图床
  const uploadPromises = hostsToUse.map((host, index) =>
    hostLimit(async () => {
      let retryCount = 0

      while (retryCount < maxRetries) {
        totalAttempts++
        const attemptInfo = retryCount > 0 ? ` (第${retryCount + 1}次尝试)` : ''

        try {
          addUploadLog(uploadFile.id, 'info', `📤 开始上传到 ${host.name} (备份${index + 1}/${hostsToUse.length})${attemptInfo}`)

          // 调用单个图床的上传方法
          const uploadResult = await uploadToSingleHost(uploadFile.file, host, onProgress)

          addUploadLog(uploadFile.id, 'success', `✅ 上传成功到 ${host.name} (备份${index + 1}): ${uploadResult.url}`)

          return {
            hostId: host.id,
            hostName: host.name,
            url: uploadResult.url,
            deleteUrl: uploadResult.deleteUrl,
            success: true,
            attempts: retryCount + 1,
            backupIndex: index + 1
          }

        } catch (error) {
          retryCount++
          const errorMessage = error instanceof Error ? error.message : '未知错误'

          if (retryCount < maxRetries) {
            addUploadLog(uploadFile.id, 'warning', `⚠️ 上传到 ${host.name} 失败 (第${retryCount}次): ${errorMessage}，2秒后重试...`)
            // 等待2秒后重试
            await new Promise(resolve => setTimeout(resolve, 2000))
          } else {
            addUploadLog(uploadFile.id, 'error', `❌ 上传到 ${host.name} 失败，已达最大重试次数: ${errorMessage}`)

            return {
              hostId: host.id,
              hostName: host.name,
              error: errorMessage,
              success: false,
              attempts: retryCount,
              backupIndex: index + 1
            }
          }
        }
      }
    })
  )

  // 等待所有上传完成
  const uploadResults = await Promise.allSettled(uploadPromises)

  const successResults = []
  const failedResults = []

  uploadResults.forEach((result) => {
    if (result.status === 'fulfilled' && result.value) {
      if (result.value.success) {
        successResults.push(result.value)
      } else {
        failedResults.push(result.value)
      }
    } else {
      failedResults.push({
        success: false,
        error: result.reason?.message || '上传失败'
      })
    }
  })

  // 检查是否有成功的上传
  if (successResults.length === 0) {
    const errorMessages = failedResults.map(e => e.error || '未知错误').join('; ')
    throw new Error(`所有图床上传失败: ${errorMessages}`)
  }

  const successRate = Math.round((successResults.length / hostsToUse.length) * 100)
  addUploadLog(uploadFile.id, 'success', `🎉 文件备份完成！成功: ${successResults.length}/${hostsToUse.length} 个图床 (${successRate}%)`)

  // 返回所有成功的结果
  return successResults
}



const uploadUrls = async () => {
  const results = []

  for (let i = 0; i < parsedUrls.value.length; i++) {
    const url = parsedUrls.value[i]
    const urlStatus = urlUploadStatuses.value[i]

    // 使用 setTimeout 避免递归更新
    await new Promise(resolve => setTimeout(resolve, 0))

    // 更新状态为上传中
    setTimeout(() => {
      urlStatus.status = 'uploading'
      urlStatus.progress = 50
    }, 10)

    try {
      const result = await saveUrlToDatabase(url)
      results.push(result)

      setTimeout(() => {
        urlStatus.status = 'success'
        urlStatus.progress = 100
        urlStatus.result = result
        uploadedCount.value++
        overallProgress.value = Math.round((uploadedCount.value / totalCount.value) * 100)
      }, 10)
    } catch (err) {
      console.error(`保存失败: ${url}`, err)
      setTimeout(() => {
        urlStatus.status = 'error'
        urlStatus.error = err instanceof Error ? err.message : '保存失败'
      }, 10)
    }
  }

  emit('uploaded', results)
  showSuccess(`成功保存 ${results.length} 张图片！可以继续添加更多图片或手动重置。`)
}

// 获取图片文件信息（大小和尺寸）
const getImageInfo = async (url: string): Promise<{ size: number; width: number; height: number }> => {
  return new Promise((resolve) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'

    img.onload = async () => {
      try {
        // 获取图片尺寸
        const width = img.naturalWidth
        const height = img.naturalHeight

        // 尝试获取文件大小
        let size = 0
        try {
          const response = await fetch(url, { method: 'HEAD', mode: 'no-cors' })
          const contentLength = response.headers.get('content-length')
          if (contentLength) {
            size = parseInt(contentLength, 10)
          }
        } catch (error) {
          // 如果HEAD请求失败，尝试通过canvas估算大小
          try {
            const canvas = document.createElement('canvas')
            const ctx = canvas.getContext('2d')
            canvas.width = width
            canvas.height = height

            if (ctx) {
              ctx.drawImage(img, 0, 0)
              const imageData = ctx.getImageData(0, 0, width, height)
              // 估算压缩后的大小（粗略估算）
              size = Math.floor(imageData.data.length * 0.3) // 假设30%的压缩率
            }
          } catch (canvasError) {
            console.warn('无法通过canvas估算图片大小:', canvasError)
            // 根据尺寸粗略估算（假设每像素3字节，压缩率30%）
            size = Math.floor(width * height * 3 * 0.3)
          }
        }

        resolve({ size, width, height })
      } catch (error) {
        console.warn('获取图片信息失败:', error)
        resolve({ size: 0, width: img.naturalWidth || 0, height: img.naturalHeight || 0 })
      }
    }

    img.onerror = () => {
      console.warn('图片加载失败，无法获取信息:', url)
      resolve({ size: 0, width: 0, height: 0 })
    }

    img.src = url
  })
}

// 保存URL到数据库
const saveUrlToDatabase = async (url: string): Promise<any> => {
  try {
    const filename = extractFilename(url)
    const displayName = batchSettings.value.namePrefix + filename

    // 获取图片信息
    const imageInfo = await getImageInfo(url)

    // 创建图片记录
    const imageRecord = {
      name: displayName,
      originalName: filename,
      size: imageInfo.size,
      type: getImageTypeFromUrl(url),
      tags: [...batchSettings.value.tags],
      description: batchSettings.value.description || '',
      uploadTime: new Date(),
      width: imageInfo.width,
      height: imageInfo.height,
      urls: [{
        hostId: 'external',
        hostName: '外部链接',
        url: url,
        deleteUrl: undefined,
        uploadTime: new Date(),
        status: 'active' as const
      }],
      thumbnail: url // 使用原URL作为缩略图
    }

    // 真正保存到数据库
    const imageId = await imageDataService.saveImage(imageRecord)
    console.log('URL图片信息已保存到数据库:', displayName, `ID: ${imageId}, 大小: ${imageInfo.size} bytes, 尺寸: ${imageInfo.width}x${imageInfo.height}`)

    return {
      id: imageId.toString(),
      url: url,
      name: displayName,
      success: true
    }
  } catch (error) {
    console.error('保存URL到数据库失败:', error)
    throw error
  }
}

const extractFilename = (url: string): string => {
  try {
    const pathname = new URL(url).pathname
    return pathname.split('/').pop() || 'image'
  } catch {
    return 'image'
  }
}

// 从URL获取图片类型
const getImageTypeFromUrl = (url: string): string => {
  try {
    // 检查是否是base64数据URL
    if (url.startsWith('data:image/')) {
      const match = url.match(/^data:image\/([^;]+)/)
      if (match) {
        return `image/${match[1]}`
      }
    }

    const urlObj = new URL(url)
    const pathname = urlObj.pathname.toLowerCase()
    const searchParams = urlObj.search.toLowerCase()
    const fullUrl = url.toLowerCase()

    // 扩展的图片类型映射
    const typeMap: Record<string, string> = {
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'webp': 'image/webp',
      'svg': 'image/svg+xml',
      'bmp': 'image/bmp',
      'tiff': 'image/tiff',
      'tif': 'image/tiff',
      'ico': 'image/x-icon',
      'avif': 'image/avif',
      'heic': 'image/heic',
      'heif': 'image/heif'
    }

    // 1. 从路径扩展名检测
    for (const [ext, mimeType] of Object.entries(typeMap)) {
      if (pathname.endsWith(`.${ext}`)) {
        return mimeType
      }
    }

    // 2. 从查询参数检测
    for (const [ext, mimeType] of Object.entries(typeMap)) {
      if (searchParams.includes(`.${ext}`) || searchParams.includes(`format=${ext}`) || searchParams.includes(`type=${ext}`)) {
        return mimeType
      }
    }

    // 3. 从完整URL检测（适用于某些特殊格式的URL）
    for (const [ext, mimeType] of Object.entries(typeMap)) {
      if (fullUrl.includes(`.${ext}`)) {
        return mimeType
      }
    }

    // 4. 根据域名特征推断类型
    const hostname = urlObj.hostname.toLowerCase()

    // SVG图标服务
    if (hostname.includes('icon') || pathname.includes('icon') || searchParams.includes('svg')) {
      return 'image/svg+xml'
    }

    // WebP优化服务
    if (hostname.includes('webp') || searchParams.includes('webp') || searchParams.includes('format=webp')) {
      return 'image/webp'
    }

    // PNG透明图片服务
    if (searchParams.includes('png') || searchParams.includes('transparent')) {
      return 'image/png'
    }

    // 默认返回JPEG（最常见的格式）
    return 'image/jpeg'
  } catch {
    return 'image/jpeg'
  }
}

// 监听备份数量变化，显示提示
watch(() => batchSettings.value.backupCount, (newCount) => {
  console.log(`备份数量已更改为: ${newCount}`)
})

// 监听 visible 变化，重置状态
watch(visible, (newVisible) => {
  if (newVisible) {
    // 模态框打开时，清空之前的状态
    console.log('模态框打开，重置状态')
    selectedFiles.value = []
    fileList.value = []
    processedFileCount.value = 0 // 重置已处理文件计数
    parsedUrls.value = []
    activeTab.value = 'local'
    currentImageIndex.value = 0
    isUploading.value = false
    uploadedCount.value = 0
    totalCount.value = 0

    // 初始化完成
    console.log('图片上传模态框已打开，备份数量:', batchSettings.value.backupCount)
  }
})

// 手动重置上传状态
const handleReset = () => {
  // 清空本地文件列表
  selectedFiles.value = []

  // 清空URL相关数据
  urlInput.value = ''
  parsedUrls.value = []
  urlUploadStatuses.value = []

  // 重置上传状态
  isUploading.value = false
  uploadedCount.value = 0
  overallProgress.value = 0

  // 重置文件输入
  if (fileInput.value) {
    fileInput.value.value = ''
  }

  // 重置 Ant Design Upload 的文件列表
  fileList.value = []
  processedFileCount.value = 0

  showSuccess('已重置上传状态')
}

// 事件处理
const handleClose = () => {
  if (isUploading.value) {
    if (confirm('上传正在进行中，确定要关闭吗？')) {
      visible.value = false
      emit('close')
    }
  } else {
    visible.value = false
    emit('close')
  }
}

const handleBackdropClick = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    handleClose()
  }
}

// 拖拽事件
const handleDragEnter = () => {
  isDragOver.value = true
}

const handleDragLeave = (event: DragEvent) => {
  if (!dropZone.value?.contains(event.relatedTarget as Node)) {
    isDragOver.value = false
  }
}

// 生命周期
onMounted(async () => {
  visible.value = true

  // 加载图床配置
  await loadImageHosts()

  // 设置默认图床选择
  if (imageLibraries.value.length > 0) {
    // 选择优先级最高的已启用图床作为默认选择
    const defaultLib = imageLibraries.value.find(lib => lib.enabled) || imageLibraries.value[0]
    selectedLibrary.value = [defaultLib.id]

    // 默认不选择任何图床，让用户手动选择或使用随机上传
    batchSettings.value.selectedHosts = []
  }

  // 添加拖拽事件监听
  if (dropZone.value) {
    dropZone.value.addEventListener('dragenter', handleDragEnter)
    dropZone.value.addEventListener('dragleave', handleDragLeave)
  }
})

// 图片导航方法
const previousImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--
  }
}

const nextImage = () => {
  if (currentImageIndex.value < selectedFiles.value.length - 1) {
    currentImageIndex.value++
  }
}

// 批量配置应用
const applyBatchSettings = () => {
  selectedFiles.value.forEach(file => {
    if (batchSettings.value.namePrefix) {
      file.displayName = batchSettings.value.namePrefix + file.displayName
    }
    file.tags = [...batchSettings.value.tags]
    file.description = batchSettings.value.description
    file.selectedHosts = [...batchSettings.value.selectedHosts]
  })

  showSuccess(`已应用到 ${selectedFiles.value.length} 张图片`)
}

// 生成标签颜色
const generateTagColor = (tagName: string): string => {
  const colors = [
    '#EF4444', '#F97316', '#F59E0B', '#EAB308', '#84CC16',
    '#22C55E', '#10B981', '#14B8A6', '#06B6D4', '#0EA5E9',
    '#3B82F6', '#6366F1', '#8B5CF6', '#A855F7', '#D946EF',
    '#EC4899', '#F43F5E'
  ]

  let hash = 0
  for (let i = 0; i < tagName.length; i++) {
    hash = tagName.charCodeAt(i) + ((hash << 5) - hash)
  }

  return colors[Math.abs(hash) % colors.length]
}

// 创建新标签
const handleCreateTag = async (tagName: string) => {
  try {
    // 标签创建现在由imageDataService在保存图片时自动处理
    // 这里只需要添加到可用标签列表用于UI显示
    const newTag = {
      id: Date.now(), // 临时ID
      name: tagName,
      resource_count: 0
    }

    // 添加到可用标签列表
    availableTags.value.push(newTag)

    showSuccess(`标签 "${tagName}" 已创建`)
  } catch (err) {
    console.error('创建标签失败:', err)
    showError('创建标签失败，请稍后重试')
  }
}

// 复制链接
const copyUrl = async (url: string) => {
  try {
    await navigator.clipboard.writeText(url)
    showSuccess('链接已复制')
  } catch (err) {
    showError('无法访问剪贴板')
  }
}

// 重试上传
const retryUpload = async (file: UploadFile) => {
  file.uploadStatus = 'pending'
  file.uploadProgress = 0
  file.uploadResults = undefined

  try {
    const result = await uploadSingleFile(file)
    file.uploadStatus = 'success'
    file.uploadProgress = 100
    file.uploadResults = result
  } catch (err) {
    file.uploadStatus = 'error'
    console.error(`重试上传失败: ${file.name}`, err)
  }
}

// 保存到数据库
const saveToDatabase = async (file: UploadFile, uploadResults: any[]) => {
  try {
    // 🔍 详细输出保存到数据库的信息
    console.log('=== 保存到数据库 ===')
    console.log('文件名:', file.name)
    console.log('原始上传结果:', uploadResults)

    // 确保uploadResults是一个有效的数组
    const results = Array.isArray(uploadResults) ? uploadResults : []
    console.log('处理后的结果数组:', results)

    // 只保存成功的上传结果
    const successfulResults = results.filter(result => result && result.success && result.url)
    console.log('成功的上传结果:', successfulResults)

    successfulResults.forEach((result, index) => {
      console.log(`成功结果[${index}]:`)
      console.log(`  - URL: ${result.url}`)
      console.log(`  - 图床ID: ${result.hostId}`)
      console.log(`  - 图床名称: ${result.hostName}`)
      console.log(`  - 删除URL: ${result.deleteUrl}`)
    })

    if (successfulResults.length === 0) {
      console.warn('没有成功的上传结果，跳过数据库保存')
      return
    }

    const imageRecord = {
      name: file.displayName,
      originalName: file.name,
      size: file.size,
      type: file.file.type,
      tags: [...file.tags], // 确保是新数组
      description: file.description || '',
      uploadTime: new Date(),
      width: 0, // 可以从图片文件中获取实际尺寸
      height: 0,
      urls: successfulResults.map(result => ({
        hostId: String(result.hostId || ''),
        hostName: String(result.hostName || ''),
        url: String(result.url),
        deleteUrl: result.deleteUrl ? String(result.deleteUrl) : undefined,
        uploadTime: new Date(),
        status: 'active' as const
      })),
      thumbnail: file.preview
    }

    // 真正保存到数据库
    const imageId = await imageDataService.saveImage(imageRecord)
    console.log('图片信息已保存到数据库:', imageRecord.name, 'ID:', imageId)

    // 🔍 输出最终保存的URL信息
    console.log('=== 最终保存的图片信息 ===')
    console.log('图片ID:', imageId)
    console.log('图片名称:', imageRecord.name)
    console.log('原始文件名:', imageRecord.originalName)
    console.log('文件大小:', imageRecord.size)
    console.log('文件类型:', imageRecord.type)
    console.log('标签:', imageRecord.tags)
    console.log('描述:', imageRecord.description)
    console.log('缩略图:', imageRecord.thumbnail)
    console.log('URL列表:')
    imageRecord.urls.forEach((url, index) => {
      console.log(`  URL[${index}]:`)
      console.log(`    - 图床ID: ${url.hostId}`)
      console.log(`    - 图床名称: ${url.hostName}`)
      console.log(`    - 图片URL: ${url.url}`)
      console.log(`    - 删除URL: ${url.deleteUrl}`)
      console.log(`    - 状态: ${url.status}`)
      console.log(`    - 上传时间: ${url.uploadTime}`)
    })
    console.log('========================')
  } catch (error) {
    console.error('保存到数据库失败:', error)
  }
}

onUnmounted(() => {
  // 清理预览URL
  selectedFiles.value.forEach(file => {
    if (file.preview.startsWith('blob:')) {
      URL.revokeObjectURL(file.preview)
    }
  })

  // 移除拖拽事件监听
  if (dropZone.value) {
    dropZone.value.removeEventListener('dragenter', handleDragEnter)
    dropZone.value.removeEventListener('dragleave', handleDragLeave)
  }
})
</script>
