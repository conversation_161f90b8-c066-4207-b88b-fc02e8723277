import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import ImageHostManagement from '../ImageHostManagement.vue'

// Mock services
vi.mock('@/services/imageHostService', () => ({
  imageHostService: {
    getConfigs: vi.fn().mockResolvedValue([]),
    testConfig: vi.fn().mockResolvedValue({ success: true }),
    updateConfig: vi.fn().mockResolvedValue({}),
    deleteConfig: vi.fn().mockResolvedValue({})
  }
}))

// Mock Ant Design components
const mockAntdComponents = {
  'a-card': { template: '<div class="ant-card"><slot /></div>' },
  'a-button': { template: '<button class="ant-btn"><slot /></button>' },
  'a-switch': { template: '<input type="checkbox" class="ant-switch" />' },
  'a-tag': { template: '<span class="ant-tag"><slot /></span>' },
  'a-empty': { template: '<div class="ant-empty"><slot /></div>' },
  'a-alert': { template: '<div class="ant-alert"><slot /></div>' },
  'a-divider': { template: '<hr class="ant-divider" />' },
  'a-modal': { template: '<div class="ant-modal"><slot /></div>' }
}

describe('ImageHostManagement', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(ImageHostManagement, {
      global: {
        components: mockAntdComponents,
        stubs: {
          ImageHostConfigModal: true,
          ImageHostTutorial: true,
          ImageHostTutorialModal: true,
          ImageTagManagementModal: true,
          ImageHostAnalytics: true
        }
      }
    })
  })

  it('应该正确渲染主容器结构', () => {
    expect(wrapper.find('.image-host-management-container').exists()).toBe(true)
    expect(wrapper.find('.function-card').exists()).toBe(true)
  })

  it('应该显示页面标题和描述', () => {
    expect(wrapper.find('.page-title').text()).toBe('图床管理')
    expect(wrapper.find('.page-description').text()).toContain('配置多个图床服务')
  })

  it('应该包含主要操作按钮', () => {
    const addButton = wrapper.find('#add-host-btn')
    expect(addButton.exists()).toBe(true)
  })

  it('应该包含辅助功能按钮', () => {
    const secondaryControls = wrapper.find('.secondary-controls')
    expect(secondaryControls.exists()).toBe(true)
  })

  it('应该在空状态时显示正确的提示', async () => {
    await nextTick()
    expect(wrapper.find('.empty-state').exists()).toBe(true)
  })

  it('应该支持响应式布局', () => {
    expect(wrapper.find('.function-row').exists()).toBe(true)
    
    // 检查CSS类是否正确应用
    const container = wrapper.find('.image-host-management-container')
    expect(container.element).toHaveClass('image-host-management-container')
  })

  it('应该正确处理配置操作', async () => {
    const component = wrapper.vm
    
    // 测试配置编辑
    const mockConfig = { id: '1', name: 'Test Config', enabled: true }
    component.editConfig(mockConfig)
    expect(component.editingConfig).toBe(mockConfig)
    expect(component.showConfigModal).toBe(true)
  })

  it('应该支持配置测试功能', async () => {
    const component = wrapper.vm
    const mockConfig = { id: '1', name: 'Test Config' }
    
    // 模拟测试配置
    await component.testConfig(mockConfig)
    expect(component.testing[mockConfig.id]).toBeFalsy()
  })

  it('应该正确处理配置保存', async () => {
    const component = wrapper.vm
    const mockConfig = { id: '1', name: 'New Config' }
    
    await component.handleSaveConfig(mockConfig)
    expect(component.showConfigModal).toBe(false)
    expect(component.editingConfig).toBe(null)
  })
})
