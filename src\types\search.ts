export type SearchType = 'global' | 'knowledge' | 'web'

export interface SearchResult {
  id: string
  type: 'resource' | 'category' | 'tag' | 'function'
  title: string
  description?: string
  category?: string
  url?: string
  action?: () => void
}

export interface SearchEngine {
  id: string
  name: string
  url: string
  icon: string
}

export interface SearchSettings {
  defaultEngine: string
  engines: SearchEngine[]
}
