# KnowlEdge AI对话功能使用说明

## 功能概述

KnowlEdge现已集成AI对话功能，支持与多种AI服务提供商进行智能对话，包括OpenAI、Claude和自定义AI服务。

## 快速开始

### 1. 配置AI服务

首次使用需要配置AI服务：

1. 点击顶部导航栏的AI对话按钮（聊天气泡图标）
2. 如果未配置，系统会自动打开设置页面
3. 或者点击设置按钮手动打开AI配置

### 2. 填写配置信息

**基础配置（必需）：**
- **服务提供商**：选择OpenAI、Claude或自定义服务
- **API Key**：输入您的API密钥
- **API端点**：API服务的基础URL地址

**高级配置（可选）：**
- **模型名称**：指定要使用的AI模型
- **温度参数**：控制回复的创造性（0-2）
- **最大Token数**：限制单次回复的长度
- **超时时间**：API请求超时时间

### 3. 测试连接

配置完成后，点击"测试连接"按钮验证配置是否正确。

### 4. 启用服务

确认配置无误后，开启"启用AI服务"开关。

## 使用AI对话

### 访问方式

1. **导航栏按钮**：点击顶部导航栏的AI对话按钮
2. **独立页面**：访问 `/ai-chat` 路由获得更大的对话空间

### 对话功能

- **发送消息**：在输入框输入问题，按Enter发送
- **换行**：使用Shift+Enter进行换行
- **查看历史**：左侧面板显示所有对话历史
- **搜索对话**：使用搜索框快速找到历史对话
- **管理会话**：重命名、删除不需要的对话

### 状态指示

导航栏AI按钮旁的状态指示器：
- 🟢 绿色：服务正常，可以使用
- 🟡 黄色：已配置但未启用
- 🔴 红色：未配置或配置错误

## 支持的AI服务提供商

### OpenAI
- **API端点**：`https://api.openai.com/v1`
- **推荐模型**：`gpt-3.5-turbo`、`gpt-4`
- **获取API Key**：访问 [OpenAI官网](https://platform.openai.com/api-keys)

### Claude (Anthropic)
- **API端点**：`https://api.anthropic.com/v1`
- **推荐模型**：`claude-3-sonnet-20240229`
- **获取API Key**：访问 [Anthropic官网](https://console.anthropic.com/)

### 自定义服务
- 支持兼容OpenAI API格式的本地或第三方服务
- 如Ollama、LocalAI等本地部署方案

## 数据存储

- **配置信息**：安全存储在浏览器本地存储中
- **对话历史**：保存在本地，支持搜索和管理
- **隐私保护**：所有数据仅存储在本地，不会上传到服务器

## 功能特性

### ✅ 已实现
- 多AI服务提供商支持
- 完整的配置管理界面
- 实时对话功能
- 对话历史管理
- Markdown格式渲染
- 响应式设计
- 深色模式支持

### 🚧 开发中
- 流式响应优化
- 对话导出功能
- 更多AI服务支持
- 对话统计分析

## 常见问题

### Q: API Key安全吗？
A: API Key仅存储在您的浏览器本地存储中，不会发送到任何第三方服务器。

### Q: 支持哪些AI模型？
A: 支持所有兼容OpenAI API格式的模型，具体可用模型取决于您选择的服务提供商。

### Q: 对话历史会丢失吗？
A: 对话历史保存在浏览器本地存储中，除非手动清除或浏览器数据被清理，否则不会丢失。

### Q: 如何使用本地AI服务？
A: 选择"自定义服务"，然后输入本地AI服务的API端点地址，如 `http://localhost:11434/v1`。

### Q: 遇到连接错误怎么办？
A: 请检查：
1. API Key是否正确
2. API端点地址是否正确
3. 网络连接是否正常
4. 服务提供商是否有使用限制

## 技术支持

如遇到问题或需要帮助，请：
1. 检查浏览器控制台错误信息
2. 确认API配置信息正确
3. 尝试重新配置AI服务
4. 查看开发日志了解更多技术细节

---

**注意**：使用AI服务可能产生费用，请根据服务提供商的计费规则合理使用。
