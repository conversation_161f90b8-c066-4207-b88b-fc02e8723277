<template>
  <div class="divide-y divide-gray-200 dark:divide-gray-700">
    <div
      v-for="tag in tags"
      :key="tag.id"
      class="flex items-center justify-between px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
    >
      <div class="flex items-center space-x-3">
        <!-- 标签颜色 -->
        <div
          class="w-4 h-4 rounded-full border border-gray-300 dark:border-gray-600"
          :style="{ backgroundColor: tag.color }"
        ></div>

        <!-- 标签信息 -->
        <div>
          <div class="font-medium text-gray-900 dark:text-gray-100">
            {{ tag.name }}
          </div>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            {{ tag.resource_count }} 个资源 • {{ formatDate(tag.created_at) }}
          </div>
        </div>

        <!-- 使用状态 -->
        <div v-if="tag.resource_count === 0" class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded">
          未使用
        </div>
        <div v-else-if="tag.resource_count >= 10" class="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 text-xs rounded">
          热门
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex items-center space-x-2">
        <BaseButton variant="ghost" size="sm" @click="handleEdit(tag)">
          <div class="i-heroicons-pencil w-4 h-4"></div>
        </BaseButton>
        
        <BaseDropdown placement="bottom-end">
          <template #trigger>
            <BaseButton variant="ghost" size="sm">
              <div class="i-heroicons-ellipsis-horizontal w-4 h-4"></div>
            </BaseButton>
          </template>
          
          <div class="w-40">
            <DropdownItem text="编辑" icon="i-heroicons-pencil" @click="handleEdit(tag)" />
            <DropdownItem text="更改颜色" icon="i-heroicons-swatch" @click="handleChangeColor(tag)" />
            <DropdownItem text="合并到..." icon="i-heroicons-arrows-right-left" @click="handleMerge(tag)" />
            <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
            <DropdownItem 
              text="删除" 
              icon="i-heroicons-trash" 
              class="text-red-600 dark:text-red-400"
              @click="handleDelete(tag)" 
            />
          </div>
        </BaseDropdown>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="tags.length === 0" class="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
      <div class="i-heroicons-tag w-12 h-12 mx-auto mb-4 opacity-50"></div>
      <p>暂无标签</p>
    </div>
  </div>

  <!-- 更改颜色对话框 -->
  <BaseModal v-model="showColorDialog" title="更改标签颜色" size="sm">
    <div class="space-y-4">
      <p class="text-gray-600 dark:text-gray-400">
        为标签 "{{ selectedTag?.name }}" 选择新颜色：
      </p>
      
      <div class="flex items-center space-x-4">
        <input
          v-model="newColor"
          type="color"
          class="w-16 h-16 rounded border border-gray-300 dark:border-gray-600 cursor-pointer"
        />
        <div class="flex-1">
          <BaseInput
            v-model="newColor"
            label="颜色值"
            placeholder="#000000"
          />
        </div>
      </div>

      <!-- 预设颜色 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          预设颜色
        </label>
        <div class="grid grid-cols-8 gap-2">
          <button
            v-for="color in presetColors"
            :key="color"
            class="w-8 h-8 rounded border border-gray-300 dark:border-gray-600 hover:scale-110 transition-transform"
            :style="{ backgroundColor: color }"
            @click="newColor = color"
          ></button>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end space-x-3">
        <BaseButton variant="outline" @click="showColorDialog = false">
          取消
        </BaseButton>
        <BaseButton @click="handleSaveColor">
          保存
        </BaseButton>
      </div>
    </template>
  </BaseModal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import BaseDropdown from '@/components/ui/BaseDropdown.vue'
import BaseModal from '@/components/ui/BaseModal.vue'
import BaseInput from '@/components/ui/BaseInput.vue'
import DropdownItem from '@/components/ui/DropdownItem.vue'
import { tagService } from '@/services/tagService'
import type { Tag } from '@/types'

interface Props {
  tags: Tag[]
}

interface Emits {
  (e: 'edit', tag: Tag): void
  (e: 'delete', tag: Tag): void
  (e: 'merge', tag: Tag): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 颜色对话框状态
const showColorDialog = ref(false)
const selectedTag = ref<Tag | null>(null)
const newColor = ref('#3B82F6')

// 预设颜色
const presetColors = [
  '#EF4444', '#F97316', '#F59E0B', '#EAB308', '#84CC16',
  '#22C55E', '#10B981', '#14B8A6', '#06B6D4', '#0EA5E9',
  '#3B82F6', '#6366F1', '#8B5CF6', '#A855F7', '#D946EF',
  '#EC4899', '#F43F5E', '#6B7280'
]

// 格式化日期
const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

// 编辑标签
const handleEdit = (tag: Tag) => {
  emit('edit', tag)
}

// 删除标签
const handleDelete = (tag: Tag) => {
  emit('delete', tag)
}

// 合并标签
const handleMerge = (tag: Tag) => {
  emit('merge', tag)
}

// 更改颜色
const handleChangeColor = (tag: Tag) => {
  selectedTag.value = tag
  newColor.value = tag.color
  showColorDialog.value = true
}

// 保存颜色
const handleSaveColor = async () => {
  if (selectedTag.value) {
    try {
      await tagService.updateTag(selectedTag.value.id!, { color: newColor.value })
      selectedTag.value.color = newColor.value
      showColorDialog.value = false
    } catch (error) {
      console.error('更新标签颜色失败:', error)
      alert('更新标签颜色失败，请重试')
    }
  }
}
</script>
