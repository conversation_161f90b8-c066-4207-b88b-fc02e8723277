<template>
  <div class="space-y-6">
    <!-- 标签统计 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
        <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ tagStats.total }}</div>
        <div class="text-sm text-blue-600 dark:text-blue-400">标签总数</div>
      </div>
      <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4">
        <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ tagStats.used }}</div>
        <div class="text-sm text-green-600 dark:text-green-400">已使用</div>
      </div>
      <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
        <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ tagStats.unused }}</div>
        <div class="text-sm text-yellow-600 dark:text-yellow-400">未使用</div>
      </div>
      <div class="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-700 rounded-lg p-4">
        <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ tagStats.mostUsed?.resource_count || 0 }}</div>
        <div class="text-sm text-purple-600 dark:text-purple-400">最热标签</div>
      </div>
    </div>

    <!-- 添加新标签 -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">添加新标签</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <BaseInput v-model="newTag.name" label="标签名称" placeholder="请输入标签名称" required />
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">标签颜色</label>
          <div class="flex items-center space-x-2">
            <input
              v-model="newTag.color"
              type="color"
              class="w-10 h-10 border border-gray-300 dark:border-gray-600 rounded cursor-pointer"
            />
            <BaseInput v-model="newTag.color" placeholder="#3B82F6" class="flex-1" />
          </div>
        </div>
        <div class="flex items-end">
          <BaseButton @click="handleAddTag" :disabled="!newTag.name.trim()" class="w-full">
            <div class="i-heroicons-plus mr-2"></div>
            添加标签
          </BaseButton>
        </div>
      </div>
    </div>

    <!-- 标签列表 -->
    <div>
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">标签列表</h3>
        <div class="flex items-center space-x-2">
          <BaseInput
            v-model="searchQuery"
            placeholder="搜索标签..."
            prefix-icon="i-heroicons-magnifying-glass"
            clearable
            class="w-64"
          />
          <BaseButton variant="outline" @click="handleCleanupUnused">
            <div class="i-heroicons-trash mr-2"></div>
            清理未使用
          </BaseButton>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
        <div class="max-h-96 overflow-y-auto">
          <TagManagementList
            :tags="filteredTags"
            @edit="handleEditTag"
            @delete="handleDeleteTag"
            @merge="handleMergeTag"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import BaseInput from '@/components/ui/BaseInput.vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import TagManagementList from './TagManagementList.vue'
import { tagService } from '@/services/tagService'
import type { Tag, TagStats } from '@/types'

// 状态
const loading = ref(false)
const tags = ref<Tag[]>([])
const searchQuery = ref('')

// 标签统计
const tagStats = ref<TagStats>({
  total: 0,
  used: 0,
  unused: 0,
  mostUsed: null
})

// 新标签表单
const newTag = ref({
  name: '',
  color: '#3B82F6'
})

// 过滤后的标签
const filteredTags = computed(() => {
  if (!searchQuery.value) return tags.value
  return tags.value.filter(tag => 
    tag.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// 加载标签数据
const loadTags = async () => {
  try {
    loading.value = true
    const [allTags, stats] = await Promise.all([
      tagService.getAllTags(),
      tagService.getTagStats()
    ])
    tags.value = allTags
    tagStats.value = stats
  } catch (error) {
    console.error('加载标签失败:', error)
  } finally {
    loading.value = false
  }
}

// 添加标签
const handleAddTag = async () => {
  try {
    await tagService.createTag({
      name: newTag.value.name,
      color: newTag.value.color
    })
    
    // 重置表单
    newTag.value = {
      name: '',
      color: '#3B82F6'
    }
    
    // 刷新数据
    await loadTags()
  } catch (error) {
    console.error('添加标签失败:', error)
    alert('添加标签失败：' + (error as Error).message)
  }
}

// 编辑标签
const handleEditTag = async (tag: Tag) => {
  const newName = prompt('请输入新的标签名称:', tag.name)
  if (newName && newName.trim() && newName !== tag.name) {
    try {
      await tagService.updateTag(tag.id!, { name: newName.trim() })
      await loadTags()
    } catch (error) {
      console.error('编辑标签失败:', error)
      alert('编辑标签失败，请重试')
    }
  }
}

// 删除标签
const handleDeleteTag = async (tag: Tag) => {
  if (confirm(`确定要删除标签"${tag.name}"吗？`)) {
    try {
      await tagService.deleteTag(tag.id!)
      await loadTags()
    } catch (error) {
      console.error('删除标签失败:', error)
      alert('删除标签失败，请重试')
    }
  }
}

// 合并标签
const handleMergeTag = async (sourceTag: Tag) => {
  const targetTagName = prompt(`将标签"${sourceTag.name}"合并到哪个标签？请输入目标标签名称:`)
  if (targetTagName && targetTagName.trim()) {
    try {
      await tagService.mergeTags(sourceTag.id!, targetTagName.trim())
      await loadTags()
    } catch (error) {
      console.error('合并标签失败:', error)
      alert('合并标签失败，请重试')
    }
  }
}

// 清理未使用的标签
const handleCleanupUnused = async () => {
  if (confirm('确定要清理所有未使用的标签吗？此操作不可撤销。')) {
    try {
      await tagService.cleanupUnusedTags()
      await loadTags()
    } catch (error) {
      console.error('清理标签失败:', error)
      alert('清理标签失败，请重试')
    }
  }
}

// 初始化
onMounted(() => {
  loadTags()
})
</script>
