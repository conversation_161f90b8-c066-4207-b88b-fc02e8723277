# 预览数据恢复问题调试指南

## 🔍 问题现状

用户反馈：编辑资源时点击预览后返回，所有输入的信息仍然丢失。

## 🛠️ 已实施的修复

### 1. **数据保存改进**
```typescript
const handlePreview = () => {
  const previewData = {
    ...form.value,
    selectedTags: selectedTags.value,
    selectedCategory: selectedCategory.value,
    isPreview: true,
    isEditing: isEditing.value,
    originalResourceId: String(route.query.id), // 确保字符串类型
    previewId: Date.now().toString()
  }
  
  console.log('保存预览数据:', previewData) // 调试日志
  sessionStorage.setItem('resource-preview-data', JSON.stringify(previewData))
}
```

### 2. **数据恢复改进**
```typescript
const restorePreviewData = () => {
  const previewData = sessionStorage.getItem('resource-preview-data')
  console.log('检查预览数据:', previewData ? '存在' : '不存在')
  
  if (!previewData) return false

  try {
    const parsedData = JSON.parse(previewData)
    console.log('解析的预览数据:', parsedData)
    console.log('当前路由ID:', route.query.id)
    console.log('预览数据中的原始ID:', parsedData.originalResourceId)
    console.log('是否为预览数据:', parsedData.isPreview)
    console.log('ID是否匹配:', String(parsedData.originalResourceId) === String(route.query.id))
    
    if (parsedData.isPreview && String(parsedData.originalResourceId) === String(route.query.id)) {
      form.value = { /* 恢复数据 */ }
      
      // 使用 nextTick 确保响应式更新
      nextTick(() => {
        console.log('nextTick 后的表单数据:', form.value)
      })
      
      sessionStorage.removeItem('resource-preview-data')
      return true
    }
  } catch (error) {
    console.error('恢复预览数据失败:', error)
  }
  
  return false
}
```

### 3. **初始化流程改进**
```typescript
onMounted(async () => {
  await Promise.all([loadCategories(), loadTags()])
  
  if (isEditing.value) {
    // 等待组件完全初始化
    await new Promise(resolve => setTimeout(resolve, 100))
    
    const restored = restorePreviewData()
    if (!restored) {
      await loadResource()
    }
  }
})
```

## 🔧 调试步骤

### 1. **检查浏览器控制台**
打开浏览器开发者工具，查看控制台输出：

1. 编辑资源页面，填写表单
2. 点击"预览效果"按钮
3. 查看控制台是否输出：`保存预览数据: {对象内容}`
4. 在预览页面，检查 sessionStorage：
   ```javascript
   console.log(sessionStorage.getItem('resource-preview-data'))
   ```
5. 返回编辑页面，查看控制台输出的恢复日志

### 2. **手动检查 sessionStorage**
在浏览器控制台中执行：
```javascript
// 检查是否有预览数据
console.log('预览数据:', sessionStorage.getItem('resource-preview-data'))

// 解析预览数据
try {
  const data = JSON.parse(sessionStorage.getItem('resource-preview-data'))
  console.log('解析后的数据:', data)
} catch (e) {
  console.log('解析失败:', e)
}
```

### 3. **检查路由参数**
```javascript
// 在编辑页面控制台执行
console.log('当前路由:', window.location.href)
console.log('路由参数:', new URLSearchParams(window.location.search).get('id'))
```

## 🚨 可能的问题原因

### 1. **路由参数不匹配**
- 预览时保存的 `originalResourceId` 与返回时的 `route.query.id` 不一致
- 解决方案：检查控制台日志中的 ID 比较

### 2. **sessionStorage 被清除**
- 浏览器设置或扩展程序清除了 sessionStorage
- 解决方案：检查浏览器设置，禁用相关扩展

### 3. **组件重新挂载**
- 路由切换导致组件完全重新创建
- 解决方案：使用 keep-alive 或改用 localStorage

### 4. **异步时序问题**
- 数据恢复在组件初始化之前执行
- 解决方案：已添加延迟和 nextTick

## 🔄 备用解决方案

如果当前方案仍然无效，可以尝试以下备用方案：

### 方案1：使用 localStorage
```typescript
// 替换 sessionStorage 为 localStorage
localStorage.setItem('resource-preview-data', JSON.stringify(previewData))
const previewData = localStorage.getItem('resource-preview-data')
```

### 方案2：使用 Vuex/Pinia 状态管理
```typescript
// 将预览数据保存到全局状态
const store = useStore()
store.commit('savePreviewData', previewData)
```

### 方案3：URL 参数传递
```typescript
// 将关键数据编码到 URL 参数中
router.push({
  name: 'ResourcePreview',
  params: { id: previewData.previewId },
  query: { 
    title: encodeURIComponent(form.value.title),
    // ... 其他关键字段
  }
})
```

### 方案4：使用 provide/inject
```typescript
// 在父组件中提供数据
provide('previewData', previewData)

// 在子组件中注入数据
const previewData = inject('previewData')
```

## 📋 测试清单

请按以下步骤测试修复效果：

- [ ] 1. 打开编辑资源页面
- [ ] 2. 填写表单内容（标题、描述、分类、标签等）
- [ ] 3. 打开浏览器开发者工具，查看控制台
- [ ] 4. 点击"预览效果"按钮
- [ ] 5. 检查控制台是否输出"保存预览数据"日志
- [ ] 6. 在预览页面检查 sessionStorage 中是否有数据
- [ ] 7. 返回编辑页面
- [ ] 8. 检查控制台是否输出恢复相关的日志
- [ ] 9. 验证表单内容是否正确恢复

## 🎯 预期结果

如果修复成功，应该看到：
1. 点击预览时控制台输出保存日志
2. 返回编辑页面时控制台输出恢复日志
3. 表单内容完整保持，包括：
   - 标题
   - 描述
   - 分类选择
   - 标签选择
   - 封面图片URL

## 📞 如果问题仍然存在

请提供以下信息：
1. 浏览器控制台的完整日志
2. sessionStorage 中的数据内容
3. 当前的路由URL
4. 使用的浏览器版本

这将帮助进一步诊断和解决问题。
