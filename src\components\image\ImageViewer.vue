<template>
  <div class="image-viewer">
    <!-- 图片网格预览 -->
    <div class="image-grid" :class="gridClass">
      <div v-for="(image, index) in images" :key="image.id || index" class="image-item group"
        @click="openPreview(index)">
        <!-- 图片容器 -->
        <div class="image-container">
          <img :src="image.thumbnail || image.src" :alt="image.name || `图片 ${index + 1}`" class="image-preview"
            loading="lazy" />

          <!-- 悬停遮罩 -->
          <div class="image-overlay">
            <div class="image-actions">
              <button class="action-btn preview-btn" @click.stop="openPreview(index)" title="预览">
                <div class="i-heroicons-eye w-5 h-5"></div>
              </button>
              <button class="action-btn edit-btn" @click.stop="editImage(image, index)" title="编辑">
                <div class="i-heroicons-pencil-square w-5 h-5"></div>
              </button>
              <button v-if="showDelete" class="action-btn delete-btn" @click.stop="deleteImage(image, index)"
                title="删除">
                <div class="i-heroicons-trash w-5 h-5"></div>
              </button>
            </div>
          </div>

          <!-- 图片信息 -->
          <div v-if="showInfo" class="image-info">
            <p class="image-name">{{ image.name || `图片 ${index + 1}` }}</p>
            <p class="image-meta">
              <span v-if="image.size">{{ formatFileSize(image.size) }}</span>
              <span v-if="image.width && image.height">{{ image.width }}×{{ image.height }}</span>
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="images.length === 0" class="empty-state">
      <div class="i-heroicons-photo w-16 h-16 text-gray-400 mx-auto mb-4"></div>
      <p class="text-gray-500 text-center">暂无图片</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import PhotoSwipe from 'photoswipe'
import 'photoswipe/style.css'

// 定义图片接口
interface ImageItem {
  id?: string | number
  src: string
  thumbnail?: string
  name?: string
  width?: number
  height?: number
  size?: number
  alt?: string
}

// 导出接口
export type { ImageItem }

// Props 定义
interface Props {
  images: ImageItem[]
  columns?: number | 'auto'
  showInfo?: boolean
  showDelete?: boolean
  aspectRatio?: number
  gap?: number
}

const props = withDefaults(defineProps<Props>(), {
  columns: 'auto',
  showInfo: true,
  showDelete: true,
  aspectRatio: 1,
  gap: 12
})

// Emits 定义
const emit = defineEmits<{
  edit: [image: ImageItem, index: number]
  delete: [image: ImageItem, index: number]
  preview: [image: ImageItem, index: number]
}>()

// 响应式数据
const photoSwipeInstance = ref<PhotoSwipe | null>(null)

// 计算属性
const gridClass = computed(() => {
  if (typeof props.columns === 'number') {
    return `grid-cols-${props.columns}`
  }
  return 'grid-cols-auto'
})

// 方法
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const openPreview = async (index: number) => {
  emit('preview', props.images[index], index)

  // 获取真实图片尺寸
  const getImageDimensions = (src: string): Promise<{ width: number; height: number }> => {
    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => {
        resolve({ width: img.naturalWidth, height: img.naturalHeight })
      }
      img.onerror = () => {
        // 如果加载失败，使用默认尺寸
        resolve({ width: 800, height: 600 })
      }
      img.src = src
    })
  }

  // 准备 PhotoSwipe 数据源，获取真实尺寸
  const dataSource = await Promise.all(
    props.images.map(async (image) => {
      let dimensions = { width: 800, height: 600 }

      // 如果已有尺寸信息，直接使用
      if (image.width && image.height) {
        dimensions = { width: image.width, height: image.height }
      } else {
        // 否则动态获取
        dimensions = await getImageDimensions(image.src)
      }

      return {
        src: image.src,
        width: dimensions.width,
        height: dimensions.height,
        alt: image.alt || image.name || ''
      }
    })
  )

  // 创建 PhotoSwipe 实例
  photoSwipeInstance.value = new PhotoSwipe({
    dataSource,
    index,
    bgOpacity: 0.9,
    showHideAnimationType: 'zoom',
    initialZoomLevel: 'fit',
    secondaryZoomLevel: 1.5,
    maxZoomLevel: 3,
    wheelToZoom: true,
    pinchToClose: false,
    closeOnVerticalDrag: true,
    padding: { top: 20, bottom: 40, left: 100, right: 100 }
  })

  photoSwipeInstance.value.init()
}

const editImage = (image: ImageItem, index: number) => {
  emit('edit', image, index)
}

const deleteImage = (image: ImageItem, index: number) => {
  emit('delete', image, index)
}

// 生命周期
onUnmounted(() => {
  if (photoSwipeInstance.value) {
    photoSwipeInstance.value.destroy()
  }
})
</script>

<style scoped>
.image-viewer {
  @apply w-full;
}

.image-grid {
  @apply grid gap-3;
}

/* 固定网格列数 */
.image-grid.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.image-grid.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

.image-grid.grid-cols-5 {
  grid-template-columns: repeat(5, 1fr);
}

@media (max-width: 768px) {
  .image-grid.grid-cols-5 {
    grid-template-columns: repeat(3, 1fr);
  }

  .image-grid.grid-cols-4 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 640px) {

  .image-grid.grid-cols-5,
  .image-grid.grid-cols-4,
  .image-grid.grid-cols-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

.image-item {
  @apply cursor-pointer;
}

.image-container {
  @apply relative bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-200;
  aspect-ratio: v-bind(aspectRatio);
}

.image-preview {
  @apply w-full h-full object-cover;
}

.image-overlay {
  @apply absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center;
}

.image-actions {
  @apply flex space-x-2;
}

.action-btn {
  @apply p-2 rounded-full text-white transition-all duration-200 shadow-lg;
}

.preview-btn {
  @apply bg-blue-500 hover:bg-blue-600;
}

.edit-btn {
  @apply bg-green-500 hover:bg-green-600;
}

.delete-btn {
  @apply bg-red-500 hover:bg-red-600;
}

.image-info {
  @apply absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3 text-white;
}

.image-name {
  @apply text-sm font-medium truncate mb-1;
}

.image-meta {
  @apply text-xs opacity-80 space-x-2;
}

.empty-state {
  @apply py-16;
}

/* 响应式网格 */
@media (min-width: 640px) {
  .grid-cols-auto {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
}

@media (min-width: 768px) {
  .grid-cols-auto {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}

@media (min-width: 1024px) {
  .grid-cols-auto {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  }
}
</style>
