<template>
  <div v-if="isActive" class="tutorial-overlay"
    style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 9999; pointer-events: none;">
    <!-- 遮罩层 -->
    <div class="tutorial-mask" @click="handleMaskClick"
      style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.5); pointer-events: auto;">
    </div>

    <!-- 高亮区域 -->
    <div v-if="currentStep && highlightElement" class="tutorial-highlight" :style="highlightStyle"
      style="position: absolute; border: 4px solid #3b82f6; border-radius: 0.5rem; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); background-color: rgba(255, 255, 255, 0.1); pointer-events: none; z-index: 10000; transition: all 0.3s ease;">
    </div>

    <!-- 教程提示框 -->
    <div v-if="currentStep" class="tutorial-tooltip" :style="tooltipStyle"
      style="position: absolute; background-color: white; border-radius: 0.75rem; box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); border: 1px solid #e5e7eb; width: 24rem; max-width: 24rem; pointer-events: auto; z-index: 10001;">
      <div class="tooltip-header"
        style="display: flex; align-items: center; justify-content: space-between; padding: 1rem; border-bottom: 1px solid #e5e7eb;">
        <div class="step-indicator"
          style="font-size: 0.875rem; font-weight: 500; color: #6b7280; background-color: #f3f4f6; padding: 0.25rem 0.75rem; border-radius: 9999px;">
          第 {{ currentStepIndex + 1 }} 步，共 {{ steps.length }} 步
        </div>
        <button @click="closeTutorial" class="close-btn"
          style="padding: 0.25rem; border-radius: 0.5rem; transition: background-color 0.2s; background: none; border: none; cursor: pointer;"
          @mouseover="$event.target.style.backgroundColor = '#f3f4f6'"
          @mouseout="$event.target.style.backgroundColor = 'transparent'">
          <div class="i-heroicons-x-mark w-5 h-5" style="color: #6b7280;"></div>
        </button>
      </div>

      <div class="tooltip-content" style="padding: 1.5rem;">
        <h3 class="tooltip-title"
          style="font-size: 1.125rem; font-weight: 600; color: #111827; margin-bottom: 0.75rem;">
          {{ currentStep.title }}
        </h3>
        <p class="tooltip-description"
          style="font-size: 0.875rem; color: #6b7280; line-height: 1.5; margin-bottom: 1rem;">
          {{ currentStep.description }}
        </p>

        <div v-if="currentStep.example" class="tooltip-example"
          style="background-color: #f9fafb; border-radius: 0.5rem; padding: 0.75rem; margin-bottom: 1rem;">
          <div class="example-label"
            style="font-size: 0.75rem; font-weight: 500; color: #6b7280; margin-bottom: 0.25rem;">
            📝 示例：
          </div>
          <code class="example-code"
            style="font-size: 0.875rem; font-family: monospace; color: #3b82f6; background-color: #eff6ff; padding: 0.25rem 0.5rem; border-radius: 0.25rem;">
            {{ currentStep.example }}
          </code>
        </div>

        <div v-if="currentStep.tips" class="tooltip-tips"
          style="background-color: #eff6ff; border-radius: 0.5rem; padding: 0.75rem;">
          <div class="tips-label" style="font-size: 0.75rem; font-weight: 500; color: #3b82f6; margin-bottom: 0.5rem;">
            💡 小贴士：
          </div>
          <ul class="tips-list" style="font-size: 0.875rem; color: #1e40af; list-style: none; padding: 0; margin: 0;">
            <li v-for="tip in currentStep.tips" :key="tip"
              style="margin-bottom: 0.25rem; padding-left: 1rem; position: relative;">
              <span style="position: absolute; left: 0; color: #3b82f6;">•</span>
              {{ tip }}
            </li>
          </ul>
        </div>
      </div>

      <div class="tooltip-actions"
        style="display: flex; align-items: center; gap: 0.75rem; padding: 1rem; border-top: 1px solid #e5e7eb;">
        <button v-if="currentStepIndex > 0" @click="previousStep" class="btn btn-secondary"
          style="display: inline-flex; align-items: center; padding: 0.5rem 1rem; font-size: 0.875rem; font-weight: 500; border-radius: 0.5rem; transition: all 0.2s; background-color: #f3f4f6; color: #374151; border: none; cursor: pointer;"
          @mouseover="$event.target.style.backgroundColor = '#e5e7eb'"
          @mouseout="$event.target.style.backgroundColor = '#f3f4f6'">
          ← 上一步
        </button>

        <div style="flex: 1;"></div>

        <button @click="skipTutorial" class="btn btn-ghost"
          style="display: inline-flex; align-items: center; padding: 0.5rem 1rem; font-size: 0.875rem; font-weight: 500; border-radius: 0.5rem; transition: all 0.2s; background: none; color: #6b7280; border: none; cursor: pointer;"
          @mouseover="$event.target.style.backgroundColor = '#f3f4f6'; $event.target.style.color = '#374151'"
          @mouseout="$event.target.style.backgroundColor = 'transparent'; $event.target.style.color = '#6b7280'">
          跳过教程
        </button>

        <button v-if="currentStepIndex < steps.length - 1" @click="nextStep" class="btn btn-primary"
          style="display: inline-flex; align-items: center; padding: 0.5rem 1rem; font-size: 0.875rem; font-weight: 500; border-radius: 0.5rem; transition: all 0.2s; background-color: #3b82f6; color: white; border: none; cursor: pointer;"
          @mouseover="$event.target.style.backgroundColor = '#2563eb'"
          @mouseout="$event.target.style.backgroundColor = '#3b82f6'">
          下一步 →
        </button>

        <button v-else @click="completeTutorial" class="btn btn-primary"
          style="display: inline-flex; align-items: center; padding: 0.5rem 1rem; font-size: 0.875rem; font-weight: 500; border-radius: 0.5rem; transition: all 0.2s; background-color: #10b981; color: white; border: none; cursor: pointer;"
          @mouseover="$event.target.style.backgroundColor = '#059669'"
          @mouseout="$event.target.style.backgroundColor = '#10b981'">
          🎉 完成教程
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

// 定义组件名称
defineOptions({
  name: 'ImageHostTutorial'
})

// 教程步骤接口
interface TutorialStep {
  id: string
  title: string
  description: string
  target: string // CSS选择器
  position?: 'top' | 'bottom' | 'left' | 'right'
  example?: string
  tips?: string[]
  action?: () => void
}

// Props
interface Props {
  steps: TutorialStep[]
  autoStart?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoStart: false
})

// Emits
const emit = defineEmits<{
  complete: []
  skip: []
  close: []
  stepChange: [step: TutorialStep, index: number]
}>()

// 响应式数据
const isActive = ref(false)
const currentStepIndex = ref(0)
const highlightElement = ref<HTMLElement | null>(null)
const tooltipPosition = ref({ x: 0, y: 0 })

// 计算属性
const currentStep = computed(() => props.steps[currentStepIndex.value])

const highlightStyle = computed(() => {
  if (!highlightElement.value) return {}

  const rect = highlightElement.value.getBoundingClientRect()
  return {
    left: `${rect.left - 4}px`,
    top: `${rect.top - 4}px`,
    width: `${rect.width + 8}px`,
    height: `${rect.height + 8}px`
  }
})

const tooltipStyle = computed(() => {
  return {
    left: `${tooltipPosition.value.x}px`,
    top: `${tooltipPosition.value.y}px`
  }
})

// 方法
const startTutorial = () => {
  isActive.value = true
  currentStepIndex.value = 0
  updateHighlight()
}

const closeTutorial = () => {
  isActive.value = false
  emit('close')
}

const skipTutorial = () => {
  isActive.value = false
  emit('skip')
}

const completeTutorial = () => {
  isActive.value = false
  emit('complete')
}

const nextStep = () => {
  if (currentStepIndex.value < props.steps.length - 1) {
    currentStepIndex.value++
    updateHighlight()
    emit('stepChange', currentStep.value, currentStepIndex.value)
  }
}

const previousStep = () => {
  if (currentStepIndex.value > 0) {
    currentStepIndex.value--
    updateHighlight()
    emit('stepChange', currentStep.value, currentStepIndex.value)
  }
}

const updateHighlight = async () => {
  await nextTick()

  if (!currentStep.value) return

  const element = document.querySelector(currentStep.value.target) as HTMLElement
  if (element) {
    highlightElement.value = element
    updateTooltipPosition(element)

    // 滚动到元素位置
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    })

    // 执行步骤动作
    if (currentStep.value.action) {
      currentStep.value.action()
    }
  }
}

const updateTooltipPosition = (element: HTMLElement) => {
  const rect = element.getBoundingClientRect()
  const position = currentStep.value.position || 'bottom'

  let x = 0
  let y = 0

  switch (position) {
    case 'top':
      x = rect.left + rect.width / 2 - 200
      y = rect.top - 20
      break
    case 'bottom':
      x = rect.left + rect.width / 2 - 200
      y = rect.bottom + 20
      break
    case 'left':
      x = rect.left - 420
      y = rect.top + rect.height / 2 - 100
      break
    case 'right':
      x = rect.right + 20
      y = rect.top + rect.height / 2 - 100
      break
  }

  // 确保提示框在视窗内
  x = Math.max(20, Math.min(x, window.innerWidth - 420))
  y = Math.max(20, Math.min(y, window.innerHeight - 200))

  tooltipPosition.value = { x, y }
}

const handleMaskClick = () => {
  // 点击遮罩不关闭教程，避免误操作
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (!isActive.value) return

  switch (event.key) {
    case 'Escape':
      closeTutorial()
      break
    case 'ArrowLeft':
      previousStep()
      break
    case 'ArrowRight':
      nextStep()
      break
  }
}

// 窗口大小变化处理
const handleResize = () => {
  if (highlightElement.value) {
    updateTooltipPosition(highlightElement.value)
  }
}

// 暂停和恢复教程
const pauseTutorial = () => {
  isActive.value = false
}

const resumeTutorial = () => {
  isActive.value = true
  updateHighlight()
}

// 跳转到指定步骤
const goToStep = (stepIndex: number) => {
  if (stepIndex >= 0 && stepIndex < props.steps.length) {
    currentStepIndex.value = stepIndex
    updateHighlight()
    emit('stepChange', currentStep.value, currentStepIndex.value)
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  window.addEventListener('resize', handleResize)

  if (props.autoStart) {
    startTutorial()
  }
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  window.removeEventListener('resize', handleResize)
})

// 暴露方法
defineExpose({
  startTutorial,
  closeTutorial,
  nextStep,
  previousStep,
  pauseTutorial,
  resumeTutorial,
  goToStep,
  isActive: () => isActive.value,
  currentStepIndex: () => currentStepIndex.value
})
</script>

<style scoped>
.tutorial-overlay {
  @apply fixed inset-0 z-[9999] pointer-events-none;
}

.tutorial-mask {
  @apply absolute inset-0 bg-black bg-opacity-50 pointer-events-auto;
}

.tutorial-highlight {
  @apply absolute border-4 border-blue-500 rounded-lg shadow-lg pointer-events-none;
  @apply bg-white bg-opacity-10;
  transition: all 0.3s ease;
  z-index: 10000;
}

.tutorial-tooltip {
  @apply absolute bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700;
  @apply w-96 max-w-sm pointer-events-auto;
  z-index: 10001;
}

.tooltip-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700;
}

.step-indicator {
  @apply text-sm font-medium text-gray-600 dark:text-gray-400;
}

.close-btn {
  @apply p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors;
}

.tooltip-content {
  @apply p-4 space-y-3;
}

.tooltip-title {
  @apply text-lg font-semibold text-gray-900 dark:text-gray-100;
}

.tooltip-description {
  @apply text-sm text-gray-600 dark:text-gray-400 leading-relaxed;
}

.tooltip-example {
  @apply bg-gray-50 dark:bg-gray-700 rounded-lg p-3;
}

.example-label {
  @apply text-xs font-medium text-gray-500 dark:text-gray-400 mb-1;
}

.example-code {
  @apply text-sm font-mono text-blue-600 dark:text-blue-400;
}

.tooltip-tips {
  @apply bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3;
}

.tips-label {
  @apply text-xs font-medium text-blue-600 dark:text-blue-400 mb-1;
}

.tips-list {
  @apply text-sm text-blue-700 dark:text-blue-300 space-y-1;
}

.tooltip-actions {
  @apply flex items-center space-x-3 p-4 border-t border-gray-200 dark:border-gray-700;
}

.btn {
  @apply inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply text-gray-700 bg-gray-100 hover:bg-gray-200 focus:ring-gray-500;
  @apply dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600;
}

.btn-ghost {
  @apply text-gray-500 hover:text-gray-700 hover:bg-gray-100;
  @apply dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-700;
}

.flex-1 {
  @apply flex-1;
}
</style>
