import { db } from '@/database'
import type { Category, CategoryWithChildren, CategoryForm } from '@/types'

export class CategoryService {
  // 创建分类
  async createCategory(categoryData: CategoryForm): Promise<number> {
    const category: Omit<Category, 'id'> = {
      name: categoryData.name,
      parent_id: categoryData.parent_id,
      resource_count: 0,
      sort_order: categoryData.sort_order,
      created_at: new Date(),
    }

    return (await db.categories.add(category)) as number
  }

  // 获取分类详情
  async getCategoryById(id: number): Promise<Category | undefined> {
    return await db.categories.get(id)
  }

  // 更新分类
  async updateCategory(id: number, categoryData: Partial<CategoryForm>): Promise<void> {
    const updateData: Partial<Category> = {}

    if (categoryData.name !== undefined) updateData.name = categoryData.name
    if (categoryData.parent_id !== undefined) updateData.parent_id = categoryData.parent_id
    if (categoryData.sort_order !== undefined) updateData.sort_order = categoryData.sort_order

    await db.categories.update(id, updateData)
  }

  // 删除分类
  async deleteCategory(id: number): Promise<void> {
    // 检查是否有子分类
    const childCategories = await db.categories.where('parent_id').equals(id).toArray()
    if (childCategories.length > 0) {
      throw new Error('无法删除包含子分类的分类')
    }

    // 检查是否有关联的资源
    const resourceCount = await db.resources.where('category_id').equals(id).count()
    if (resourceCount > 0) {
      throw new Error('无法删除包含资源的分类')
    }

    await db.categories.delete(id)
  }

  // 获取所有分类（平铺）
  async getAllCategories(): Promise<Category[]> {
    return await db.categories.orderBy('sort_order').toArray()
  }

  // 获取根分类
  async getRootCategories(): Promise<Category[]> {
    return await db.categories.where('parent_id').equals(0).sortBy('sort_order')
  }

  // 获取子分类
  async getChildCategories(parentId: number): Promise<Category[]> {
    return await db.categories.where('parent_id').equals(parentId).sortBy('sort_order')
  }

  // 获取分类树
  async getCategoryTree(): Promise<CategoryWithChildren[]> {
    const allCategories = await this.getAllCategories()
    const categoryMap = new Map<number, CategoryWithChildren>()

    // 创建分类映射
    allCategories.forEach((category) => {
      categoryMap.set(category.id!, {
        ...category,
        children: [],
      })
    })

    const rootCategories: CategoryWithChildren[] = []

    // 构建树结构
    allCategories.forEach((category) => {
      const categoryWithChildren = categoryMap.get(category.id!)!

      if (category.parent_id === 0) {
        rootCategories.push(categoryWithChildren)
      } else {
        const parent = categoryMap.get(category.parent_id)
        if (parent) {
          parent.children!.push(categoryWithChildren)
        }
      }
    })

    // 计算包含子分类的资源总数
    await this.calculateTotalResourceCount(rootCategories)

    return rootCategories
  }

  // 计算包含子分类的资源总数
  private async calculateTotalResourceCount(categories: CategoryWithChildren[]): Promise<void> {
    for (const category of categories) {
      if (category.children && category.children.length > 0) {
        // 递归计算子分类的总数
        await this.calculateTotalResourceCount(category.children)

        // 计算当前分类及其所有子分类的资源总数
        const descendantIds = await this.getAllDescendantIds(category.id!)
        const totalCount = await this.getResourceCountForCategories([
          category.id!,
          ...descendantIds,
        ])
        category.resource_count = totalCount
      }
    }
  }

  // 获取分类的所有子孙分类ID
  async getAllDescendantIds(categoryId: number): Promise<number[]> {
    const descendants: number[] = []
    const children = await this.getChildCategories(categoryId)

    for (const child of children) {
      descendants.push(child.id!)
      const grandChildren = await this.getAllDescendantIds(child.id!)
      descendants.push(...grandChildren)
    }

    return descendants
  }

  // 获取多个分类的资源总数
  private async getResourceCountForCategories(categoryIds: number[]): Promise<number> {
    const { db } = await import('@/database')
    let totalCount = 0

    for (const categoryId of categoryIds) {
      const count = await db.resources.where('category_id').equals(categoryId).count()
      totalCount += count
    }

    return totalCount
  }

  // 获取分类路径（面包屑）
  async getCategoryPath(categoryId: number): Promise<Category[]> {
    const path: Category[] = []
    let currentId = categoryId

    while (currentId !== 0) {
      const category = await db.categories.get(currentId)
      if (!category) break

      path.unshift(category)
      currentId = category.parent_id
    }

    return path
  }

  // 移动分类
  async moveCategory(
    categoryId: number,
    newParentId: number,
    newSortOrder?: number,
  ): Promise<void> {
    // 检查是否会形成循环引用
    if (await this.wouldCreateCycle(categoryId, newParentId)) {
      throw new Error('无法移动分类：会形成循环引用')
    }

    const updateData: Partial<Category> = {
      parent_id: newParentId,
    }

    if (newSortOrder !== undefined) {
      updateData.sort_order = newSortOrder
    }

    await db.categories.update(categoryId, updateData)
  }

  // 检查是否会形成循环引用
  private async wouldCreateCycle(categoryId: number, newParentId: number): Promise<boolean> {
    if (newParentId === 0) return false
    if (newParentId === categoryId) return true

    let currentId = newParentId
    while (currentId !== 0) {
      const category = await db.categories.get(currentId)
      if (!category) break

      if (category.parent_id === categoryId) return true
      currentId = category.parent_id
    }

    return false
  }

  // 重新排序分类
  async reorderCategories(parentId: number, categoryIds: number[]): Promise<void> {
    const updates = categoryIds.map((categoryId, index) => ({
      id: categoryId,
      sort_order: index + 1,
    }))

    for (const update of updates) {
      await db.categories.update(update.id, { sort_order: update.sort_order })
    }
  }

  // 搜索分类
  async searchCategories(keyword: string): Promise<Category[]> {
    const allCategories = await this.getAllCategories()
    const lowerKeyword = keyword.toLowerCase()

    return allCategories.filter((category) => category.name.toLowerCase().includes(lowerKeyword))
  }
}

export const categoryService = new CategoryService()
