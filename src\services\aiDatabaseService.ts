import { db } from '@/database'
import type { AiProvider, AiModel, AiConfig } from '@/database'

/**
 * AI配置数据库服务
 * 基于现有的Dexie数据库实现AI配置管理功能
 */
export class AiDatabaseService {
  // ==================== AI服务商管理 ====================

  /**
   * 获取所有AI服务商
   */
  async getAllProviders(): Promise<AiProvider[]> {
    try {
      return await db.ai_providers.orderBy('sort_order').toArray()
    } catch (error) {
      console.error('获取AI服务商失败:', error)
      return []
    }
  }

  /**
   * 获取启用的AI服务商
   */
  async getActiveProviders(): Promise<AiProvider[]> {
    try {
      console.log('开始查询启用的AI服务商...')

      // 先查询所有服务商
      const allProviders = await db.ai_providers.toArray()
      console.log('数据库中所有服务商:', allProviders)

      // 再查询启用的服务商 - 使用filter方式
      const activeProviders = await db.ai_providers
        .filter((provider) => provider.is_active === true)
        .sortBy('sort_order')
      console.log('启用的服务商:', activeProviders)

      return activeProviders
    } catch (error) {
      console.error('获取启用的AI服务商失败:', error)
      return []
    }
  }

  /**
   * 根据ID获取AI服务商
   */
  async getProviderById(id: number): Promise<AiProvider | undefined> {
    try {
      return await db.ai_providers.get(id)
    } catch (error) {
      console.error('获取AI服务商失败:', error)
      return undefined
    }
  }

  /**
   * 根据名称获取AI服务商
   */
  async getProviderByName(name: string): Promise<AiProvider | undefined> {
    try {
      return await db.ai_providers.where('name').equals(name).first()
    } catch (error) {
      console.error('获取AI服务商失败:', error)
      return undefined
    }
  }

  /**
   * 创建自定义AI服务商
   */
  async createCustomProvider(
    providerData: Omit<AiProvider, 'id' | 'created_at' | 'updated_at'>,
  ): Promise<number> {
    try {
      // 检查名称是否已存在
      const existing = await this.getProviderByName(providerData.name)
      if (existing) {
        throw new Error('服务商名称已存在')
      }

      const provider: Omit<AiProvider, 'id'> = {
        ...providerData,
        type: 'custom',
        created_at: new Date(),
        updated_at: new Date(),
      }

      return (await db.ai_providers.add(provider)) as number
    } catch (error) {
      console.error('创建自定义AI服务商失败:', error)
      throw error
    }
  }

  /**
   * 更新AI服务商
   */
  async updateProvider(
    id: number,
    updates: Partial<Omit<AiProvider, 'id' | 'created_at'>>,
  ): Promise<boolean> {
    try {
      const updateData = {
        ...updates,
        updated_at: new Date(),
      }

      const result = await db.ai_providers.update(id, updateData)
      return result === 1
    } catch (error) {
      console.error('更新AI服务商失败:', error)
      return false
    }
  }

  /**
   * 删除自定义AI服务商
   */
  async deleteCustomProvider(id: number): Promise<boolean> {
    try {
      // 检查是否为内置服务商
      const provider = await this.getProviderById(id)
      if (!provider || provider.type === 'builtin') {
        throw new Error('不能删除内置服务商')
      }

      // 检查是否有关联的配置
      const configCount = await db.ai_configs.where('provider_id').equals(id).count()
      if (configCount > 0) {
        throw new Error('该服务商下还有配置，请先删除相关配置')
      }

      // 删除相关模型
      await db.ai_models.where('provider_id').equals(id).delete()

      // 删除服务商
      await db.ai_providers.delete(id)
      return true
    } catch (error) {
      console.error('删除自定义AI服务商失败:', error)
      throw error
    }
  }

  // ==================== AI模型管理 ====================

  /**
   * 获取服务商的所有模型
   */
  async getModelsByProvider(providerId: number): Promise<AiModel[]> {
    try {
      console.log('查询模型，服务商ID:', providerId)

      // 先查询所有模型
      const allModels = await db.ai_models.toArray()
      console.log('数据库中所有模型:', allModels)

      // 再查询指定服务商的模型
      const providerModels = await db.ai_models
        .where('provider_id')
        .equals(providerId)
        .and((model) => model.is_active)
        .sortBy('sort_order')

      console.log('指定服务商的模型:', providerModels)
      return providerModels
    } catch (error) {
      console.error('获取AI模型失败:', error)
      return []
    }
  }

  /**
   * 获取所有AI模型
   */
  async getAllModels(): Promise<AiModel[]> {
    try {
      return await db.ai_models.toArray()
    } catch (error) {
      console.error('获取所有AI模型失败:', error)
      return []
    }
  }

  /**
   * 根据ID获取AI模型
   */
  async getModelById(id: number): Promise<AiModel | undefined> {
    try {
      return await db.ai_models.get(id)
    } catch (error) {
      console.error('获取AI模型失败:', error)
      return undefined
    }
  }

  /**
   * 创建自定义AI模型
   */
  async createCustomModel(
    modelData: Omit<AiModel, 'id' | 'created_at' | 'updated_at'>,
  ): Promise<number> {
    try {
      // 检查服务商是否存在
      const provider = await this.getProviderById(modelData.provider_id)
      if (!provider) {
        throw new Error('服务商不存在')
      }

      const model: Omit<AiModel, 'id'> = {
        ...modelData,
        type: 'custom',
        created_at: new Date(),
        updated_at: new Date(),
      }

      return (await db.ai_models.add(model)) as number
    } catch (error) {
      console.error('创建自定义AI模型失败:', error)
      throw error
    }
  }

  /**
   * 更新AI模型
   */
  async updateModel(
    id: number,
    updates: Partial<Omit<AiModel, 'id' | 'created_at'>>,
  ): Promise<boolean> {
    try {
      const updateData = {
        ...updates,
        updated_at: new Date(),
      }

      const result = await db.ai_models.update(id, updateData)
      return result === 1
    } catch (error) {
      console.error('更新AI模型失败:', error)
      return false
    }
  }

  /**
   * 删除自定义AI模型
   */
  async deleteCustomModel(id: number): Promise<boolean> {
    try {
      // 检查是否为内置模型
      const model = await this.getModelById(id)
      if (!model || model.type === 'builtin') {
        throw new Error('不能删除内置模型')
      }

      // 检查是否有关联的配置
      const configCount = await db.ai_configs.where('model_id').equals(id).count()
      if (configCount > 0) {
        throw new Error('该模型下还有配置，请先删除相关配置')
      }

      await db.ai_models.delete(id)
      return true
    } catch (error) {
      console.error('删除自定义AI模型失败:', error)
      throw error
    }
  }

  // ==================== AI配置管理 ====================

  /**
   * 获取所有AI配置
   */
  async getAllConfigs(): Promise<AiConfig[]> {
    try {
      return await db.ai_configs.orderBy('created_at').reverse().toArray()
    } catch (error) {
      console.error('获取AI配置失败:', error)
      return []
    }
  }

  /**
   * 获取启用的AI配置
   */
  async getActiveConfigs(): Promise<AiConfig[]> {
    try {
      return await db.ai_configs
        .filter((config) => config.is_enabled === true)
        .reverse()
        .sortBy('created_at')
    } catch (error) {
      console.error('获取启用的AI配置失败:', error)
      return []
    }
  }

  /**
   * 获取默认AI配置
   */
  async getDefaultConfig(): Promise<AiConfig | undefined> {
    try {
      return await db.ai_configs
        .filter((config) => config.is_default === true && config.is_enabled === true)
        .first()
    } catch (error) {
      console.error('获取默认AI配置失败:', error)
      return undefined
    }
  }

  /**
   * 根据ID获取AI配置
   */
  async getConfigById(id: number): Promise<AiConfig | undefined> {
    try {
      return await db.ai_configs.get(id)
    } catch (error) {
      console.error('获取AI配置失败:', error)
      return undefined
    }
  }

  /**
   * 创建AI配置
   */
  async createConfig(
    configData: Omit<AiConfig, 'id' | 'created_at' | 'updated_at'>,
  ): Promise<number> {
    try {
      // 验证服务商是否存在
      const provider = await this.getProviderById(configData.provider_id)
      if (!provider) {
        throw new Error('服务商不存在')
      }

      // 验证模型是否存在（如果指定了模型）
      if (configData.model_id) {
        const model = await this.getModelById(configData.model_id)
        if (!model) {
          throw new Error('模型不存在')
        }
      }

      const config: Omit<AiConfig, 'id'> = {
        ...configData,
        created_at: new Date(),
        updated_at: new Date(),
      }

      // 如果设置为默认配置，先取消其他默认配置
      if (config.is_default) {
        await db.ai_configs.filter((c) => c.is_default === true).modify({ is_default: false })
      }

      return (await db.ai_configs.add(config)) as number
    } catch (error) {
      console.error('创建AI配置失败:', error)
      throw error
    }
  }

  /**
   * 更新AI配置
   */
  async updateConfig(
    id: number,
    updates: Partial<Omit<AiConfig, 'id' | 'created_at'>>,
  ): Promise<boolean> {
    try {
      const updateData = {
        ...updates,
        updated_at: new Date(),
      }

      // 如果设置为默认配置，先取消其他默认配置
      if (updateData.is_default) {
        await db.ai_configs.filter((c) => c.is_default === true).modify({ is_default: false })
      }

      const result = await db.ai_configs.update(id, updateData)
      return result === 1
    } catch (error) {
      console.error('更新AI配置失败:', error)
      return false
    }
  }

  /**
   * 删除AI配置
   */
  async deleteConfig(id: number): Promise<boolean> {
    try {
      await db.ai_configs.delete(id)
      return true
    } catch (error) {
      console.error('删除AI配置失败:', error)
      return false
    }
  }

  /**
   * 设置默认配置
   */
  async setDefaultConfig(id: number): Promise<boolean> {
    try {
      // 先取消所有默认配置
      await db.ai_configs.filter((c) => c.is_default === true).modify({ is_default: false })

      // 设置新的默认配置
      const result = await db.ai_configs.update(id, {
        is_default: true,
        updated_at: new Date(),
      })

      return result === 1
    } catch (error) {
      console.error('设置默认配置失败:', error)
      return false
    }
  }
}

// 导出单例实例
export const aiDatabaseService = new AiDatabaseService()
export default aiDatabaseService
