<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import BaseButton from '@/components/ui/BaseButton.vue'
import BaseCard from '@/components/ui/BaseCard.vue'
import { resourceService } from '@/services/resourceService'
import { categoryService } from '@/services/categoryService'
import { tagService } from '@/services/tagService'

const router = useRouter()

// 统计数据
const stats = ref({
  totalResources: 0,
  totalCategories: 0,
  totalTags: 0,
  recentResources: []
})

const loading = ref(true)

onMounted(async () => {
  try {
    const [resources, categories, tags, recent] = await Promise.all([
      resourceService.searchResources({ limit: 1000 }),
      categoryService.getAllCategories(),
      tagService.getAllTags(),
      resourceService.searchResources({ limit: 6, sort_by: 'created_at', sort_order: 'desc' })
    ])

    stats.value = {
      totalResources: resources.length,
      totalCategories: categories.length,
      totalTags: tags.length,
      recentResources: recent
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  } finally {
    loading.value = false
  }
})

const navigateToKnowledge = () => {
  router.push('/knowledge')
}

const navigateToCreate = () => {
  router.push('/knowledge/create')
}
</script>

<template>
  <!-- 首页内容容器 - 背景占满屏幕，内容限制1440px -->
  <div
    class="page-content-wrapper bg-gradient-to-br from-primary-25 via-white to-primary-50/30 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
    <!-- 英雄区域 -->
    <section class="relative overflow-hidden">
      <div class="content-container py-20">
        <div class="text-center">
          <h1 class="text-6xl font-bold text-primary mb-6 animate-fade-in">
            <span class="bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">
              KnowlEdge
            </span>
          </h1>
          <div class="reading-container-wide animate-slide-up">
            <p class="text-2xl text-secondary mb-8 text-chinese paragraph-chinese-no-indent">
              您的个人知识库管理系统，让知识更有序，让学习更高效
            </p>
          </div>
          <div class="flex gap-4 justify-center animate-slide-up">
            <BaseButton size="lg" @click="navigateToKnowledge">
              <div class="i-heroicons-book-open mr-2"></div>
              浏览知识库
            </BaseButton>
            <BaseButton variant="outline" size="lg" @click="navigateToCreate">
              <div class="i-heroicons-plus mr-2"></div>
              添加资源
            </BaseButton>
          </div>
        </div>
      </div>

      <!-- 装饰性背景 -->
      <div class="absolute top-0 left-0 w-full h-full overflow-hidden -z-10">
        <div class="absolute top-20 left-10 w-20 h-20 bg-primary-200 rounded-full opacity-20 animate-bounce-in"></div>
        <div class="absolute top-40 right-20 w-16 h-16 bg-blue-200 rounded-full opacity-20 animate-bounce-in"
          style="animation-delay: 0.5s"></div>
        <div class="absolute bottom-20 left-1/4 w-12 h-12 bg-green-200 rounded-full opacity-20 animate-bounce-in"
          style="animation-delay: 1s"></div>
      </div>
    </section>

    <!-- 统计数据 -->
    <section class="py-16">
      <div class="content-container">
        <div class="grid grid-cols-3 gap-8">
          <BaseCard variant="shadow" hoverable class="text-center animate-slide-up">
            <div class="p-6">
              <div
                class="w-16 h-16 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <div class="i-heroicons-document-text text-primary-600 w-8 h-8"></div>
              </div>
              <h3 class="text-3xl font-bold text-primary mb-2">{{ stats.totalResources }}</h3>
              <p class="text-secondary">知识资源</p>
            </div>
          </BaseCard>

          <BaseCard variant="shadow" hoverable class="text-center animate-slide-up" style="animation-delay: 0.2s">
            <div class="p-6">
              <div
                class="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <div class="i-heroicons-folder text-blue-600 w-8 h-8"></div>
              </div>
              <h3 class="text-3xl font-bold text-primary mb-2">{{ stats.totalCategories }}</h3>
              <p class="text-secondary">分类目录</p>
            </div>
          </BaseCard>

          <BaseCard variant="shadow" hoverable class="text-center animate-slide-up" style="animation-delay: 0.4s">
            <div class="p-6">
              <div
                class="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <div class="i-heroicons-tag text-green-600 w-8 h-8"></div>
              </div>
              <h3 class="text-3xl font-bold text-primary mb-2">{{ stats.totalTags }}</h3>
              <p class="text-secondary">标签标记</p>
            </div>
          </BaseCard>
        </div>
      </div>
    </section>

    <!-- 功能特性 -->
    <section class="py-16 bg-white/50 dark:bg-gray-800/50">
      <div class="content-container">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold text-primary mb-4 heading-chinese">强大功能</h2>
          <div class="reading-container">
            <p class="text-xl text-secondary text-chinese paragraph-chinese-no-indent">
              为您提供完整的知识管理解决方案
            </p>
          </div>
        </div>

        <div class="grid grid-cols-3 gap-8">
          <BaseCard variant="shadow" hoverable class="animate-slide-up">
            <div class="p-6">
              <div
                class="w-12 h-12 bg-primary-100 dark:bg-primary-900/20 rounded-lg flex items-center justify-center mb-4">
                <div class="i-heroicons-cloud-arrow-down text-primary-600 w-6 h-6"></div>
              </div>
              <h3 class="text-xl font-semibold text-primary mb-2 heading-chinese">离线存储</h3>
              <p class="text-secondary text-chinese paragraph-chinese-no-indent">数据存储在本地浏览器中，无需网络连接即可使用</p>
            </div>
          </BaseCard>

          <BaseCard variant="shadow" hoverable class="animate-slide-up" style="animation-delay: 0.1s">
            <div class="p-6">
              <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center mb-4">
                <div class="i-heroicons-magnifying-glass text-blue-600 w-6 h-6"></div>
              </div>
              <h3 class="text-xl font-semibold text-primary mb-2 heading-chinese">智能搜索</h3>
              <p class="text-secondary text-chinese paragraph-chinese-no-indent">支持全文搜索，快速找到您需要的知识资源</p>
            </div>
          </BaseCard>

          <BaseCard variant="shadow" hoverable class="animate-slide-up" style="animation-delay: 0.2s">
            <div class="p-6">
              <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center mb-4">
                <div class="i-heroicons-squares-2x2 text-green-600 w-6 h-6"></div>
              </div>
              <h3 class="text-xl font-semibold text-primary mb-2 heading-chinese">分类管理</h3>
              <p class="text-secondary text-chinese paragraph-chinese-no-indent">树形分类结构，让您的知识更有条理</p>
            </div>
          </BaseCard>

          <BaseCard variant="shadow" hoverable class="animate-slide-up" style="animation-delay: 0.3s">
            <div class="p-6">
              <div
                class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mb-4">
                <div class="i-heroicons-hashtag text-purple-600 w-6 h-6"></div>
              </div>
              <h3 class="text-xl font-semibold text-primary mb-2 heading-chinese">标签系统</h3>
              <p class="text-secondary text-chinese paragraph-chinese-no-indent">灵活的标签管理，多维度组织您的知识</p>
            </div>
          </BaseCard>

          <BaseCard variant="shadow" hoverable class="animate-slide-up" style="animation-delay: 0.4s">
            <div class="p-6">
              <div
                class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center mb-4">
                <div class="i-heroicons-document-text text-yellow-600 w-6 h-6"></div>
              </div>
              <h3 class="text-xl font-semibold text-primary mb-2 heading-chinese">Markdown支持</h3>
              <p class="text-secondary text-chinese paragraph-chinese-no-indent">支持Markdown格式，让您的笔记更加丰富</p>
            </div>
          </BaseCard>

          <BaseCard variant="shadow" hoverable class="animate-slide-up" style="animation-delay: 0.5s">
            <div class="p-6">
              <div class="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center mb-4">
                <div class="i-heroicons-arrow-up-tray text-red-600 w-6 h-6"></div>
              </div>
              <h3 class="text-xl font-semibold text-primary mb-2 heading-chinese">数据导出</h3>
              <p class="text-secondary text-chinese paragraph-chinese-no-indent">支持数据导入导出，保障您的数据安全</p>
            </div>
          </BaseCard>
        </div>
      </div>
    </section>
  </div>
</template>
