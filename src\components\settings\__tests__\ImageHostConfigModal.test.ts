import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import ImageHostConfigModal from '../ImageHostConfigModal.vue'

// Mock services
vi.mock('@/services/imageHostService', () => ({
  imageHostService: {
    saveConfig: vi.fn().mockResolvedValue({}),
    testConfig: vi.fn().mockResolvedValue({ success: true })
  }
}))

// Mock Ant Design components
const mockAntdComponents = {
  'a-modal': { 
    template: '<div class="ant-modal"><slot /></div>',
    props: ['open', 'title', 'width', 'footer', 'destroy-on-close'],
    emits: ['cancel']
  },
  'a-form': { 
    template: '<form class="ant-form"><slot /></form>',
    props: ['model', 'rules', 'layout'],
    emits: ['finish']
  },
  'a-form-item': { 
    template: '<div class="ant-form-item"><slot /></div>',
    props: ['label', 'name']
  },
  'a-input': { 
    template: '<input class="ant-input" />',
    props: ['value', 'placeholder'],
    emits: ['update:value']
  },
  'a-input-number': { 
    template: '<input type="number" class="ant-input-number" />',
    props: ['value', 'min', 'max', 'placeholder'],
    emits: ['update:value']
  },
  'a-input-password': { 
    template: '<input type="password" class="ant-input-password" />',
    props: ['value', 'placeholder'],
    emits: ['update:value']
  },
  'a-select': { 
    template: '<select class="ant-select"><slot /></select>',
    props: ['value'],
    emits: ['update:value']
  },
  'a-select-option': { 
    template: '<option class="ant-select-option"><slot /></option>',
    props: ['value']
  },
  'a-switch': { 
    template: '<input type="checkbox" class="ant-switch" />',
    props: ['checked'],
    emits: ['update:checked']
  },
  'a-row': { template: '<div class="ant-row"><slot /></div>' },
  'a-col': { template: '<div class="ant-col"><slot /></div>' },
  'a-space': { template: '<div class="ant-space"><slot /></div>' },
  'a-button': { 
    template: '<button class="ant-btn"><slot /></button>',
    props: ['type', 'loading', 'html-type']
  }
}

describe('ImageHostConfigModal', () => {
  let wrapper: any

  const defaultProps = {
    config: null
  }

  beforeEach(() => {
    wrapper = mount(ImageHostConfigModal, {
      props: defaultProps,
      global: {
        components: mockAntdComponents
      }
    })
  })

  it('应该正确渲染模态框结构', () => {
    expect(wrapper.find('.ant-modal').exists()).toBe(true)
    expect(wrapper.find('.config-form').exists()).toBe(true)
  })

  it('应该显示正确的标题', () => {
    // 新增模式
    expect(wrapper.vm.isEditing).toBe(false)
    
    // 编辑模式
    const editWrapper = mount(ImageHostConfigModal, {
      props: {
        config: { id: '1', name: 'Test Config' }
      },
      global: {
        components: mockAntdComponents
      }
    })
    expect(editWrapper.vm.isEditing).toBe(true)
  })

  it('应该包含所有必要的表单字段', () => {
    expect(wrapper.find('.form-section').exists()).toBe(true)
    expect(wrapper.findAll('.section-title').length).toBeGreaterThan(0)
  })

  it('应该包含基础信息字段', () => {
    const basicSection = wrapper.find('.form-section')
    expect(basicSection.exists()).toBe(true)
  })

  it('应该支持表单验证', () => {
    expect(wrapper.vm.rules).toBeDefined()
    expect(wrapper.vm.rules.name).toBeDefined()
    expect(wrapper.vm.rules.apiUrl).toBeDefined()
  })

  it('应该正确处理表单提交', async () => {
    const component = wrapper.vm
    
    // 设置表单数据
    component.form.name = 'Test Config'
    component.form.apiUrl = 'https://example.com/api'
    component.form.provider = 'test'
    
    // 模拟表单提交
    await component.handleSubmit()
    expect(component.saving).toBe(false)
  })

  it('应该支持认证配置', async () => {
    const component = wrapper.vm
    
    // 测试不同认证类型
    component.form.authType = 'token'
    await nextTick()
    
    component.form.authType = 'header'
    await nextTick()
    
    component.form.authType = 'none'
    await nextTick()
    
    expect(component.form.authType).toBe('none')
  })

  it('应该正确处理允许格式', () => {
    const component = wrapper.vm
    
    // 测试格式文本转换
    component.allowedFormatsText = 'jpg,png,gif'
    expect(component.allowedFormatsText).toBe('jpg,png,gif')
  })

  it('应该包含操作按钮', () => {
    expect(wrapper.find('.form-actions').exists()).toBe(true)
    expect(wrapper.findAll('.ant-btn').length).toBeGreaterThanOrEqual(2)
  })

  it('应该支持取消操作', async () => {
    const cancelButton = wrapper.findAll('.ant-btn')[0]
    await cancelButton.trigger('click')
    
    expect(wrapper.emitted('close')).toBeTruthy()
  })
})
