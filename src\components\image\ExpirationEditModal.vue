<template>
  <div class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h3 class="modal-title">过期设置</h3>
        <button @click="$emit('close')" class="modal-close">
          <div class="i-heroicons-x-mark w-6 h-6"></div>
        </button>
      </div>

      <div class="modal-body">
        <!-- 图片信息 -->
        <div class="image-info">
          <div class="image-preview">
            <img :src="primaryImageUrl" :alt="image.originalName" @error="handleImageError" class="preview-thumbnail" />
          </div>
          <div class="image-details">
            <h4 class="image-name">{{ image.originalName }}</h4>
            <p class="image-meta">
              {{ formatFileSize(image.size) }} •
              {{ formatDate(image.uploadTime) }}
            </p>
          </div>
        </div>

        <!-- 过期设置表单 -->
        <form @submit.prevent="saveSettings" class="settings-form">
          <!-- 启用过期管理 -->
          <div class="form-section">
            <div class="form-field">
              <div class="field-header">
                <label class="field-label">启用过期管理</label>
                <BaseToggle v-model="settings.enabled" />
              </div>
              <p class="field-hint">
                启用后，系统将监控图片的过期状态并提供提醒
              </p>
            </div>
          </div>

          <!-- 过期时间设置 -->
          <div v-if="settings.enabled" class="form-section">
            <h4 class="section-title">过期时间设置</h4>

            <div class="form-field">
              <label class="field-label">设置方式</label>
              <div class="radio-group">
                <label class="radio-item">
                  <input type="radio" v-model="expirationMode" value="duration" class="radio-input" />
                  <span class="radio-label">按时长设置</span>
                </label>
                <label class="radio-item">
                  <input type="radio" v-model="expirationMode" value="date" class="radio-input" />
                  <span class="radio-label">指定日期</span>
                </label>
              </div>
            </div>

            <!-- 按时长设置 -->
            <div v-if="expirationMode === 'duration'" class="form-field">
              <label class="field-label">过期时长</label>
              <div class="duration-input">
                <BaseInput v-model.number="durationValue" type="number" min="1" max="3650" placeholder="30"
                  class="duration-number" />
                <BaseSelect v-model="durationUnit" :options="durationOptions" class="duration-unit" />
              </div>
              <p class="field-hint">
                从上传时间开始计算，{{ durationValue }} {{ getDurationUnitText(durationUnit) }}后过期
              </p>
            </div>

            <!-- 指定日期 -->
            <div v-if="expirationMode === 'date'" class="form-field">
              <label class="field-label">过期日期</label>
              <BaseInput v-model="expirationDate" type="datetime-local" :min="minDate" class="date-input" />
              <p class="field-hint">
                指定具体的过期时间
              </p>
            </div>

            <!-- 提前通知 -->
            <div class="form-field">
              <label class="field-label">提前通知</label>
              <div class="notify-input">
                <BaseInput v-model.number="settings.notifyBefore" type="number" min="1" max="365" placeholder="7"
                  class="notify-number" />
                <span class="notify-unit">天前提醒</span>
              </div>
              <p class="field-hint">
                在图片过期前 {{ settings.notifyBefore }} 天开始提醒
              </p>
            </div>

            <!-- 过期预览 -->
            <div class="expiration-preview">
              <div class="preview-card">
                <div class="i-heroicons-clock w-5 h-5 text-blue-500 flex-shrink-0"></div>
                <div class="preview-content">
                  <p class="preview-title">过期时间预览</p>
                  <p class="preview-text">
                    {{ getExpirationPreview() }}
                  </p>
                  <p v-if="settings.notifyBefore > 0" class="preview-notify">
                    将在 {{ getNotifyPreview() }} 开始提醒
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- 当前状态 -->
          <div class="current-status">
            <h4 class="section-title">当前状态</h4>
            <div class="status-grid">
              <div class="status-item">
                <span class="status-label">过期管理</span>
                <span class="status-value">
                  {{ image.expirationSettings.enabled ? '已启用' : '已禁用' }}
                </span>
              </div>
              <div v-if="image.expirationSettings.enabled" class="status-item">
                <span class="status-label">过期时间</span>
                <span class="status-value">
                  {{ image.expirationSettings.expiresAt
                    ? formatDate(image.expirationSettings.expiresAt)
                    : '未设置'
                  }}
                </span>
              </div>
              <div v-if="image.expirationSettings.enabled" class="status-item">
                <span class="status-label">剩余时间</span>
                <span class="status-value">
                  {{ getRemainingTime() }}
                </span>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- 操作按钮 -->
      <div class="modal-footer">
        <button @click="$emit('close')" class="btn btn-secondary">
          取消
        </button>
        <button @click="saveSettings" :disabled="saving" class="btn btn-primary">
          <div v-if="saving" class="i-heroicons-arrow-path w-4 h-4 animate-spin mr-2"></div>
          {{ saving ? '保存中...' : '保存设置' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { ImageRecord } from '@/types/imageHost'
import BaseInput from '@/components/ui/BaseInput.vue'
import BaseSelect from '@/components/ui/BaseSelect.vue'
import BaseToggle from '@/components/ui/BaseToggle.vue'

// Props
interface Props {
  image: ImageRecord
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  save: [image: ImageRecord]
}>()

// 响应式数据
const saving = ref(false)
const expirationMode = ref<'duration' | 'date'>('duration')
const durationValue = ref(30)
const durationUnit = ref('days')
const expirationDate = ref('')

// 过期设置
const settings = ref({
  enabled: false,
  expiresAt: undefined as Date | undefined,
  notifyBefore: 7
})

// 选项数据
const durationOptions = [
  { label: '天', value: 'days' },
  { label: '周', value: 'weeks' },
  { label: '月', value: 'months' },
  { label: '年', value: 'years' }
]

// 计算属性
const primaryImageUrl = computed(() => {
  const activeBackup = props.image.backups.find(backup => backup.status === 'active')
  return activeBackup?.url || props.image.backups[0]?.url || ''
})

const minDate = computed(() => {
  const now = new Date()
  now.setMinutes(now.getMinutes() - now.getTimezoneOffset())
  return now.toISOString().slice(0, 16)
})

// 方法
const initializeSettings = () => {
  settings.value = {
    enabled: props.image.expirationSettings.enabled,
    expiresAt: props.image.expirationSettings.expiresAt,
    notifyBefore: props.image.expirationSettings.notifyBefore
  }

  // 如果已有过期时间，设置表单值
  if (settings.value.expiresAt) {
    const expiresAt = new Date(settings.value.expiresAt)
    expirationDate.value = expiresAt.toISOString().slice(0, 16)

    // 计算时长模式的值
    const uploadTime = new Date(props.image.uploadTime)
    const diffMs = expiresAt.getTime() - uploadTime.getTime()
    const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24))

    if (diffDays <= 365) {
      durationValue.value = diffDays
      durationUnit.value = 'days'
    } else {
      durationValue.value = Math.ceil(diffDays / 365)
      durationUnit.value = 'years'
    }
  }
}

const getDurationUnitText = (unit: string): string => {
  const unitMap = {
    days: '天',
    weeks: '周',
    months: '月',
    years: '年'
  }
  return unitMap[unit as keyof typeof unitMap] || unit
}

const calculateExpirationDate = (): Date => {
  const uploadTime = new Date(props.image.uploadTime)
  let expiresAt = new Date(uploadTime)

  switch (durationUnit.value) {
    case 'days':
      expiresAt.setDate(expiresAt.getDate() + durationValue.value)
      break
    case 'weeks':
      expiresAt.setDate(expiresAt.getDate() + durationValue.value * 7)
      break
    case 'months':
      expiresAt.setMonth(expiresAt.getMonth() + durationValue.value)
      break
    case 'years':
      expiresAt.setFullYear(expiresAt.getFullYear() + durationValue.value)
      break
  }

  return expiresAt
}

const getExpirationPreview = (): string => {
  if (!settings.value.enabled) return '未启用过期管理'

  let expiresAt: Date

  if (expirationMode.value === 'duration') {
    expiresAt = calculateExpirationDate()
  } else {
    expiresAt = new Date(expirationDate.value)
  }

  return `将在 ${formatDate(expiresAt)} 过期`
}

const getNotifyPreview = (): string => {
  let expiresAt: Date

  if (expirationMode.value === 'duration') {
    expiresAt = calculateExpirationDate()
  } else {
    expiresAt = new Date(expirationDate.value)
  }

  const notifyAt = new Date(expiresAt.getTime() - settings.value.notifyBefore * 24 * 60 * 60 * 1000)
  return formatDate(notifyAt)
}

const getRemainingTime = (): string => {
  if (!props.image.expirationSettings.enabled || !props.image.expirationSettings.expiresAt) {
    return '无限制'
  }

  const now = new Date()
  const expiresAt = new Date(props.image.expirationSettings.expiresAt)
  const diffMs = expiresAt.getTime() - now.getTime()

  if (diffMs <= 0) {
    return '已过期'
  }

  const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24))

  if (diffDays === 1) {
    return '1天'
  } else if (diffDays < 30) {
    return `${diffDays}天`
  } else if (diffDays < 365) {
    const months = Math.floor(diffDays / 30)
    const days = diffDays % 30
    return days > 0 ? `${months}个月${days}天` : `${months}个月`
  } else {
    const years = Math.floor(diffDays / 365)
    const remainingDays = diffDays % 365
    const months = Math.floor(remainingDays / 30)
    return months > 0 ? `${years}年${months}个月` : `${years}年`
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const handleImageError = (e: Event) => {
  const img = e.target as HTMLImageElement
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMyNCA0IDI4IDggMjggMTJDMjggMTYgMjQgMjAgMjAgMjBDMTYgMjAgMTIgMTYgMTIgMTJDMTIgOCAxNiA0IDIwIDRaIiBmaWxsPSIjOUI5QkEwIi8+Cjwvc3ZnPgo='
}

const saveSettings = async () => {
  saving.value = true

  try {
    // 计算过期时间
    let expiresAt: Date | undefined

    if (settings.value.enabled) {
      if (expirationMode.value === 'duration') {
        expiresAt = calculateExpirationDate()
      } else {
        expiresAt = new Date(expirationDate.value)
      }
    }

    // 创建更新后的图片记录
    const updatedImage: ImageRecord = {
      ...props.image,
      expirationSettings: {
        enabled: settings.value.enabled,
        expiresAt,
        notifyBefore: settings.value.notifyBefore
      },
      updatedAt: new Date()
    }

    emit('save', updatedImage)
  } catch (error) {
    console.error('保存过期设置失败:', error)
    alert('保存设置失败')
  } finally {
    saving.value = false
  }
}

const handleOverlayClick = () => {
  emit('close')
}

// 组件挂载时初始化
onMounted(() => {
  initializeSettings()
})
</script>

<style scoped>
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4;
}

.modal-container {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700;
}

.modal-title {
  @apply text-lg font-semibold text-gray-900 dark:text-gray-100;
}

.modal-close {
  @apply p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors;
}

.modal-body {
  @apply p-6 overflow-y-auto max-h-[calc(90vh-200px)] space-y-6;
}

.modal-footer {
  @apply flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700;
}

.image-info {
  @apply flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg;
}

.image-preview {
  @apply flex-shrink-0;
}

.preview-thumbnail {
  @apply w-16 h-16 object-cover rounded-lg border border-gray-200 dark:border-gray-600;
}

.image-details {
  @apply flex-1 min-w-0;
}

.image-name {
  @apply font-medium text-gray-900 dark:text-gray-100 truncate;
}

.image-meta {
  @apply text-sm text-gray-500 dark:text-gray-400 mt-1;
}

.settings-form {
  @apply space-y-6;
}

.form-section {
  @apply space-y-4;
}

.section-title {
  @apply text-base font-medium text-gray-900 dark:text-gray-100 mb-4;
}

.form-field {
  @apply space-y-2;
}

.field-header {
  @apply flex items-center justify-between;
}

.field-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.field-hint {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

.radio-group {
  @apply flex space-x-6;
}

.radio-item {
  @apply flex items-center cursor-pointer;
}

.radio-input {
  @apply w-4 h-4 text-primary-600 border-gray-300 dark:border-gray-600 focus:ring-primary-500;
}

.radio-label {
  @apply ml-2 text-sm text-gray-700 dark:text-gray-300;
}

.duration-input {
  @apply flex space-x-3;
}

.duration-number {
  @apply flex-1;
}

.duration-unit {
  @apply w-24;
}

.date-input {
  @apply w-full;
}

.notify-input {
  @apply flex items-center space-x-2;
}

.notify-number {
  @apply w-24;
}

.notify-unit {
  @apply text-sm text-gray-700 dark:text-gray-300;
}

.expiration-preview {
  @apply mt-4;
}

.preview-card {
  @apply flex space-x-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg;
}

.preview-content {
  @apply space-y-1;
}

.preview-title {
  @apply text-sm font-medium text-blue-900 dark:text-blue-100;
}

.preview-text {
  @apply text-sm text-blue-700 dark:text-blue-300;
}

.preview-notify {
  @apply text-xs text-blue-600 dark:text-blue-400;
}

.current-status {
  @apply space-y-3;
}

.status-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 gap-4;
}

.status-item {
  @apply space-y-1;
}

.status-label {
  @apply text-sm font-medium text-gray-500 dark:text-gray-400;
}

.status-value {
  @apply text-sm text-gray-900 dark:text-gray-100;
}

.btn {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-primary {
  @apply text-white bg-primary-600 hover:bg-primary-700 focus:ring-primary-500;
}

.btn-secondary {
  @apply text-gray-700 bg-gray-100 hover:bg-gray-200 focus:ring-gray-500;
  @apply dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}
</style>
