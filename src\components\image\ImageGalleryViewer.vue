<template>
  <div class="image-gallery-viewer">
    <!-- 工具栏 -->
    <div v-if="showToolbar" class="gallery-toolbar">
      <div class="toolbar-left">
        <span class="image-count">共 {{ images.length }} 张图片</span>
      </div>
      <div class="toolbar-right">
        <!-- 视图切换 -->
        <a-radio-group v-model:value="viewMode" size="small">
          <a-radio-button value="grid">
            <div class="i-heroicons-squares-2x2 w-4 h-4"></div>
          </a-radio-button>
          <a-radio-button value="list">
            <div class="i-heroicons-list-bullet w-4 h-4"></div>
          </a-radio-button>
        </a-radio-group>
        
        <!-- 排序 -->
        <a-select v-model:value="sortBy" size="small" style="width: 120px">
          <a-select-option value="name">按名称</a-select-option>
          <a-select-option value="size">按大小</a-select-option>
          <a-select-option value="date">按日期</a-select-option>
        </a-select>
        
        <!-- 批量操作 -->
        <a-dropdown v-if="selectedImages.length > 0">
          <a-button size="small">
            批量操作 ({{ selectedImages.length }})
            <div class="i-heroicons-chevron-down w-4 h-4 ml-1"></div>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item @click="batchDownload">
                <div class="i-heroicons-arrow-down-tray w-4 h-4 mr-2"></div>
                批量下载
              </a-menu-item>
              <a-menu-item @click="batchDelete" danger>
                <div class="i-heroicons-trash w-4 h-4 mr-2"></div>
                批量删除
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </div>

    <!-- 图片查看器 -->
    <ImageViewer
      :images="sortedImages"
      :columns="viewMode === 'grid' ? 'auto' : 1"
      :show-info="showInfo"
      :show-delete="showDelete"
      :aspect-ratio="viewMode === 'grid' ? 1 : 'auto'"
      @edit="handleEdit"
      @delete="handleDelete"
      @preview="handlePreview"
    />

    <!-- 图片编辑器 -->
    <ImageEditor
      v-model:visible="editorVisible"
      :image-src="editingImage?.src || ''"
      :image-name="editingImage?.name || ''"
      @save="handleSave"
      @cancel="handleCancel"
    />

    <!-- 批量选择模式 -->
    <div v-if="selectionMode" class="selection-overlay">
      <div class="selection-toolbar">
        <a-button @click="selectAll">全选</a-button>
        <a-button @click="clearSelection">清空</a-button>
        <a-button @click="exitSelectionMode">退出选择</a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import ImageViewer, { type ImageItem } from './ImageViewer.vue'
import ImageEditor from './ImageEditor.vue'

// Props 定义
interface Props {
  images: ImageItem[]
  showToolbar?: boolean
  showInfo?: boolean
  showDelete?: boolean
  selectionMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showToolbar: true,
  showInfo: true,
  showDelete: true,
  selectionMode: false
})

// Emits 定义
const emit = defineEmits<{
  edit: [image: ImageItem, index: number]
  delete: [image: ImageItem, index: number]
  save: [result: { canvas: HTMLCanvasElement; blob: Blob; dataUrl: string }, originalImage: ImageItem]
  batchDelete: [images: ImageItem[]]
  batchDownload: [images: ImageItem[]]
}>()

// 响应式数据
const viewMode = ref<'grid' | 'list'>('grid')
const sortBy = ref<'name' | 'size' | 'date'>('name')
const selectedImages = ref<ImageItem[]>([])
const editorVisible = ref(false)
const editingImage = ref<ImageItem | null>(null)
const editingIndex = ref(-1)

// 计算属性
const sortedImages = computed(() => {
  const sorted = [...props.images]
  
  switch (sortBy.value) {
    case 'name':
      return sorted.sort((a, b) => (a.name || '').localeCompare(b.name || ''))
    case 'size':
      return sorted.sort((a, b) => (b.size || 0) - (a.size || 0))
    case 'date':
      // 假设有创建时间字段
      return sorted.sort((a, b) => {
        const aTime = (a as any).createdAt || 0
        const bTime = (b as any).createdAt || 0
        return bTime - aTime
      })
    default:
      return sorted
  }
})

// 方法
const handleEdit = (image: ImageItem, index: number) => {
  editingImage.value = image
  editingIndex.value = index
  editorVisible.value = true
  emit('edit', image, index)
}

const handleDelete = (image: ImageItem, index: number) => {
  emit('delete', image, index)
}

const handlePreview = (image: ImageItem, index: number) => {
  // PhotoSwipe 预览已在 ImageViewer 中处理
}

const handleSave = (result: { canvas: HTMLCanvasElement; blob: Blob; dataUrl: string }) => {
  if (editingImage.value) {
    emit('save', result, editingImage.value)
  }
}

const handleCancel = () => {
  editingImage.value = null
  editingIndex.value = -1
}

const selectAll = () => {
  selectedImages.value = [...props.images]
}

const clearSelection = () => {
  selectedImages.value = []
}

const exitSelectionMode = () => {
  selectedImages.value = []
}

const batchDownload = () => {
  emit('batchDownload', selectedImages.value)
}

const batchDelete = () => {
  emit('batchDelete', selectedImages.value)
  selectedImages.value = []
}

// 监听器
watch(() => props.selectionMode, (newVal) => {
  if (!newVal) {
    selectedImages.value = []
  }
})
</script>

<style scoped>
.image-gallery-viewer {
  @apply relative;
}

.gallery-toolbar {
  @apply flex justify-between items-center mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm;
}

.toolbar-left {
  @apply flex items-center space-x-4;
}

.toolbar-right {
  @apply flex items-center space-x-3;
}

.image-count {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.selection-overlay {
  @apply fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50;
}

.selection-toolbar {
  @apply flex space-x-2 p-3 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700;
}

/* 列表视图样式 */
.list-view .image-container {
  @apply flex items-center p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm mb-3;
  aspect-ratio: auto;
}

.list-view .image-preview {
  @apply w-20 h-20 object-cover rounded mr-4;
}

.list-view .image-info {
  @apply flex-1 static bg-transparent p-0 text-gray-900 dark:text-gray-100;
}

.list-view .image-actions {
  @apply ml-auto;
}

.list-view .image-overlay {
  @apply static opacity-100 bg-transparent;
}
</style>
