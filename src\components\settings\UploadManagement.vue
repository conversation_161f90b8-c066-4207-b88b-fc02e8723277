<template>
  <div class="upload-management">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">上传管理设置</h2>
      <p class="text-gray-600 dark:text-gray-400">配置图片上传的默认行为和链接检测设置</p>
    </div>

    <!-- 设置卡片 -->
    <div class="space-y-6">
      <!-- 默认上传设置 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center mb-6">
          <div class="i-heroicons-cloud-arrow-up w-6 h-6 text-blue-600 mr-3"></div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">默认上传设置</h3>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- 默认上传图床数量 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              默认上传图床数量
            </label>
            <select v-model="settings.defaultHostCount" 
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
              <option value="1">1个图床</option>
              <option value="2">2个图床</option>
              <option value="3">3个图床</option>
              <option value="4">4个图床</option>
              <option value="5">5个图床</option>
            </select>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              每张图片默认上传到几个不同的图床进行备份
            </p>
          </div>

          <!-- 图床选择策略 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              图床选择策略
            </label>
            <select v-model="settings.hostSelectionStrategy" 
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
              <option value="random">随机选择</option>
              <option value="priority">按优先级</option>
              <option value="speed">按速度优先</option>
              <option value="reliability">按可靠性</option>
            </select>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              选择多个图床时的选择策略
            </p>
          </div>

          <!-- 上传失败重试 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              上传失败重试次数
            </label>
            <select v-model="settings.maxRetries" 
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
              <option value="0">不重试</option>
              <option value="1">重试1次</option>
              <option value="2">重试2次</option>
              <option value="3">重试3次</option>
              <option value="5">重试5次</option>
            </select>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              上传失败时的自动重试次数
            </p>
          </div>

          <!-- 重试间隔 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              重试间隔（秒）
            </label>
            <input v-model.number="settings.retryDelay" type="number" min="1" max="60"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              重试之间的等待时间
            </p>
          </div>
        </div>
      </div>

      <!-- 链接失效检测设置 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center">
            <div class="i-heroicons-shield-check w-6 h-6 text-green-600 mr-3"></div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">链接失效检测</h3>
          </div>
          <div class="flex items-center">
            <label class="text-sm text-gray-700 dark:text-gray-300 mr-3">启用检测</label>
            <button @click="settings.linkCheckEnabled = !settings.linkCheckEnabled"
              :class="[
                'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
                settings.linkCheckEnabled 
                  ? 'bg-blue-600' 
                  : 'bg-gray-200 dark:bg-gray-600'
              ]">
              <span :class="[
                'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
                settings.linkCheckEnabled ? 'translate-x-6' : 'translate-x-1'
              ]"></span>
            </button>
          </div>
        </div>

        <div v-if="settings.linkCheckEnabled" class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- 检测间隔 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              检测间隔
            </label>
            <select v-model="settings.checkInterval" 
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
              <option value="1">每天</option>
              <option value="3">每3天</option>
              <option value="7">每周</option>
              <option value="14">每2周</option>
              <option value="30">每月</option>
            </select>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              自动检测链接有效性的频率
            </p>
          </div>

          <!-- 检测超时时间 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              检测超时时间（秒）
            </label>
            <input v-model.number="settings.checkTimeout" type="number" min="5" max="60"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              单个链接检测的超时时间
            </p>
          </div>

          <!-- 失效处理策略 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              失效处理策略
            </label>
            <select v-model="settings.failureAction" 
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
              <option value="mark">仅标记失效</option>
              <option value="notify">标记并通知</option>
              <option value="backup">自动创建备份</option>
              <option value="remove">移除失效链接</option>
            </select>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              发现链接失效时的处理方式
            </p>
          </div>

          <!-- 通知设置 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              失效通知方式
            </label>
            <div class="space-y-2">
              <label class="flex items-center">
                <input v-model="settings.notifyMethods" value="browser" type="checkbox"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">浏览器通知</span>
              </label>
              <label class="flex items-center">
                <input v-model="settings.notifyMethods" value="console" type="checkbox"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">控制台日志</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- 链接过期设置 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center mb-6">
          <div class="i-heroicons-clock w-6 h-6 text-orange-600 mr-3"></div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">链接过期设置</h3>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- 默认过期时间 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              默认过期时间
            </label>
            <select v-model="settings.defaultExpiration" 
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
              <option value="0">永不过期</option>
              <option value="7">7天</option>
              <option value="30">30天</option>
              <option value="90">90天</option>
              <option value="180">180天</option>
              <option value="365">1年</option>
            </select>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              新上传图片的默认过期时间
            </p>
          </div>

          <!-- 过期提醒时间 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              过期提醒时间（天）
            </label>
            <input v-model.number="settings.expirationWarning" type="number" min="1" max="30"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              在过期前多少天开始提醒
            </p>
          </div>

          <!-- 过期处理策略 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              过期处理策略
            </label>
            <select v-model="settings.expirationAction" 
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
              <option value="keep">保留记录</option>
              <option value="archive">归档处理</option>
              <option value="delete">自动删除</option>
            </select>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              图片过期后的处理方式
            </p>
          </div>

          <!-- 自动续期 -->
          <div class="flex items-center justify-between">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                自动续期
              </label>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                在过期前自动延长有效期
              </p>
            </div>
            <button @click="settings.autoRenew = !settings.autoRenew"
              :class="[
                'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
                settings.autoRenew 
                  ? 'bg-blue-600' 
                  : 'bg-gray-200 dark:bg-gray-600'
              ]">
              <span :class="[
                'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
                settings.autoRenew ? 'translate-x-6' : 'translate-x-1'
              ]"></span>
            </button>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-end space-x-4">
        <button @click="resetSettings" 
          class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors">
          重置默认
        </button>
        <button @click="saveSettings" :disabled="saving"
          class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50">
          <div v-if="saving" class="i-heroicons-arrow-path w-4 h-4 animate-spin mr-2 inline-block"></div>
          {{ saving ? '保存中...' : '保存设置' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { uploadSettingsService } from '@/services/uploadSettingsService'
import { useToast } from '@/composables/useToast'

const { success, error } = useToast()

// 响应式数据
const saving = ref(false)

// 上传设置
const settings = ref({
  // 默认上传设置
  defaultHostCount: 2,
  hostSelectionStrategy: 'random' as 'random' | 'priority' | 'speed' | 'reliability',
  maxRetries: 3,
  retryDelay: 2,
  
  // 链接检测设置
  linkCheckEnabled: true,
  checkInterval: 7,
  checkTimeout: 15,
  failureAction: 'notify' as 'mark' | 'notify' | 'backup' | 'remove',
  notifyMethods: ['browser'] as string[],
  
  // 过期设置
  defaultExpiration: 0,
  expirationWarning: 7,
  expirationAction: 'keep' as 'keep' | 'archive' | 'delete',
  autoRenew: false
})

// 保存设置
const saveSettings = async () => {
  if (saving.value) return
  
  try {
    saving.value = true
    await uploadSettingsService.saveSettings(settings.value)
    success('设置保存成功', '上传管理设置已更新')
  } catch (err) {
    console.error('保存设置失败:', err)
    error('保存失败', '无法保存上传管理设置')
  } finally {
    saving.value = false
  }
}

// 重置设置
const resetSettings = () => {
  settings.value = {
    defaultHostCount: 2,
    hostSelectionStrategy: 'random',
    maxRetries: 3,
    retryDelay: 2,
    linkCheckEnabled: true,
    checkInterval: 7,
    checkTimeout: 15,
    failureAction: 'notify',
    notifyMethods: ['browser'],
    defaultExpiration: 0,
    expirationWarning: 7,
    expirationAction: 'keep',
    autoRenew: false
  }
}

// 加载设置
const loadSettings = async () => {
  try {
    const savedSettings = await uploadSettingsService.getSettings()
    if (savedSettings) {
      settings.value = { ...settings.value, ...savedSettings }
    }
  } catch (err) {
    console.error('加载设置失败:', err)
  }
}

// 组件挂载时加载设置
onMounted(() => {
  loadSettings()
})
</script>
