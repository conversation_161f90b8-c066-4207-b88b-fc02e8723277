<template>
  <div v-if="visible" class="fixed inset-0 z-50 overflow-y-auto">
    <!-- 背景遮罩 -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" @click="close"></div>
    
    <!-- 模态框内容 -->
    <div class="flex min-h-full items-center justify-center p-4">
      <div class="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-2xl w-full max-h-[80vh] overflow-y-auto">
        <!-- 头部 -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            GitHub云存储配置说明
          </h3>
          <button
            @click="close"
            class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <div class="i-heroicons-x-mark w-6 h-6"></div>
          </button>
        </div>

        <!-- 内容 -->
        <div class="p-6 space-y-6">
          <!-- 概述 -->
          <div>
            <h4 class="text-base font-medium text-gray-900 dark:text-gray-100 mb-3">
              <div class="i-heroicons-cloud-arrow-up inline-block w-5 h-5 mr-2 text-primary-500"></div>
              功能概述
            </h4>
            <p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
              GitHub云存储功能允许您将知识库数据安全地备份到您的GitHub账户中。通过GitHub Gist服务，
              您可以实现数据的云端同步、跨设备访问和自动备份。所有数据都存储在您自己的GitHub账户中，
              确保数据隐私和安全。
            </p>
          </div>

          <!-- 创建Token步骤 -->
          <div>
            <h4 class="text-base font-medium text-gray-900 dark:text-gray-100 mb-3">
              <div class="i-heroicons-key inline-block w-5 h-5 mr-2 text-primary-500"></div>
              创建GitHub Personal Access Token
            </h4>
            <div class="space-y-3">
              <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <ol class="list-decimal list-inside space-y-2 text-sm text-gray-700 dark:text-gray-300">
                  <li>访问GitHub设置页面：
                    <a href="https://github.com/settings/tokens" target="_blank" 
                       class="text-primary-600 dark:text-primary-400 hover:underline ml-1">
                      https://github.com/settings/tokens
                    </a>
                  </li>
                  <li>点击 "Generate new token" → "Generate new token (classic)"</li>
                  <li>填写Token描述，例如："Knowledge Base Backup"</li>
                  <li>设置过期时间（建议选择"No expiration"或较长时间）</li>
                  <li>在权限列表中<strong>只勾选 "gist"</strong> 权限</li>
                  <li>点击 "Generate token" 生成Token</li>
                  <li>复制生成的Token（注意：离开页面后将无法再次查看）</li>
                </ol>
              </div>
              
              <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-3">
                <div class="flex items-start">
                  <div class="i-heroicons-exclamation-triangle w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5"></div>
                  <div class="text-sm text-yellow-700 dark:text-yellow-300">
                    <strong>重要提醒：</strong>Token生成后请立即复制并保存，GitHub不会再次显示完整的Token内容。
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 权限说明 -->
          <div>
            <h4 class="text-base font-medium text-gray-900 dark:text-gray-100 mb-3">
              <div class="i-heroicons-shield-check inline-block w-5 h-5 mr-2 text-primary-500"></div>
              权限说明
            </h4>
            <div class="space-y-3">
              <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4">
                <h5 class="text-sm font-medium text-green-800 dark:text-green-200 mb-2">
                  所需权限：gist
                </h5>
                <ul class="list-disc list-inside space-y-1 text-sm text-green-700 dark:text-green-300">
                  <li>创建私有Gist用于存储备份数据</li>
                  <li>读取和更新您的Gist内容</li>
                  <li>管理您的Gist（删除、修改描述等）</li>
                </ul>
              </div>
              
              <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-3">
                <div class="flex items-start">
                  <div class="i-heroicons-information-circle w-5 h-5 text-blue-600 dark:text-blue-400 mr-2 mt-0.5"></div>
                  <div class="text-sm text-blue-700 dark:text-blue-300">
                    我们只需要最小权限来确保您的账户安全。不需要访问您的代码仓库、个人信息或其他敏感数据。
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 数据安全说明 -->
          <div>
            <h4 class="text-base font-medium text-gray-900 dark:text-gray-100 mb-3">
              <div class="i-heroicons-lock-closed inline-block w-5 h-5 mr-2 text-primary-500"></div>
              数据安全与隐私
            </h4>
            <div class="space-y-3">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                  <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    <div class="i-heroicons-eye-slash inline-block w-4 h-4 mr-1"></div>
                    私有存储
                  </h5>
                  <p class="text-xs text-gray-600 dark:text-gray-400">
                    所有备份都创建为私有Gist，只有您可以访问
                  </p>
                </div>
                
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                  <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    <div class="i-heroicons-server inline-block w-4 h-4 mr-1"></div>
                    本地加密
                  </h5>
                  <p class="text-xs text-gray-600 dark:text-gray-400">
                    Token在本地加密存储，不会发送到第三方服务器
                  </p>
                </div>
                
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                  <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    <div class="i-heroicons-user-circle inline-block w-4 h-4 mr-1"></div>
                    完全控制
                  </h5>
                  <p class="text-xs text-gray-600 dark:text-gray-400">
                    您拥有数据的完全控制权，可随时删除或修改
                  </p>
                </div>
                
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                  <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    <div class="i-heroicons-trash inline-block w-4 h-4 mr-1"></div>
                    随时断开
                  </h5>
                  <p class="text-xs text-gray-600 dark:text-gray-400">
                    可随时断开连接并删除本地存储的Token
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- 常见问题 -->
          <div>
            <h4 class="text-base font-medium text-gray-900 dark:text-gray-100 mb-3">
              <div class="i-heroicons-question-mark-circle inline-block w-5 h-5 mr-2 text-primary-500"></div>
              常见问题
            </h4>
            <div class="space-y-3">
              <details class="bg-gray-50 dark:bg-gray-700 rounded-lg">
                <summary class="p-3 cursor-pointer text-sm font-medium text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg">
                  Token验证失败怎么办？
                </summary>
                <div class="px-3 pb-3 text-sm text-gray-600 dark:text-gray-400">
                  <ul class="list-disc list-inside space-y-1">
                    <li>检查Token是否正确复制（没有多余的空格）</li>
                    <li>确认Token具有"gist"权限</li>
                    <li>检查Token是否已过期</li>
                    <li>尝试重新生成Token</li>
                  </ul>
                </div>
              </details>
              
              <details class="bg-gray-50 dark:bg-gray-700 rounded-lg">
                <summary class="p-3 cursor-pointer text-sm font-medium text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg">
                  数据会被其他人看到吗？
                </summary>
                <div class="px-3 pb-3 text-sm text-gray-600 dark:text-gray-400">
                  不会。所有备份都创建为私有Gist，只有您登录GitHub账户后才能查看。即使有人知道Gist ID，也无法访问私有内容。
                </div>
              </details>
              
              <details class="bg-gray-50 dark:bg-gray-700 rounded-lg">
                <summary class="p-3 cursor-pointer text-sm font-medium text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg">
                  如何删除云端备份？
                </summary>
                <div class="px-3 pb-3 text-sm text-gray-600 dark:text-gray-400">
                  您可以直接在GitHub网站上删除对应的Gist，或者在本应用中断开连接后，Gist将不再被更新（但不会自动删除）。
                </div>
              </details>
              
              <details class="bg-gray-50 dark:bg-gray-700 rounded-lg">
                <summary class="p-3 cursor-pointer text-sm font-medium text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg">
                  自动上传功能安全吗？
                </summary>
                <div class="px-3 pb-3 text-sm text-gray-600 dark:text-gray-400">
                  是的。自动上传只在浏览器标签页打开时工作，不会在后台运行。所有数据传输都通过HTTPS加密，且只上传到您自己的GitHub账户。
                </div>
              </details>
            </div>
          </div>
        </div>

        <!-- 底部 -->
        <div class="flex justify-end p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            @click="close"
            class="btn-primary"
          >
            <div class="i-heroicons-check w-4 h-4 mr-2"></div>
            我已了解
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  visible: boolean
}

interface Emits {
  (e: 'close'): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const close = () => {
  emit('close')
}
</script>
