<template>
  <div class="relative">
    <!-- 已选择标签显示 -->
    <div v-if="selectedTags.length > 0" class="flex flex-wrap gap-1 mb-2">
      <span v-for="tag in selectedTags" :key="tag.id"
        class="inline-flex items-center px-2 py-1 bg-primary-100 dark:bg-primary-800/30 text-primary-700 dark:text-primary-300 text-xs rounded-md">
        {{ tag.name }}
        <button @click="removeTag(tag.id)" class="ml-1 text-primary-500 hover:text-primary-700 transition-colors">
          <div class="i-heroicons-x-mark w-3 h-3"></div>
        </button>
      </span>
    </div>

    <!-- 搜索输入框 -->
    <div class="relative">
      <BaseInput ref="searchInput" v-model="searchQuery" type="text" :placeholder="placeholder"
        @focus="showDropdown = true" @blur="handleBlur" @keydown="handleKeydown" size="sm" clearable />
      <div class="absolute inset-y-0 right-0 flex items-center pr-3">
        <div class="i-heroicons-chevron-down w-4 h-4 text-gray-400"></div>
      </div>
    </div>

    <!-- 下拉选项 -->
    <Transition enter-active-class="transition ease-out duration-100" enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100" leave-active-class="transition ease-in duration-75"
      leave-from-class="transform opacity-100 scale-100" leave-to-class="transform opacity-0 scale-95">
      <div v-if="showDropdown && filteredTags.length > 0"
        class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-auto">
        <div v-for="(tag, index) in filteredTags" :key="tag.id" @click="selectTag(tag)" :class="[
          'px-3 py-2 cursor-pointer transition-colors',
          index === highlightedIndex
            ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
            : 'text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700'
        ]">
          <div class="flex items-center justify-between">
            <span class="text-sm">{{ tag.name }}</span>
            <span class="text-xs text-gray-500 dark:text-gray-400">
              {{ tag.resource_count }}
            </span>
          </div>
        </div>

        <!-- 创建新标签选项 -->
        <div v-if="searchQuery && !exactMatch" @click="createTag" :class="[
          'px-3 py-2 cursor-pointer transition-colors border-t border-gray-200 dark:border-gray-600',
          highlightedIndex === filteredTags.length
            ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
            : 'text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700'
        ]">
          <div class="flex items-center">
            <div class="i-heroicons-plus w-4 h-4 mr-2"></div>
            <span class="text-sm">创建标签 "{{ searchQuery }}"</span>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import BaseInput from '@/components/ui/BaseInput.vue'

interface Tag {
  id: number
  name: string
  resource_count: number
}

interface Props {
  modelValue: number[]
  availableTags: Tag[]
  placeholder?: string
  maxTags?: number
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '搜索或选择标签',
  maxTags: 10
})

const emit = defineEmits<{
  'update:modelValue': [value: number[]]
  'create-tag': [name: string]
}>()

const searchInput = ref<HTMLInputElement>()
const searchQuery = ref('')
const showDropdown = ref(false)
const highlightedIndex = ref(-1)

// 计算属性
const selectedTags = computed(() => {
  return props.availableTags.filter(tag => props.modelValue.includes(tag.id))
})

const filteredTags = computed(() => {
  if (!searchQuery.value) {
    return props.availableTags.filter(tag => !props.modelValue.includes(tag.id))
  }

  return props.availableTags.filter(tag =>
    !props.modelValue.includes(tag.id) &&
    tag.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const exactMatch = computed(() => {
  return props.availableTags.some(tag =>
    tag.name.toLowerCase() === searchQuery.value.toLowerCase()
  )
})

const canAddMore = computed(() => {
  return props.modelValue.length < props.maxTags
})

// 方法
const selectTag = (tag: Tag) => {
  if (!canAddMore.value) return

  const newValue = [...props.modelValue, tag.id]
  emit('update:modelValue', newValue)

  searchQuery.value = ''
  showDropdown.value = false
  highlightedIndex.value = -1
}

const removeTag = (tagId: number) => {
  const newValue = props.modelValue.filter(id => id !== tagId)
  emit('update:modelValue', newValue)
}

const createTag = () => {
  if (!searchQuery.value.trim() || exactMatch.value) return

  emit('create-tag', searchQuery.value.trim())
  searchQuery.value = ''
  showDropdown.value = false
  highlightedIndex.value = -1
}

const handleBlur = () => {
  // 延迟关闭下拉框，以便点击事件能够触发
  setTimeout(() => {
    showDropdown.value = false
    highlightedIndex.value = -1
  }, 150)
}

const handleKeydown = (event: KeyboardEvent) => {
  const totalOptions = filteredTags.value.length + (searchQuery.value && !exactMatch.value ? 1 : 0)

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      highlightedIndex.value = Math.min(highlightedIndex.value + 1, totalOptions - 1)
      break
    case 'ArrowUp':
      event.preventDefault()
      highlightedIndex.value = Math.max(highlightedIndex.value - 1, -1)
      break
    case 'Enter':
      event.preventDefault()
      if (highlightedIndex.value >= 0) {
        if (highlightedIndex.value < filteredTags.value.length) {
          selectTag(filteredTags.value[highlightedIndex.value])
        } else {
          createTag()
        }
      }
      break
    case 'Escape':
      showDropdown.value = false
      highlightedIndex.value = -1
      searchInput.value?.blur()
      break
    case 'Backspace':
      if (!searchQuery.value && selectedTags.value.length > 0) {
        removeTag(selectedTags.value[selectedTags.value.length - 1].id)
      }
      break
  }
}

// 监听搜索查询变化
watch(searchQuery, () => {
  highlightedIndex.value = -1
  if (searchQuery.value) {
    showDropdown.value = true
  }
})

// 监听下拉框显示状态
watch(showDropdown, async (show) => {
  if (show) {
    await nextTick()
    highlightedIndex.value = -1
  }
})
</script>
