<template>
  <div class="space-y-1">
    <label v-if="label" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>
    
    <div :class="[
      direction === 'horizontal' ? 'flex flex-wrap gap-4' : 'space-y-2'
    ]">
      <label
        v-for="option in options"
        :key="getOptionValue(option)"
        class="flex items-center cursor-pointer"
      >
        <input
          :value="getOptionValue(option)"
          :checked="modelValue === getOptionValue(option)"
          :disabled="disabled || getOptionDisabled(option)"
          type="radio"
          :class="[
            'w-4 h-4 border-gray-300 dark:border-gray-600 transition-colors',
            'text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-400',
            'bg-gray-100 dark:bg-gray-700',
            'focus:ring-2 focus:ring-offset-0',
            disabled || getOptionDisabled(option) ? 'opacity-50 cursor-not-allowed' : '',
            error ? 'border-red-300 dark:border-red-600' : ''
          ]"
          @change="handleChange"
        />
        <span :class="[
          'ml-2 text-sm',
          disabled || getOptionDisabled(option) 
            ? 'text-gray-400 dark:text-gray-500' 
            : 'text-gray-700 dark:text-gray-300'
        ]">
          {{ getOptionLabel(option) }}
        </span>
      </label>
    </div>
    
    <p v-if="hint && !error" class="text-xs text-gray-500 dark:text-gray-400">
      {{ hint }}
    </p>
    
    <p v-if="error" class="text-xs text-red-600 dark:text-red-400">
      {{ error }}
    </p>
  </div>
</template>

<script setup lang="ts">
// 选项类型
interface Option {
  label: string
  value: string | number
  disabled?: boolean
}

// Props
interface Props {
  modelValue?: string | number
  label?: string
  hint?: string
  error?: string
  disabled?: boolean
  required?: boolean
  direction?: 'vertical' | 'horizontal'
  options?: Option[] | string[] | number[]
  valueKey?: string
  labelKey?: string
  disabledKey?: string
}

const props = withDefaults(defineProps<Props>(), {
  direction: 'vertical',
  valueKey: 'value',
  labelKey: 'label',
  disabledKey: 'disabled'
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string | number]
  'change': [value: string | number]
}>()

// 获取选项值
const getOptionValue = (option: any): string | number => {
  if (typeof option === 'object' && option !== null) {
    return option[props.valueKey]
  }
  return option
}

// 获取选项标签
const getOptionLabel = (option: any): string => {
  if (typeof option === 'object' && option !== null) {
    return option[props.labelKey]
  }
  return String(option)
}

// 获取选项禁用状态
const getOptionDisabled = (option: any): boolean => {
  if (typeof option === 'object' && option !== null) {
    return Boolean(option[props.disabledKey])
  }
  return false
}

// 事件处理
const handleChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  let value: string | number = target.value
  
  // 尝试转换为数字（如果原始值是数字类型）
  if (props.options && props.options.length > 0) {
    const firstOption = props.options[0]
    const firstValue = getOptionValue(firstOption)
    if (typeof firstValue === 'number') {
      value = Number(value)
    }
  }
  
  emit('update:modelValue', value)
  emit('change', value)
}
</script>
