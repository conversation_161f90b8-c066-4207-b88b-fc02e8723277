<template>
  <div class="relative" ref="dropdownRef">
    <!-- 触发器 -->
    <div 
      @click="toggle" 
      :class="[
        'flex items-center justify-between px-4 py-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl cursor-pointer transition-all duration-200',
        'hover:border-primary-300 dark:hover:border-primary-600',
        'focus-within:border-primary-500 focus-within:ring-2 focus-within:ring-primary-200 dark:focus-within:ring-primary-800',
        isOpen ? 'border-primary-500 ring-2 ring-primary-200 dark:ring-primary-800' : ''
      ]"
    >
      <div class="flex items-center space-x-3">
        <!-- 搜索图标 -->
        <div class="i-heroicons-magnifying-glass w-5 h-5 text-gray-400"></div>
        
        <!-- 显示文本 -->
        <span :class="[
          'text-sm',
          selectedOption ? 'text-gray-900 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'
        ]">
          {{ selectedOption ? getOptionLabel(selectedOption) : placeholder }}
        </span>
      </div>
      
      <!-- 下拉箭头 -->
      <div :class="[
        'i-heroicons-chevron-down w-5 h-5 text-gray-400 transition-transform duration-200',
        isOpen ? 'rotate-180' : ''
      ]"></div>
    </div>

    <!-- 下拉菜单 -->
    <Transition
      enter-active-class="transition-all duration-200"
      enter-from-class="opacity-0 scale-95 translate-y-1"
      enter-to-class="opacity-100 scale-100 translate-y-0"
      leave-active-class="transition-all duration-200"
      leave-from-class="opacity-100 scale-100 translate-y-0"
      leave-to-class="opacity-0 scale-95 translate-y-1"
    >
      <div 
        v-if="isOpen" 
        :class="[
          'absolute z-50 mt-2 w-full bg-white dark:bg-gray-800 rounded-xl shadow-lg shadow-primary-200/50 dark:shadow-primary-900/30 border border-primary-200 dark:border-primary-700',
          'py-2 max-h-64 overflow-y-auto'
        ]"
        @click.stop
      >
        <!-- 搜索框 -->
        <div v-if="searchable" class="px-3 pb-2">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <div class="i-heroicons-magnifying-glass w-4 h-4 text-gray-400"></div>
            </div>
            <input
              v-model="searchQuery"
              type="text"
              :placeholder="searchPlaceholder"
              class="block w-full pl-10 pr-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg text-sm bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              @click.stop
            />
          </div>
        </div>
        
        <!-- 选项列表 -->
        <div class="space-y-1">
          <!-- 无分类选项 -->
          <div
            v-if="showNoCategory"
            @click="selectOption(null)"
            :class="[
              'flex items-center px-4 py-2 text-sm cursor-pointer transition-colors',
              'hover:bg-primary-50 dark:hover:bg-primary-900/20',
              !modelValue ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300' : 'text-gray-700 dark:text-gray-300'
            ]"
          >
            <div class="i-heroicons-check w-4 h-4 mr-3 text-primary-600" v-if="!modelValue"></div>
            <div class="w-4 h-4 mr-3" v-else></div>
            <span>{{ noCategoryText }}</span>
          </div>
          
          <!-- 分类选项 -->
          <template v-for="option in filteredOptions" :key="getOptionValue(option)">
            <div
              @click="selectOption(option)"
              :class="[
                'flex items-center px-4 py-2 text-sm cursor-pointer transition-colors',
                'hover:bg-primary-50 dark:hover:bg-primary-900/20',
                getOptionValue(option) === modelValue ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300' : 'text-gray-700 dark:text-gray-300',
                getOptionDisabled(option) ? 'opacity-50 cursor-not-allowed' : ''
              ]"
            >
              <div class="i-heroicons-check w-4 h-4 mr-3 text-primary-600" v-if="getOptionValue(option) === modelValue"></div>
              <div class="w-4 h-4 mr-3" v-else></div>
              
              <!-- 层级缩进 -->
              <div v-if="getOptionLevel && getOptionLevel(option) > 0" :style="{ marginLeft: `${getOptionLevel(option) * 16}px` }"></div>
              
              <!-- 文件夹图标 -->
              <div class="i-heroicons-folder w-4 h-4 mr-2 text-gray-400"></div>
              
              <span>{{ getOptionLabel(option) }}</span>
            </div>
          </template>
        </div>
        
        <!-- 无结果提示 -->
        <div v-if="filteredOptions.length === 0 && searchQuery" class="px-4 py-3 text-sm text-gray-500 dark:text-gray-400 text-center">
          未找到匹配的分类
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 选项类型
interface Option {
  label: string
  value: string | number
  disabled?: boolean
  level?: number
}

// Props
interface Props {
  modelValue?: string | number | null
  placeholder?: string
  searchPlaceholder?: string
  options?: Option[] | string[] | number[]
  valueKey?: string
  labelKey?: string
  disabledKey?: string
  levelKey?: string
  searchable?: boolean
  showNoCategory?: boolean
  noCategoryText?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '选择分类',
  searchPlaceholder: '搜索分类...',
  valueKey: 'value',
  labelKey: 'label',
  disabledKey: 'disabled',
  levelKey: 'level',
  searchable: true,
  showNoCategory: true,
  noCategoryText: '无分类 (根分类)'
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string | number | null]
  'change': [value: string | number | null]
}>()

// 响应式数据
const dropdownRef = ref<HTMLElement>()
const isOpen = ref(false)
const searchQuery = ref('')

// 计算属性
const selectedOption = computed(() => {
  if (!props.modelValue) return null
  return props.options?.find(option => getOptionValue(option) === props.modelValue) || null
})

const filteredOptions = computed(() => {
  if (!props.options) return []
  
  let filtered = props.options
  
  // 搜索过滤
  if (searchQuery.value && props.searchable) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(option => 
      getOptionLabel(option).toLowerCase().includes(query)
    )
  }
  
  return filtered
})

// 工具方法
const getOptionValue = (option: any): string | number => {
  if (typeof option === 'string' || typeof option === 'number') return option
  return option[props.valueKey]
}

const getOptionLabel = (option: any): string => {
  if (typeof option === 'string' || typeof option === 'number') return String(option)
  return option[props.labelKey]
}

const getOptionDisabled = (option: any): boolean => {
  if (typeof option === 'string' || typeof option === 'number') return false
  return option[props.disabledKey] || false
}

const getOptionLevel = (option: any): number => {
  if (typeof option === 'string' || typeof option === 'number') return 0
  return option[props.levelKey] || 0
}

// 事件处理
const toggle = () => {
  isOpen.value = !isOpen.value
  if (isOpen.value) {
    searchQuery.value = ''
  }
}

const close = () => {
  isOpen.value = false
  searchQuery.value = ''
}

const selectOption = (option: any) => {
  const value = option ? getOptionValue(option) : null
  emit('update:modelValue', value)
  emit('change', value)
  close()
}

// 点击外部关闭
const handleClickOutside = (event: Event) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    close()
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
