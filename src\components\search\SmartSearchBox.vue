<template>
  <div class="relative">
    <!-- 搜索输入框 -->
    <div
      class="flex items-center bg-gray-50 dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 focus-within:border-primary-500 dark:focus-within:border-primary-400 transition-colors">
      <!-- 搜索类型选择器 -->
      <BaseDropdown placement="bottom-start" class="flex-shrink-0">
        <template #trigger>
          <button
            class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors border-r border-gray-200 dark:border-gray-700">
            <div :class="[currentSearchType.icon, 'w-4 h-4 mr-2']"></div>
            <span>{{ currentSearchType.label }}</span>
            <div class="i-heroicons-chevron-down w-4 h-4 ml-1"></div>
          </button>
        </template>

        <div class="w-56">
          <div class="px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
            搜索类型
          </div>
          <DropdownItem v-for="type in searchTypes" :key="type.value" :text="type.label"
            :icon="searchType === type.value ? 'i-heroicons-check' : type.icon" :description="type.description"
            @click="handleSearchTypeChange(type.value)" />
        </div>
      </BaseDropdown>

      <!-- 搜索输入 -->
      <input ref="searchInput" v-model="searchQuery" type="text" :placeholder="currentSearchType.placeholder"
        class="flex-1 px-4 py-2 bg-transparent border-0 focus:outline-none text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
        @input="handleInput" @keydown="handleKeydown" @focus="showResults = true" @blur="handleBlur" />

      <!-- 搜索按钮 -->
      <button v-if="searchQuery" @click="handleSearch"
        class="flex-shrink-0 px-3 py-2 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors">
        <div class="i-heroicons-magnifying-glass w-5 h-5"></div>
      </button>
    </div>

    <!-- 搜索结果下拉框 -->
    <div v-if="showResults && (searchResults.length > 0 || isLoading)"
      class="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="px-4 py-3 text-center text-gray-500 dark:text-gray-400">
        <div class="i-heroicons-arrow-path w-4 h-4 animate-spin inline-block mr-2"></div>
        搜索中...
      </div>

      <!-- 搜索结果 -->
      <div v-else>
        <div v-for="(result, index) in searchResults" :key="result.id" :class="[
          'px-4 py-3 cursor-pointer transition-colors border-b border-gray-100 dark:border-gray-700 last:border-b-0',
          selectedIndex === index ? 'bg-primary-50 dark:bg-primary-900/20' : 'hover:bg-gray-50 dark:hover:bg-gray-700'
        ]" @click="handleResultClick(result)">
          <div class="flex items-start space-x-3">
            <!-- 结果图标 -->
            <div :class="[getResultIcon(result.type), 'w-5 h-5 mt-0.5 flex-shrink-0', getResultIconColor(result.type)]">
            </div>

            <!-- 结果内容 -->
            <div class="flex-1 min-w-0">
              <div class="font-medium text-gray-900 dark:text-gray-100 truncate">
                {{ result.title }}
              </div>
              <div v-if="result.description" class="text-sm text-gray-500 dark:text-gray-400 truncate mt-1">
                {{ result.description }}
              </div>
              <div class="flex items-center space-x-2 mt-1">
                <span
                  class="text-xs px-2 py-1 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">
                  {{ getResultTypeLabel(result.type) }}
                </span>
                <span v-if="result.category" class="text-xs text-gray-500 dark:text-gray-400">
                  {{ result.category }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 无结果 -->
        <div v-if="searchResults.length === 0 && searchQuery"
          class="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
          <div class="i-heroicons-magnifying-glass w-8 h-8 mx-auto mb-2 opacity-50"></div>
          <p>未找到相关结果</p>
          <p class="text-sm mt-1">尝试使用不同的关键词</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import BaseDropdown from '@/components/ui/BaseDropdown.vue'
import DropdownItem from '@/components/ui/DropdownItem.vue'
import { searchService } from '@/services/searchService'
import { searchEngineService } from '@/services/searchEngineService'
import type { SearchResult, SearchType } from '@/types/search'

const router = useRouter()

// 搜索状态
const searchQuery = ref('')
const searchType = ref<SearchType>('global')
const showResults = ref(false)
const isLoading = ref(false)
const searchResults = ref<SearchResult[]>([])
const selectedIndex = ref(-1)
const searchInput = ref<HTMLInputElement>()

// 搜索类型配置
const searchTypes = [
  {
    value: 'global' as SearchType,
    label: '全局搜索',
    icon: 'i-heroicons-globe-alt',
    description: '搜索分类、标签、资源和功能',
    placeholder: '搜索分类、标签、资源...'
  },
  {
    value: 'knowledge' as SearchType,
    label: '知识库搜索',
    icon: 'i-heroicons-book-open',
    description: '搜索知识库中的资源',
    placeholder: '搜索知识库资源...'
  },
  {
    value: 'web' as SearchType,
    label: '网络搜索',
    icon: 'i-heroicons-magnifying-glass',
    description: '使用搜索引擎搜索',
    placeholder: '搜索网络内容...'
  }
]

// 当前搜索类型
const currentSearchType = computed(() => {
  return searchTypes.find(type => type.value === searchType.value) || searchTypes[0]
})

// 防抖搜索
let searchTimeout: ReturnType<typeof setTimeout> | null = null

const handleInput = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }

  if (!searchQuery.value.trim()) {
    searchResults.value = []
    showResults.value = false
    return
  }

  searchTimeout = setTimeout(async () => {
    await performSearch()
  }, 300)
}

// 执行搜索
const performSearch = async () => {
  if (!searchQuery.value.trim()) return

  isLoading.value = true
  selectedIndex.value = -1

  try {
    const results = await searchService.search(searchQuery.value, searchType.value)
    searchResults.value = results
    showResults.value = true
  } catch (error) {
    console.error('搜索失败:', error)
    searchResults.value = []
  } finally {
    isLoading.value = false
  }
}

// 处理搜索类型变化
const handleSearchTypeChange = (newType: SearchType) => {
  searchType.value = newType
  if (searchQuery.value.trim()) {
    performSearch()
  }
}

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (!showResults.value || searchResults.value.length === 0) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      selectedIndex.value = Math.min(selectedIndex.value + 1, searchResults.value.length - 1)
      break
    case 'ArrowUp':
      event.preventDefault()
      selectedIndex.value = Math.max(selectedIndex.value - 1, -1)
      break
    case 'Enter':
      event.preventDefault()
      if (selectedIndex.value >= 0) {
        handleResultClick(searchResults.value[selectedIndex.value])
      } else {
        handleSearch()
      }
      break
    case 'Escape':
      showResults.value = false
      selectedIndex.value = -1
      searchInput.value?.blur()
      break
  }
}

// 处理失焦
const handleBlur = () => {
  // 延迟隐藏，允许点击结果
  setTimeout(() => {
    showResults.value = false
    selectedIndex.value = -1
  }, 200)
}

// 处理搜索
const handleSearch = () => {
  if (!searchQuery.value.trim()) return

  if (searchType.value === 'web') {
    // 网络搜索，使用配置的搜索引擎
    searchEngineService.search(searchQuery.value)
  } else {
    // 跳转到搜索结果页面
    router.push({
      path: '/knowledge',
      query: {
        q: searchQuery.value,
        type: searchType.value
      }
    })
  }

  showResults.value = false
}

// 处理结果点击
const handleResultClick = (result: SearchResult) => {
  showResults.value = false
  searchQuery.value = '' // 清空搜索框

  switch (result.type) {
    case 'resource':
      // 跳转到知识库页面并高亮显示该资源
      router.push({
        path: '/knowledge',
        query: { highlight: result.id }
      })
      break
    case 'category':
      // 跳转到知识库页面并筛选该分类
      router.push({
        path: '/knowledge',
        query: { category: result.id }
      })
      break
    case 'tag':
      // 跳转到知识库页面并筛选该标签
      router.push({
        path: '/knowledge',
        query: { tag: result.id }
      })
      break
    case 'function':
      // 执行功能相关的操作
      if (result.action) {
        result.action()
      }
      break
  }
}

// 获取结果图标
const getResultIcon = (type: string) => {
  const icons = {
    resource: 'i-heroicons-document',
    category: 'i-heroicons-folder',
    tag: 'i-heroicons-tag',
    function: 'i-heroicons-cog-6-tooth'
  }
  return icons[type as keyof typeof icons] || 'i-heroicons-document'
}

// 获取结果图标颜色
const getResultIconColor = (type: string) => {
  const colors = {
    resource: 'text-blue-500',
    category: 'text-yellow-500',
    tag: 'text-green-500',
    function: 'text-purple-500'
  }
  return colors[type as keyof typeof colors] || 'text-gray-500'
}

// 获取结果类型标签
const getResultTypeLabel = (type: string) => {
  const labels = {
    resource: '资源',
    category: '分类',
    tag: '标签',
    function: '功能'
  }
  return labels[type as keyof typeof labels] || '未知'
}
</script>
