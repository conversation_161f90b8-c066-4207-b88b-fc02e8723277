// 通知类型
export type NotificationType = 'success' | 'error' | 'warning' | 'info'

// 通知接口
export interface Notification {
  id: string
  type: NotificationType
  title: string
  message: string
  duration?: number
  actions?: NotificationAction[]
}

export interface NotificationAction {
  label: string
  action: () => void
  style?: 'primary' | 'secondary'
}

// 通知事件
export interface NotificationEvent {
  type: 'add' | 'remove'
  notification: Notification
}

class NotificationService {
  private notifications: Notification[] = []
  private listeners: ((event: NotificationEvent) => void)[] = []
  private idCounter = 0

  // 添加监听器
  addListener(listener: (event: NotificationEvent) => void): () => void {
    this.listeners.push(listener)
    
    // 返回取消监听的函数
    return () => {
      const index = this.listeners.indexOf(listener)
      if (index > -1) {
        this.listeners.splice(index, 1)
      }
    }
  }

  // 触发事件
  private emit(event: NotificationEvent): void {
    this.listeners.forEach(listener => listener(event))
  }

  // 生成唯一ID
  private generateId(): string {
    return `notification-${++this.idCounter}-${Date.now()}`
  }

  // 添加通知
  private addNotification(notification: Omit<Notification, 'id'>): string {
    const id = this.generateId()
    const fullNotification: Notification = {
      id,
      duration: 5000, // 默认5秒
      ...notification
    }

    this.notifications.push(fullNotification)
    this.emit({ type: 'add', notification: fullNotification })

    // 自动移除通知
    if (fullNotification.duration && fullNotification.duration > 0) {
      setTimeout(() => {
        this.remove(id)
      }, fullNotification.duration)
    }

    return id
  }

  // 成功通知
  success(title: string, message: string, options?: { duration?: number; actions?: NotificationAction[] }): string {
    return this.addNotification({
      type: 'success',
      title,
      message,
      duration: options?.duration,
      actions: options?.actions
    })
  }

  // 错误通知
  error(title: string, message: string, options?: { duration?: number; actions?: NotificationAction[] }): string {
    return this.addNotification({
      type: 'error',
      title,
      message,
      duration: options?.duration || 8000, // 错误通知显示更久
      actions: options?.actions
    })
  }

  // 警告通知
  warning(title: string, message: string, options?: { duration?: number; actions?: NotificationAction[] }): string {
    return this.addNotification({
      type: 'warning',
      title,
      message,
      duration: options?.duration || 6000,
      actions: options?.actions
    })
  }

  // 信息通知
  info(title: string, message: string, options?: { duration?: number; actions?: NotificationAction[] }): string {
    return this.addNotification({
      type: 'info',
      title,
      message,
      duration: options?.duration,
      actions: options?.actions
    })
  }

  // 移除通知
  remove(id: string): void {
    const index = this.notifications.findIndex(n => n.id === id)
    if (index > -1) {
      const notification = this.notifications[index]
      this.notifications.splice(index, 1)
      this.emit({ type: 'remove', notification })
    }
  }

  // 清空所有通知
  clear(): void {
    const notifications = [...this.notifications]
    this.notifications = []
    notifications.forEach(notification => {
      this.emit({ type: 'remove', notification })
    })
  }

  // 获取所有通知
  getAll(): Notification[] {
    return [...this.notifications]
  }

  // 数据管理相关的便捷方法
  dataExportSuccess(filename: string): string {
    return this.success(
      '数据导出成功',
      `文件 ${filename} 已开始下载`,
      {
        duration: 4000
      }
    )
  }

  dataExportError(error: string): string {
    return this.error(
      '数据导出失败',
      error,
      {
        duration: 8000
      }
    )
  }

  dataImportSuccess(details: { knowledgeEntries: number; categories: number; tags: number }): string {
    const items = []
    if (details.knowledgeEntries > 0) items.push(`${details.knowledgeEntries}条知识库`)
    if (details.categories > 0) items.push(`${details.categories}个分类`)
    if (details.tags > 0) items.push(`${details.tags}个标签`)
    
    return this.success(
      '数据导入成功',
      `已导入：${items.join('、')}`,
      {
        duration: 5000
      }
    )
  }

  dataImportError(error: string): string {
    return this.error(
      '数据导入失败',
      error,
      {
        duration: 8000
      }
    )
  }

  cloudUploadSuccess(url?: string): string {
    return this.success(
      '云端上传成功',
      '数据已成功备份到GitHub Gist',
      {
        duration: 4000,
        actions: url ? [{
          label: '查看备份',
          action: () => window.open(url, '_blank'),
          style: 'primary'
        }] : undefined
      }
    )
  }

  cloudUploadError(error: string): string {
    return this.error(
      '云端上传失败',
      error,
      {
        duration: 8000
      }
    )
  }

  cloudDownloadSuccess(): string {
    return this.success(
      '云端下载成功',
      '数据已从云端恢复到本地',
      {
        duration: 4000
      }
    )
  }

  cloudDownloadError(error: string): string {
    return this.error(
      '云端下载失败',
      error,
      {
        duration: 8000
      }
    )
  }

  cloudUpdateDetected(onDownload: () => void): string {
    return this.warning(
      '检测到云端更新',
      '云端有新的备份数据可以下载',
      {
        duration: 0, // 不自动消失
        actions: [
          {
            label: '立即下载',
            action: onDownload,
            style: 'primary'
          },
          {
            label: '稍后提醒',
            action: () => {}, // 关闭通知
            style: 'secondary'
          }
        ]
      }
    )
  }

  githubConnectSuccess(): string {
    return this.success(
      'GitHub连接成功',
      '云端同步功能已启用',
      {
        duration: 3000
      }
    )
  }

  githubConnectError(error: string): string {
    return this.error(
      'GitHub连接失败',
      error,
      {
        duration: 8000
      }
    )
  }
}

// 创建单例实例
export const notificationService = new NotificationService()

// 导出类型
export type { Notification, NotificationAction, NotificationEvent }
