<template>
  <div class="fixed top-20 right-4 z-50 space-y-3 max-w-sm">
    <TransitionGroup name="notification" tag="div">
      <div v-for="notification in notifications" :key="notification.id" :class="[
        'bg-white/95 dark:bg-gray-800/95 backdrop-blur-lg rounded-xl shadow-xl border-l-4 p-4 max-w-sm',
        'border border-gray-200/50 dark:border-gray-700/50',
        'hover:shadow-2xl transition-all duration-200 transform hover:scale-[1.02]',
        getNotificationStyles(notification.type)
      ]">
        <!-- 通知头部 -->
        <div class="flex items-start justify-between">
          <div class="flex items-start space-x-3 flex-1">
            <!-- 图标 -->
            <div :class="[
              'w-5 h-5 mt-0.5 flex-shrink-0',
              getIconClass(notification.type)
            ]"></div>

            <!-- 内容 -->
            <div class="flex-1 min-w-0">
              <h4 :class="[
                'text-sm font-medium',
                getTitleClass(notification.type)
              ]">
                {{ notification.title }}
              </h4>
              <p :class="[
                'text-sm mt-1',
                getMessageClass(notification.type)
              ]">
                {{ notification.message }}
              </p>

              <!-- 操作按钮 -->
              <div v-if="notification.actions && notification.actions.length > 0" class="mt-3 flex space-x-2">
                <button v-for="action in notification.actions" :key="action.label"
                  @click="handleAction(action, notification.id)" :class="[
                    'px-3 py-1 text-xs font-medium rounded transition-colors',
                    action.style === 'primary'
                      ? 'bg-primary-500 text-white hover:bg-primary-600'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  ]">
                  {{ action.label }}
                </button>
              </div>
            </div>
          </div>

          <!-- 关闭按钮 -->
          <button @click="removeNotification(notification.id)"
            class="ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
            <div class="i-heroicons-x-mark w-4 h-4"></div>
          </button>
        </div>
      </div>
    </TransitionGroup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { notificationService, type Notification, type NotificationAction } from '@/services/notificationService'

const notifications = ref<Notification[]>([])

// 获取通知样式
const getNotificationStyles = (type: string): string => {
  switch (type) {
    case 'success':
      return 'border-green-500 bg-green-50 dark:bg-green-900/20'
    case 'error':
      return 'border-red-500 bg-red-50 dark:bg-red-900/20'
    case 'warning':
      return 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20'
    case 'info':
      return 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
    default:
      return 'border-gray-500 bg-gray-50 dark:bg-gray-900/20'
  }
}

// 获取图标类
const getIconClass = (type: string): string => {
  switch (type) {
    case 'success':
      return 'i-heroicons-check-circle text-green-500'
    case 'error':
      return 'i-heroicons-x-circle text-red-500'
    case 'warning':
      return 'i-heroicons-exclamation-triangle text-yellow-500'
    case 'info':
      return 'i-heroicons-information-circle text-blue-500'
    default:
      return 'i-heroicons-bell text-gray-500'
  }
}

// 获取标题样式
const getTitleClass = (type: string): string => {
  switch (type) {
    case 'success':
      return 'text-green-800 dark:text-green-200'
    case 'error':
      return 'text-red-800 dark:text-red-200'
    case 'warning':
      return 'text-yellow-800 dark:text-yellow-200'
    case 'info':
      return 'text-blue-800 dark:text-blue-200'
    default:
      return 'text-gray-800 dark:text-gray-200'
  }
}

// 获取消息样式
const getMessageClass = (type: string): string => {
  switch (type) {
    case 'success':
      return 'text-green-700 dark:text-green-300'
    case 'error':
      return 'text-red-700 dark:text-red-300'
    case 'warning':
      return 'text-yellow-700 dark:text-yellow-300'
    case 'info':
      return 'text-blue-700 dark:text-blue-300'
    default:
      return 'text-gray-700 dark:text-gray-300'
  }
}

// 处理操作按钮点击
const handleAction = (action: NotificationAction, notificationId: string) => {
  action.action()
  removeNotification(notificationId)
}

// 移除通知
const removeNotification = (id: string) => {
  notificationService.remove(id)
}

// 监听通知事件
let unsubscribe: (() => void) | null = null

onMounted(() => {
  // 初始化现有通知
  notifications.value = notificationService.getAll()

  // 监听通知变化
  unsubscribe = notificationService.addListener((event) => {
    if (event.type === 'add') {
      notifications.value.push(event.notification)
    } else if (event.type === 'remove') {
      const index = notifications.value.findIndex(n => n.id === event.notification.id)
      if (index > -1) {
        notifications.value.splice(index, 1)
      }
    }
  })
})

onUnmounted(() => {
  if (unsubscribe) {
    unsubscribe()
  }
})
</script>

<style scoped>
/* 通知动画 - 优化版本 */
.notification-enter-active {
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

.notification-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 1, 1);
}

.notification-enter-from {
  transform: translateX(100%) scale(0.95);
  opacity: 0;
}

.notification-leave-to {
  transform: translateX(100%) scale(0.95);
  opacity: 0;
}

.notification-move {
  transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

/* 响应式设计 */
@media (max-width: 640px) {
  .fixed {
    left: 1rem;
    right: 1rem;
    max-width: none;
  }

  .max-w-sm {
    max-width: none;
  }
}
</style>
