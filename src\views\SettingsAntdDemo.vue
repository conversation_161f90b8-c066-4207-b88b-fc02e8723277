<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-4xl mx-auto px-4">
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-8">设置界面 Ant Design 组件演示</h1>
        
        <div class="space-y-12">
          <!-- 用户设置表单 -->
          <section>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">用户设置</h2>
            <a-form
              :model="userSettings"
              :rules="userRules"
              layout="vertical"
              @finish="onUserSettingsSubmit"
            >
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <a-form-item label="用户名" name="username">
                  <a-input
                    v-model:value="userSettings.username"
                    placeholder="请输入用户名"
                    size="large"
                  />
                </a-form-item>

                <a-form-item label="邮箱" name="email">
                  <a-input
                    v-model:value="userSettings.email"
                    placeholder="请输入邮箱"
                    size="large"
                  />
                </a-form-item>

                <a-form-item label="显示名称" name="displayName">
                  <a-input
                    v-model:value="userSettings.displayName"
                    placeholder="请输入显示名称"
                    size="large"
                  />
                </a-form-item>

                <a-form-item label="语言设置" name="language">
                  <a-select
                    v-model:value="userSettings.language"
                    placeholder="请选择语言"
                    size="large"
                    :options="languageOptions"
                  />
                </a-form-item>

                <a-form-item label="时区" name="timezone">
                  <a-select
                    v-model:value="userSettings.timezone"
                    placeholder="请选择时区"
                    size="large"
                    show-search
                    :options="timezoneOptions"
                    :filter-option="filterOption"
                  />
                </a-form-item>

                <a-form-item label="主题色" name="themeColor">
                  <a-select
                    v-model:value="userSettings.themeColor"
                    placeholder="请选择主题色"
                    size="large"
                    :options="themeColorOptions"
                  />
                </a-form-item>
              </div>

              <a-form-item label="个人简介" name="bio">
                <a-textarea
                  v-model:value="userSettings.bio"
                  placeholder="请输入个人简介"
                  :rows="4"
                  show-count
                  :maxlength="200"
                />
              </a-form-item>

              <a-form-item>
                <a-space>
                  <a-button type="primary" html-type="submit" size="large">
                    保存设置
                  </a-button>
                  <a-button size="large" @click="resetUserSettings">
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </section>

          <!-- 系统设置 -->
          <section>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">系统设置</h2>
            <div class="space-y-6">
              <!-- 开关设置 -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <a-card title="通知设置" size="small">
                  <div class="space-y-4">
                    <div class="flex items-center justify-between">
                      <span>桌面通知</span>
                      <a-switch v-model:checked="systemSettings.notifications.desktop" />
                    </div>
                    <div class="flex items-center justify-between">
                      <span>邮件通知</span>
                      <a-switch v-model:checked="systemSettings.notifications.email" />
                    </div>
                    <div class="flex items-center justify-between">
                      <span>声音提醒</span>
                      <a-switch v-model:checked="systemSettings.notifications.sound" />
                    </div>
                  </div>
                </a-card>

                <a-card title="界面设置" size="small">
                  <div class="space-y-4">
                    <div class="flex items-center justify-between">
                      <span>暗色模式</span>
                      <a-switch v-model:checked="systemSettings.ui.darkMode" />
                    </div>
                    <div class="flex items-center justify-between">
                      <span>紧凑布局</span>
                      <a-switch v-model:checked="systemSettings.ui.compactMode" />
                    </div>
                    <div class="flex items-center justify-between">
                      <span>显示侧边栏</span>
                      <a-switch v-model:checked="systemSettings.ui.showSidebar" />
                    </div>
                  </div>
                </a-card>
              </div>

              <!-- 数值设置 -->
              <a-card title="性能设置" size="small">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label class="block text-sm font-medium mb-2">缓存大小 (MB)</label>
                    <a-input-number
                      v-model:value="systemSettings.performance.cacheSize"
                      :min="100"
                      :max="2000"
                      :step="100"
                      style="width: 100%"
                    />
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium mb-2">同步间隔 (分钟)</label>
                    <a-input-number
                      v-model:value="systemSettings.performance.syncInterval"
                      :min="1"
                      :max="60"
                      style="width: 100%"
                    />
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium mb-2">最大连接数</label>
                    <a-input-number
                      v-model:value="systemSettings.performance.maxConnections"
                      :min="1"
                      :max="100"
                      style="width: 100%"
                    />
                  </div>
                </div>
              </a-card>

              <!-- 高级设置 -->
              <a-card title="高级设置" size="small">
                <div class="space-y-4">
                  <a-form-item label="API 端点">
                    <a-input
                      v-model:value="systemSettings.advanced.apiEndpoint"
                      placeholder="https://api.example.com"
                      size="large"
                    />
                  </a-form-item>

                  <a-form-item label="调试级别">
                    <a-select
                      v-model:value="systemSettings.advanced.debugLevel"
                      size="large"
                      :options="debugLevelOptions"
                    />
                  </a-form-item>

                  <a-form-item label="自定义CSS">
                    <a-textarea
                      v-model:value="systemSettings.advanced.customCSS"
                      placeholder="/* 输入自定义CSS代码 */"
                      :rows="6"
                      show-count
                    />
                  </a-form-item>
                </div>
              </a-card>

              <!-- 操作按钮 -->
              <div class="flex justify-end space-x-4">
                <a-button size="large" @click="resetSystemSettings">
                  重置系统设置
                </a-button>
                <a-button type="primary" size="large" @click="saveSystemSettings">
                  保存系统设置
                </a-button>
              </div>
            </div>
          </section>

          <!-- 导入导出设置 -->
          <section>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">数据管理</h2>
            <a-card title="导入导出" size="small">
              <div class="space-y-4">
                <div class="flex items-center space-x-4">
                  <a-upload
                    :before-upload="beforeUpload"
                    :show-upload-list="false"
                  >
                    <a-button>
                      <UploadOutlined />
                      导入设置
                    </a-button>
                  </a-upload>
                  
                  <a-button @click="exportSettings">
                    <DownloadOutlined />
                    导出设置
                  </a-button>
                  
                  <a-button danger @click="clearAllSettings">
                    <DeleteOutlined />
                    清空所有设置
                  </a-button>
                </div>
                
                <a-alert
                  message="数据管理提示"
                  description="导入设置将覆盖当前所有配置，请谨慎操作。建议在导入前先导出当前设置作为备份。"
                  type="info"
                  show-icon
                />
              </div>
            </a-card>
          </section>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { UploadOutlined, DownloadOutlined, DeleteOutlined } from '@ant-design/icons-vue'

// 用户设置数据
const userSettings = reactive({
  username: '',
  email: '',
  displayName: '',
  language: 'zh-CN',
  timezone: 'Asia/Shanghai',
  themeColor: 'blue',
  bio: ''
})

// 系统设置数据
const systemSettings = reactive({
  notifications: {
    desktop: true,
    email: false,
    sound: true
  },
  ui: {
    darkMode: false,
    compactMode: false,
    showSidebar: true
  },
  performance: {
    cacheSize: 500,
    syncInterval: 5,
    maxConnections: 10
  },
  advanced: {
    apiEndpoint: '',
    debugLevel: 'info',
    customCSS: ''
  }
})

// 选项数据
const languageOptions = [
  { value: 'zh-CN', label: '简体中文' },
  { value: 'zh-TW', label: '繁体中文' },
  { value: 'en-US', label: 'English' },
  { value: 'ja-JP', label: '日本語' },
  { value: 'ko-KR', label: '한국어' }
]

const timezoneOptions = [
  { value: 'Asia/Shanghai', label: '北京时间 (UTC+8)' },
  { value: 'Asia/Tokyo', label: '东京时间 (UTC+9)' },
  { value: 'America/New_York', label: '纽约时间 (UTC-5)' },
  { value: 'Europe/London', label: '伦敦时间 (UTC+0)' },
  { value: 'Australia/Sydney', label: '悉尼时间 (UTC+11)' }
]

const themeColorOptions = [
  { value: 'blue', label: '蓝色' },
  { value: 'green', label: '绿色' },
  { value: 'purple', label: '紫色' },
  { value: 'red', label: '红色' },
  { value: 'orange', label: '橙色' }
]

const debugLevelOptions = [
  { value: 'error', label: 'Error' },
  { value: 'warn', label: 'Warning' },
  { value: 'info', label: 'Info' },
  { value: 'debug', label: 'Debug' },
  { value: 'trace', label: 'Trace' }
]

// 表单验证规则
const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度应为3-20个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  displayName: [
    { required: true, message: '请输入显示名称', trigger: 'blur' }
  ]
}

// 事件处理
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const onUserSettingsSubmit = (values: any) => {
  console.log('用户设置提交:', values)
  message.success('用户设置保存成功!')
}

const resetUserSettings = () => {
  Object.assign(userSettings, {
    username: '',
    email: '',
    displayName: '',
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    themeColor: 'blue',
    bio: ''
  })
  message.info('用户设置已重置')
}

const saveSystemSettings = () => {
  console.log('系统设置:', systemSettings)
  message.success('系统设置保存成功!')
}

const resetSystemSettings = () => {
  Object.assign(systemSettings, {
    notifications: {
      desktop: true,
      email: false,
      sound: true
    },
    ui: {
      darkMode: false,
      compactMode: false,
      showSidebar: true
    },
    performance: {
      cacheSize: 500,
      syncInterval: 5,
      maxConnections: 10
    },
    advanced: {
      apiEndpoint: '',
      debugLevel: 'info',
      customCSS: ''
    }
  })
  message.info('系统设置已重置')
}

const beforeUpload = (file: File) => {
  const isJSON = file.type === 'application/json'
  if (!isJSON) {
    message.error('只能上传JSON格式的设置文件!')
    return false
  }
  
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const settings = JSON.parse(e.target?.result as string)
      Object.assign(userSettings, settings.user || {})
      Object.assign(systemSettings, settings.system || {})
      message.success('设置导入成功!')
    } catch (error) {
      message.error('设置文件格式错误!')
    }
  }
  reader.readAsText(file)
  
  return false // 阻止自动上传
}

const exportSettings = () => {
  const settings = {
    user: userSettings,
    system: systemSettings,
    exportTime: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `settings-${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  message.success('设置导出成功!')
}

const clearAllSettings = () => {
  resetUserSettings()
  resetSystemSettings()
  message.warning('所有设置已清空!')
}
</script>
