import type { GenericApiConfig, ApiParameter, ResponseMapping, ApiMethod, ParameterLocation } from '@/types'

/**
 * 通用API调用服务
 * 负责根据配置动态调用各种非标准API接口
 */
class GenericApiService {
  
  /**
   * 根据配置调用API
   */
  async callApi(config: GenericApiConfig, userInputs: Record<string, any>): Promise<any> {
    try {
      // 构建请求参数
      const requestConfig = this.buildRequestConfig(config, userInputs)
      
      // 发送请求
      const response = await this.sendRequest(requestConfig)
      
      // 解析响应
      const parsedResponse = this.parseResponse(response, config.responseMapping)
      
      return {
        success: true,
        data: parsedResponse,
        raw: response
      }
    } catch (error: any) {
      console.error('通用API调用失败:', error)
      return {
        success: false,
        error: error.message || '未知错误',
        raw: null
      }
    }
  }

  /**
   * 构建请求配置
   */
  private buildRequestConfig(config: GenericApiConfig, userInputs: Record<string, any>) {
    const requestConfig: {
      method: ApiMethod
      url: string
      headers: Record<string, string>
      params?: Record<string, any>
      data?: any
      timeout: number
    } = {
      method: config.method,
      url: config.endpoint,
      headers: { ...config.headers },
      timeout: config.timeout || 30000
    }

    // 处理参数
    const queryParams: Record<string, any> = {}
    const bodyParams: Record<string, any> = {}
    const headerParams: Record<string, string> = {}
    let pathUrl = config.endpoint

    for (const param of config.parameters) {
      const value = this.getParameterValue(param, userInputs)
      
      if (value === undefined || value === null) {
        if (param.required) {
          throw new Error(`必填参数 ${param.name} 未提供`)
        }
        continue
      }

      switch (param.location) {
        case 'query':
          queryParams[param.name] = value
          break
        case 'body':
          bodyParams[param.name] = value
          break
        case 'header':
          headerParams[param.name] = String(value)
          break
        case 'path':
          // 替换路径参数，支持 {paramName} 格式
          pathUrl = pathUrl.replace(`{${param.name}}`, encodeURIComponent(String(value)))
          pathUrl = pathUrl.replace(`:${param.name}`, encodeURIComponent(String(value)))
          break
      }
    }

    // 设置最终的URL
    requestConfig.url = pathUrl

    // 设置查询参数
    if (Object.keys(queryParams).length > 0) {
      requestConfig.params = queryParams
    }

    // 设置请求体
    if (Object.keys(bodyParams).length > 0) {
      if (config.method === 'GET') {
        // GET请求将body参数合并到query参数中
        requestConfig.params = { ...requestConfig.params, ...bodyParams }
      } else {
        requestConfig.data = bodyParams
      }
    }

    // 设置请求头
    Object.assign(requestConfig.headers, headerParams)

    return requestConfig
  }

  /**
   * 获取参数值
   */
  private getParameterValue(param: ApiParameter, userInputs: Record<string, any>): any {
    // 优先使用用户输入的值
    if (userInputs.hasOwnProperty(param.name)) {
      return this.convertParameterValue(userInputs[param.name], param.type)
    }

    // 使用默认值
    if (param.defaultValue !== undefined && param.defaultValue !== null && param.defaultValue !== '') {
      return this.convertParameterValue(param.defaultValue, param.type)
    }

    return undefined
  }

  /**
   * 转换参数值类型
   */
  private convertParameterValue(value: any, type: string): any {
    if (value === null || value === undefined) {
      return value
    }

    switch (type) {
      case 'string':
        return String(value)
      case 'number':
        const num = Number(value)
        return isNaN(num) ? value : num
      case 'boolean':
        if (typeof value === 'boolean') return value
        if (typeof value === 'string') {
          return value.toLowerCase() === 'true' || value === '1'
        }
        return Boolean(value)
      case 'object':
        if (typeof value === 'string') {
          try {
            return JSON.parse(value)
          } catch {
            return value
          }
        }
        return value
      case 'array':
        if (Array.isArray(value)) return value
        if (typeof value === 'string') {
          try {
            const parsed = JSON.parse(value)
            return Array.isArray(parsed) ? parsed : [value]
          } catch {
            return [value]
          }
        }
        return [value]
      default:
        return value
    }
  }

  /**
   * 发送HTTP请求
   */
  private async sendRequest(config: any): Promise<any> {
    const { method, url, headers, params, data, timeout } = config

    // 构建完整的URL
    let fullUrl = url
    if (params && Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams()
      for (const [key, value] of Object.entries(params)) {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      }
      const queryString = searchParams.toString()
      if (queryString) {
        fullUrl += (url.includes('?') ? '&' : '?') + queryString
      }
    }

    // 构建fetch选项
    const fetchOptions: RequestInit = {
      method,
      headers,
      signal: AbortSignal.timeout(timeout)
    }

    // 添加请求体
    if (data && method !== 'GET') {
      if (headers['Content-Type']?.includes('application/json')) {
        fetchOptions.body = JSON.stringify(data)
      } else if (headers['Content-Type']?.includes('application/x-www-form-urlencoded')) {
        const formData = new URLSearchParams()
        for (const [key, value] of Object.entries(data)) {
          if (value !== undefined && value !== null) {
            formData.append(key, String(value))
          }
        }
        fetchOptions.body = formData.toString()
      } else {
        fetchOptions.body = JSON.stringify(data)
      }
    }

    // 发送请求
    const response = await fetch(fullUrl, fetchOptions)
    
    // 检查响应状态
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    // 解析响应
    const contentType = response.headers.get('content-type')
    if (contentType?.includes('application/json')) {
      return await response.json()
    } else {
      return await response.text()
    }
  }

  /**
   * 解析API响应
   */
  private parseResponse(response: any, mappings: ResponseMapping[]): any {
    if (!mappings || mappings.length === 0) {
      return response
    }

    const result: Record<string, any> = {}

    for (const mapping of mappings) {
      try {
        const value = this.extractFieldValue(response, mapping.sourceField)
        if (value !== undefined) {
          result[mapping.targetField] = this.convertResponseValue(value, mapping.dataType)
        }
      } catch (error) {
        console.warn(`字段映射失败: ${mapping.sourceField} -> ${mapping.targetField}`, error)
      }
    }

    return result
  }

  /**
   * 从响应中提取字段值
   */
  private extractFieldValue(obj: any, fieldPath: string): any {
    if (!fieldPath || !obj) {
      return obj
    }

    const paths = fieldPath.split('.')
    let current = obj

    for (const path of paths) {
      if (current === null || current === undefined) {
        return undefined
      }

      // 处理数组索引，如 data[0] 或 items[1].name
      if (path.includes('[') && path.includes(']')) {
        const [arrayPath, indexStr] = path.split('[')
        const index = parseInt(indexStr.replace(']', ''))
        
        if (arrayPath) {
          current = current[arrayPath]
        }
        
        if (Array.isArray(current) && !isNaN(index)) {
          current = current[index]
        }
      } else {
        current = current[path]
      }
    }

    return current
  }

  /**
   * 转换响应值类型
   */
  private convertResponseValue(value: any, dataType: string): any {
    if (value === null || value === undefined) {
      return value
    }

    switch (dataType) {
      case 'string':
        return String(value)
      case 'number':
        const num = Number(value)
        return isNaN(num) ? value : num
      case 'boolean':
        if (typeof value === 'boolean') return value
        if (typeof value === 'string') {
          return value.toLowerCase() === 'true' || value === '1'
        }
        return Boolean(value)
      case 'array':
        return Array.isArray(value) ? value : [value]
      case 'object':
        return typeof value === 'object' ? value : { value }
      default:
        return value
    }
  }

  /**
   * 验证配置
   */
  validateConfig(config: GenericApiConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!config.name?.trim()) {
      errors.push('配置名称不能为空')
    }

    if (!config.endpoint?.trim()) {
      errors.push('API端点不能为空')
    }

    if (!['GET', 'POST', 'PUT', 'DELETE'].includes(config.method)) {
      errors.push('HTTP方法无效')
    }

    // 验证参数配置
    for (const param of config.parameters) {
      if (!param.name?.trim()) {
        errors.push('参数名称不能为空')
      }
    }

    // 验证响应映射
    for (const mapping of config.responseMapping) {
      if (!mapping.sourceField?.trim()) {
        errors.push('源字段路径不能为空')
      }
      if (!mapping.targetField?.trim()) {
        errors.push('目标字段名称不能为空')
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }
}

// 导出单例实例
export const genericApiService = new GenericApiService()
export default genericApiService
