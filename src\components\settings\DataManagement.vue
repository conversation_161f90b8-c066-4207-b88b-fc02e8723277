<template>
  <div class="space-y-8">
    <!-- 说明信息 -->
    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
      <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
        <div class="i-heroicons-information-circle inline-block w-4 h-4 mr-1"></div>
        数据管理
      </h3>
      <p class="text-sm text-blue-700 dark:text-blue-300">
        管理您的知识库数据，包括本地导出导入和云端同步功能。所有操作都会保留数据完整性。
      </p>
    </div>

    <!-- 本地数据管理 -->
    <div class="space-y-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">本地数据管理</h3>

      <!-- 数据导出 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-4">
          <div>
            <h4 class="text-base font-medium text-gray-900 dark:text-gray-100">数据导出</h4>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
              将所有数据导出为压缩文件，包括知识库、分类、标签、设置、AI配置和对话历史
            </p>
          </div>
          <button @click="exportData" :disabled="exporting" class="btn-primary">
            <div v-if="exporting" class="i-heroicons-arrow-path w-4 h-4 mr-2 animate-spin"></div>
            <div v-else class="i-heroicons-arrow-down-tray w-4 h-4 mr-2"></div>
            {{ exporting ? '导出中...' : '导出数据' }}
          </button>
        </div>

        <div v-if="exportStatus" class="mt-4 p-3 rounded-lg" :class="[
          exportStatus.success
            ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300'
            : 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300'
        ]">
          <div class="flex items-center">
            <div :class="[
              exportStatus.success ? 'i-heroicons-check-circle' : 'i-heroicons-x-circle',
              'w-4 h-4 mr-2'
            ]"></div>
            {{ exportStatus.message }}
          </div>
        </div>
      </div>

      <!-- 数据导入 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-4">
          <div>
            <h4 class="text-base font-medium text-gray-900 dark:text-gray-100">数据导入</h4>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
              从备份文件导入数据，支持JSON和压缩格式(.kbz)，可选择完全覆盖或合并模式
            </p>
          </div>
          <div class="flex space-x-3">
            <input ref="fileInput" type="file" accept=".json,.kbz" @change="handleFileSelect" class="hidden" />
            <button @click="selectFile" :disabled="importing" class="btn-secondary">
              <div class="i-heroicons-document-arrow-up w-4 h-4 mr-2"></div>
              选择文件
            </button>
          </div>
        </div>

        <!-- 导入选项 -->
        <div v-if="selectedFile" class="space-y-4">
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">导入选项</h5>

            <!-- 导入模式 -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                导入模式
              </label>
              <div class="space-y-2">
                <label class="flex items-center">
                  <input type="radio" v-model="importOptions.mode" value="merge" class="mr-2" />
                  <span class="text-sm text-gray-700 dark:text-gray-300">
                    合并导入（保留现有数据，添加新数据）
                  </span>
                </label>
                <label class="flex items-center">
                  <input type="radio" v-model="importOptions.mode" value="overwrite" class="mr-2" />
                  <span class="text-sm text-gray-700 dark:text-gray-300">
                    完全覆盖（清空现有数据后导入）
                  </span>
                </label>
              </div>
            </div>

            <!-- 导入内容选择 -->
            <div class="grid grid-cols-2 gap-4">
              <label class="flex items-center">
                <input type="checkbox" v-model="importOptions.importKnowledge" class="mr-2" />
                <span class="text-sm text-gray-700 dark:text-gray-300">知识库条目</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" v-model="importOptions.importCategories" class="mr-2" />
                <span class="text-sm text-gray-700 dark:text-gray-300">分类数据</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" v-model="importOptions.importTags" class="mr-2" />
                <span class="text-sm text-gray-700 dark:text-gray-300">标签数据</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" v-model="importOptions.importSettings" class="mr-2" />
                <span class="text-sm text-gray-700 dark:text-gray-300">用户设置</span>
              </label>
            </div>
          </div>

          <!-- 文件信息 -->
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">文件信息</h5>
            <div class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <div>文件名: {{ selectedFile.name }}</div>
              <div>文件大小: {{ formatFileSize(selectedFile.size) }}</div>
              <div v-if="filePreview">导出时间: {{ formatDate(filePreview.exportTime) }}</div>
              <div v-if="filePreview">数据版本: {{ filePreview.version }}</div>
            </div>
          </div>

          <!-- 导入按钮 -->
          <div class="flex justify-end space-x-3">
            <button @click="cancelImport" class="btn-secondary">
              取消
            </button>
            <button @click="confirmImport" :disabled="importing || !canImport" class="btn-primary">
              <div v-if="importing" class="i-heroicons-arrow-path w-4 h-4 mr-2 animate-spin"></div>
              <div v-else class="i-heroicons-arrow-up-tray w-4 h-4 mr-2"></div>
              {{ importing ? '导入中...' : '确认导入' }}
            </button>
          </div>
        </div>

        <!-- 导入状态 -->
        <div v-if="importStatus" class="mt-4 p-3 rounded-lg" :class="[
          importStatus.success
            ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300'
            : 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300'
        ]">
          <div class="flex items-center mb-2">
            <div :class="[
              importStatus.success ? 'i-heroicons-check-circle' : 'i-heroicons-x-circle',
              'w-4 h-4 mr-2'
            ]"></div>
            {{ importStatus.message }}
          </div>

          <div v-if="importStatus.success && importStatus.details" class="text-xs space-y-1">
            <div v-if="importStatus.details.knowledgeEntries > 0">
              导入知识库条目: {{ importStatus.details.knowledgeEntries }} 条
            </div>
            <div v-if="importStatus.details.categories > 0">
              导入分类: {{ importStatus.details.categories }} 个
            </div>
            <div v-if="importStatus.details.tags > 0">
              导入标签: {{ importStatus.details.tags }} 个
            </div>
            <div v-if="importStatus.details.settingsApplied">
              已应用用户设置
            </div>
          </div>

          <div v-if="!importStatus.success && importStatus.errors?.length" class="text-xs mt-2">
            <div class="font-medium mb-1">错误详情:</div>
            <ul class="list-disc list-inside space-y-1">
              <li v-for="error in importStatus.errors" :key="error">{{ error }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- GitHub云同步 -->
    <div class="space-y-6">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">GitHub云同步</h3>
        <button @click="showConfigModal = true"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors" title="查看配置说明">
          <div class="i-heroicons-information-circle w-5 h-5"></div>
        </button>
      </div>

      <!-- GitHub配置 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-4">
          <div>
            <h4 class="text-base font-medium text-gray-900 dark:text-gray-100">GitHub配置</h4>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
              配置GitHub Personal Access Token以启用云端同步功能
            </p>
          </div>
          <div class="flex items-center space-x-2">
            <div :class="[
              'px-3 py-1 rounded-full text-xs font-medium flex items-center',
              githubConnected
                ? 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
            ]">
              <div :class="[
                'w-2 h-2 rounded-full mr-2',
                githubConnected
                  ? 'bg-green-500'
                  : 'bg-gray-400'
              ]"></div>
              {{ githubConnected ? '已连接' : '未连接' }}
            </div>
          </div>
        </div>

        <div v-if="!githubConnected" class="space-y-4">
          <!-- Token输入 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              GitHub Personal Access Token
            </label>
            <div class="flex space-x-3">
              <input v-model="githubToken" type="password" placeholder="ghp_xxxxxxxxxxxxxxxxxxxx"
                class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent" />
              <button @click="connectGitHub" :disabled="!githubToken.trim() || connecting" class="btn-primary">
                <div v-if="connecting" class="i-heroicons-arrow-path w-4 h-4 mr-2 animate-spin"></div>
                <div v-else class="i-heroicons-link w-4 h-4 mr-2"></div>
                {{ connecting ? '连接中...' : '连接' }}
              </button>
            </div>
          </div>

          <!-- Token说明 -->
          <div
            class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
            <h5 class="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2">
              <div class="i-heroicons-exclamation-triangle inline-block w-4 h-4 mr-1"></div>
              Token权限说明
            </h5>
            <div class="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
              <p>请确保您的GitHub Personal Access Token具有以下权限：</p>
              <ul class="list-disc list-inside ml-4 space-y-1">
                <li><code>gist</code> - 创建和管理Gist</li>
              </ul>
              <p class="mt-2">
                <a href="https://github.com/settings/tokens" target="_blank"
                  class="text-yellow-600 dark:text-yellow-400 hover:underline">
                  点击这里创建Token →
                </a>
              </p>
            </div>
          </div>
        </div>

        <div v-else class="space-y-4">
          <!-- 连接状态 -->
          <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div>
                <h5 class="text-sm font-medium text-green-800 dark:text-green-200">
                  <div class="i-heroicons-check-circle inline-block w-4 h-4 mr-1"></div>
                  GitHub已连接
                </h5>
                <p class="text-sm text-green-700 dark:text-green-300 mt-1">
                  云端同步功能已启用
                  <span v-if="lastSyncTime" class="ml-2">
                    (最后同步: {{ formatDate(lastSyncTime) }})
                  </span>
                </p>
              </div>
              <button @click="disconnectGitHub"
                class="text-sm text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300">
                断开连接
              </button>
            </div>
          </div>

          <!-- 云端数据信息 -->
          <div v-if="cloudBackupInfo"
            class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
            <h5 class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
              <div class="i-heroicons-cloud inline-block w-4 h-4 mr-1"></div>
              云端备份信息
            </h5>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-3 text-sm">
              <div>
                <span class="text-blue-600 dark:text-blue-400">创建时间:</span>
                <div class="text-blue-700 dark:text-blue-300">{{ formatDate(cloudBackupInfo.createdAt) }}</div>
              </div>
              <div>
                <span class="text-blue-600 dark:text-blue-400">更新时间:</span>
                <div class="text-blue-700 dark:text-blue-300">{{ formatDate(cloudBackupInfo.updatedAt) }}</div>
              </div>
              <div>
                <span class="text-blue-600 dark:text-blue-400">文件大小:</span>
                <div class="text-blue-700 dark:text-blue-300">{{ formatFileSize(cloudBackupInfo.fileSize) }}</div>
              </div>
            </div>
            <div class="mt-3 flex space-x-3">
              <a :href="cloudBackupInfo.url" target="_blank"
                class="text-sm text-blue-600 dark:text-blue-400 hover:underline">
                查看云端备份 →
              </a>
              <button @click="downloadFromCloud" :disabled="downloading"
                class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">
                快速恢复
              </button>
            </div>
          </div>

          <!-- 数据文件预览 -->
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
              <div class="i-heroicons-document-text inline-block w-4 h-4 mr-1"></div>
              即将上传的数据预览
            </h5>
            <div v-if="dataPreview" class="grid grid-cols-2 sm:grid-cols-4 gap-3 text-sm">
              <div class="text-center">
                <div class="text-lg font-semibold text-gray-700 dark:text-gray-300">{{ dataPreview.knowledgeCount }}
                </div>
                <div class="text-gray-600 dark:text-gray-400">知识库条目</div>
              </div>
              <div class="text-center">
                <div class="text-lg font-semibold text-gray-700 dark:text-gray-300">{{ dataPreview.categoryCount }}
                </div>
                <div class="text-gray-600 dark:text-gray-400">分类</div>
              </div>
              <div class="text-center">
                <div class="text-lg font-semibold text-gray-700 dark:text-gray-300">{{ dataPreview.tagCount }}</div>
                <div class="text-gray-600 dark:text-gray-400">标签</div>
              </div>
              <div class="text-center">
                <div class="text-lg font-semibold text-gray-700 dark:text-gray-300">{{
                  formatFileSize(dataPreview.dataSize) }}</div>
                <div class="text-gray-600 dark:text-gray-400">数据大小</div>
              </div>
            </div>
            <div class="mt-3 flex justify-center">
              <button @click="previewJsonData"
                class="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200">
                <div class="i-heroicons-eye inline-block w-4 h-4 mr-1"></div>
                预览JSON内容
              </button>
            </div>
          </div>

          <!-- 云同步操作 -->
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <button @click="uploadToCloud" :disabled="uploading" class="btn-primary">
              <div v-if="uploading" class="i-heroicons-arrow-path w-4 h-4 mr-2 animate-spin"></div>
              <div v-else class="i-heroicons-cloud-arrow-up w-4 h-4 mr-2"></div>
              {{ uploading ? '上传中...' : '上传到云端' }}
            </button>

            <button @click="downloadFromCloud" :disabled="downloading || !cloudBackupInfo?.hasBackup"
              class="btn-secondary">
              <div v-if="downloading" class="i-heroicons-arrow-path w-4 h-4 mr-2 animate-spin"></div>
              <div v-else class="i-heroicons-cloud-arrow-down w-4 h-4 mr-2"></div>
              {{ downloading ? '下载中...' : '从云端下载' }}
            </button>
          </div>

          <!-- 自动上传设置 -->
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div class="flex items-center justify-between mb-3">
              <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                <div class="i-heroicons-clock inline-block w-4 h-4 mr-1"></div>
                自动上传设置
              </h5>
              <label class="flex items-center">
                <input type="checkbox" v-model="autoUploadEnabled" @change="handleAutoUploadToggle" class="sr-only" />
                <div :class="[
                  'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
                  autoUploadEnabled ? 'bg-primary-500' : 'bg-gray-200 dark:bg-gray-600'
                ]">
                  <span :class="[
                    'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
                    autoUploadEnabled ? 'translate-x-6' : 'translate-x-1'
                  ]"></span>
                </div>
                <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">启用</span>
              </label>
            </div>

            <div v-if="autoUploadEnabled" class="space-y-3">
              <!-- 上传间隔选择 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  上传间隔
                </label>
                <select v-model="autoUploadInterval" @change="handleIntervalChange"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                  <option :value="5">5分钟</option>
                  <option :value="15">15分钟</option>
                  <option :value="30">30分钟</option>
                  <option :value="60">1小时</option>
                  <option :value="120">2小时</option>
                  <option :value="360">6小时</option>
                  <option :value="720">12小时</option>
                  <option :value="1440">24小时</option>
                </select>
              </div>

              <!-- 下次上传时间 -->
              <div v-if="nextUploadTime" class="text-sm text-gray-600 dark:text-gray-400">
                <div class="i-heroicons-clock inline-block w-4 h-4 mr-1"></div>
                下次自动上传: {{ formatDate(nextUploadTime) }}
              </div>

              <!-- 自动上传状态 -->
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-400">自动上传状态:</span>
                <span :class="[
                  'px-2 py-1 rounded text-xs font-medium',
                  autoUploadRunning
                    ? 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                ]">
                  {{ autoUploadRunning ? '运行中' : '已暂停' }}
                </span>
              </div>
            </div>
          </div>

          <!-- 云检测设置 -->
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div class="flex items-center justify-between mb-3">
              <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                <div class="i-heroicons-magnifying-glass inline-block w-4 h-4 mr-1"></div>
                云端检测设置
              </h5>
              <label class="flex items-center">
                <input type="checkbox" v-model="cloudCheckEnabled" @change="handleCloudCheckToggle" class="sr-only" />
                <div :class="[
                  'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
                  cloudCheckEnabled ? 'bg-primary-500' : 'bg-gray-200 dark:bg-gray-600'
                ]">
                  <span :class="[
                    'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
                    cloudCheckEnabled ? 'translate-x-6' : 'translate-x-1'
                  ]"></span>
                </div>
                <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">启用</span>
              </label>
            </div>

            <div v-if="cloudCheckEnabled" class="space-y-3">
              <!-- 检测间隔选择 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  检测间隔
                </label>
                <select v-model="cloudCheckInterval" @change="handleCloudCheckIntervalChange"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                  <option :value="1">1分钟</option>
                  <option :value="5">5分钟</option>
                  <option :value="10">10分钟</option>
                  <option :value="15">15分钟</option>
                  <option :value="30">30分钟</option>
                  <option :value="60">1小时</option>
                  <option :value="120">2小时</option>
                  <option :value="360">6小时</option>
                </select>
              </div>

              <!-- 下次检测时间 -->
              <div v-if="nextCloudCheckTime" class="text-sm text-gray-600 dark:text-gray-400">
                <div class="i-heroicons-clock inline-block w-4 h-4 mr-1"></div>
                下次检测: {{ formatDate(nextCloudCheckTime) }}
              </div>

              <!-- 检测状态 -->
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-400">检测状态:</span>
                <span :class="[
                  'px-2 py-1 rounded text-xs font-medium',
                  cloudCheckRunning
                    ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                ]">
                  {{ cloudCheckRunning ? '检测中' : '已暂停' }}
                </span>
              </div>

              <!-- 最新检测结果 -->
              <div v-if="lastCloudCheckResult" class="text-sm">
                <div class="flex items-center justify-between">
                  <span class="text-gray-600 dark:text-gray-400">最后检测:</span>
                  <span class="text-gray-700 dark:text-gray-300">{{ formatDate(lastCloudCheckResult.time) }}</span>
                </div>
                <div v-if="lastCloudCheckResult.hasUpdate" class="mt-1 text-orange-600 dark:text-orange-400">
                  <div class="i-heroicons-exclamation-triangle inline-block w-4 h-4 mr-1"></div>
                  检测到云端有更新数据
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- GitHub操作状态 -->
        <div v-if="githubStatus" class="mt-4 p-3 rounded-lg" :class="[
          githubStatus.success
            ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300'
            : 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300'
        ]">
          <div class="flex items-center">
            <div :class="[
              githubStatus.success ? 'i-heroicons-check-circle' : 'i-heroicons-x-circle',
              'w-4 h-4 mr-2'
            ]"></div>
            {{ githubStatus.message }}
          </div>
          <div v-if="githubStatus.url" class="mt-2">
            <a :href="githubStatus.url" target="_blank" class="text-sm underline hover:no-underline">
              查看云端备份 →
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- 配置说明模态框 -->
    <GitHubConfigModal :visible="showConfigModal" @close="showConfigModal = false" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { dataManagementService, type ImportOptions, type ImportResult } from '@/services/dataManagementService'
import { githubGistService, type SyncResult } from '@/services/githubGistService'
import { notificationService } from '@/services/notificationService'
import GitHubConfigModal from './GitHubConfigModal.vue'

// 响应式数据
const exporting = ref(false)
const importing = ref(false)
const connecting = ref(false)
const uploading = ref(false)
const downloading = ref(false)

const exportStatus = ref<{ success: boolean; message: string } | null>(null)
const importStatus = ref<ImportResult | null>(null)
const githubStatus = ref<SyncResult | null>(null)

const selectedFile = ref<File | null>(null)
const filePreview = ref<any>(null)
const fileInput = ref<HTMLInputElement>()

const githubToken = ref('')
const githubConnected = ref(false)
const hasCloudBackup = ref(false)
const lastSyncTime = ref<string | null>(null)

// 新增的响应式数据
const showConfigModal = ref(false)
const cloudBackupInfo = ref<any>(null)
const dataPreview = ref<any>(null)
const autoUploadEnabled = ref(false)
const autoUploadInterval = ref(60) // 默认1小时
const nextUploadTime = ref<string | null>(null)
const autoUploadRunning = ref(false)
const autoUploadTimer = ref<number | null>(null)

// 云检测相关
const cloudCheckEnabled = ref(false)
const cloudCheckInterval = ref(10) // 默认10分钟
const nextCloudCheckTime = ref<string | null>(null)
const cloudCheckRunning = ref(false)
const cloudCheckTimer = ref<number | null>(null)
const lastCloudCheckResult = ref<{ time: string; hasUpdate: boolean } | null>(null)

// 导入选项
const importOptions = ref<ImportOptions>({
  mode: 'merge',
  importKnowledge: true,
  importCategories: true,
  importTags: true,
  importSettings: true
})

// 计算属性
const canImport = computed(() => {
  return selectedFile.value && (
    importOptions.value.importKnowledge ||
    importOptions.value.importCategories ||
    importOptions.value.importTags ||
    importOptions.value.importSettings
  )
})

// 导出数据
const exportData = async () => {
  try {
    exporting.value = true
    exportStatus.value = null

    const data = await dataManagementService.exportAllData()
    dataManagementService.downloadExportFile(data, true) // 启用压缩

    const timestamp = new Date().toISOString().split('T')[0]
    const filename = `knowledge-backup-${timestamp}.kbz` // 压缩文件扩展名
    notificationService.dataExportSuccess(filename)

    exportStatus.value = {
      success: true,
      message: '数据导出成功！文件已开始下载。'
    }
  } catch (error) {
    notificationService.dataExportError((error as Error).message)
    exportStatus.value = {
      success: false,
      message: `导出失败: ${(error as Error).message}`
    }
  } finally {
    exporting.value = false
  }
}

// 选择文件
const selectFile = () => {
  fileInput.value?.click()
}

// 处理文件选择
const handleFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (file) {
    selectedFile.value = file
    importStatus.value = null

    // 预览文件内容
    try {
      const text = await file.text()
      let data

      // 检查文件扩展名判断是否为压缩文件
      if (file.name.endsWith('.kbz')) {
        // 压缩文件，需要解压
        const { decompressJson } = await import('@/utils/compression')
        data = decompressJson(text)
      } else {
        // 普通JSON文件
        data = JSON.parse(text)
      }

      filePreview.value = data
    } catch (error) {
      filePreview.value = null
      importStatus.value = {
        success: false,
        message: '文件格式错误，请选择有效的备份文件',
        details: {
          knowledgeEntries: 0,
          categories: 0,
          tags: 0,
          settingsApplied: false
        },
        errors: ['文件格式错误']
      }
    }
  }
}

// 取消导入
const cancelImport = () => {
  selectedFile.value = null
  filePreview.value = null
  importStatus.value = null
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 确认导入
const confirmImport = async () => {
  if (!selectedFile.value || !filePreview.value) return

  try {
    importing.value = true
    importStatus.value = null

    const result = await dataManagementService.importData(filePreview.value, importOptions.value)
    importStatus.value = result

    if (result.success) {
      notificationService.dataImportSuccess(result.details)
      // 导入成功后清理
      setTimeout(() => {
        cancelImport()
      }, 3000)
    } else {
      notificationService.dataImportError(result.message)
    }
  } catch (error) {
    importStatus.value = {
      success: false,
      message: `导入失败: ${(error as Error).message}`,
      details: {
        knowledgeEntries: 0,
        categories: 0,
        tags: 0,
        settingsApplied: false
      },
      errors: [(error as Error).message]
    }
  } finally {
    importing.value = false
  }
}

// 连接GitHub
const connectGitHub = async () => {
  try {
    connecting.value = true
    githubStatus.value = null

    githubGistService.setToken(githubToken.value)

    const validation = await githubGistService.validateToken()
    if (validation.valid) {
      githubConnected.value = true
      githubToken.value = ''
      notificationService.githubConnectSuccess()
      githubStatus.value = {
        success: true,
        message: 'GitHub连接成功！'
      }
      updateGitHubStatus()
    } else {
      notificationService.githubConnectError(validation.error || 'GitHub连接失败')
      githubStatus.value = {
        success: false,
        message: validation.error || 'GitHub连接失败',
        error: validation.error
      }
    }
  } catch (error) {
    githubStatus.value = {
      success: false,
      message: `连接失败: ${(error as Error).message}`,
      error: (error as Error).message
    }
  } finally {
    connecting.value = false
  }
}

// 断开GitHub连接
const disconnectGitHub = () => {
  githubGistService.clearConfig()
  githubConnected.value = false
  hasCloudBackup.value = false
  lastSyncTime.value = null
  githubStatus.value = {
    success: true,
    message: 'GitHub连接已断开'
  }
}

// 上传到云端
const uploadToCloud = async () => {
  try {
    uploading.value = true
    githubStatus.value = null

    const data = await dataManagementService.exportAllData()
    const result = await githubGistService.uploadToGist(data)

    githubStatus.value = result

    if (result.success) {
      notificationService.cloudUploadSuccess(result.url)
      updateGitHubStatus()
    } else {
      notificationService.cloudUploadError(result.error || '上传失败')
    }
  } catch (error) {
    githubStatus.value = {
      success: false,
      message: `上传失败: ${(error as Error).message}`,
      error: (error as Error).message
    }
  } finally {
    uploading.value = false
  }
}

// 从云端下载
const downloadFromCloud = async () => {
  try {
    downloading.value = true
    githubStatus.value = null

    const result = await githubGistService.downloadFromGist()

    if (result.success && result.data) {
      // 自动导入下载的数据
      const importResult = await dataManagementService.importData(result.data, {
        mode: 'overwrite',
        importKnowledge: true,
        importCategories: true,
        importTags: true,
        importSettings: true,
        importAiConfigs: true,
        importAiSessions: true
      })

      if (importResult.success) {
        notificationService.cloudDownloadSuccess()
        githubStatus.value = {
          success: true,
          message: '云端数据下载并导入成功！'
        }
      } else {
        notificationService.cloudDownloadError(importResult.message)
        githubStatus.value = {
          success: false,
          message: '数据下载成功但导入失败',
          error: importResult.message
        }
      }
    } else {
      notificationService.cloudDownloadError(result.error || '下载失败')
      githubStatus.value = {
        success: false,
        message: result.error || '下载失败',
        error: result.error
      }
    }
  } catch (error) {
    githubStatus.value = {
      success: false,
      message: `下载失败: ${(error as Error).message}`,
      error: (error as Error).message
    }
  } finally {
    downloading.value = false
  }
}



// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化日期
const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取云端备份详情
const fetchCloudBackupInfo = async () => {
  if (!githubConnected.value) {
    cloudBackupInfo.value = null
    return
  }

  try {
    const result = await githubGistService.getGistDetails()
    if (result.success && result.details) {
      cloudBackupInfo.value = result.details
      console.log('云端备份信息:', result.details)
    } else {
      cloudBackupInfo.value = null
      console.log('获取云端备份信息失败:', result.error)
    }
  } catch (error) {
    console.error('获取云端备份信息失败:', error)
    cloudBackupInfo.value = null
  }
}

// 生成数据预览
const generateDataPreview = async () => {
  try {
    const data = await dataManagementService.exportAllData()
    const jsonString = JSON.stringify(data, null, 2)

    dataPreview.value = {
      knowledgeCount: data.data.knowledgeEntries?.length || 0,
      categoryCount: data.data.categories?.length || 0,
      tagCount: data.data.tags?.length || 0,
      dataSize: new Blob([jsonString]).size
    }
  } catch (error) {
    console.error('生成数据预览失败:', error)
  }
}

// 预览JSON数据
const previewJsonData = async () => {
  try {
    const data = await dataManagementService.exportAllData()
    const jsonString = JSON.stringify(data, null, 2)

    // 创建新窗口显示JSON内容
    const newWindow = window.open('', '_blank')
    if (newWindow) {
      newWindow.document.write(`
        <html>
          <head>
            <title>数据预览 - JSON格式</title>
            <style>
              body { font-family: monospace; margin: 20px; background: #f5f5f5; }
              pre { background: white; padding: 20px; border-radius: 8px; overflow: auto; }
            </style>
          </head>
          <body>
            <h2>知识库数据预览</h2>
            <pre>${jsonString}</pre>
          </body>
        </html>
      `)
      newWindow.document.close()
    }
  } catch (error) {
    console.error('预览JSON数据失败:', error)
  }
}

// 处理自动上传开关
const handleAutoUploadToggle = () => {
  githubGistService.setAutoUploadConfig(autoUploadEnabled.value, autoUploadInterval.value)

  if (autoUploadEnabled.value) {
    startAutoUpload()
  } else {
    stopAutoUpload()
  }

  updateAutoUploadStatus()
}

// 处理间隔变化
const handleIntervalChange = () => {
  githubGistService.setAutoUploadConfig(autoUploadEnabled.value, autoUploadInterval.value)

  if (autoUploadEnabled.value) {
    stopAutoUpload()
    startAutoUpload()
  }

  updateAutoUploadStatus()
}

// 开始自动上传
const startAutoUpload = () => {
  if (autoUploadTimer.value) {
    clearInterval(autoUploadTimer.value)
  }

  autoUploadRunning.value = true

  // 设置定时器
  autoUploadTimer.value = window.setInterval(async () => {
    if (githubGistService.shouldAutoUpload()) {
      try {
        const data = await dataManagementService.exportAllData()
        await githubGistService.uploadToGist(data, `自动备份 - ${new Date().toLocaleString()}`)
        githubGistService.updateNextUploadTime()
        updateAutoUploadStatus()
        console.log('自动上传完成')
      } catch (error) {
        console.error('自动上传失败:', error)
      }
    }
  }, 60000) // 每分钟检查一次
}

// 停止自动上传
const stopAutoUpload = () => {
  if (autoUploadTimer.value) {
    clearInterval(autoUploadTimer.value)
    autoUploadTimer.value = null
  }
  autoUploadRunning.value = false
}

// 更新自动上传状态
const updateAutoUploadStatus = () => {
  const config = githubGistService.getAutoUploadConfig()
  autoUploadEnabled.value = config.enabled
  autoUploadInterval.value = config.interval
  nextUploadTime.value = config.nextUpload || null
}



// 初始化
onMounted(() => {
  updateGitHubStatus()
})

// 处理云检测开关
const handleCloudCheckToggle = () => {
  if (cloudCheckEnabled.value) {
    startCloudCheck()
  } else {
    stopCloudCheck()
  }

  // 保存配置到localStorage
  localStorage.setItem('cloud-check-config', JSON.stringify({
    enabled: cloudCheckEnabled.value,
    interval: cloudCheckInterval.value
  }))
}

// 处理云检测间隔变化
const handleCloudCheckIntervalChange = () => {
  if (cloudCheckEnabled.value) {
    stopCloudCheck()
    startCloudCheck()
  }

  // 保存配置
  localStorage.setItem('cloud-check-config', JSON.stringify({
    enabled: cloudCheckEnabled.value,
    interval: cloudCheckInterval.value
  }))
}

// 开始云检测
const startCloudCheck = () => {
  if (!githubConnected.value || cloudCheckTimer.value) return

  cloudCheckRunning.value = true
  nextCloudCheckTime.value = new Date(Date.now() + cloudCheckInterval.value * 60000).toISOString()

  // 设置定时器
  cloudCheckTimer.value = window.setInterval(async () => {
    await performCloudCheck()
    nextCloudCheckTime.value = new Date(Date.now() + cloudCheckInterval.value * 60000).toISOString()
  }, cloudCheckInterval.value * 60000)

  // 立即执行一次检测
  performCloudCheck()
}

// 停止云检测
const stopCloudCheck = () => {
  if (cloudCheckTimer.value) {
    clearInterval(cloudCheckTimer.value)
    cloudCheckTimer.value = null
  }
  cloudCheckRunning.value = false
  nextCloudCheckTime.value = null
}

// 执行云检测
const performCloudCheck = async () => {
  try {
    const result = await githubGistService.getGistDetails()
    if (result.success && result.details) {
      // GitHub API返回的是UTC时间字符串
      const cloudUpdatedAtUTC = result.details.updatedAt

      // 获取用户最后上传时间
      const lastUploadTime = githubGistService.getLastUploadTime()

      let hasUpdate = false

      if (!lastUploadTime) {
        // 如果用户从未上传过，但云端有数据，说明是其他设备或首次使用
        // 检查云端是否真的有备份文件
        hasUpdate = result.details.hasBackup
        console.log('首次检测或从未上传:', {
          hasCloudBackup: result.details.hasBackup,
          cloudUpdatedAtUTC: cloudUpdatedAtUTC,
          cloudUpdatedAtLocal: new Date(cloudUpdatedAtUTC).toLocaleString(),
          hasUpdate
        })
      } else {
        // 用户之前同步过，使用时区安全的时间比较
        hasUpdate = githubGistService.hasCloudUpdate(cloudUpdatedAtUTC)

        // 获取同步时间信息
        const lastUploadTime = githubGistService.getLastUploadTime()
        const lastDownloadTime = githubGistService.getLastDownloadTime()
        const lastSyncTime = githubGistService.getLastSyncTime()

        // 详细的时间比较调试信息
        const cloudDate = new Date(cloudUpdatedAtUTC)
        const syncDate = new Date(lastSyncTime!)
        const timeDiffMs = cloudDate.getTime() - syncDate.getTime()
        const timeDiffMinutes = timeDiffMs / (1000 * 60)

        console.group('🔍 云端更新检测 - 时间比较详情')
        console.log('📅 当前云端时间:')
        console.log('  - UTC字符串:', cloudUpdatedAtUTC)
        console.log('  - 本地显示:', cloudDate.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }))
        console.log('  - UTC时间戳:', cloudDate.getTime())
        console.log('')
        console.log('📅 同步历史:')
        console.log('  - 最后上传时间:', lastUploadTime || '从未上传')
        console.log('  - 最后下载时间:', lastDownloadTime || '从未下载')
        console.log('  - 最后同步时间:', lastSyncTime)
        console.log('  - 同步时间本地显示:', syncDate.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }))
        console.log('  - 同步时间戳:', syncDate.getTime())
        console.log('')
        console.log('⏰ 时间差异:')
        console.log('  - 毫秒差:', timeDiffMs)
        console.log('  - 分钟差:', Math.round(timeDiffMinutes * 100) / 100)
        console.log('  - 小时差:', Math.round((timeDiffMinutes / 60) * 100) / 100)
        console.log('')
        console.log('🎯 检测结果:')
        console.log('  - 云端是否更新:', hasUpdate)
        console.log('  - 判断逻辑: 当前云端时间', hasUpdate ? '>' : '<=', '最后同步时间')
        console.log('  - 修复说明: 比较当前云端时间与最后同步时间（上传/下载中较新的）')
        console.groupEnd()
      }

      lastCloudCheckResult.value = {
        time: new Date().toISOString(),
        hasUpdate
      }

      // 如果有更新，触发全局通知
      if (hasUpdate) {
        // 触发自定义事件，通知顶部导航栏
        window.dispatchEvent(new CustomEvent('cloud-update-detected', {
          detail: { updatedAt: result.details.updatedAt }
        }))
      }
    }
  } catch (error) {
    console.error('云检测失败:', error)
    lastCloudCheckResult.value = {
      time: new Date().toISOString(),
      hasUpdate: false
    }
  }
}

// 加载云检测配置
const loadCloudCheckConfig = () => {
  try {
    const saved = localStorage.getItem('cloud-check-config')
    if (saved) {
      const config = JSON.parse(saved)
      cloudCheckEnabled.value = config.enabled || false
      cloudCheckInterval.value = config.interval || 10

      if (cloudCheckEnabled.value && githubConnected.value) {
        startCloudCheck()
      }
    }
  } catch (error) {
    console.error('加载云检测配置失败:', error)
  }
}

// 更新GitHub状态（增强版）
const updateGitHubStatus = async () => {
  githubConnected.value = githubGistService.isConfigured()
  hasCloudBackup.value = githubGistService.hasCloudBackup()
  lastSyncTime.value = githubGistService.getLastSyncTime()

  if (githubConnected.value) {
    await fetchCloudBackupInfo()
    await generateDataPreview()
    updateAutoUploadStatus()
    loadCloudCheckConfig() // 加载云检测配置

    if (autoUploadEnabled.value) {
      startAutoUpload()
    }
  } else {
    // 如果断开连接，停止云检测
    stopCloudCheck()
  }
}

// 清理
onUnmounted(() => {
  stopAutoUpload()
  stopCloudCheck()
})
</script>
