import type { AiConfig, AiConfigForm, AiProvider, CustomModel } from '@/types'

/**
 * AI配置服务
 * 负责AI配置的存储、读取、验证和管理
 */
class AiConfigService {
  private readonly STORAGE_KEY = 'ai_configs'
  private readonly CUSTOM_MODELS_KEY = 'custom_models'
  private readonly DEFAULT_CONFIG_KEY = 'ai_default_config_id'
  private readonly DEFAULT_CONFIG: Partial<AiConfigForm> = {
    name: 'OpenAI GPT',
    provider: 'openai',
    baseUrl: 'https://api.openai.com/v1',
    modelName: 'gpt-4o-mini',
    temperature: 0.7,
    maxTokens: 4000,
    timeout: 30000,
    systemPrompt: '',
    customHeaders: {},
    customParams: {},
    enabled: false,
    isDefault: true,
  }

  /**
   * 获取所有AI配置
   */
  async getAllConfigs(): Promise<AiConfig[]> {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (!stored) {
        return []
      }

      const configs = JSON.parse(stored) as AiConfig[]
      // 转换日期字符串为Date对象
      return configs.map((config) => ({
        ...config,
        createdAt: new Date(config.createdAt),
        updatedAt: new Date(config.updatedAt),
      }))
    } catch (error) {
      console.error('获取AI配置失败:', error)
      return []
    }
  }

  /**
   * 获取默认AI配置
   */
  async getDefaultConfig(): Promise<AiConfig | null> {
    try {
      const configs = await this.getAllConfigs()
      const defaultConfigId = localStorage.getItem(this.DEFAULT_CONFIG_KEY)

      if (defaultConfigId) {
        const defaultConfig = configs.find((config) => config.id === defaultConfigId)
        if (defaultConfig) {
          return defaultConfig
        }
      }

      // 如果没有设置默认配置，返回第一个启用的配置
      const enabledConfig = configs.find((config) => config.enabled)
      if (enabledConfig) {
        return enabledConfig
      }

      // 如果没有启用的配置，返回第一个配置
      return configs.length > 0 ? configs[0] : null
    } catch (error) {
      console.error('获取默认AI配置失败:', error)
      return null
    }
  }

  /**
   * 根据ID获取配置
   */
  async getConfigById(id: string): Promise<AiConfig | null> {
    const configs = await this.getAllConfigs()
    return configs.find((config) => config.id === id) || null
  }

  /**
   * 保存AI配置
   */
  async saveConfig(configForm: AiConfigForm, configId?: string): Promise<AiConfig> {
    try {
      // 验证配置
      this.validateConfig(configForm)

      const configs = await this.getAllConfigs()
      const now = new Date()
      let config: AiConfig

      if (configId) {
        // 更新现有配置
        const existingIndex = configs.findIndex((c) => c.id === configId)
        if (existingIndex >= 0) {
          config = {
            ...configs[existingIndex],
            ...configForm,
            updatedAt: now,
          }
          configs[existingIndex] = config
        } else {
          throw new Error('配置不存在')
        }
      } else {
        // 创建新配置
        config = {
          id: this.generateId(),
          ...configForm,
          createdAt: now,
          updatedAt: now,
        }
        configs.push(config)
      }

      // 如果设置为默认配置，取消其他配置的默认状态
      if (configForm.isDefault) {
        configs.forEach((c) => {
          if (c.id !== config.id) {
            c.isDefault = false
          }
        })
        localStorage.setItem(this.DEFAULT_CONFIG_KEY, config.id)
      }

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(configs))

      console.log('AI配置保存成功:', config.name)
      return config
    } catch (error) {
      console.error('保存AI配置失败:', error)
      throw error
    }
  }

  /**
   * 删除AI配置
   */
  async deleteConfig(configId: string): Promise<void> {
    try {
      const configs = await this.getAllConfigs()
      const configIndex = configs.findIndex((c) => c.id === configId)

      if (configIndex === -1) {
        throw new Error('配置不存在')
      }

      const configToDelete = configs[configIndex]
      configs.splice(configIndex, 1)

      // 如果删除的是默认配置，设置第一个配置为默认
      if (configToDelete.isDefault && configs.length > 0) {
        configs[0].isDefault = true
        localStorage.setItem(this.DEFAULT_CONFIG_KEY, configs[0].id)
      } else if (configs.length === 0) {
        localStorage.removeItem(this.DEFAULT_CONFIG_KEY)
      }

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(configs))
      console.log('AI配置删除成功:', configToDelete.name)
    } catch (error) {
      console.error('删除AI配置失败:', error)
      throw error
    }
  }

  /**
   * 设置默认配置
   */
  async setDefaultConfig(configId: string): Promise<void> {
    try {
      const configs = await this.getAllConfigs()

      configs.forEach((config) => {
        config.isDefault = config.id === configId
      })

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(configs))
      localStorage.setItem(this.DEFAULT_CONFIG_KEY, configId)

      console.log('默认AI配置设置成功:', configId)
    } catch (error) {
      console.error('设置默认AI配置失败:', error)
      throw error
    }
  }

  /**
   * 获取默认配置
   */
  getDefaultConfigForm(): AiConfigForm {
    return {
      name: this.DEFAULT_CONFIG.name!,
      provider: this.DEFAULT_CONFIG.provider!,
      apiKey: '',
      baseUrl: this.DEFAULT_CONFIG.baseUrl!,
      modelName: this.DEFAULT_CONFIG.modelName!,
      temperature: this.DEFAULT_CONFIG.temperature!,
      maxTokens: this.DEFAULT_CONFIG.maxTokens!,
      timeout: this.DEFAULT_CONFIG.timeout!,
      systemPrompt: this.DEFAULT_CONFIG.systemPrompt!,
      customHeaders: this.DEFAULT_CONFIG.customHeaders!,
      customParams: this.DEFAULT_CONFIG.customParams!,
      enabled: this.DEFAULT_CONFIG.enabled!,
      isDefault: this.DEFAULT_CONFIG.isDefault!,
    }
  }

  /**
   * 根据提供商获取默认配置
   */
  getProviderDefaults(provider: AiProvider): Partial<AiConfigForm> {
    const defaults: Record<AiProvider, Partial<AiConfigForm>> = {
      openai: {
        baseUrl: 'https://api.openai.com/v1',
        modelName: 'gpt-4o-mini',
        temperature: 0.7,
        maxTokens: 4000,
        timeout: 30000,
      },
      claude: {
        baseUrl: 'https://api.anthropic.com/v1',
        modelName: 'claude-3-5-sonnet-20241022',
        temperature: 0.7,
        maxTokens: 4000,
        timeout: 30000,
        customHeaders: {
          'anthropic-version': '2023-06-01',
        },
      },
      gemini: {
        baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
        modelName: 'gemini-1.5-pro',
        temperature: 0.7,
        maxTokens: 4000,
        timeout: 30000,
      },
      baidu: {
        baseUrl: 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat',
        modelName: 'ernie-4.0-8k',
        temperature: 0.7,
        maxTokens: 4000,
        timeout: 30000,
      },
      alibaba: {
        baseUrl: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
        modelName: 'qwen-turbo',
        temperature: 0.7,
        maxTokens: 4000,
        timeout: 30000,
      },
      tencent: {
        baseUrl: 'https://hunyuan.tencentcloudapi.com',
        modelName: 'hunyuan-lite',
        temperature: 0.7,
        maxTokens: 4000,
        timeout: 30000,
      },
      zhipu: {
        baseUrl: 'https://open.bigmodel.cn/api/paas/v4',
        modelName: 'glm-4',
        temperature: 0.7,
        maxTokens: 4000,
        timeout: 30000,
      },
      moonshot: {
        baseUrl: 'https://api.moonshot.cn/v1',
        modelName: 'moonshot-v1-8k',
        temperature: 0.7,
        maxTokens: 4000,
        timeout: 30000,
      },
      bytedance: {
        baseUrl: 'https://ark.cn-beijing.volces.com/api/v3',
        modelName: 'doubao-lite-4k',
        temperature: 0.7,
        maxTokens: 4000,
        timeout: 30000,
      },
      custom: {
        baseUrl: '',
        modelName: '',
        temperature: 0.7,
        maxTokens: 4000,
        timeout: 30000,
      },
    }

    return defaults[provider] || {}
  }

  /**
   * 根据提供商获取可用模型列表
   */
  getAvailableModels(provider: AiProvider): Array<{ label: string; value: string }> {
    const models: Record<AiProvider, Array<{ label: string; value: string }>> = {
      openai: [
        { label: 'GPT-4o', value: 'gpt-4o' },
        { label: 'GPT-4o Mini', value: 'gpt-4o-mini' },
        { label: 'GPT-4 Turbo', value: 'gpt-4-turbo' },
        { label: 'GPT-4', value: 'gpt-4' },
        { label: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo' },
      ],
      claude: [
        { label: 'Claude 3.5 Sonnet', value: 'claude-3-5-sonnet-20241022' },
        { label: 'Claude 3 Sonnet', value: 'claude-3-sonnet-20240229' },
        { label: 'Claude 3 Haiku', value: 'claude-3-haiku-20240307' },
        { label: 'Claude 3 Opus', value: 'claude-3-opus-20240229' },
      ],
      gemini: [
        { label: 'Gemini 1.5 Pro', value: 'gemini-1.5-pro' },
        { label: 'Gemini 1.5 Flash', value: 'gemini-1.5-flash' },
        { label: 'Gemini Pro', value: 'gemini-pro' },
        { label: 'Gemini Pro Vision', value: 'gemini-pro-vision' },
      ],
      baidu: [
        { label: '文心一言 4.0', value: 'ernie-4.0-8k' },
        { label: '文心一言 3.5', value: 'ernie-3.5-8k' },
        { label: '文心一言 Turbo', value: 'ernie-turbo-8k' },
        { label: '文心一言 Speed', value: 'ernie-speed-8k' },
      ],
      alibaba: [
        { label: '通义千问 Max', value: 'qwen-max' },
        { label: '通义千问 Plus', value: 'qwen-plus' },
        { label: '通义千问 Turbo', value: 'qwen-turbo' },
        { label: '通义千问 Long', value: 'qwen-long' },
      ],
      tencent: [
        { label: '混元 Pro', value: 'hunyuan-pro' },
        { label: '混元 Standard', value: 'hunyuan-standard' },
        { label: '混元 Lite', value: 'hunyuan-lite' },
      ],
      zhipu: [
        { label: 'GLM-4', value: 'glm-4' },
        { label: 'GLM-4V', value: 'glm-4v' },
        { label: 'GLM-3 Turbo', value: 'glm-3-turbo' },
        { label: 'ChatGLM Pro', value: 'chatglm_pro' },
      ],
      moonshot: [
        { label: 'Moonshot v1 8K', value: 'moonshot-v1-8k' },
        { label: 'Moonshot v1 32K', value: 'moonshot-v1-32k' },
        { label: 'Moonshot v1 128K', value: 'moonshot-v1-128k' },
      ],
      bytedance: [
        { label: '豆包 Lite 4K', value: 'doubao-lite-4k' },
        { label: '豆包 Lite 32K', value: 'doubao-lite-32k' },
        { label: '豆包 Lite 128K', value: 'doubao-lite-128k' },
        { label: '豆包 Pro 4K', value: 'doubao-pro-4k' },
        { label: '豆包 Pro 32K', value: 'doubao-pro-32k' },
        { label: '豆包 Pro 128K', value: 'doubao-pro-128k' },
      ],
      custom: [
        // 添加服务商没有预设模型，只显示用户自定义的模型
      ],
    }

    return models[provider] || []
  }

  /**
   * 获取服务商显示标签
   */
  getProviderLabel(provider: AiProvider): string {
    const labels: Record<AiProvider, string> = {
      openai: 'OpenAI',
      claude: 'Claude',
      gemini: 'Google Gemini',
      baidu: '百度文心一言',
      alibaba: '阿里通义千问',
      tencent: '腾讯混元',
      zhipu: '智谱GLM',
      moonshot: '月之暗面Kimi',
      bytedance: '字节豆包',
      custom: '添加AI服务商',
    }
    return labels[provider] || provider
  }

  /**
   * 获取服务商颜色标识
   */
  getProviderColor(provider: AiProvider): string {
    const colors: Record<AiProvider, string> = {
      openai: '#10a37f',
      claude: '#cc785c',
      gemini: '#4285f4',
      baidu: '#2932e1',
      alibaba: '#ff6a00',
      tencent: '#00a971',
      zhipu: '#6366f1',
      moonshot: '#8b5cf6',
      bytedance: '#1677ff',
      custom: '#8c8c8c',
    }
    return colors[provider] || '#8c8c8c'
  }

  /**
   * 验证AI配置
   */
  private validateConfig(config: AiConfigForm): void {
    const errors: string[] = []

    // 验证配置名称
    if (!config.name || config.name.trim() === '') {
      errors.push('配置名称不能为空')
    }

    // 验证API Key
    if (!config.apiKey || config.apiKey.trim() === '') {
      errors.push('API Key不能为空')
    }

    // 验证Base URL
    if (!config.baseUrl || config.baseUrl.trim() === '') {
      errors.push('Base URL不能为空')
    } else {
      try {
        new URL(config.baseUrl)
      } catch {
        errors.push('Base URL格式不正确')
      }
    }

    // 验证模型名称
    if (!config.modelName || config.modelName.trim() === '') {
      errors.push('模型名称不能为空')
    }

    // 验证温度参数
    if (config.temperature < 0 || config.temperature > 2) {
      errors.push('温度参数必须在0-2之间')
    }

    // 验证最大token数
    if (config.maxTokens <= 0 || config.maxTokens > 100000) {
      errors.push('最大token数必须在1-100000之间')
    }

    // 验证超时时间
    if (config.timeout <= 0 || config.timeout > 300000) {
      errors.push('超时时间必须在1-300000毫秒之间')
    }

    if (errors.length > 0) {
      throw new Error(`配置验证失败: ${errors.join(', ')}`)
    }
  }

  /**
   * 测试AI配置连接
   */
  async testConfig(config: AiConfigForm): Promise<boolean> {
    try {
      // 构建测试请求
      const testMessage = '你好，这是一个连接测试。'
      const requestBody = this.buildRequestBody(config, testMessage)

      const response = await fetch(`${config.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${config.apiKey}`,
        },
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(config.timeout),
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      // 检查响应格式
      if (!data.choices || !Array.isArray(data.choices) || data.choices.length === 0) {
        throw new Error('API响应格式不正确')
      }

      console.log('AI配置测试成功')
      return true
    } catch (error) {
      console.error('AI配置测试失败:', error)
      throw error
    }
  }

  /**
   * 构建请求体
   */
  private buildRequestBody(config: AiConfigForm, message: string) {
    return {
      model: config.modelName,
      messages: [
        {
          role: 'user',
          content: message,
        },
      ],
      temperature: config.temperature,
      max_tokens: Math.min(config.maxTokens, 100), // 测试时限制token数
      stream: false,
    }
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `ai_config_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 检查配置是否已启用
   */
  async isEnabled(): Promise<boolean> {
    const config = await this.getDefaultConfig()
    return config?.enabled || false
  }

  /**
   * 切换启用状态
   */
  async toggleEnabled(): Promise<boolean> {
    const config = await this.getDefaultConfig()
    if (!config) {
      throw new Error('未找到AI配置')
    }

    const updatedForm: AiConfigForm = {
      name: config.name,
      provider: config.provider,
      apiKey: config.apiKey,
      baseUrl: config.baseUrl,
      modelName: config.modelName || this.DEFAULT_CONFIG.modelName!,
      temperature: config.temperature || this.DEFAULT_CONFIG.temperature!,
      maxTokens: config.maxTokens || this.DEFAULT_CONFIG.maxTokens!,
      timeout: config.timeout || this.DEFAULT_CONFIG.timeout!,
      enabled: !config.enabled,
      isDefault: config.isDefault,
    }

    await this.saveConfig(updatedForm, config.id)
    return updatedForm.enabled
  }

  // ==================== 自定义模型管理 ====================

  /**
   * 获取所有自定义模型
   */
  getCustomModels(): CustomModel[] {
    try {
      const stored = localStorage.getItem(this.CUSTOM_MODELS_KEY)
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      console.error('获取自定义模型失败:', error)
      return []
    }
  }

  /**
   * 根据服务商获取自定义模型
   */
  getCustomModelsByProvider(provider: AiProvider): CustomModel[] {
    return this.getCustomModels().filter((model) => model.provider === provider)
  }

  /**
   * 添加自定义模型
   */
  addCustomModel(provider: AiProvider, name: string, label: string): CustomModel {
    const customModels = this.getCustomModels()

    // 检查是否已存在相同名称的模型
    const exists = customModels.some((model) => model.provider === provider && model.name === name)

    if (exists) {
      throw new Error('该服务商已存在同名模型')
    }

    const newModel: CustomModel = {
      id: this.generateCustomModelId(),
      provider,
      name,
      label,
      createdAt: new Date(),
    }

    customModels.push(newModel)
    localStorage.setItem(this.CUSTOM_MODELS_KEY, JSON.stringify(customModels))

    return newModel
  }

  /**
   * 删除自定义模型
   */
  deleteCustomModel(modelId: string): boolean {
    try {
      const customModels = this.getCustomModels()
      const filteredModels = customModels.filter((model) => model.id !== modelId)

      if (filteredModels.length === customModels.length) {
        return false // 未找到要删除的模型
      }

      localStorage.setItem(this.CUSTOM_MODELS_KEY, JSON.stringify(filteredModels))
      return true
    } catch (error) {
      console.error('删除自定义模型失败:', error)
      return false
    }
  }

  /**
   * 获取包含自定义模型的完整模型列表
   */
  getAvailableModelsWithCustom(
    provider: AiProvider,
  ): Array<{ label: string; value: string; isCustom?: boolean; id?: string }> {
    // 获取系统预设模型
    const systemModels = this.getAvailableModels(provider).map((model) => ({
      ...model,
      isCustom: false,
    }))

    // 获取自定义模型
    const customModels = this.getCustomModelsByProvider(provider).map((model) => ({
      label: `${model.label} (自定义)`,
      value: model.name,
      isCustom: true,
      id: model.id,
    }))

    // 添加"添加自定义模型"选项
    const addCustomOption = {
      label: '+ 添加自定义模型',
      value: '__ADD_CUSTOM__',
      isCustom: false,
    }

    return [...systemModels, ...customModels, addCustomOption]
  }

  /**
   * 生成自定义模型ID
   */
  private generateCustomModelId(): string {
    return `custom_model_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }
}

// 导出单例实例
export const aiConfigService = new AiConfigService()
export default aiConfigService
