import './assets/main.css'
import 'uno.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

// Ant Design Vue
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'

// Vue Query
import { VueQueryPlugin, QueryClient } from '@tanstack/vue-query'

import App from './App.vue'
import router from './router'
import { knowledgeSettingsService } from './services/knowledgeSettingsService'
import { aiConfigMigration } from './services/aiConfigMigration'

const app = createApp(App)

// 创建 Query Client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5分钟
      gcTime: 10 * 60 * 1000, // 10分钟
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
})

app.use(createPinia())
app.use(router)
app.use(Antd)
app.use(VueQueryPlugin, { queryClient })

// 初始化知识库设置
knowledgeSettingsService.applyCSSVariables()

// 初始化AI配置数据迁移
aiConfigMigration.autoMigrate().catch((error) => {
  console.error('AI配置数据迁移失败:', error)
})

app.mount('#app')
