<template>
  <div>
    <!-- 当前分类节点 -->
    <div 
      :class="[
        'flex items-center px-4 py-2 text-sm transition-colors duration-150 cursor-pointer',
        'hover:bg-gray-100 dark:hover:bg-gray-700',
        selectedId === category.id ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400' : 'text-gray-700 dark:text-gray-300'
      ]"
      :style="{ paddingLeft: `${16 + level * 20}px` }"
      @click="handleSelect"
    >
      <!-- 展开/收起按钮 -->
      <button 
        v-if="hasChildren"
        type="button"
        :class="[
          'mr-2 p-0.5 rounded transition-transform duration-200',
          'hover:bg-gray-200 dark:hover:bg-gray-600',
          expanded ? 'rotate-90' : ''
        ]"
        @click.stop="toggleExpanded"
      >
        <div class="i-heroicons-chevron-right w-3 h-3"></div>
      </button>
      
      <!-- 占位空间（无子分类时） -->
      <div v-else class="w-4 mr-2"></div>
      
      <!-- 分类图标 -->
      <div :class="[
        'i-heroicons-folder',
        'w-4 h-4 mr-2 flex-shrink-0',
        selectedId === category.id ? 'text-primary-500' : 'text-gray-400'
      ]"></div>
      
      <!-- 分类名称 -->
      <div class="flex-1 flex items-center justify-between min-w-0">
        <span class="truncate">{{ category.name }}</span>
      </div>
      
      <!-- 选中标记 -->
      <div v-if="selectedId === category.id" class="i-heroicons-check w-4 h-4 ml-2 text-primary-500 flex-shrink-0"></div>
    </div>
    
    <!-- 子分类 -->
    <div v-if="hasChildren && expanded">
      <ParentCategoryTreeNode
        v-for="child in children"
        :key="child.id"
        :category="child"
        :selected-id="selectedId"
        :search-query="searchQuery"
        :level="level + 1"
        :all-categories="allCategories"
        @select="$emit('select', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { Category } from '@/types'

interface Props {
  category: Category
  selectedId: number | null
  searchQuery: string
  level: number
  allCategories: Category[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  select: [categoryId: number | null]
}>()

// 响应式数据
const expanded = ref(false)

// 计算属性
const children = computed(() => {
  return props.allCategories.filter(cat => cat.parent_id === props.category.id)
})

const hasChildren = computed(() => {
  return children.value.length > 0
})

// 如果有搜索查询且当前分类或其子分类匹配，则自动展开
const shouldAutoExpand = computed(() => {
  if (!props.searchQuery) return false
  
  // 检查当前分类是否匹配
  if (props.category.name.toLowerCase().includes(props.searchQuery.toLowerCase())) {
    return true
  }
  
  // 检查子分类是否有匹配的
  const hasMatchingChild = (categoryId: number): boolean => {
    const childCategories = props.allCategories.filter(cat => cat.parent_id === categoryId)
    return childCategories.some(child => 
      child.name.toLowerCase().includes(props.searchQuery.toLowerCase()) ||
      hasMatchingChild(child.id!)
    )
  }
  
  return hasMatchingChild(props.category.id!)
})

// 监听搜索查询变化，自动展开匹配的分类
watch(() => props.searchQuery, () => {
  if (shouldAutoExpand.value) {
    expanded.value = true
  } else if (!props.searchQuery) {
    expanded.value = false
  }
}, { immediate: true })

// 方法
const handleSelect = () => {
  emit('select', props.category.id!)
}

const toggleExpanded = () => {
  if (hasChildren.value) {
    expanded.value = !expanded.value
  }
}

// 初始化展开状态
if (props.searchQuery && shouldAutoExpand.value) {
  expanded.value = true
}
</script>
