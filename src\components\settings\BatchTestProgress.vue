<template>
  <!-- 批量测试管理卡片 - 统一紧凑型布局 -->
  <a-card size="small" class="batch-test-management-card">
    <template #title>
      <div class="card-title-wrapper">
        <span class="card-title">批量测试与启用</span>
        <a-tooltip title="一键测试所有图床配置，自动启用测试通过的图床" placement="bottom">
          <a-button type="text" size="small" class="help-icon-btn">
            <QuestionCircleOutlined />
          </a-button>
        </a-tooltip>
      </div>
    </template>

    <!-- 批量测试统计 -->
    <a-row :gutter="12" class="mb-3" v-if="batchTesting || batchTestCompleted">
      <a-col :span="6">
        <div class="stat-card">
          <div class="stat-icon stat-icon-primary">
            <DatabaseOutlined />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ batchProgress.total }}</div>
            <div class="stat-label">总数</div>
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="stat-card">
          <div class="stat-icon stat-icon-info">
            <ClockCircleOutlined />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ batchProgress.completed }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="stat-card">
          <div class="stat-icon stat-icon-success">
            <CheckCircleOutlined />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ batchProgress.results.success }}</div>
            <div class="stat-label">成功</div>
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="stat-card">
          <div class="stat-icon stat-icon-warning">
            <ExclamationCircleOutlined />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ batchProgress.results.failed }}</div>
            <div class="stat-label">失败</div>
          </div>
        </div>
      </a-col>
    </a-row>

    <!-- 操作工具栏 -->
    <div class="toolbar-section">
      <div class="toolbar-left">
        <a-button v-if="!batchTesting" type="primary" :disabled="configs.length === 0" @click="startBatchTest">
          <ThunderboltOutlined />
          开始批量测试
        </a-button>

        <a-button v-else danger @click="cancelBatchTest">
          <StopOutlined />
          取消测试
        </a-button>
      </div>

      <div class="toolbar-right" v-if="batchTestCompleted">
        <a-space>
          <a-button v-if="batchProgress.results.failed > 0" @click="retryFailedConfigs" size="small">
            <ReloadOutlined />
            重试失败 ({{ batchProgress.results.failed }})
          </a-button>

          <a-button @click="exportResults" size="small">
            <DownloadOutlined />
            导出报告
          </a-button>

          <a-button @click="clearBatchResults" size="small">
            <ClearOutlined />
            清除结果
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 进度显示区域 -->
    <div v-if="batchTesting || batchTestCompleted" class="progress-section">
      <!-- 总体进度条 -->
      <div class="progress-bar-container">
        <div class="progress-info">
          <span class="progress-text">
            {{ batchTesting ? '测试进行中...' : '测试完成' }}
          </span>
          <span class="progress-percent">{{ batchProgressPercent }}%</span>
        </div>

        <a-progress :percent="batchProgressPercent" :status="getProgressStatus()" :stroke-color="getProgressColor()"
          :show-info="false" stroke-width="8" />
      </div>

      <!-- 当前测试信息 -->
      <div v-if="batchTesting" class="current-test">
        <a-spin :spinning="true" size="small" />
        <span class="current-test-text">
          正在测试: <strong>{{ batchProgress.current || '准备中...' }}</strong>
        </span>
      </div>

    </div>

    <!-- 实时结果列表 - 紧凑型布局 -->
    <div v-if="batchTesting || batchTestCompleted" class="results-section">
      <div class="results-header">
        <span class="results-title">测试结果</span>
        <a-tag v-if="batchTestCompleted" :color="batchProgress.results.failed > 0 ? 'warning' : 'success'">
          {{ batchProgress.results.success }}/{{ batchProgress.total }} 通过
        </a-tag>
      </div>

      <div class="results-list-compact">
        <div v-for="config in configs" :key="config.id" class="result-item-compact" :class="getResultItemClass(config)">
          <div class="result-status">
            <a-spin v-if="isConfigTesting(config)" size="small" />
            <CheckCircleOutlined v-else-if="getConfigResult(config)?.success" class="success-icon" />
            <CloseCircleOutlined v-else-if="getConfigResult(config)" class="error-icon" />
            <ClockCircleOutlined v-else class="pending-icon" />
          </div>

          <div class="result-content-compact">
            <div class="result-header-compact">
              <span class="config-name-compact">{{ config.name }}</span>
              <div class="result-tags">
                <a-tag v-if="getConfigResult(config)" :color="getConfigResult(config)?.success ? 'success' : 'error'"
                  size="small">
                  {{ getConfigResult(config)?.success ? '通过' : '失败' }}
                </a-tag>
                <a-tag v-else-if="isConfigTesting(config)" color="processing" size="small">
                  测试中
                </a-tag>
                <a-tag v-else color="default" size="small">
                  等待中
                </a-tag>
                <a-tag v-if="config.enabled" color="blue" size="small">
                  已启用
                </a-tag>
              </div>
            </div>

            <div class="result-message-compact">
              {{ getConfigResult(config)?.message || (isConfigTesting(config) ? '正在测试连接...' : '等待测试') }}
            </div>

            <div v-if="getConfigResult(config)" class="result-meta">
              <span class="test-duration">
                耗时: {{ getConfigResult(config)?.duration || 0 }}ms
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import {
  QuestionCircleOutlined,
  DatabaseOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ThunderboltOutlined,
  StopOutlined,
  ReloadOutlined,
  DownloadOutlined,
  ClearOutlined,
  CloseCircleOutlined
} from '@ant-design/icons-vue'
import { useImageHostStore } from '@/stores/imageHostStore'
import type { ImageHostConfig } from '@/types/imageHost'

// 使用 store
const imageHostStore = useImageHostStore()

// 获取响应式状态
const {
  configs,
  batchTesting,
  batchProgress,
  batchResults,
  batchProgressPercent,
  batchTestCompleted,
} = storeToRefs(imageHostStore)

// 获取方法
const {
  batchTestAllConfigs,
  cancelBatchTest,
  retryFailedConfigs,
} = imageHostStore

// 开始批量测试
const startBatchTest = async () => {
  try {
    await batchTestAllConfigs()
  } catch (error) {
    console.error('批量测试失败:', error)
  }
}

// 获取进度条状态
const getProgressStatus = () => {
  if (batchTesting.value) return 'active'
  if (batchTestCompleted.value) {
    return batchProgress.value.results.failed > 0 ? 'exception' : 'success'
  }
  return 'normal'
}

// 获取进度条颜色
const getProgressColor = () => {
  if (batchTesting.value) return '#1890ff'
  if (batchTestCompleted.value) {
    return batchProgress.value.results.failed > 0 ? '#ff4d4f' : '#52c41a'
  }
  return '#1890ff'
}

// 获取配置的测试结果
const getConfigResult = (config: ImageHostConfig) => {
  return batchResults.value.get(config.id)
}

// 检查配置是否正在测试
const isConfigTesting = (config: ImageHostConfig) => {
  return batchTesting.value && batchProgress.value.current === config.name
}

// 获取结果项的样式类
const getResultItemClass = (config: ImageHostConfig) => {
  const result = getConfigResult(config)
  if (isConfigTesting(config)) return 'testing'
  if (result?.success) return 'success'
  if (result) return 'failed'
  return 'pending'
}

// 导出测试结果
const exportResults = () => {
  const results = Array.from(batchResults.value.entries()).map(([configId, result]) => {
    const config = configs.value.find((c: ImageHostConfig) => c.id === configId)
    return {
      name: config?.name || 'Unknown',
      success: result.success,
      message: result.message,
      duration: result.duration,
      enabled: config?.enabled || false
    }
  })

  const dataStr = JSON.stringify(results, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)

  const link = document.createElement('a')
  link.href = url
  link.download = `图床测试报告_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`
  link.click()

  URL.revokeObjectURL(url)
}

// 清除批量测试结果
const clearBatchResults = () => {
  batchResults.value.clear()
  batchProgress.value = {
    total: 0,
    completed: 0,
    current: '',
    results: {
      success: 0,
      failed: 0,
      enabled: 0
    }
  }
}
</script>

<style scoped>
/* 批量测试管理卡片 - 统一紧凑型布局 */
.batch-test-management-card {
  margin-bottom: 16px;
}

/* 卡片标题样式 - 与AI配置统一 */
.card-title-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.dark .card-title {
  color: #fff;
}

.help-icon-btn {
  color: #8c8c8c;
  border: none;
  background: transparent;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.help-icon-btn:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.06);
}

.dark .help-icon-btn:hover {
  color: #40a9ff;
  background: rgba(64, 169, 255, 0.1);
}

/* 统计卡片紧凑样式 - 与AI配置统一 */
.stat-card {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
  position: relative;
}

.stat-card:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.dark .stat-card {
  background: #1f1f1f;
  border-color: #303030;
}

.dark .stat-card:hover {
  border-color: #434343;
  box-shadow: 0 2px 6px rgba(255, 255, 255, 0.02);
}

.stat-icon {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-size: 16px;
  color: white;
}

.stat-icon-primary {
  background: #e6f7ff;
  color: #1890ff;
}

.stat-icon-success {
  background: #f6ffed;
  color: #52c41a;
}

.stat-icon-warning {
  background: #fffbe6;
  color: #faad14;
}

.stat-icon-info {
  background: #f0f5ff;
  color: #2f54eb;
}

.dark .stat-icon-primary {
  background: #111b26;
  color: #177ddc;
}

.dark .stat-icon-success {
  background: #162312;
  color: #49aa19;
}

.dark .stat-icon-warning {
  background: #2b2611;
  color: #d89614;
}

.dark .stat-icon-info {
  background: #10239e;
  color: #2f54eb;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  line-height: 1;
  margin-bottom: 2px;
}

.dark .stat-value {
  color: #fff;
}

.stat-label {
  font-size: 13px;
  color: #8c8c8c;
  font-weight: 400;
}

.dark .stat-label {
  color: #a6a6a6;
}

/* 工具栏 - 紧凑型统一标准 */
.toolbar-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 8px 0;
  gap: 12px;
}

.toolbar-left {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
}

.toolbar-right {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
}

/* 进度显示区域 - 紧凑型 */
.progress-section {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-top: 12px;
}

.dark .progress-section {
  background: #1f1f1f;
  border-color: #303030;
}

.progress-bar-container {
  margin-bottom: 12px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.progress-text {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

.dark .progress-text {
  color: #d9d9d9;
}

.progress-percent {
  font-weight: 600;
  color: #1890ff;
  font-size: 14px;
}

.current-test {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  background: #f6ffed;
  border-radius: 6px;
  border: 1px solid #b7eb8f;
}

.dark .current-test {
  background: #162312;
  border-color: #274916;
}

.current-test-text {
  color: #389e0d;
  font-size: 13px;
}

.dark .current-test-text {
  color: #49aa19;
}

/* 结果列表区域 - 紧凑型布局 */
.results-section {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-top: 12px;
}

.dark .results-section {
  background: #1f1f1f;
  border-color: #303030;
}

.results-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.results-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.dark .results-title {
  color: #fff;
}

.results-list-compact {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

/* 紧凑型结果项 */
.result-item-compact {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 12px;
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.dark .result-item-compact {
  background: #262626;
  border-color: #303030;
}

.result-item-compact.testing {
  background: #e6f7ff;
  border-color: #91d5ff;
}

.dark .result-item-compact.testing {
  background: #111b26;
  border-color: #1d39c4;
}

.result-item-compact.success {
  background: #f6ffed;
  border-color: #b7eb8f;
}

.dark .result-item-compact.success {
  background: #162312;
  border-color: #274916;
}

.result-item-compact.failed {
  background: #fff2f0;
  border-color: #ffccc7;
}

.dark .result-item-compact.failed {
  background: #2a1215;
  border-color: #58181c;
}

.result-item-compact.pending {
  background: #fafafa;
  border-color: #d9d9d9;
}

.dark .result-item-compact.pending {
  background: #1f1f1f;
  border-color: #303030;
}

/* 结果项内容 */
.result-status {
  flex-shrink: 0;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-icon {
  color: #52c41a;
  font-size: 16px;
}

.error-icon {
  color: #ff4d4f;
  font-size: 16px;
}

.pending-icon {
  color: #d9d9d9;
  font-size: 16px;
}

.result-content-compact {
  flex: 1;
  min-width: 0;
}

.result-header-compact {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.config-name-compact {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

.dark .config-name-compact {
  color: #fff;
}

.result-tags {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.result-message-compact {
  font-size: 12px;
  color: #666;
  margin-bottom: 6px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.dark .result-message-compact {
  color: #a6a6a6;
}

.result-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 11px;
  color: #8c8c8c;
}

.dark .result-meta {
  color: #737373;
}

.test-duration {
  color: #8c8c8c;
}

.dark .test-duration {
  color: #737373;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .results-list-compact {
    max-height: 250px;
  }

  .result-header-compact {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .result-tags {
    align-self: flex-end;
  }
}
</style>
